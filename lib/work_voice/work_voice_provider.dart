import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:flutter_lx/flutter_lx.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/voice_asr_callback.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/plugin/voice_assistant_plugin.dart';
import 'package:waimai_e_horn/waimai_e_horn.dart';

class WorkVoiceProvider extends ChangeNotifier with VoiceASRCallback {
  VoiceAssistantASRPlugin asrPlugin = VoiceAssistantASRPlugin();
  TimerHelper timerHelper = TimerHelper();

  WorkVoiceProvider() {
    asrPlugin.asrCallback = this;
  }
  loadingExam() {
    items = [];
    WaimaiEHorn.getHornValue('wmb_voice_exam').then((value) {
      if (value != null) {
        var json = jsonDecode(value);
        var section = json['section'];
        List dataList = section['dataList'];
        activity = section['activity'];
        if (dataList != null && dataList.length > 0) {
          dataList.forEach((element) {
            WorkVoiceItem item = WorkVoiceItem.fromJson(element);
            items.add(item);
          });
        }
      }
      notifyListeners();
    }).catchError((e) {});
  }

  String activity;
  WorkVoiceItem currenItem;
  List<WorkVoiceItem> items = [];
  int finishedRecord() {
    int result = 0;
    items.forEach((element) {
      if (element.state != ItemState.normal) {
        result++;
      }
    });
    return result;
  }

  bool canSubmit() {
    for (var element in items) {
      if (element.state == ItemState.normal) {
        return false;
      }
    }
    return true;
  }

  void startRecortdItem(WorkVoiceItem item) {
    if (currenItem != null) {
      currenItem.state = ItemState.normal;
    }
    currenItem = item;
    currenItem.state = ItemState.listening;
    timerHelper.start();
    asrPlugin.startListening();
    notifyListeners();
  }

  void resetRecordItem(WorkVoiceItem item) {
    currenItem.state = ItemState.normal;
    timerHelper.stop();
    currenItem = null;
    notifyListeners();
    asrPlugin.stopListening();
  }

  void stopRecordItem(WorkVoiceItem item) {
    if (currenItem != null) {
      currenItem.state = ItemState.analysing;
      timerHelper.stop();
      int value = timerHelper.getDuration().inSeconds;
      // 如果时间太短会导致用户无法收到回掉
      if (value < 0.2) {
        Future.delayed(Duration(milliseconds: 800), () {
          asrPlugin.stopListening();
        });
      } else {
        asrPlugin.stopListening();
      }
    }
  }

  int totalItems() {
    return items?.length;
  }

  void subMitResult(String poiID) {
    var result = [];
    items.forEach((element) {
      result.add(element.toJson());
    });
    FlutterLx.moudleClick(
        '42041416', 'c_waimai_e_7zqo13nu', 'b_waimai_e_k2msg965_mc',
        val: {"result": jsonEncode(result), "poiID": poiID});
  }

  @override
  void onASRFailed() {
    currenItem.state = ItemState.normal;
    currenItem = null;
    notifyListeners();
  }

  @override
  void onASRSuccess(String audioId, String result) {
    int value = timerHelper.getDuration().inSeconds;
    currenItem.state = ItemState.finish;
    currenItem.asrText = result;
    currenItem.second = value;
    currenItem = null;
    notifyListeners();
  }

  @override
  void onASRTempResult(String audioId, String result) {
    debugPrint("" + audioId + result);
  }

  @override
  void onOvertimeClose() {
    currenItem.state = ItemState.normal;
    currenItem = null;
    notifyListeners();
  }

  @override
  void onVoiceDBSize(double voiceDB) {
    debugPrint("vocieDB" + voiceDB.toString());
    if (currenItem != null) {
      currenItem.voiceDB.add(voiceDB.toString());
    }
  }
}

class WorkVoiceItem {
  int index;
  int second = 0;
  List<String> voiceDB = [];
  String voiceText;
  String asrText;
  ItemState state;
  WorkVoiceItem({this.index, this.state, this.voiceText, this.asrText});
  String statusStr() {
    if (state == ItemState.normal) {
      return "未识别到声音";
    } else if (state == ItemState.listening) {
      return "正在收录声音...";
    } else {
      if (second != null && second > 1) {
        return "停止录音，已收录音${second}秒";
      } else {
        return "收录声音小于1秒，建议重新录音";
      }
    }
  }

  String buttonStr() {
    return state.value;
  }

  factory WorkVoiceItem.fromJson(Map<String, dynamic> json) {
    return WorkVoiceItem(
      index: json['index'],
      state: ItemState.normal, // 假设 state 的值是 enum 的 index
      voiceText: json['voiceText'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'second': second,
      'voiceDB': jsonEncode(voiceDB),
      'voiceText': voiceText,
      'asrText': asrText,
    };
  }
}

class ItemState {
  static const ItemState normal = ItemState._('按住录音');
  static const ItemState listening = ItemState._('录音中');
  static const ItemState analysing = ItemState._('语音分析中');
  static const ItemState finish = ItemState._('重新录音'); // 播放声音
  final String value;
  const ItemState._(this.value);
}

class TimerHelper {
  DateTime _startTime;
  DateTime _endTime;

  TimerHelper() {
    _startTime = DateTime.now();
    _endTime = DateTime.now();
  }

  void start() {
    _startTime = DateTime.now();
  }

  void stop() {
    _endTime = DateTime.now();
  }

  Duration getDuration() {
    var duration = _endTime.difference(_startTime);
    String result = "Duration: " + duration.toString();
    return duration;
  }
}
