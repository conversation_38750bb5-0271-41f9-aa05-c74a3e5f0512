import 'dart:async';

import 'package:flap/flap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lx/flutter_lx.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:provider/provider.dart';
import 'package:roo_flutter/basic_components/action_sheet/roo_alert_action_section.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_flutter_house_keeper/work_voice/time_line.dart';
import 'package:waimai_e_flutter_order/common_class/index.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';

import 'work_voice_provider.dart';

@Flap('work_vocie')
@MTFRoute('work_vocie')
class WorkVoicePage extends StatelessWidget {
  const WorkVoicePage({this.params});
  final Map<dynamic, dynamic> params;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: _WorkVoicePage(
        params: params,
      ),
    );
  }
}

class _WorkVoicePage extends StatefulWidget {
  const _WorkVoicePage({Key key, this.params}) : super(key: key);

  @override
  final Map<dynamic, dynamic> params;

  @override
  State<_WorkVoicePage> createState() => _WorkVoicePagePageState();
}

class _WorkVoicePagePageState extends State<_WorkVoicePage>
    with SingleTickerProviderStateMixin, StatisticsMixin {
  @override
  String get cid => 'c_waimai_e_7zqo13nu';

  String doning =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/511b938a58e26a18/doing.png';
  String finishRecord =
      "https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/47d2f428210b870d/finish.png";
  WorkVoiceProvider _provider = WorkVoiceProvider();

  @override
  void initState() {
    super.initState();
    KNB
        .requestPermission(
            forceJump: true,
            type: 'microphone',
            sceneToken: "dj-ad492866f1fc5a10")
        .then((value) {
      if (value['status'] == "fail") {
        showDialog(
            context: context,
            builder: (context) {
              return RooDialog(
                context,
                subTitleText: '未监测到您有语音录制权限，请前往设置页面打开当前App的语音录制权限',
                confirmText: "我知道了",
                confirmCallback: () {
                  RouteUtils.close(context);
                },
              );
            });
      }
    });
    FlutterLx.moudleView(
        '42041416', 'c_waimai_e_7zqo13nu', 'b_waimai_e_k2msg965_mv');
    _provider.loadingExam();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ChangeNotifierProvider<WorkVoiceProvider>.value(
      value: _provider,
      child: SafeArea(
        top: false,
        child: Scaffold(
          appBar: AppBar(
            centerTitle: true,
            title: Text('工作环境语音测试活动'),
          ),
          body: Consumer<WorkVoiceProvider>(
            builder: (context, provider, child) {
              return LayoutBuilder(
                  builder: (BuildContext context, BoxConstraints constraints) {
                return _provider.items != null
                    ? Stack(
                        children: [
                          Container(
                              height: constraints.maxHeight,
                              color: Color(0xfff5f6fa),
                              child: SingleChildScrollView(
                                child: Column(
                                  children: [
                                    _builderHeader(),
                                    _builderContainer()
                                  ],
                                ),
                              )),
                          Positioned(
                            bottom: 0,
                            child: renderBottomWidget(),
                          ),
                        ],
                      )
                    : Container();
              });
            },
          ),
        ),
      ),
    );
  }

  Widget _builderHeader() {
    return Container(
      margin: EdgeInsets.only(left: 12, right: 12, top: 12),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(10))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 12, right: 12, top: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  '活动说明',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF222222),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 12),
                  child: Text(
                    _provider.activity ?? "",
                    style: TextStyle(
                      color: Color.fromRGBO(102, 102, 102, 1),
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      height: 1.21,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '数据安全说明',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF222222),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "① 仅在此页面，经过用户授权收取语音内容",
                        style: TextStyle(
                          color: Color(0xff666666),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Text(
                        "② 收取的语音仅用于测试，使用完毕会即时删除",
                        style: TextStyle(
                          color: Color(0xff666666),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _builderContainer() {
    return Container(
      margin: EdgeInsets.only(top: 12, bottom: 60),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(left: 12, right: 12),
            child: Stack(
              children: [
                Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color.fromRGBO(255, 244, 188, 1),
                            Color.fromRGBO(255, 246, 202, 1),
                          ],
                          begin: Alignment(0.83, 0.71),
                          end: Alignment(0.86, 0.07),
                        ),
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(10),
                            topRight: Radius.circular(10)),
                      ),
                      child: Container(
                        margin: EdgeInsets.only(
                            left: 12, right: 12, top: 12, bottom: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  '我们开始测试吧',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF222222),
                                  ),
                                ),
                                Spacer(),
                                RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: _provider
                                            .finishedRecord()
                                            .toString(),
                                        style: TextStyle(
                                          color: Colors.red,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                            "/${_provider.items.length.toString()}",
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 12),
                              child: Text(
                                "共有${_provider.items.length}条测试，请按住录音按钮进行测试",
                                style: TextStyle(
                                  color: Color(0xff666666),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.only(top: 12),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(10),
                              bottomRight: Radius.circular(10))),
                      child: Column(
                        children: [
                          Column(
                            children: _builderTimeLines(),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget renderBottomWidget() {
    double width = MediaQuery.of(context).size.width;
    return Container(
        width: width,
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          boxShadow: [
            BoxShadow(
                color: Color(0x1033312d), offset: Offset(0, -8), blurRadius: 15)
          ],
        ),
        child: SafeArea(
          child: Container(
            decoration: _provider.canSubmit()
                ? BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    gradient: LinearGradient(
                        begin: Alignment.bottomRight,
                        end: Alignment.topLeft,
                        colors: [Color(0xFFFFC34D), Color(0xFFFFE14D)]))
                : BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    color: Color(0xffEEEEEE)),
            margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: TextButton(
              style: ButtonStyle(
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(25)),
              ))),
              onPressed: () {
                if (!_provider.canSubmit()) {
                  return;
                }

                /// 判断当前是否存在门店ID。如果不存在门店ID 则不让用户提交。
                WaimaiENativeBusiness.getPoiInfo().then((poiInfo) {
                  if (poiInfo != null && poiInfo.poiId != null) {
                    Loading.showLoading(title: '提交结果中', showmask: true);
                    _provider.subMitResult(poiInfo.poiId);
                    Future.delayed(Duration(seconds: 1), () {
                      Loading.dismissLoading();
                      Loading.showToast(
                          message: "问卷提交成功", duration: Duration(seconds: 1));
                      Future.delayed(Duration(seconds: 1), () {
                        if (context != null) {
                          RouteUtils.close(context);
                        }
                      });
                    });
                  } else {
                    Loading.showToast(
                        message: "请登录后进行问卷调研", duration: Duration(seconds: 1));
                    Future.delayed(Duration(seconds: 1), () {
                      if (context != null) {
                        RouteUtils.close(context);
                      }
                    });
                  }
                });
              },
              child: GestureDetector(
                child: Text(
                  '提交测试',
                  style: TextStyle(
                      color: Color(0xff222222),
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        ));
  }

  List<Widget> _builderTimeLines() {
    List<Widget> widgetList = [];
    for (int i = 0; i < _provider.items.length; i++) {
      WorkVoiceItem item = _provider.items[i];
      if (i == 0) {
        widgetList.add(_buildTimelineTile(item,
            isFirst: true, isLast: _provider.items.length == 1 ? true : false));
      } else if (i == _provider.items.length - 1) {
        widgetList.add(_buildTimelineTile(item, isLast: true));
      } else {
        widgetList.add(_buildTimelineTile(item));
      }
    }
    return widgetList;
  }

  TimelineTile _buildTimelineTile(WorkVoiceItem serviceLog,
      {isFirst = false, isLast = false}) {
    Color lineColor = Color(0xffEEEEEE);

    return TimelineTile(
        afterLineStyle: LineStyle(color: lineColor, thickness: 1),
        beforeLineStyle: LineStyle(color: lineColor, thickness: 1),
        indicatorStyle: _getIndicatorStyle(serviceLog),
        isLast: isLast,
        isFirst: isFirst,
        endChild: _buildLineContent(serviceLog));
  }

  Widget _buildLineContent(WorkVoiceItem log) {
    var width = MediaQuery.of(context).size.width - 73 - 58;
    return Container(
      child: Container(
          margin: EdgeInsets.only(right: 6, top: 6, bottom: 32),
          child: Row(
            children: [
              Column(
                children: [
                  Container(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: width,
                          child: Text(
                            log.voiceText,
                            maxLines: 2,
                            textAlign: TextAlign.left,
                            style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 16,
                                color: Color(0xff222222)),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 4),
                          child: Text(
                            log.statusStr(),
                            style: TextStyle(
                                color: Color(0xff666666),
                                fontSize: 12,
                                fontWeight: FontWeight.w400),
                          ),
                        )
                      ],
                    ),
                  ),
                  Container()
                ],
              ),
              Spacer(),
              GestureDetector(
                onTapDown: (detail) {
                  _provider.startRecortdItem(log);
                },
                onTapUp: (detail) {
                  _provider.stopRecordItem(log);
                },
                onTapCancel: () {
                  _provider.resetRecordItem(log);
                },
                child: Container(
                  alignment: Alignment.center,
                  width: 73,
                  height: 32,
                  child: Text(
                    log.buttonStr(),
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Color(0xff222222)),
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(32),
                    border: Border.all(
                      color: Color.fromRGBO(
                          153, 153, 153, 1), // Grey color with 0.6 opacity
                      width: 1,
                    ),
                  ),
                ),
              )
            ],
          )),
    );
  }

  IndicatorStyle _getIndicatorStyle(WorkVoiceItem log) {
    double xy = 0.01;
    if (log.state == ItemState.normal) {
      return IndicatorStyle(
        indicatorXY: xy,
        drawGap: true,
        width: 16,
        height: 16,
        indicator: Container(
          alignment: Alignment.center,
          child: Text(
            '${log.index}',
            style: TextStyle(
                fontSize: 12, fontWeight: FontWeight.w500, color: Colors.white),
          ),
          decoration: BoxDecoration(
              color: Color(0xffcccccc),
              borderRadius: BorderRadius.all(Radius.circular(8))),
        ),
        padding: EdgeInsets.all(6),
      );
    } else if (log.state == ItemState.listening ||
        log.state == ItemState.analysing) {
      return IndicatorStyle(
        indicatorXY: xy,
        drawGap: true,
        width: 20,
        height: 20,
        indicator: Image.network(doning),
        padding: EdgeInsets.all(6),
      );
    } else if (log.state == ItemState.finish) {
      return IndicatorStyle(
        indicatorXY: xy,
        drawGap: true,
        width: 20,
        height: 20,
        indicator: Image.network(finishRecord),
        padding: EdgeInsets.all(6),
      );
    }
  }
}
