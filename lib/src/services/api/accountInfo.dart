import 'package:waimai_e_fe_flutter_finance/src/services/model/assetAccountInfo.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// AccountInfo
Future<AssetAccountInfoModel> fetchAccountInfo(Map<String, dynamic> params) {
  return comGetApi(
          path: '/finance/waimai/account/api/poiAccountBaseInfo',
          params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return AssetAccountInfoModel.fromJson(response?.data);
  });
}

Future<AssetAccountInfoModel> fetchSchoolAccountInfo(
    Map<String, dynamic> params) {
  return comGetApi(
          path: '/finance/waimai/money/channel/poiAccountBaseInfo',
          params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return AssetAccountInfoModel.fromJson(response?.data);
  });
}
