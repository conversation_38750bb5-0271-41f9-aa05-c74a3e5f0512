import 'dart:async';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lx/flutter_lx.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_statistics_route/mtf_statistics_exposure_container.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/string_utils.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/aigcAnalysis.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

/// APP上的AIGC缩略入口
class AigcCard extends StatefulWidget {
  AigcCard(this.sceneType, this.dailyBillDate);

  final String sceneType;
  final String dailyBillDate;
  @override
  State<StatefulWidget> createState() => _AigcCardState();
}

class _AigcCardState extends State<AigcCard> {
  final String cid = 'c_waimai_e_72m50aaj';
  final String pageInfoKey = 'AIGC_CARD';
  final String authorization = 'finance_aigc_analysis_authorize';
  String template;
  bool isAuthorized = false;
  bool isOSLow = true;
  bool isLoading = true;

  /// 是否展示新AIGC面板
  bool showNewAigcPanel = false;

  /// 流式订阅
  StreamSubscription<String> _streamSubscription;

  /// 是否正在流式加载
  bool isStreamLoading = false;

  String acctId = Util.getUrlParam('acctId') ?? Util.getCookie('acctId');
  String wmPoiId = Util.getUrlParam('wmPoiId') ?? Util.getCookie('wmPoiId');
  String token = Util.getUrlParam('token') ?? Util.getCookie('token');

  @override
  void initState() {
    super.initState();
    if (PlatformTool.isAndroid) {
      KNB.getDeviceInfo(sceneToken: '*').then((deviceInfo) {
        if (deviceInfo != null &&
            StringUtil.isNotEmpty(deviceInfo['osVersion'])) {
          String osVersion = deviceInfo['osVersion'];
          int os = int.tryParse(osVersion.split('.')[0]);
          if (os != null && os >= 7) {
            if (mounted) {
              setState(() {
                isOSLow = false;
              });
            }
            checkAuthorizationAndFetchData();
          }
        }
      });
    } else {
      if (mounted) {
        setState(() {
          isOSLow = false;
        });
      }
      checkAuthorizationAndFetchData();
    }
  }

  void checkAuthorizationAndFetchData() {
    WaimaiENativeBusiness.getUserInfo().then((userInfo) {
      if (userInfo == null || StringUtil.isEmpty(userInfo.acctId)) {
        return;
      }
      String acctId = userInfo.acctId;
      KNB.getStorage(key: '${authorization}_${acctId}').then((result) {
        if (result != null &&
            result.containsKey('value') &&
            StringUtil.isNotEmpty(result['value']) &&
            result['value'] == 'true') {
          if (mounted) {
            setState(() {
              // isAuthorized = true;
            });
          }
        }
        initData();
      });
    });
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AigcCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (isAuthorized &&
        widget.sceneType == AigcSceneType.dailyBill &&
        oldWidget.dailyBillDate != widget.dailyBillDate) {
      initData();
    }
  }

  void initData() {
    fetchAigcNewGray().then((value) {
      showNewAigcPanel = value;
      if (isAuthorized) {
        fetchAigcTemplate(value);
      }
      setState(() {
        isLoading = false;
      });
    });
  }

  void fetchAigcTemplate(value) {
    if (value) {
      fetchAigcTemplateWithStream();
    } else {
      fetchAigcTemplateContent(widget.sceneType, widget.dailyBillDate)
          .then((value) {
        if (value != null && mounted) {
          setState(() {
            template = value;
          });
        }
      });
    }
  }

  /// 使用流式接口获取内容，失败时降级到原接口
  void fetchAigcTemplateWithStream() {
    print('🚀 开始流式请求: sceneType=${widget.sceneType}, dailyBillDate=${widget.dailyBillDate}');

    // 取消之前的订阅
    _streamSubscription?.cancel();

    if (mounted) {
      setState(() {
        isStreamLoading = true;
        template = ''; // 清空之前的内容
      });
    }

    String accumulatedContent = '';

    _streamSubscription = fetchAigcTemplateStreamContent(widget.sceneType, widget.dailyBillDate)
        .listen(
      (data) {
        print('📥 UI收到流式数据: "$data" 时间: ${_formatTime(DateTime.now())}');
        // 累积流式数据
        accumulatedContent += data;
        if (mounted) {
          setState(() {
            template = accumulatedContent;
            // 一旦收到第一个数据，就停止显示loading状态
            if (isStreamLoading && accumulatedContent.isNotEmpty) {
              isStreamLoading = false;
            }
            print('🎨 UI更新template: "$template" 时间: ${_formatTime(DateTime.now())}');
          });
        }
      },
      onDone: () {
        print('✅ UI流式请求完成 时间: ${_formatTime(DateTime.now())}');
        if (mounted) {
          setState(() {
            isStreamLoading = false;
          });
        }
      },
      onError: (error) {
        print('❌ 流式请求失败: $error');
        // 流式接口失败，降级到原接口
        if (mounted) {
          setState(() {
            isStreamLoading = false;
          });
          fallbackToOriginalApi();
        }
      },
    );
  }

  /// 降级到原接口
  void fallbackToOriginalApi() {
    fetchAigcTemplateNewContent(widget.sceneType, widget.dailyBillDate)
        .then((value) {
      if (value != null && mounted) {
        setState(() {
          template = value;
        });
      }
    });
  }

  String getBidPrefix() {
    switch (widget.sceneType) {
      case AigcSceneType.home:
        // 模块visible时灰度接口已返回
        return showNewAigcPanel ? 'b_waimai_e_v38336jc' : 'b_waimai_e_zczigiax';
      case AigcSceneType.dailyBill:
        return 'b_waimai_e_svajhg5m';
      case AigcSceneType.balanceFlow:
        return 'b_waimai_e_nh6h7wnh';
    }

    return '';
  }

  void showAuthorizationDialog() {
    showDialog(
        context: context,
        builder: (context) {
          return RooDialog(context,
              title: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(width: 20),
                  Text('财务管家上线啦！'),
                  GestureDetector(
                      onTap: () {
                        if (Navigator.canPop(context)) {
                          Navigator.of(context).pop();
                        }
                      },
                      child: Icon(
                        Icons.close,
                        size: 20,
                        color: Color(0xFF666666),
                      ))
                ],
              ),
              confirmText: '立即启用', confirmCallback: () {
            if (mounted) {
              setState(() {
                isAuthorized = true;
              });
            }
            WaimaiENativeBusiness.getUserInfo().then((userInfo) {
              if (userInfo == null || StringUtil.isEmpty(userInfo.acctId)) {
                return;
              }
              String acctId = userInfo.acctId;
              KNB.setStorage(
                  key: '${authorization}_${acctId}', value: "true", level: 1);
            });
            fetchAigcTemplate(showNewAigcPanel);
          },
              content: Container(
                  padding: EdgeInsets.fromLTRB(12, 4, 12, 0),
                  child: Column(children: [
                    Text(
                      '使用您的门店经营数据为您提供智能分析服务，让经营分析更简单！',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF666666),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 24),
                    Text(
                      '报告由AI生成，仅供参考。',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                      ),
                    ),
                  ])));
        });
  }

  Widget analyzeString(String content) {
    // 如果正在加载且没有内容，显示空白等待
    if (isStreamLoading && StringUtil.isEmpty(content)) {
      return Container(
        padding: EdgeInsets.only(top: 10),
        child: SizedBox.shrink(),
      );
    }

    // 如果有内容，即使还在加载中也显示内容
    if (!StringUtil.isEmpty(content)) {
      return _buildContentWidget(content);
    }

    if (StringUtil.isEmpty(content)) {
      if (isAuthorized) {
        return Container(
          padding: EdgeInsets.only(top: 10, bottom: 12),
          child: Text('暂无经营数据',
              style: TextStyle(fontSize: 12, color: Color(0xFF999999))),
        );
      } else {
        return Container(
          padding: EdgeInsets.only(top: 4),
          child: Text('使用您的门店经营数据为您提供智能分析服务，让经营分析更简单！',
              style: TextStyle(fontSize: 12, color: Color(0xFF666666))),
        );
      }
    }

    return Container();
  }

  /// 构建内容显示组件
  Widget _buildContentWidget(String content) {
    RegExp linkRegExp = RegExp(r'\[(.*?)\|(.*?)\]');
    List<TextSpan> spans = [];
    int start = 0;

    for (final Match match in linkRegExp.allMatches(content)) {
      if (match.start > start) {
        spans.add(TextSpan(
          text: content.substring(start, match.start),
          style: TextStyle(color: Color(0xFF222222), fontSize: 12),
        ));
      }

      String text = match.group(1);
      String property = match.group(2);

      if (property.startsWith('http') ||
          property.startsWith('/') ||
          property.startsWith('itakeawaybiz')) {
        spans.add(
          TextSpan(
            text: text,
            style: TextStyle(color: Color(0xFFFF6A00), fontSize: 12),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                RouteUtils.open(property);
              },
          ),
        );
      } else {
        spans.add(
          TextSpan(
            text: text,
            style: TextStyle(color: Color(0xFFFF6A00), fontSize: 12),
          ),
        );
      }

      start = match.end;
    }

    if (start < content.length) {
      spans.add(TextSpan(
        text: content.substring(start),
        style: TextStyle(color: Color(0xFF222222), fontSize: 12),
      ));
    }

    return Container(
        margin: EdgeInsets.only(top: 10),
        child: RichText(
          overflow: TextOverflow.ellipsis,
          maxLines: 3,
          text: TextSpan(
            children: spans,
          ),
        ));
  }

  getAigcUrl() {
    if (PlatformTool.isWeb) {
      String baseUrl = Util.getOriginUrl();
      String acctId = Util.getUrlParam('acctId') ?? Util.getCookie('acctId');
      String wmPoiId = Util.getUrlParam('wmPoiId') ?? Util.getCookie('wmPoiId');
      String token = Util.getUrlParam('token') ?? Util.getCookie('token');
      return '${baseUrl}/finance/fe/aigcAnalysis_v2?token=${token}&acctId=${acctId}&wmPoiId=${wmPoiId}';
    } else {
      return "https://waimaieapp.meituan.com/finance/fe/aigcAnalysis_v2";
    }
  }

  Widget _buildTitle() {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Row(children: [
        Image.network(
          showNewAigcPanel
              ? 'https://p0.meituan.net/merchantoperation/ai_manage_logo'
              : 'http://p0.meituan.net/tuling/f801c3237149d349de8c978e4bab21d92562.png',
          width: 16,
          height: 16,
        ),
        Container(
            margin: EdgeInsets.only(left: 2, right: 4),
            child: Text(
              '智能财务分析',
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: Color(0xFF222222)),
            )),
        GestureDetector(
          onTap: () {
            showDialog(
                context: context,
                builder: (context) {
                  return RooDialog(
                    context,
                    contentText:
                        '💡财务管家是一款为外卖商家设计的账单分析工具，它能基于财务数据，利用自然语言处理技术，智能生成对账分析报告，努力使商家对账更简单。\n💪功能目前处于内测阶段，还在持续迭代中，内容仅供参考。',
                    confirmText: '我知道了',
                  );
                });
          },
          child: Image(
              width: 14,
              image: NetworkImage(
                  'http://p0.meituan.net/tuling/1922f6d6ed30414cf74acbef94eca82f712.png')),
        ),
      ]),
      Visibility(
          visible: StringUtil.isNotEmpty(template),
          child: Row(
            children: [
              Text('详情',
                  style: TextStyle(
                      fontSize: 12, color: Color(0xFF666666))),
              Image(
                  width: 12,
                  image: NetworkImage(
                      'https://p0.meituan.net/ingee/50072cb3dacd1c741d6f3bef7bed205e631.png')),
            ],
          ))
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
        visible: !isOSLow && !isLoading,
        child: MTFStatisticsExposureContainer(
            pageInfoKey: pageInfoKey,
            cid: cid,
            bid: '${getBidPrefix()}_mv',
            child: GestureDetector(
              onTap: StringUtil.isNotEmpty(template) ? () {
                FlutterLx.moudleClick(
                    pageInfoKey, cid, '${getBidPrefix()}_mc');
                RouteUtils.open(showNewAigcPanel
                    ? getAigcUrl()
                    : 'https://waimaieapp.meituan.com/finance/fe/aigcAnalysis?sceneType=${widget.sceneType}&dailyBillDate=${widget.dailyBillDate}');
              } : null,
              child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 14, vertical: 12),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          fit: BoxFit.fill,
                          image: NetworkImage(showNewAigcPanel
                              ? 'https://p0.meituan.net/merchantoperation/ai_manage_bg'
                              : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/d98309eddd9e6466/<EMAIL>')),
                      borderRadius: BorderRadius.circular(10.5)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [_buildTitle(), analyzeString(template)]),
                        ),
                        isAuthorized
                            ? Container()
                            : GestureDetector(
                                child: Container(
                                  width: 72,
                                  height: 28,
                                  margin: EdgeInsets.only(left: 16),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(colors: [
                                      Color(0XFFFFE74D),
                                      Color(0XFFFFDD1A)
                                    ]),
                                    borderRadius: BorderRadius.circular(14),
                                  ),
                                  child: Text('立即启用',
                                      style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF222222),
                                          fontWeight: FontWeight.w500)),
                                ),
                                onTap: () => showAuthorizationDialog(),
                              ),
                      ])),
            )));
  }

  /// 格式化时间为易读格式
  String _formatTime(DateTime dateTime) {
    return '${dateTime.year}-${_pad(dateTime.month)}-${_pad(dateTime.day)} '
        '${_pad(dateTime.hour)}:${_pad(dateTime.minute)}:${_pad(dateTime.second)}.'
        '${dateTime.millisecond.toString().padLeft(3, '0')}';
  }

  /// 数字补零
  String _pad(int number) {
    return number.toString().padLeft(2, '0');
  }
}
