import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/accountInfo/components/util.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/steps.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/settleListUtils.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/accountInfo.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/aigcCard.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/aigcPanel.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/aigcPanelWrapper.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/appBar.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/menu.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/message.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/multiPoiPage.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/notify.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/poi_info.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/settleList.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/settlePeriod.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/tab.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/monthStatic.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderList/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/historyFlow.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/monthBill.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/aigcAnalysis.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/historyFlows.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/commonUtil.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

import 'widgets/realTimeCard.dart';

@Flap('finance')
@MTFRoute('test_finance')
class HomePage extends StatelessWidget {
  HomePage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: HomeWidget(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class HomeWidget extends StatefulWidget {
  HomeWidget({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;
  @override
  _HomeWidgetState createState() => _HomeWidgetState();
}

class _HomeWidgetState extends State<HomeWidget> with HomeMixin {
  final GlobalKey<MoneyInfoState> moneyInfoKey = GlobalKey();
  ScrollController _controller;
  SimpleInfoModel simpleInfoModel;
  WithdrawDisplayMsgModel withdrawDisplayMsgModle;

  bool isMultiple;
  bool isMultiPoiGray;

  // 待结算账单
  SettleBill settlingBill;
  // 已结算账单
  List<SettleBill> settledBillList = [];

  bool displayMessage = false;
  int tabIndex = 0;
  int pageSize = 20;
  int pageNo = 1;
  // 总数
  int totalCount = 0;
  double showFilterHeight = 350;

  // 是否展示滚动后的账单筛选区域
  bool showBillFilter = false;

  // 是否点击了筛选数据按钮
  bool showFilterData = false;

  Map param = Map();

  int preNow = 0;

  bool isLoadingHistoryData = false;
  bool hasMoreData = true;
  Timer timer;

  String newKfUrl = '';
  int commissionRebateDate;

  /// 最近流水记录内容
  List<HistoryFlowsModel> flowList = [];
  String wmPoiId;

  /// 是否为滚动结算
  int isDelay;
  int delayNum;

  /// 是否展示更新标识
  int isIncrementDelay;

  /// 是否展示旧AIGC面板
  bool showAigcPanel = false;

  /// 是否展示新AIGC面板
  bool showNewAigcPanel = false;

  /// 是否展示预开票入口
  bool showEntryGray = false;
  bool showOpenSesame = false;
  bool isNewAigcPanel = false;

  @override
  void initState() {
    super.initState();
    Util.isMultiple().then((value) {
      isMultiple = value;
      if (value == false) {
        // 单店的情况下请求
        initParam();
        fetchData();
        fetchSettleBillData();

        fetchAigcGray(AigcSceneType.home).then((value) => setData(() {
              showAigcPanel = value;
            }));
      } else {
        fetchMultiGray();
      }
    });
    if (!PlatformTools.isPC) {
      fetchEntryGray().then((value) => setData(() {
            showEntryGray = value;
          }));
    }
    Util.getPoiId().then((value) {
      wmPoiId = value;
    });
    if (widget.params != null && widget?.params['tabIndex'] != null) {
      tabIndex = 2;
    }
    fetchDatas();
    _controller = ScrollController();
    Function throttleScrollCallBack = CommonUtils.throttleFun(scrollCallback);
    _controller.addListener(throttleScrollCallBack);
    ReportLX.pv(pageKeyInfo, cid);

    fetchAigcNewGray().then((value) {
      if (mounted) {
        setState(() {
          showNewAigcPanel = value;
        });
      }
    });
  }

  fetchDatas() async {
    // 查询是否可以开票
    int data = await fetchOpenSesame();
    setState(() {
      showOpenSesame = data != 0;
    });
  }

  void setData(Function fn) {
    if (!mounted) {
      return;
    }
    setState(fn);
  }

  scrollCallback() {
    if (!mounted) return;
    if (PlatformTool.isPC) return;

    /// 如果是统计页面，不应该继续执行监听逻辑
    if (tabIndex == 1 || tabIndex == 2) return;
    if (_controller == null) return;
    double offset = _controller.offset;
    if (offset >= _controller.position.maxScrollExtent) {
      return;
    }
    if (offset >= _controller.position.maxScrollExtent * 0.9 &&
        isLoadingHistoryData == false &&
        hasMoreData) {
      fetchSettleBillData();
    }
    if (offset >= showFilterHeight && showBillFilter == false) {
      setData(() {
        showBillFilter = true;
      });
    } else if (offset < showFilterHeight && showBillFilter == true) {
      setData(() {
        showBillFilter = false;
      });
    }
  }

  // 新手引导
  void _showNewbeeGuideOverlay() async {
    final result = await KNB.getStorage(key: 'Finance_StoreNewbeeGuide');
    // 不存在该值，或者为 0，则表示未展示新手引导
    if (result['value'] == "" && mounted) {
      showDialog(
          context: context,
          barrierDismissible: true,
          builder: (
            BuildContext context,
          ) {
            return NewbeeSteps(params: {"moneyInfoKey": moneyInfoKey});
          });
    }
  }

  initParam() {
    String today =
        DateFormat.formatYYYYMMDD(DateTime.now().millisecondsSinceEpoch);
    String fourMonth = DateFormat.formatYYYYMMDD(DateFormat.threeMonthsAgo());
    param.putIfAbsent('pageSize', () => pageSize);
    param.putIfAbsent('pageNo', () => pageNo);
    param.putIfAbsent('settleBillStartDate', () => fourMonth);
    param.putIfAbsent('settleBillEndDate', () => today);
  }

  fetchData() {
    fetchSimpleInfo().then((simpleInfo) {
      setData(() {
        simpleInfoModel = simpleInfo;
        commissionRebateDate = simpleInfo?.commissionRebateDate;
      });

      // 显示新手引导
      if (!PlatformTool.isWeb) {
        // 新手引导
        _showNewbeeGuideOverlay();
      }

      /// 神抢手引导
      _getUserIsGuideModel();
    });
    if (PlatformTools.isPC) {
      Map para = {
        'beginDate': '',
        'endDate': '',
        'pageNo': 1,
        'pageSize': 4,
        "acctType": AccountTypeMap.balanceType
      };
      fetchAccountFlows(para).then((AccountFlowsList data) {
        setData(() {
          flowList = data?.flowList ?? [];
        });
      });
    }
    fetchDisplayMsg().then((withdrawDisplayMsg) {
      setData(() {
        withdrawDisplayMsgModle = withdrawDisplayMsg;
      });
    });
    if (!PlatformTools.isPC) {
      // 获取客服URL
      fetchKfUrl().then((url) {
        newKfUrl = url;
        setData(() {});
      });
    }
  }

  fetchMultiGray() {
    fetchMultiPoiGray().then((inGray) {
      isMultiPoiGray = inGray;
      setState(() {});
    });
  }

  /// 神抢手引导
  void _getUserIsGuideModel() async {
    // final SharedPreferences prefs = await SharedPreferences.getInstance();
    final resultPc = PlatformTools.isPC
        ? await HomeUtils.getLocalStorage(
            '${Util.getCookie('wmPoiId')}Finance_UserGuide')
        : '';
    if (PlatformTools.isPC && resultPc == '' && commissionRebateDate != null) {
      showDialog(
          context: context,
          builder: (context) {
            return RooDialog(
              context,
              title: Container(),
              edgeInsets: EdgeInsets.all(0),
              content: Container(
                  child: Column(
                children: [
                  Transform.translate(
                      offset: Offset(0, -12),
                      child: Image.network(
                          'https://p0.meituan.net/ingee/2d28927034a56b0498d90f2a9643aee228244.png')),
                  Container(
                      padding: EdgeInsets.all(12),
                      child: Column(
                        children: [
                          Text('参加神抢手活动的商品，平台将进行佣金优惠，并返还到您的账户。可以在日账单详情页查看。'),
                          SizedBox(
                            height: 19,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(),
                              Container(
                                  child: Row(
                                children: [
                                  Container(
                                    width: 64,
                                    height: 36,
                                    child: RooShapeButton(
                                      child: Text("取消"),
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                        HomeUtils.setLocalStorage(
                                            '${Util.getCookie('wmPoiId')}Finance_UserGuide',
                                            '1');
                                      },
                                    ),
                                  ),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Container(
                                      width: 64,
                                      height: 36,
                                      child: RooGradientButton(
                                        child: Text("查看"),
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                          RouterTools.flutterPageUrl(
                                              context, '/dailyBills',
                                              params: {
                                                "dailyBillDate":
                                                    DateFormat.formatYYYYMMDD(
                                                  simpleInfoModel
                                                      .commissionRebateDate,
                                                ),
                                                "key": '2'
                                              });
                                          HomeUtils.setLocalStorage(
                                              '${Util.getCookie('wmPoiId')}Finance_UserGuide',
                                              '1');
                                        },
                                      ))
                                ],
                              ))
                            ],
                          )
                        ],
                      ))
                ],
              )),
            );
          });
    }
  }

  fetchSettleBillData({
    int paramPageNo,
  }) {
    Loading.showLoading();
    isLoadingHistoryData = true;
    int curPageNo = paramPageNo ?? pageNo;
    // 查询待结算/已结算
    fetchHistorySettleBill(curPageNo, param).then((historySettleBillModel) {
      if (historySettleBillModel != null) {
        if (pageNo == 1) {
          settledBillList = [];
          settlingBill = null;
        }
        // 已结算总数
        totalCount = historySettleBillModel?.settledBillCount ?? 0;
        // 滚动结算判断
        isDelay = historySettleBillModel?.isDelay ?? 0;
        isIncrementDelay = historySettleBillModel?.isIncrementDelay ?? 0;
        delayNum = historySettleBillModel?.delayNum ?? 0;
        // 结算中
        List<SettleBill> list = historySettleBillModel?.unSettledBills ?? [];
        if (list?.isNotEmpty == true) {
          settlingBill = list[0];
        }
        // 已结算
        List<SettleBill> resSettledBills =
            historySettleBillModel?.settledBills ?? [];
        // 两端分页区别处理
        if (PlatformTools.isPC) {
          settledBillList = resSettledBills;
          pageNo = curPageNo;
        } else {
          settledBillList.addAll(resSettledBills);
          if (isDelay != null && isDelay == 1) {
            SettleListUtils.tryPlaySettleListStatisticAsg();
          }
          // 这个判断条件无法满足PC分页能力
          if (settledBillList.length >= totalCount) {
            hasMoreData = false;
          } else {
            pageNo += 1;
            hasMoreData = true;
          }
        }
      }
    }).whenComplete(() {
      isLoadingHistoryData = false;
      // 筛选数据
      filterData();
      Loading.dismissLoading();
    });
  }

  // 筛选数据
  filterData() {
    if (!showFilterData) {
      setData(() {});
      return;
    }
    if (settlingBill != null) {
      int subCnt = 0;
      List<DailyBill> newDailyBills = [];
      settlingBill.dailyBills?.forEach((e) {
        if (e.dailyBillAmount != 0) {
          newDailyBills.add(e);
        }
        subCnt += e.dailyBillAmount;
      });
      settlingBill.dailyBills = newDailyBills;
      if (settlingBill.totalAmount == 0 && subCnt == 0) {
        settlingBill = null;
      }
    }

    List<SettleBill> newSettledBillList = [];
    if (settledBillList != null && settledBillList.length > 0) {
      settledBillList.forEach((settledBill) {
        int subCnt = 0;
        List<DailyBill> newDailyBills = [];
        settledBill.dailyBills.forEach((bill) {
          subCnt += bill.dailyBillAmount;
          if (bill.dailyBillAmount != 0) {
            newDailyBills.add(bill);
          }
        });
        // 筛选不为0的数据
        settledBill.dailyBills = newDailyBills;
        if (settledBill.totalAmount != 0 || subCnt != 0) {
          newSettledBillList.add(settledBill);
        }
      });
      settledBillList = newSettledBillList;
    }
    setData(() {});
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  _scrollToTop() {
    if (_controller != null) {
      _controller.animateTo(
        .0,
        duration: Duration(milliseconds: 200),
        curve: Curves.ease,
      );
    }
    showBillFilter = false;
  }

  /// 中间查询区域
  _buildPositionWidget() {
    // 将筛选区域缓存下来，确保app上吸顶内容和实际内容引用同一个
    return SettlePeriod(
      showBillFilter: showBillFilter,
      filterData: showFilterData,
      simpleInfoModel: simpleInfoModel,
      isDelay: isDelay,
      isIncrementDelay: isIncrementDelay,
      delayNum: delayNum,
      onMonthSelected: (List<String> values) {
        if (values != null && values.length > 2) {
          String startDate = values[0];
          String endDate = values[1];
          pageNo = 1;
          _scrollToTop();
          param = {
            'pageSize': pageSize,
            'pageNo': pageNo,
            'settleBillStartDate': DateFormat.changeSplitChar(startDate),
            'settleBillEndDate': DateFormat.changeSplitChar(endDate),
          };
          fetchSettleBillData();
        }
      },
      cb: () {
        showFilterData = !showFilterData;
        pageNo = 1;
        _scrollToTop();
        fetchSettleBillData();
      },
    );
  }

  toTop() {
    return Positioned(
      right: 12,
      bottom: 26,
      child: GestureDetector(
        onTap: () {
          _scrollToTop();
          setData(() {});
        },
        child: Image.network(
          'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/cdc0bf1b82f8232afad8cdafdfd3458e/top.png',
          width: 40,
          height: 40,
        ),
      ),
    );
  }

  /// 根据冻结来源frozenReasonType （5，6）-- 女娲 展示冻结原因
  freezeReasons(int frozenReasonType, String frozenReason) {
    if (frozenReason != null &&
        (frozenReasonType == 5 || frozenReasonType == 6)) {
      return frozenReason;
    }
    return '很抱歉，您当前门店的账户已冻结，无法提现。如有问题请联系业务经理处理，相关款项会在账户解冻后的下个账期发起提现。谢谢！';
  }

  edit() {
    RouteUtils.open('${Util.getMainOrigin()}/v2/shop/manage');
  }

  editApp() async {
    final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
    String host =
        'https://waimaieapp.meituan.com/igate/shangdan_qualification_m/qualification.html?source=1';
    if (envInfo != null) {
      if (envInfo["hostType"] == "TEST") {
        host =
            "http://e.platform.proxy.b.waimai.test.sankuai.com/igate/shangdan_qualification_m/qualification.html?source=1";
      }
      if (envInfo["hostType"] == "STAGE") {
        host =
            "https://proxy.waimai.st.sankuai.com/igate/shangdan_qualification_m/qualification.html?source=1";
      }
    }
    RouteUtils.open(host);
  }

  // 顶部账户基础信息+滚动提现金额信息
  renderBasicInfo() {
    bool isFrozen = simpleInfoModel?.isFrozenFlag != null &&
        simpleInfoModel?.isFrozenFlag != 0;
    bool fronzeReasonTypes = simpleInfoModel?.frozenReasonType != null &&
        simpleInfoModel?.frozenReasonType == 5;
    return Container(
      color: ResponsiveSystem.bothAppPc(
        runApp: Colors.white,
        runPc: Colors.transparent,
      ),
      child: Column(
        children: <Widget>[
          SizedBox(
            height: isFrozen ? 10 : 0,
          ),
          isFrozen
              ? GestureDetector(
                  child: Container(
                      height: 38,
                      padding: EdgeInsets.only(left: 12, right: 12),
                      decoration: BoxDecoration(
                          color: Color(0x1AFF192D),
                          borderRadius: BorderRadius.circular(
                              ResponsiveSystem.bothAppPc(
                                  runApp: 9.5, runPc: 0))),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(children: [
                            Container(
                              width: 9,
                              height: 11.5,
                              margin: EdgeInsets.only(right: 8),
                              child: Image.network(
                                  'https://p0.meituan.net/ingee/6c2e10b550197dc1be0f942cf7792e63622.png'),
                            ),
                            Text('门店被冻结，影响正常结算，无法提现',
                                style: TextStyle(fontSize: 12))
                          ]),
                          Container(
                              child: Text(
                            '查看详情',
                            style: TextStyle(fontSize: 12),
                          )),
                        ],
                      )),
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (context) {
                          return RooDialog(
                            context,
                            titleText: "门店被冻结",
                            content: Container(
                                child: Text(
                                    freezeReasons(
                                        simpleInfoModel?.frozenReasonType,
                                        simpleInfoModel?.frozenReason),
                                    // style: TextStyle(),
                                    textAlign: PlatformTool.isPC
                                        ? TextAlign.left
                                        : TextAlign.center)),
                            cancelText: fronzeReasonTypes ? "关闭" : null,
                            confirmText: fronzeReasonTypes ? "修改资质信息" : '我知道了',
                            confirmAutoClose: false,
                            cancelCallback: () {},
                            confirmCallback: () {
                              Navigator.of(context).pop();
                              if (fronzeReasonTypes) {
                                PlatformTools.isPC ? edit() : editApp();
                              }
                            },
                          );
                        });
                  },
                )
              : SizedBox.shrink(),
          SizedBox(
            height: PlatformTool.isPC && isFrozen ? 10 : 0,
          ),
          ResponsiveSystem(
            pc: PoiBaseInfo(
              simpleInfoModel: simpleInfoModel,
            ),
          ),
          AccountInfo(
            simpleInfoModel: simpleInfoModel,
            recentBillList: flowList,
          ),
          ResponsiveSystem(
            app: MoneyInfo(
              key: moneyInfoKey,
              simpleInfoModel: simpleInfoModel,
            ),
          ),
        ],
      ),
    );
  }

  // 白色背景区域，包含基本信息、实时账单、账期
  backgroudWhiteArea() {
    EdgeInsets appPading = EdgeInsets.fromLTRB(12, 12, 12, 0);
    return Container(
      color: ResponsiveSystem.bothAppPc(
          runApp: Colors.white, runPc: Colors.transparent),
      padding: ResponsiveSystem.bothAppPc(
        runApp: appPading,
        runPc: EdgeInsets.zero,
      ),
      child: Column(
        children: <Widget>[
          // 服务端下发的文本，可关闭
          Message(
            display: withdrawDisplayMsgModle?.display == true,
            message: withdrawDisplayMsgModle?.message,
            url: withdrawDisplayMsgModle?.url,
            closeTap: () {
              setData(() {
                withdrawDisplayMsgModle?.display = false;
              });
            },
          ),
          // 基本信息，黄色块区域
          renderBasicInfo(),

          /// 今日实时账单【饼图】
          RealTimeWCard(cid: cid, pageKeyInfo: pageKeyInfo),
          // 结算周期
          ResponsiveSystem(
            app: _buildPositionWidget(),
          ),
        ],
      ),
    );
  }

  // 灰色背景，包含：待结算、已结算、无数据提示
  backgroudGrayArea() {
    return Container(
      padding: EdgeInsets.all(ResponsiveSystem.bothAppPc(
        runApp: 12.0,
        runPc: 0.0,
      )),
      child: Column(children: <Widget>[
        Visibility(
            visible: showAigcPanel && !PlatformTool.isPC,
            child: Container(
                margin: EdgeInsets.only(bottom: 12),
                child: AigcCard(
                    AigcSceneType.home,
                    DateFormat.formatXfYYYYMMDD(
                        DateTime.now().millisecondsSinceEpoch)))),
        SettleBillWidget(
          settlingBill: settlingBill,
          settledBillList: settledBillList,
          pageNo: pageNo,
          totalPage: (totalCount / pageSize).ceil(),
          isDelay: isDelay,
          onPageChange: (int currentPage) {
            fetchSettleBillData(paramPageNo: currentPage);
          },
        ),
        isLoadingHistoryData && hasMoreData
            ? CupertinoActivityIndicator()
            : SizedBox(),
      ]),
    );
  }

  getIframeUrl() {
    String baseUrl = Util.getOriginUrl();
    String acctId = Util.getUrlParam('acctId') ?? Util.getCookie('acctId');
    String wmPoiId = Util.getUrlParam('wmPoiId') ?? Util.getCookie('wmPoiId');
    String token = Util.getUrlParam('token') ?? Util.getCookie('token');
    return '${baseUrl}/finance/fe/aigcAnalysis_v2?token=${token}&acctId=${acctId}&wmPoiId=${wmPoiId}';
  }

  // 底部加载问题
  refreshWidget() {
    if (isMultiple == null) {
      return SizedBox.shrink();
    }
    if (isMultiple) {
      return MultiPoiPage(isMultiPoiGray);
    } else {
      return showAigcPanel && PlatformTool.isPC
          ? CustomScrollView(
              controller: _controller,
              scrollDirection: Axis.horizontal,
              slivers: <Widget>[
                SliverToBoxAdapter(
                  child: Container(
                    width: 1014,
                    child: CustomScrollView(
                      controller: ScrollController(),
                      scrollDirection: Axis.vertical,
                      slivers: <Widget>[
                        SliverToBoxAdapter(
                          child: backgroudWhiteArea(),
                        ),
                        SliverToBoxAdapter(
                          child: ResponsiveSystem(
                            pc: _buildPositionWidget(),
                          ),
                        ),
                        SliverToBoxAdapter(
                          child: backgroudGrayArea(),
                        ),
                      ],
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: showNewAigcPanel
                      ? AigcPanelWrapper(
                          sceneType: AigcSceneType.home,
                          dailyBillDate: DateFormat.formatXfYYYYMMDD(
                              DateTime.now().millisecondsSinceEpoch),
                          iframeUrl: getIframeUrl(),
                        )
                      : AigcPanel(
                          AigcSceneType.home,
                          DateFormat.formatXfYYYYMMDD(
                              DateTime.now().millisecondsSinceEpoch)),
                )
              ],
            )
          : SingleChildScrollView(
              controller: _controller,
              child: Column(
                children: <Widget>[
                  backgroudWhiteArea(),
                  ResponsiveSystem(
                    pc: _buildPositionWidget(),
                  ),
                  backgroudGrayArea(),
                ],
              ),
            );
    }
  }

  homeAndMonth() {
    return Flex(
      direction: Axis.vertical,
      children: <Widget>[
        // 对账  / 统计页签
        ResponsiveSystem(
          app: CustomTab(
            cb: (index) {
              // 对账 or 统计 or 订单
              tabIndex = index;
              // 切换tab时，把回到顶部和筛选区域隐藏
              showBillFilter = false;
              setData(() {});
            },
            param: widget.params != null ? widget.params : null,
          ),
        ),
        tabIndex == 0
            ? Expanded(
                flex: 1,
                child: refreshWidget(),
              )
            : SizedBox.shrink(),
        tabIndex == 1
            ? Expanded(
                flex: 1,
                child: MonthStatic(),
              )
            : SizedBox.shrink(),
        tabIndex == 2
            ? Expanded(
                flex: 1,
                child: OrderListPage(
                  param: widget.params != null ? widget.params : null,
                ))
            : SizedBox.shrink(),
      ],
    );
  }

  bool showMenu = false;
  menuTap() {
    setData(() {
      showMenu = !showMenu;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF7F8FA),
      appBar: UITools.renderNavbar(
        context: context,
        title: '财务对账',
        actions: [
          HomeAppbar(
            moreTap: menuTap,
            kfUrl: newKfUrl,
            showEntryGray: showEntryGray,
            showOpenSesame: showOpenSesame,
          )
        ],
      ),
      body: Stack(
        children: <Widget>[
          homeAndMonth(),
          showBillFilter ? toTop() : SizedBox(),
          ResponsiveSystem(
            app: showBillFilter
                ? Positioned(
                    width: UITools.getScreenWidth(context),
                    child: Container(
                      color: Colors.white,
                      padding: EdgeInsets.fromLTRB(12, 0, 12, 0),
                      child: _buildPositionWidget(),
                    ),
                    top: 30,
                  )
                : SizedBox(),
          ),
          showMenu
              ? Menu(
                  showEntryGray: showEntryGray, showOpenSesame: showOpenSesame)
              : SizedBox(),
        ],
      ),
    );
  }
}
