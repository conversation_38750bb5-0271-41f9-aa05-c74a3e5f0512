import 'package:flutter/material.dart';
// import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
// import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question_icon.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/tag.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/recent_bill.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/historyFlows.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';

class AccountInfo extends StatefulWidget {
  AccountInfo({
    @required this.simpleInfoModel,
    @required this.recentBillList,
  });
  final SimpleInfoModel simpleInfoModel;
  final List<HistoryFlowsModel> recentBillList;

  @override
  AccountInfoState createState() => AccountInfoState();
}

class AccountInfoState extends State<AccountInfo> with HomeMixin {
  SimpleInfoModel simpleInfoModel;

  String acctManagerText = '银行卡、推广费、保证金管理';
  // 合规账户
  bool isLimitedAccount = false;

  @override
  void initState() {
    super.initState();
    fetchShowSpecialItem().then((show) {
      if (show == false) {
        isLimitedAccount = true;
        setState(() {
          acctManagerText = '银行卡、推广费管理';
        });
      }
    });
  }

  _buildAvalableMoney() {
    return Row(
      children: [
        Text(
          '可提现余额 (元）',
          style: TextStyle(
            color: Color(0xFF222222),
            fontWeight: FontWeight.w400,
            fontSize: 12,
          ),
        ),
        RooTooltip(
            lineWordCount: 100,
            target: QuestionIcon(
              color: Color(0xFFFFE862),
            ),
            title: '详情',
            tip:
                '以下情况无法发起手动提现操作：\n\n1. 当前可提现余额小于最低提现金额\n\n2. 当前门店属于多门店合并打款\n\n3. 可提现余额小于等于0')
      ],
    );
  }

  frozenText() {
    return Text(
      '很抱歉，您当前门店的账户已冻结，无法提现。\n如有问题请联系业务经理处理，相关款项会在账户解冻后的下个账期发起提现。\n谢谢！',
      style: TextStyle(
          color: Color(0xFF666666),
          fontWeight: FontWeight.w500,
          fontSize: 14,
          height: 1.5),
    );
  }

  moneyToBank() {
    /// 参数是接口请求回来的异步数据，一开始是null, 没有做判断，也会使得isFrozen=true
    int asyncFrozenFlag = widget.simpleInfoModel?.isFrozenFlag;
    bool isFrozen = asyncFrozenFlag != null && asyncFrozenFlag != 0;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (isFrozen) {
          return;
        }
        RouterTools.flutterPageUrl(
          context,
          '/balanceWithdraw',
          params: {"acctType": 0},
        );
        // PlatformTools.isPC
        //     ? MTFlutterWebUtils.bridgeJump(
        //         '/finance/web/balanceWithdraw?acctType=0')
        //     : RouteUtils.open(
        //         'https://waimaieapp.meituan.com/finance/fe/balanceWithdraw?acctType=0');
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_ia5lwsyt_mc');
      },
      child: Row(
        children: <Widget>[
          Container(
            // width: 67.5,
            padding: EdgeInsets.fromLTRB(20, 6, 20, 6),
            height: 32,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Center(
              child: Text(
                '提现',
                textAlign: TextAlign.center,
                strutStyle: StrutStyle(
                  forceStrutHeight: true,
                ),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(isFrozen ? 0xFFCCCCCC : 0xFF222222),
                ),
              ),
            ),
          ),
          // isFrozen
          //     ? Row(
          //         children: <Widget>[
          //           SizedBox(width: 6),
          //           Question(
          //             onTap: () {
          //               Modal.showModalDialog(context,
          //                   title: '账户已冻结', child: frozenText());
          //             },
          //           )
          //         ],
          //       )
          //     : SizedBox()

          // 提现提示
        ],
      ),
    );
  }

  toBankPage() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        RouterTools.flutterPageUrl(context, '/accountInfo');
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_4fk6m6d5_mc');
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // 账户和图标
          Row(
            children: <Widget>[
              Image.network(
                'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/20e6aac46d60abad856eee96b6e1193c/bank-card.png',
                width: 20,
                height: 20,
              ),
              SizedBox(
                width: 4,
              ),
              Text(
                '账户',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              )
            ],
          ),
          // 银行卡、推广费、保证金管理
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Text(
                '$acctManagerText',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                ),
                strutStyle: PlatformTool.isWeb
                    ? null
                    : StrutStyle(
                        forceStrutHeight: true,
                        height: 1,
                      ),
              ),
              ArrowIcon(),
            ],
          )
        ],
      ),
    );
  }

  _buildMoneyCnt() {
    int cnt = widget.simpleInfoModel?.balance ?? 0;
    // 合规账户余额<0时，展示0
    if (isLimitedAccount) {
      cnt = cnt < 0 ? 0 : cnt;
    }
    int cntLength = '$cnt'.length;
    double size = 32;
    if (PlatformTool.isPC) {
      size = 35;
    } else {
      if (cntLength >= 7 && cntLength < 9) {
        size = 24;
      } else if (cntLength > 9) {
        size = 18;
      }
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            // PC下不触发点击跳转
            if (PlatformTools.isPC) return;
            RouterTools.flutterPageUrl(context, '/balanceFlow');
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_vh5j1p87_mc');
          },
          child: Row(
            children: <Widget>[
              Text(
                MoneyTool.formatMoney(cnt),
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w600,
                  fontSize: size,
                ),
              ),
              ResponsiveSystem(
                app: ArrowIcon(),
              ),
              ResponsiveSystem(
                app: settleTypeWidget(),
              ),
            ],
          ),
        ),
        ResponsiveSystem(
          app: moneyToBank(),
        ),
      ],
    );
  }

  settleTypeWidget() {
    String type = '';
    switch (widget.simpleInfoModel?.settleType) {
      case 1:
        type = '自动提现';
        break;
      case 2:
        type = '自动汇入账户';
        break;
      case 3:
        type = '手动提现';
        break;
    }
    if (type != '') {
      return Tag(text: type);
    }
    return SizedBox();
  }

  _buildPoiColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 可提现金额文本
        _buildAvalableMoney(),
        SizedBox(height: 5),
        // 真正的钱数
        _buildMoneyCnt(),
        // 空白区域
        SizedBox(
          height: ResponsiveSystem.bothAppPc(
            runApp: 37.0,
            runPc: 30.0,
          ),
        ),
        ResponsiveSystem(
          pc: moneyToBank(),
        ),
        ResponsiveSystem(
          app: toBankPage(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSystem(
      app: Container(
        margin: EdgeInsets.fromLTRB(0, 13, 0, 0),
        padding: EdgeInsets.fromLTRB(16, 24, 16, 24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.5),
          gradient: yellowGradient,
        ),
        child: _buildPoiColumn(),
      ),
      pc: Container(
        margin: EdgeInsets.fromLTRB(0, 1, 0, 0),
        padding: EdgeInsets.fromLTRB(30, 24, 16, 24),
        decoration: BoxDecoration(
          image: DecorationImage(
              image: NetworkImage(
                'http://p0.meituan.net/tuling/44daaeafe9604c18cb4b9fa2886ff47337829.png',
              ),
              fit: BoxFit.fill),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(6),
            bottomRight: Radius.circular(6),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: 400,
              child: _buildPoiColumn(),
            ),
            RecentBillListPage(
              recentBillList: widget?.recentBillList ?? [],
            ),
          ],
        ),
      ),
    );
  }
}
