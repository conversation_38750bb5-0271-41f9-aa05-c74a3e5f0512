import 'dart:core';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_scene_constant.dart';
import 'package:waimai_e_flutter_order_stone/native_plugins/order_business_plugin.dart';

class VoiceAssistantPlugin {
  VoiceAssistantPlugin() {
    init();
  }

  static final VoiceAssistantPlugin _instance = VoiceAssistantPlugin();
  static const _voicePlugin = MethodChannel('com.wme.wake/plugin');
  static const methodPlayTTS = "playTTS";

  static VoiceAssistantPlugin get instance => _instance;

  void init() {
    _voicePlugin.setMethodCallHandler(_methodHandler);
  }

  Future<dynamic> _methodHandler(MethodCall call) async {
    debugPrint('asr from flutter plugin ${call.method}');
    switch (call.method) {
      //美团唤醒后asr识别回调：
      case 'onASR':
        OrderBusinessPlugin.isForeground().then((isForeground) {
          if (isForeground) {
            Map<String, dynamic> params = {
              'mainEntranceType': entranceFromWake,
            };
            final url = SchemeUrls.flutterPageUrl('voice_assistant_gpt',
                params: params, channel: 'waimai_e_flutter');
            RouteUtils.open(url, present: true, opaque: false);
          }
          FlutterLx.moudleClick(
              '42041416', 'c_waimai_e_7zqo13nu', 'b_waimai_e_283os4ey_mc',
              val: {'status': isForeground ? 1 : 0});
        });

        // funcFoodDone(command);
        // debugPrint(' 👺  on asr from flutter plugin $command');
        // VoiceCommandManager.handleCommand(
        //     PrepareCompleteCommand(command), CommandType.newPrepareComplete);
        break;
    }
  }

  Future invokeMethod(String method, dynamic arguments) {
    return _voicePlugin.invokeMethod(method, arguments);
  }
}
