import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/prepared_voice_command.dart';

class VoiceCommandConstant {
  static String orderPreparedSuccess(PrepareCompleteCommand command) =>
      '${command?.orderId}号${command.isPreOrder ? '预' : ''}订单出餐成功';

  static String orderNotFound(PrepareCompleteCommand command) =>
      '没有找到${command?.orderId}号${command.isPreOrder ? '预' : ''}订单';

  static String orderPreparedError(PrepareCompleteCommand command) =>
      '${command?.orderId}号${command.isPreOrder ? '预' : ''}订单出餐失败';

  static const String parseFailure = '未能识别您的语音指令，请您按照标准语音指令上报出餐。';

  static const String parseError = '语音指令未识别';
}

// 语音指令对应的类型
enum CommandType {
  newPrepareComplete, // 新订单策略 订单出餐完成
}
