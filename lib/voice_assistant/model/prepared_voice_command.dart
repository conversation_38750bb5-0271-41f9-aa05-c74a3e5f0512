import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/voice_command.dart';
import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/voice_command_constant.dart';
import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/voice_command_result.dart';
import 'package:waimai_e_flutter_house_keeper/voice_assistant/voice_assistant_plugin.dart';

// 出餐完成指令
class PrepareCompleteCommand extends VoiceCommand {
  PrepareCompleteCommand(String command) : super(command: command);

  VoiceCommand successParse(orderId, isPreOrder) {
    parseError = false;
    this.orderId = orderId;
    this.isPreOrder = isPreOrder;
    return this;
  }

  VoiceCommand failParse(String errorInfo) {
    parseError = true;
    this.errorInfo = errorInfo;
    return this;
  }

  int orderId;
  bool isPreOrder;

  @override
  void complete(CommandResult result) {
    debugPrint('👺   command Result $command ${result.info}');
    if (result == null) {
      return;
    }

    List<double> valueList = [];
    double resultCode = result.isSuccess ? 1 : 0;
    valueList.add(resultCode);
    Map<String, List<double>> values = {'voice_assistant_order': valueList};

    Map<String, String> tags = {
      'platform': Platform.isAndroid ? 'android' : 'ios',
      'errorCode': result?.reportInfo ?? 'otherFailure',
      'command': command,
      'orderID': '${orderId ?? -1}',
    };
    FlutterCat.reportCustomField(values: values, tags: tags);

    if (result.info?.isEmpty ?? true) {
      return;
    }

    VoiceAssistantPlugin.instance
        .invokeMethod(VoiceAssistantPlugin.methodPlayTTS, result.info);
  }

  @override
  void failToParse() {
    complete(
        CommandResult.fail(errorCode: 'failedParse', voiceInfo: errorInfo));
  }

  @override
  VoiceCommand tryParse() {
    if (command?.isEmpty ?? true) {
      return null;
    }
    RegExp numExp = RegExp(r"([0-9]+)(?=号)");
    RegExp preOrderMatchExp = RegExp(r"(?=.*预订单)(?=.*出餐)^.*$");
    RegExp orderMatchExp = RegExp(r"(?=.*订单)(?=.*出餐)^.*$");

    String tobeOrderId = numExp.stringMatch(command);

    num orderId = int.tryParse(tobeOrderId ?? '') ?? -1;
    if (orderId < 0) {
      return failParse(VoiceCommandConstant.parseFailure);
    }

    if (preOrderMatchExp.hasMatch(command)) {
      return successParse(orderId, true);
    } else if (orderMatchExp.hasMatch(command)) {
      return successParse(orderId, false);
    }
    return failParse(VoiceCommandConstant.parseFailure);
  }
}
