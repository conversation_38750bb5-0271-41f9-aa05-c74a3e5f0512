// 指令最终执行结果
class CommandResult {
  CommandResult(this.isSuccess, {this.info, this.reportInfo});

  CommandResult.fail({String errorCode, String voiceInfo}) {
    isSuccess = false;
    reportInfo = errorCode;
    info = voiceInfo;
  }

  CommandResult.success(String voiceInfo) {
    isSuccess = true;
    reportInfo = 'preparedSuccess';
    info = voiceInfo;
  }

  bool isSuccess;
  String reportInfo; // raptor 上报code
  String info; // 语音播报信息
}
