import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/voice_command_constant.dart';
import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/voice_command_result.dart';

// 语音文本指令转换器
mixin CommandParser {
  tryParse() {
    return VoiceCommand.error();
  }
}

// 语音指令基础类
class VoiceCommand implements CommandParser {
  VoiceCommand({this.command, this.parseError, this.errorInfo});

  VoiceCommand.error({this.parseError, this.errorInfo});

  bool parseError;
  String errorInfo;
  String command;

  void complete(CommandResult result) {}

  void failToParse() {}

  @override
  tryParse() {
    return VoiceCommand.error(errorInfo: VoiceCommandConstant.parseError);
  }
}
