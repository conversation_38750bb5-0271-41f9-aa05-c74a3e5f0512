import 'dart:collection';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/voice_command.dart';
import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/voice_command_constant.dart';
import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/voice_command_result.dart';
import 'package:waimai_e_flutter_house_keeper/voice_assistant/voice_assistant_plugin.dart';

typedef RunCommand = Future<CommandResult> Function(dynamic);

class VoiceCommandManager {
  static HashMap<CommandType, RunCommand> methodHandler = HashMap();
  static VoiceAssistantPlugin voicePlugin;

  static void init() {
    voicePlugin = VoiceAssistantPlugin.instance;
    // 拼好饭出餐完成 方法回调
    // registerCommand(CommandType.newPrepareComplete, (command) {
    //   return NewFoodPreparedImpl.foodDone(command);
    // });
  }

  static registerCommand(CommandType cmdType, RunCommand func) {
    methodHandler[cmdType] = func;
  }

  static unregisterCommand(ContentType cmdType) {
    methodHandler.remove(cmdType);
  }

  static handleCommand(CommandParser parser, CommandType cmdType) {
    VoiceCommand voiceCommand = parser?.tryParse();

    if (voiceCommand == null) {
      return;
    }

    if (voiceCommand.parseError) {
      debugPrint('🦑  parse error');
      voiceCommand.failToParse();
      return;
    }

    RunCommand handleFunc = methodHandler[cmdType];
    if (handleFunc != null) {
      debugPrint('🦑  voiceCommand $voiceCommand $handleFunc');
      handleFunc(voiceCommand).then((result) {
        debugPrint('as - enter handleFunc then');
        voiceCommand.complete(result);
      });
    } else {
      debugPrint('as - enter handleFunc == null');
    }
  }
}
