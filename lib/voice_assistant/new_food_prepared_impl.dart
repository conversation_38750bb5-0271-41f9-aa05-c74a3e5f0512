import 'dart:io';

import 'package:waimai_e_flutter_house_keeper/voice_assistant/model/voice_command_result.dart';
import 'package:waimai_e_flutter_order_stone/model/order_base.dart';
import 'package:waimai_e_flutter_order_stone/native_plugins/order_business_plugin.dart';
import 'package:wef_network/wef_request.dart';

// 新订单策略出餐完成逻辑
class NewFoodPreparedImpl {
  static Future<CommandResult> foodDone(command) {
    return Future.value(null);
  }

  static Future<Map> finishFoodDone(OrderBase order) {
    num orderViewId = order.baseViewId;
    Map param = {'orderViewId': orderViewId};
    return postEApi(path: '/api/order/v5/fooddone', params: param)
        .then((response) {
      if (response?.data != null && response.code != null) {
        Map dataDic = response.data['wmOrderVo'];
        order.updateOrderWithJsonAndNotify(dataDic);
        order.syncOrder(order.baseViewId);
        OrderBusinessPlugin.syncIncrementOrders();
        return dataDic;
      }
      return null;
    });
  }
}
