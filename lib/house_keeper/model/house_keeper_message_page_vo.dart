import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_role_vo.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_push_data.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_general.dart';

import 'house_keeper_message_vo.dart';

enum HouseKeeperInputType {
  text,
  voice,
}

class HouseKeeperMessagePageVo extends ChangeNotifier {
  String sourceType = '';
  String origin = '';
  HouseKeeperRoleVo roleVo;

  /// 输入类型
  HouseKeeperInputType inputType = HouseKeeperInputType.voice;

  /// 语音输入状态
  VoiceAssistantState _voiceInputState = VoiceAssistantState.normal;
  VoiceAssistantState get voiceInputState => _voiceInputState;
  set voiceInputState(state) {
    _voiceInputState = state;
  }

  /// 消息列表
  List<HouseKeeperMessage> messages = [];

  List<String> shortcutMessages = [];

  List<String> associates = [];

  //IM发过来的数据
  SceneData sceneData;

  int dbSize = 0;

  String voiceInput;

//底部文案
  String bottomBtnText;

  @override
  void dispose() {
    disposed = true;
    super.dispose();
  }

  bool disposed = false;

  /// 通知页面刷新
  void safeNotifyListeners() {
    if (disposed) {
      return;
    }
    notifyListeners();
  }
}
