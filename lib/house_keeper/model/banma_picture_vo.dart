class BanPictureVo {
  int widgetId;
  List<BanProduct> productList;
  String remainingWaitingTime;
  int status;

  BanPictureVo(
      {this.widgetId,
      this.productList,
      this.remainingWaitingTime,
      this.status});

  factory BanPictureVo.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return null;
    }
    return BanPictureVo(
        widgetId: json['widgetId'],
        productList:
            (json['productList'] != null && json['productList'] is List)
                ? (json['productList'] as List)
                    .map((item) => BanProduct.fromJson(item))
                    .toList()
                : [],
        remainingWaitingTime: json['remainingWaitingTime'] ?? '',
        status: json['status']);
  }
}

class BanProduct {
  int productId;
  List<BanImageVo> imageList;
  String productName;
  BanProduct({
    this.productId,
    this.imageList,
    this.productName,
  });

  BanProduct.fromJson(Map<String, dynamic> json) {
    productId = json['productId'];
    imageList = (json['imageList'] != null && json['imageList'] is List)
        ? (json['imageList'] as List)
            .map((item) => BanImageVo.fromJson(item))
            .toList()
        : [];
    productName = json['productName'];
  }
}

class BanImageVo {
  final int imageId;
  final String url;

  BanImageVo({
    this.imageId,
    this.url,
  });

  factory BanImageVo.fromJson(Map<String, dynamic> json) {
    return BanImageVo(imageId: json['imageId'], url: json['url']);
  }
}
