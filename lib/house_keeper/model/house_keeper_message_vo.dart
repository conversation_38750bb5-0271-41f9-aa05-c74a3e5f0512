import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/service/house_keeper_sse_client_dio.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';

enum HouseKeeperMessageType {
  text,
  image,
  voice,
  goodsItem,
  confirmItem,
  commentChooseItem,
  commentCard,
  commentCardGPT,
  commentCardReply,
  placeholder, // GPT 思考占位消息
  userPlaceholder, // 商家占位消息
  guide, // 引导
  roleGuide, // 角色引导
  feedbackText,
  switchCard,
  editTextCard,
  goodsDetail, // 商品详情
  goodsChooseItem,
  contentChooseItem, // 反馈
  contentList,
  ask, //
  uploadSamplePicture,
  choosePicture,
  commentCardCui,
  commentCardGPTCui,
  commentCardChooseCui,
  commentCardReply<PERSON>ui,
  choosePattern, //选择海报
  singleToGroupTip, //单聊转群聊提示
  commentText, //评价分析
  serviceCard, //服务市场
  roleMentionGuide, // 角色被@后引导
}

enum StreamMessageState {
  initial, // 初始状态
  streaming, // 流式输出中
  completed, // 已完成
  error, // 错误状态
}

class HouseKeeperMessage extends ChangeNotifier {
  HouseKeeperMessage({
    this.role = 'ASSISTANT',
    this.content = '',
    this.data = '',
    this.type,
    this.messageId,
    String endStatus,
  }) : id = DateTime.now().millisecondsSinceEpoch.toString() +
            '_' +
            (Random().nextInt(1000)).toString();

  StreamMessageState _state = StreamMessageState.initial;
  StreamMessageState get state => _state;

  final String id;
  final String role;
  String content;
  String readableContent;
  final HouseKeeperMessageType type;
  final dynamic data;
  int messageId;

  String getReadableContent() {
    return readableContent.isNotEmpty ? readableContent : content;
  }

  factory HouseKeeperMessage.fromJson(Map<String, dynamic> json) {
    return HouseKeeperMessage(
      role: json['role'],
      type: json['type'],
      content: json['content'],
      data: json['data'],
      messageId: json['messageId'],
    );
  }

  Map<String, dynamic> toJson() {
    final dataMap = <String, dynamic>{};
    dataMap['role'] = role;
    dataMap['content'] = content;
    dataMap['type'] = type;
    dataMap['messageId'] = messageId;
    if (data != null) {
      dataMap['data'] = data.toString();
    }
    return dataMap;
  }
}

/// 商家消息
class HouseKeeperUserMessageVo extends HouseKeeperMessage {
  HouseKeeperUserMessageVo(
      {String content, dynamic data, HouseKeeperMessageType type, String role})
      : super(role: role ?? 'USER', content: content, type: type, data: data);
}

/// 流式历史消息
class HouseKeeperStreamHistoryMessageVo extends HouseKeeperMessage {
  HouseKeeperStreamHistoryMessageVo({
    String content,
    dynamic data,
    HouseKeeperMessageType type,
    String role,
  }) : super(
          role: role ?? 'ASSISTANT',
          content: content,
          type: type,
          data: data,
        );
  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json['role'] = 'ASSISTANT'; // 确保角色设置正确
    return json;
  }

  factory HouseKeeperStreamHistoryMessageVo.fromJson(
      Map<String, dynamic> json) {
    return HouseKeeperStreamHistoryMessageVo(
      type: json['type'],
      content: json['content'],
      data: json['data'],
    );
  }

  @override
  String getReadableContent() {
    return readableContent.isNotEmpty ? readableContent : content;
  }
}

/// 流式消息类型，用于处理流式响应
class HouseKeeperStreamMessageVo extends HouseKeeperMessage {
  final StreamController<SSEDioModel> streamController;
  Timer _debounceTimer;
  final _contentBuffer = StringBuffer();
  static const _debounceTime = Duration(milliseconds: 16); // 约一帧的时间

  bool appError = true;
  String currentContent = '';
  String errorMessage;
  String errorFullMessage; // 用于研发定位的全部错误信息
  String userMessage;
  Map<String, dynamic> extraData = {};
  bool isError = false;
  // ignore: prefer_typing_uninitialized_variables
  var contextMessage;

  @override
  String getReadableContent() {
    String text = content ?? '';

    // 移除自定义代码块（以:::开始和结束的内容）
    text = text.replaceAll(RegExp(r':{3}.*?:{3}', dotAll: true), '');

    // 移除think标签
    text = text.replaceAll(
        RegExp(r'<think>.*?<\/think>', dotAll: true, caseSensitive: false), '');

    // 移除猜测标签
    text = text.replaceAll(
        RegExp(r'<guess>.*?<\/guess>', dotAll: true, caseSensitive: false), '');

    // 移除markdown语法
    text = text
        // 移除标题（包括多个#号的情况）
        .replaceAll(RegExp(r'^#{1,6}\s+.*$', multiLine: true), '')
        // 移除粗体
        .replaceAll(RegExp(r'\*{2}(.*?)\*{2}'), r'')
        // 移除斜体
        .replaceAll(RegExp(r'\*(.*?)\*'), r'')
        // 移除代码块
        .replaceAll(RegExp(r'`{3}.*?`{3}', dotAll: true), '')
        // 移除行内代码
        .replaceAll(RegExp(r'`(.*?)`'), r'')
        // 移除链接
        .replaceAll(RegExp(r'\[(.*?)\]\(.*?\)'), r'')
        // 移除列表标记
        .replaceAll(RegExp(r'^\s*[-*+]\s', multiLine: true), '')
        .replaceAll(RegExp(r'^\s*\d+\.\s', multiLine: true), '')
        // 移除引用
        .replaceAll(RegExp(r'^\s*>\s', multiLine: true), '')
        // 移除分隔线
        .replaceAll(RegExp(r'\n\s*[-]{3,}\s*\n'), '\n')
        // 移除冒号后的空格
        .replaceAll(RegExp(r'：\s+'), '：')
        // 移除连续的空格
        .replaceAll(RegExp(r'\s+'), ' ');

    // 清理多余的空行和空格
    text = text
        .replaceAll(RegExp(r'\n{3,}'), '\n\n')
        .split('\n')
        .map((line) => line.trim())
        .where((line) => line.isNotEmpty)
        .join('\n')
        .trim();

    return text;
  }

  HouseKeeperStreamMessageVo({
    @required this.userMessage,
    @required this.streamController,
    this.contextMessage,
    HouseKeeperMessagePageModel pageModel,
  }) : super(
          type: HouseKeeperMessageType.text,
          role: 'ASSISTANT',
          content: '',
        ) {
    streamController.stream.listen((data) {
      if (data != null &&
          data.extraData != null &&
          data.extraData.isNotEmpty &&
          data.extraData['userMessageId'] != null &&
          data.extraData['agentMessageId'] != null &&
          int.tryParse(data.extraData['userMessageId'].toString()) != null &&
          int.tryParse(data.extraData['agentMessageId'].toString()) != null) {
        messageId =
            int.tryParse(data.extraData['agentMessageId']?.toString() ?? '0');
        extraData.addAll(data.extraData);
      }
      if (data != null && data.data.toString().isNotEmpty) {
        currentContent = data.data.toString();
        _contentBuffer.write(currentContent);
        extraData = data.extraData;

        // 使用防抖进行更新
        _debounceTimer?.cancel();
        _debounceTimer = Timer(_debounceTime, () {
          content = _contentBuffer.toString();
          startStreaming();
        });
      }
    }, onError: (error) {
      if (error is Map<String, String>) {
        errorMessage = error['msg'];
        errorFullMessage = error['fullMessage'];
      } else if (error is String) {
        errorMessage = error;
        errorFullMessage = '';
      } else {
        errorMessage = '内部错误';
        errorFullMessage = '';
      }
      _state = StreamMessageState.error;
      setError();
      isError = true;
      _cleanUp();
    }, onDone: () {
      if (!isError) {
        _state = StreamMessageState.completed;
        Future.delayed(const Duration(milliseconds: 200), () {
          setComplete();

          if (content.isNotEmpty && content.contains(':::')) {
            RuzhuCardStatusManager().batchSetExpired(excludeMessageId: id);
            pageModel.forceRefresh();
          }
        });
      }
      _cleanUp();
    });
  }

  void _cleanUp() {
    _debounceTimer?.cancel();
    _contentBuffer.clear();
    if (!streamController.isClosed) {
      streamController.close();
    }
  }

  @override
  void dispose() {
    _cleanUp();
    appError = false;
    super.dispose();
  }

  void startStreaming() {
    _state = StreamMessageState.streaming;
    appError = true;
    notifyListeners();
  }

  void setComplete() {
    _state = StreamMessageState.completed;
    appError = false;
    notifyListeners();
  }

  void setError() {
    _state = StreamMessageState.error;
    notifyListeners();
  }

  /// 取消流式传输
  void cancelStream() {
    if (!streamController.isClosed) {
      streamController.close();
    }
    _state = StreamMessageState.completed;
    notifyListeners();
  }

  // 卡片消息是否可读
  bool isMessageReadable() {
    return getReadableContent().isNotEmpty;
  }

  // 卡片消息是否可重发
  bool isMessageResend() {
    if (contextMessage != null) {
      return false;
    }
    return content.isNotEmpty;
  }
}
