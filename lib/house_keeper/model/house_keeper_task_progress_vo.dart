class TaskModule {
  final String moudleId;
  final int moudleStatus;
  final String moudleRejectMsg;
  final List<TaskCard> cardList;

  TaskModule({
    this.moudleId = '',
    this.moudleStatus = 0,
    this.moudleRejectMsg = '',
    this.cardList = const [],
  });

  factory TaskModule.fromJson(Map<String, dynamic> json) {
    return TaskModule(
      moudleId: json['moudleId'] as String ?? '',
      moudleStatus: json['moudleStatus'] as int ?? 0,
      moudleRejectMsg: json['moudleRejectMsg'] as String ?? '',
      cardList: (json['cardList'] as List<dynamic>)
              ?.map((e) => TaskCard.fromJson(e as Map<String, dynamic>))
              ?.toList() ??
          [],
    );
  }
}

class TaskCard {
  final int cardId;
  final int status;
  final String rejectMsg;

  TaskCard({
    this.cardId = 0,
    this.status = 0,
    this.rejectMsg = '',
  });

  factory TaskCard.fromJson(Map<String, dynamic> json) {
    return TaskCard(
      cardId: json['cardId'] as int ?? 0,
      status: json['status'] as int ?? 0,
      rejectMsg: json['rejectMsg'] as String ?? '',
    );
  }
}
