/// 分组VO
class GoodsGroupVo {
  GoodsGroupVo({
    this.groupId,
    this.groupName,
    this.goodsCount,
  });

  GoodsGroupVo.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'] ?? 0;
    groupName = json['groupName'] ?? '';
    goodsCount = json['count'] ?? 0;
  }

  int groupId; // 分组id
  String groupName; // 分组名称
  int goodsCount; //商品数量

  // 页面交互需要，记录该分组下选中的商品数量
  int _selectedCount;

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = Map<String, dynamic>();
    data['groupId'] = this.groupId;
    data['groupName'] = this.groupName;
    data['count'] = goodsCount;
    return data;
  }

  /// 获取选中的商品数量
  int get selectedCount => _selectedCount ?? 0;

  /// 设置选中的商品数量
  void setSelectedCount(int count) {
    _selectedCount = count;
  }
}
