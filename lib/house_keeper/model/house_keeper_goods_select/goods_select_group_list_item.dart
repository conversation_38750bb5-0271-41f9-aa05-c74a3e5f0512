import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_goods_select/goods_group_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_goods_select/single_poi_goods_select_page_vo.dart';

/// 通用选品页分组列表Item
class GoodsSelectGroupListItem extends StatelessWidget {
  GoodsSelectGroupListItem({
    this.groupVo,
    this.onTapGroup,
  });

  final GoodsGroupVo groupVo;
  final Function(GoodsGroupVo) onTapGroup;

  /// 点击分组
  void _onTapGroup() {
    if (onTapGroup != null) {
      onTapGroup(groupVo);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Selector<SinglePoiGoodsSelectPageVo, Tuple3<int, int, int>>(
      selector: (BuildContext context, SinglePoiGoodsSelectPageVo pageVo) {
        return Tuple3(
          groupVo.groupId,
          groupVo.selectedCount,
          pageVo.currentSelectedGroupId,
        );
      },
      builder: (context, tuple, _) {
        bool isChecked = tuple.item1 == tuple.item3;
        return GestureDetector(
          onTap: _onTapGroup,
          behavior: HitTestBehavior.opaque,
          child: DecoratedBox(
            decoration: BoxDecoration(color: isChecked ? Colors.white : null),
            child: Stack(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.fromLTRB(12, 14, 20, 14),
                  child: Text(
                    groupVo.groupName ?? '',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF666666),
                    ),
                  ),
                ),
                Visibility(
                  visible: groupVo.selectedCount > 0,
                  child: Positioned(
                    top: 3,
                    right: 6,
                    child: Container(
                      child: Text(
                        '${groupVo.selectedCount}',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      constraints: BoxConstraints(minWidth: 14),
                      decoration: BoxDecoration(
                        color: Color(0xFFFF5F59),
                        borderRadius: BorderRadius.circular(6.0),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
