import 'package:waimai_e_flutter_house_keeper/house_keeper/model/goods_spu_vo.dart';

/// 商品信息TO
class GoodsInfoTo {
  GoodsInfoTo({
    this.totalSpuCount,
    this.pageSpuCount,
    this.hasNextPage,
    this.pageNum,
    this.pageSize,
    this.spuList,
  });

  GoodsInfoTo.fromJson(Map<String, dynamic> json) {
    totalSpuCount = json['totalSpuCount'] ?? 0;
    pageSpuCount = json['pageSpuCount'] ?? 0;
    hasNextPage = json['hasNextPage'] ?? false;
    pageNum = json['pageNum'] ?? 0;
    pageSize = json['pageSize'] ?? 0;
    spuList = [];
    if (json['spuList'] != null) {
      json['spuList'].forEach((v) {
        spuList.add(GoodsSpuVo.fromJson(v));
      });
    }
  }

  int totalSpuCount; // spu总数
  int pageSpuCount; // 当前分页spu数
  bool hasNextPage; // 是否还有下个分页
  int pageNum; // 页码
  int pageSize; // 每页商品数量
  List<GoodsSpuVo> spuList; // 分组列表

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = Map<String, dynamic>();
    data['totalSpuCount'] = this.totalSpuCount;
    data['pageSpuCount'] = this.pageSpuCount;
    data['hasNextPage'] = this.hasNextPage;
    data['pageNum'] = this.pageNum;
    data['pageSize'] = this.pageSize;
    if (this.spuList != null) {
      data['spuList'] = this.spuList.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
