/// 商品活动VO
class GoodsActivityVo {
  GoodsActivityVo({
    this.actId,
    this.actType,
    this.actShowName,
    this.actShowPrice,
  });

  GoodsActivityVo.fromJson(Map<String, dynamic> json) {
    actId = json['actId'];
    actType = json['actType'];
    actShowName = json['actShowName'] ?? '';
    actShowPrice = json['actShowPrice'] ?? '';
  }

  int actId; // 活动id
  int actType; // 活动类型
  String actShowName; // 活动名称
  String actShowPrice; // 活动价格

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['actId'] = this.actId;
    data['actType'] = this.actType;
    data['actShowName'] = this.actShowName;
    data['actShowPrice'] = this.actShowPrice;
    return data;
  }
}
