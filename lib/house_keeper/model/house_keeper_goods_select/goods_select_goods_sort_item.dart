import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/goods_spu_vo.dart';

/// 商品选品页排序Item
class GoodsSelectGoodsSortItem extends StatelessWidget {
  GoodsSelectGoodsSortItem({
    this.spuVo,
    this.index,
    this.onTapDelete,
    this.onTagMoveTop,
    Key key,
  }) : super(key: key);

  final GoodsSpuVo spuVo;
  final int index;
  final Function(int) onTapDelete;
  final Function(int) onTagMoveTop;

  /// 点击删除
  void _onTapDelete() {
    if (onTapDelete != null) {
      onTapDelete(index);
    }
  }

  /// 点击置顶
  void _onTapMoveTop() {
    if (index == 0) {
      MTFToast.showToast(msg: '已经处于置顶状态');
      return;
    }
    if (onTagMoveTop != null) {
      onTagMoveTop(index);
    }
  }

  /// 构建图片widget
  Widget _buildImageWidget() {
    return Stack(
      children: <Widget>[
        Container(
          height: 60,
          width: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            image: DecorationImage(
              image: AdvancedNetworkImage(
                spuVo.displayImage,
                useDiskCache: true,
              ),
              fit: BoxFit.fill,
            ),
          ),
        ),
        Container(
          height: 16,
          width: 16,
          margin: EdgeInsets.only(top: 4),
          alignment: Alignment.center,
          color: Color(0xCC222222),
          child: Text(
            '${index + 1}',
            style: TextStyle(
              color: Color(0xFFFFFFFF),
              fontSize: 11,
              fontWeight: FontWeight.w500,
              decoration: TextDecoration.none,
            ),
          ),
        )
      ],
    );
  }

  /// 构建商品widget
  Widget _buildGoodsWidget() {
    return Container(
      margin: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            spuVo?.spuName ?? '',
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
              decoration: TextDecoration.none,
            ),
          ),
          SizedBox(height: 1.5),
          Text(
            '月售${spuVo.monthSale}  库存${spuVo.stockString}',
            style: TextStyle(
              fontSize: 12.0,
              color: Color(0xFF666666),
              fontWeight: FontWeight.normal,
              decoration: TextDecoration.none,
            ),
          ),
          SizedBox(height: 1.5),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: '￥',
                  style: TextStyle(
                    color: Color(0xFFFF5F59),
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.none,
                  ),
                ),
                TextSpan(
                  text: spuVo.priceString,
                  style: TextStyle(
                    color: Color(0xFFFF5F59),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.none,
                  ),
                ),
                TextSpan(
                  text: '${spuVo.minPrice != spuVo.maxPrice ? '起' : ''}',
                  style: TextStyle(
                    color: Color(0xFF999999),
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    decoration: TextDecoration.none,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮widget
  Widget _buildOperateWidget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Padding(
          padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
          child: _buildOperateItemWidget(
            'http://p0.meituan.net/tuling/8ad3ed57840c7482dd6c637193b89f06366.png',
            '移除',
            onPress: _onTapDelete,
          ),
        ),
        Padding(
          padding: EdgeInsets.fromLTRB(25, 0, 0, 0),
          child: _buildOperateItemWidget(
            'http://p1.meituan.net/tuling/df90b20c59bab81166d79221fd6cc05c253.png',
            '置顶',
            onPress: _onTapMoveTop,
          ),
        ),
        Padding(
          padding: EdgeInsets.fromLTRB(24, 0, 0, 0),
          child: _buildOperateItemWidget(
            'http://p0.meituan.net/tuling/6ba47710e27f862243a7585316eba520155.png',
            '拖动',
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮Item
  Widget _buildOperateItemWidget(String url, String text, {Function onPress}) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onPress,
      child: Column(
        children: <Widget>[
          Image(
            width: 14,
            height: 14,
            image: AdvancedNetworkImage(
              url,
              useDiskCache: true,
            ),
            fit: BoxFit.fill,
          ),
          SizedBox(height: 10),
          Text(
            text,
            style: TextStyle(
              color: Color(0xFF999999),
              fontSize: 11,
              fontWeight: FontWeight.w400,
              decoration: TextDecoration.none,
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(12, 8, 12, 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          _buildImageWidget(),
          Expanded(
            child: _buildGoodsWidget(),
          ),
          _buildOperateWidget(),
        ],
      ),
    );
  }
}
