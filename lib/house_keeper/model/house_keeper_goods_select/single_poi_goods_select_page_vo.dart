import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_goods_select/goods_group_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/goods_spu_vo.dart';

/// 通用选品页PageVo
class SinglePoiGoodsSelectPageVo extends ChangeNotifier {
  static final int pageSizeDefault = 20;

  int sceneType; // 选品场景
  int subSceneType; // 选品子场景，目前已知只有橱窗有子场景
  int minNum; // 最少选品数量
  int maxNum; // 最多选品数量
  String selectRules; // 选品规则
  List<GoodsGroupVo> groupVoList; // 商品分组列表
  List<GoodsSpuVo> spuVoList; // 商品Spu列表
  List<GoodsSpuVo> selectedSpuList; // 已选择的商品列表
  int currentSelectedGroupId; // 当前选中的分组id
  String searchKey; // 搜索关键词
  List<GoodsSpuVo> searchSpuVoList; // 搜索结果商品列表
  int pageNum; // 当前页码
  int searchPageNum; // 搜索模式当前页码
  int pageSize; // 每页商品数量
  bool hasNextPage; // 是否还有下一页
  bool searchHasNextPage; // 搜索模式是否还有下一页
  bool showMask; // 是否正在展示选品结果蒙层
  bool isSpuListRefreshing = false; // 是否正在刷新商品列表
  bool isDisposed = false; // 页面是否被销毁

  /// 获取已选商品数量
  int get selectedGoodsCount {
    return selectedSpuList?.length ?? 0;
  }

  /// 通知页面刷新
  void safeNotifyListeners() {
    if (isDisposed) return;
    notifyListeners();
  }

  @override
  void dispose() {
    isDisposed = true;
    super.dispose();
  }
}
