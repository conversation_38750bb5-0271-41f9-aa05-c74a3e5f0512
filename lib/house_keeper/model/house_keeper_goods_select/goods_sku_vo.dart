import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_goods_select/goods_activity_vo.dart';

/// 商品SkuVo
class GoodsSkuVo {
  GoodsSkuVo({
    this.skuId,
    this.skuSpec,
    this.price,
    this.stock,
    this.sellStatus,
    this.actInfoList,
  });

  GoodsSkuVo.fromJson(Map<String, dynamic> json) {
    skuId = json['skuId'] ?? 0;
    skuSpec = json['skuSpec'] ?? '';
    price = json['price'] ?? '';
    stock = json['stock'] ?? '';
    sellStatus = json['sellStatus'] ?? 0;
    actInfoList = [];
    if (json['actInfoList'] != null) {
      json['actInfoList'].forEach((v) {
        actInfoList.add(GoodsActivityVo.fromJson(v));
      });
    }
  }

  int skuId; // skuId
  String skuSpec; // 规格名称
  String price; // 价格
  String stock; // 库存
  int sellStatus; // 售卖状态
  List<GoodsActivityVo> actInfoList; // 活动列表

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = Map<String, dynamic>();
    data['skuId'] = this.skuId;
    data['skuSpec'] = this.skuSpec;
    data['price'] = this.price;
    data['stock'] = this.stock;
    data['sellStatus'] = this.sellStatus;
    if (this.actInfoList != null) {
      data['actInfoList'] = this.actInfoList.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
