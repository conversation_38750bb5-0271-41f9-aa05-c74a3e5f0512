import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/merchant_registration/registration_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';

/// 历史消息模型
class HouseKeeperHistoryMessageModel extends ChangeNotifier {
  HouseKeeperMessagePageModel messagePageModel;
  bool hasMore = false;
  int pageNum = 1;
  int pageSize = 20;
  int currentSize = 0;

  int lastMessageId = 0; // 最后一条消息 id

  List<HistoryMessageItem> messages = [];

  void init(HouseKeeperMessagePageModel messagePageModel) {
    /// 初始化消息模型
    this.messagePageModel = messagePageModel;
    // 不自动拉取历史消息，交由外部 loadHistoryMessages 控制
  }

  @override
  void dispose() {
    // 重置分页
    pageNum = 1;
    messages = [];
    hasMore = false;

    super.dispose();
  }

  void loadMore() {
    if (hasMore) {
      fetchHistoryMessages();
    }
  }

  Future<HistoryMessages> fetchHistoryMessages() async {
    Map<String, dynamic> params =
        CommonParamsUtils.getInstance().getCommonParams();
    params['pageSize'] = pageSize;
    params['lastMessageId'] =
        CommonParamsUtils.getInstance().getLastMessageId();

    final value = await MerchantRegistrationApi.getHistoryMessages(params);
    if (value == null) {
      return null;
    }

    // 更新最后一条消息 id, 只有在大于0的时候才更新
    if (value.lastMessageId != null && value.lastMessageId > 0) {
      CommonParamsUtils.getInstance().setLastMessageId(value.lastMessageId);
    }
    HistoryMessages historyMessages = value;
    List<HistoryMessageItem> messages = historyMessages.items ?? [];

    // 添加消息
    addMessages(messages);
    currentSize = messages.length + currentSize;
    hasMore = messages.length >= pageSize;
    return value;
  }

  void addMessages(List<HistoryMessageItem> messages) {
    for (HistoryMessageItem message in messages) {
      HouseKeeperMessage houseKeeperMessage = HouseKeeperMessage(
        role: message.role,
        content: message.content,
        messageId: message.id,
      );
      messagePageModel?.pageVo?.messages?.insert(0, houseKeeperMessage);
    }

    messagePageModel?.pageVo?.safeNotifyListeners();
  }
}
