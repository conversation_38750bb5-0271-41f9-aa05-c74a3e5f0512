/// 通参处理
class CommonParamsUtils {
  // 单例实例
  static final CommonParamsUtils _instance = CommonParamsUtils._internal();

  // 私有构造函数
  CommonParamsUtils._internal();

  // 工厂构造函数
  static CommonParamsUtils getInstance() => _instance;

  // 通用参数存储
  Map<String, dynamic> _commonParams = {};

  int lastMessageId = 0; // 最后一条消息 id

  /// 获取通参
  Map<String, dynamic> getCommonParams() {
    return _commonParams;
  }

  int getLastMessageId() {
    return lastMessageId;
  }

  void setLastMessageId(int lastMessageId) {
    this.lastMessageId = lastMessageId;
  }

  /// 设置通参
  void setCommonParams(Map<String, dynamic> params) {
    if (params == null) {
      _commonParams = {};
      return;
    }

    // 如果params为空，则清空_commonParams
    Map<String, dynamic> newParams = {};
    newParams['candidateId'] = params['candidateId'];
    newParams['epToken'] = params['epToken'];
    newParams['token'] = params['token'];
    newParams['epId'] = params['epId'];
    newParams['acctId'] = params['acctId'];
    newParams['token'] = params['token'];
    newParams['source'] = params['source'];

    _commonParams = newParams;
  }
}
