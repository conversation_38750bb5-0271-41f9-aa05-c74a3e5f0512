import 'package:flutter/foundation.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_character_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HouseKeeperSPUtils {
  static const String _nameStorageKey = 'houseKeeperAvatarName';
  static const String _idStorageKey = 'houseKeeperAvatarId';
  static const String _autoPlayVoiceKey = 'houseKeeperAutoPlayVoice';
  static const String _mutedKey = 'houseKeeperMuted';

  // 获取当前头像id
  static Future<String> getAvatarId() async {
    var result = await KNB.getStorage(key: _idStorageKey);
    if (result != null && result['value'] != null) {
      String value = result['value'].toString();
      // 确保返回值是有效的角色ID
      if (value == CharacterConfig.xiaoDaiId ||
          value == CharacterConfig.xiaoMeiId) {
        return value;
      }
    }
    // 默认头像为小袋
    return CharacterConfig.xiaoMeiId;
  }

  // 设置当前头像id
  static void setAvatarId(String id) {
    if (StringUtil.isNotEmpty(id)) {
      KNB.setStorage(key: _idStorageKey, value: id);
    }
  }

  // 获取当前头像昵称
  static Future<String> getAvatarName() async {
    var result = await KNB.getStorage(key: _nameStorageKey);
    if (result != null &&
        result.containsKey('value') &&
        StringUtil.isNotEmpty(result['value'])) {
      return result['value'];
    }

    // 默认昵称为小美
    return CharacterConfig.defaultAvatarName;
  }

  // 设置当前头像昵称
  static void setAvatarName(String name) {
    if (StringUtil.isNotEmpty(name)) {
      KNB.setStorage(key: _nameStorageKey, value: name);
    }
  }

  // 获取欢迎页头像url
  static Future<String> getAvatarUrl() async {
    String id = await getAvatarId();
    switch (id) {
      case CharacterConfig.xiaoDaiId:
        return 'https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/undefined/%E7%94%B7-%E5%A4%A7%E5%9B%BE1743673539785.gif';
      case CharacterConfig.xiaoMeiId:
        return 'https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/undefined/%E5%A5%B3-%E5%A4%A7%E5%9B%BE1743673524849.gif';
      default:
        return 'https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/undefined/%E5%A5%B3-%E5%A4%A7%E5%9B%BE1743673524849.gif';
    }
  }

  // 获取形象选择页头像url
  static Future<String> getAvatarSelectionPageUrl() async {
    String id = await getAvatarId();
    switch (id) {
      case CharacterConfig.xiaoDaiId:
        return 'https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/undefined/%E7%94%B7-%E5%B0%8F%E5%9B%BE1743670577322.gif';
      case CharacterConfig.xiaoMeiId:
        return 'https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/undefined/%E5%A5%B3-%E5%B0%8F%E5%9B%BE1743670552512.gif';
      default:
        return 'https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/undefined/%E5%A5%B3-%E5%B0%8F%E5%9B%BE1743670552512.gif';
    }
  }

  // 获取会话页头像url
  static Future<String> getMessagePageUrl() async {
    String id = await getAvatarId();
    switch (id) {
      case CharacterConfig.xiaoDaiId:
        return 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/e219b1290336fafe/xiaodai.png';
      case CharacterConfig.xiaoMeiId:
        return 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/61f4b988a793942a/xiaomei.png';
      default:
        return 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/61f4b988a793942a/xiaomei.png';
    }
  }

  // 获取指定角色ID的名字
  static Future<String> getCharacterName(String avatarId) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('character_name_$avatarId') ?? '';
  }

  // 设置指定角色ID的名字
  static Future<void> setCharacterName(String avatarId, String name) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('character_name_$avatarId', name);
  }

  // 获取自动语音播放状态
  static Future<bool> getAutoPlayVoiceEnabled() async {
    var result = await KNB.getStorage(key: _autoPlayVoiceKey);
    if (result != null &&
        result.containsKey('value') &&
        StringUtil.isNotEmpty(result['value'])) {
      return result['value'].toLowerCase() == 'true';
    }
    // 默认开启自动播放
    return true;
  }

  // 设置自动语音播放状态
  static void setAutoPlayVoiceEnabled(bool enabled) {
    KNB.setStorage(key: _autoPlayVoiceKey, value: enabled.toString());
  }

  // 获取静音状态
  static Future<bool> getMutedState() async {
    var result = await KNB.getStorage(key: _mutedKey);
    if (result != null &&
        result.containsKey('value') &&
        StringUtil.isNotEmpty(result['value'])) {
      return result['value'].toLowerCase() == 'true';
    }
    // 默认不静音
    return false;
  }

  // 设置静音状态
  static void setMutedState(bool muted) {
    try {
      KNB.setStorage(key: _mutedKey, value: muted.toString());
    } catch (e) {
      debugPrint('setMutedState error: $e');
    }
  }
}
