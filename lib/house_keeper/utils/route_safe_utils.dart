import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:wef_network/wef_request.dart';

class RouteSafeUtils {
  // 存储路由地址的变量
  static List<String> _safeRoutes = [];

  // 初始化路由数据
  static void initSafeRoutes(List<String> routes) {
    _safeRoutes = List<String>.from(routes);
  }

  // 安全跳转方法
  static Future<dynamic> safeOpenUrl(String route) async {
    if (ArrayUtil.isEmpty(_safeRoutes)) {
      getSafeRoutes();
    }
    if (_safeRoutes.contains(route)) {
      RouteUtils.open(route);
    }
  }

  // 获取安全路由列表（可选）
  static getSafeRoutes() async {
    // todo 需要有一个接口
    // ResponseData result = await postEApi(
    //     path: '/api/product/setSpuSell', params: {}, isControlShowToast: true);
    // _safeRoutes = [];
  }
}
