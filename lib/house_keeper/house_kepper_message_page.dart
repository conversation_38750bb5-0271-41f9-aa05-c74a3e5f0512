import 'package:flap/flap.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:provider/provider.dart';

import 'package:waimai_e_flutter_house_keeper/house_keeper/common/constant.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/service/house_keeper_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/avatar_url_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/api/api_service2.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/api/services/action_tracking_api_service.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/api_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/next_card_util.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/phone_verify_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_screen_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/house_keeper_message_list.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/house_keeper_text_input_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/house_keeper_voice_input_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/test/custom_block_test.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/progress/task_progress_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/config/prompt_config.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_character_model.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter/cupertino.dart';

@Flap('open_assistant')
@MTFRoute('open_assistant_message')
class HouseKeeperMessagePage extends StatefulWidget {
  const HouseKeeperMessagePage({Key key, this.pageName, this.params})
      : super(key: key);

  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  _HouseKeeperMessagePageState createState() => _HouseKeeperMessagePageState();
}

class _HouseKeeperMessagePageState extends State<HouseKeeperMessagePage>
    with RouteLifecycleStateMixin {
  HouseKeeperMessagePageModel pageModel;
  HouseKeeperPageVo pageVo = HouseKeeperPageVo();
  String avatarUrl =
      'https://p0.meituan.net/waimaieassistant/ca98d48412ce94dd4815b21bb9e8b34214716.png';
  String characterName = '袋鼠管家';
  int _tapCount = 0;
  int _lastTapTime = 0;
  String _mode = CustomMode.assistant;
  int _candidateId = 0;
  String _token = '';

  bool isMuted = false;

  String muteImg =
      'http://p0.meituan.net/tuling/114dcca7a0537b6b77df4fe5b679af6d1243.png';
  String unmuteImg =
      'http://p0.meituan.net/tuling/8871a9538a1090f5fea39d5d08b0b929972.png';

  final ScrollController _scrollController = ScrollController();
  final RefreshController _refreshController = RefreshController();
  String avatarId;

  // 添加配置管理器
  PromptConfigData _configManager;

  bool isPageDisAppear = false;

  bool _isNeedLogoutIfBack = false;

  @override
  void initState() {
    super.initState();
    FlutterLx.moudleView(
        '43426408', 'c_waimai_e_sbg10hua', 'b_waimai_e_nccla9zc_mv');

    pageModel = HouseKeeperMessagePageModel();
    if (widget != null && widget.params != null) {
      _mode = widget?.params["mode"] ?? CustomMode.assistant;
      _candidateId = widget?.params["candidateId"] != null
          ? int.tryParse(widget?.params["candidateId"].toString()) ?? 0
          : 0;
      _token = widget?.params["token"] ?? '';
      _isNeedLogoutIfBack = widget?.params['isNeedLogoutIfBack'] == 'true';
      pageModel.mode = _mode;
    }
    pageModel.init(pageVo, widget.params);

    // 将滚动控制器传递给 pageModel
    pageModel.scrollController = _scrollController;
    // 初始化语音识别插件
    pageModel.asrPlugin.init();

    // 设置上下文
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 设置状态栏为透明
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ));

      RuzhuScreenUtils.setScreenInfo(MediaQuery.of(context).size.width.toInt(),
          MediaQuery.of(context).size.height.toInt());
    });

    if (widget != null && widget.params != null) {
      String query = widget.params["initial_message"];
      String subAgentCode = widget.params["sub_agent_code"];
      // 定制模式初始化操作
      if (_mode == CustomMode.finance) {
        // 财务管家模式
        pageModel.insertMessage(HouseKeeperMessage(
            type: HouseKeeperMessageType.guide,
            content:
                '老板您好！可以告诉我您的账单定制化需求。我会帮您智能定制专属数据分析。例如您可以说："展示近7天的退款订单情况"'));
      } else if (_mode == CustomMode.selfSettleIn) {
        MerchantApiService.getWelcomeMsg(candidateId: _candidateId)
            .then((value) {
          CommonParamsUtils.getInstance()
              .setLastMessageId(value?.messageId ?? 0);
          if (value != null && (value.welcomeMsg?.isNotEmpty ?? false)) {
            pageModel.insertMessage(HouseKeeperMessage(
                type: HouseKeeperMessageType.text,
                content: value.welcomeMsg,
                messageId: value.messageId));
          }

          // 在设置完 lastMessageId 后，检查是否有历史消息
          pageModel.checkInitialHistory();

          NextCardUtil.addNextCard(
            '0',
            model: pageModel,
            candidateId: _candidateId,
            token: _token,
          );
        });
      }
      if (query != null && query.isNotEmpty) {
        if (subAgentCode != null && subAgentCode.isNotEmpty) {
          pageModel.sendPlainMessageWithAgent(query, subAgentCode);
        } else {
          pageModel.sendPlainMessage(query);
        }
      }
    }
    // 加载配置
    _loadConfig();

    HouseKeeperSPUtils.getMutedState().then((muted) {
      setState(() {
        isMuted = muted;
      });
    });
  }

  // 加载配置
  void _loadConfig() {
    if (_mode != CustomMode.assistant) {
      return;
    }
    HouseKeeperAPI.fetchFrequentQuestions().then((config) {
      setState(() {
        _configManager = config ?? {};
      });
    });
  }

  @override
  void didAppear() {
    RouteUtils.enableSwipeBack(context, false);

    // 每次页面可见，刷新任务状态
    if (isPageDisAppear) {
      RouteUtils.publish(ruzhuActionUpdateProgress);
      isPageDisAppear = false;
    }
  }

  @override
  void didDisappear() {
    isPageDisAppear = true;
  }

  @override
  void dispose() {
    // 取消所有正在进行的消息流
    pageModel.cancelAllMessages();

    // 释放语音识别插件资源
    pageModel.asrPlugin.stopListening();

    // 释放滚动控制器
    _scrollController.dispose();

    RuzhuCardStatusManager().clear();
    PhoneVerificationStateManager().resetAll();
    NextCardUtil.resetLoading();

    super.dispose();
  }

  // 添加气泡显示方法
  void _showBubbleDialog() {
    showDialog(
      context: context,
      barrierColor: Colors.transparent, // 透明背景
      barrierDismissible: true, // 点击外部可关闭
      builder: (BuildContext context) {
        return Stack(
          children: [
            Positioned(
              top: 24, // AppBar下方
              right: 5, // 距离右边20像素
              child: GestureDetector(
                onTap: () async {
                  // 关闭气泡
                  Navigator.of(context).pop();

                  FlutterLx.moudleClick('43426408', 'c_waimai_e_sbg10hua',
                      'b_waimai_e_fe55ue0g_mc');

                  // 执行切换为手动开店的逻辑
                  final apiHost = await ApiUtils.getApiHost();
                  final params =
                      CommonParamsUtils.getInstance().getCommonParams();
                  final token = params['token'];

                  ActionTrackingApiService().recordAction(
                    candidateId: params['candidateId'],
                    keys: ['userBackToOldPage'],
                  ).catchError((e) {
                    // 可以选择记录错误但不中断执行
                  });

                  String url =
                      '$apiHost/kd?hideNativeNavBar=1#/pages/shop_info/index?taskId=$_candidateId&hideNativeNavBar=1&token=$token&scene=assistant';
                  RouteUtils.open(url);
                },
                child: Image(
                  image: AdvancedNetworkImage(
                    "https://p0.meituan.net/wmsettleimgs/4137c9b0ca2518438cd0e0834179da387230.png",
                    useDiskCache: true,
                    timeoutDuration: const Duration(seconds: 10),
                  ),
                  width: 160,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // 获取AppBar的高度
    final double appBarHeight = AppBar().preferredSize.height;
    final double statusBarHeight = MediaQuery.of(context).padding.top;
    final double topPadding = appBarHeight + statusBarHeight;

    return ChangeNotifierProvider<HouseKeeperPageVo>.value(
      value: pageVo,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
        ),
        child: WillPopScope(
          onWillPop: () {
            if (_mode == CustomMode.selfSettleIn) {
              if (_isNeedLogoutIfBack) {
                RouteUtils.publish('ACTION_LOGOUT_FROM_AI_ASSISTANT');
              }
            }
            return Future.value(true);
          },
          child: Scaffold(
            // 添加 resizeToAvoidBottomInset 确保键盘弹出时内容正确调整
            resizeToAvoidBottomInset: true,
            backgroundColor: Colors.transparent,
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: TextButton(
                onPressed: () {
                  if (_mode == CustomMode.selfSettleIn) {
                    if (_isNeedLogoutIfBack) {
                      RouteUtils.publish('ACTION_LOGOUT_FROM_AI_ASSISTANT');
                    }
                  }
                  RouteUtils.close(context);
                },
                child: Image.asset(
                  'images/icon_black_back.png',
                  width: 22,
                  height: 22,
                ),
              ),
              title: GestureDetector(
                onTap: () {
                  final now = DateTime.now().millisecondsSinceEpoch;
                  // 如果两次点击间隔超过 1.5 秒，重置计数器
                  if (now - _lastTapTime > 1500) {
                    _tapCount = 0;
                  }
                  _lastTapTime = now;
                  _tapCount++;

                  // 达到 8 次点击时执行操作
                  if (_tapCount >= 8) {
                    _tapCount = 0; // 重置计数器
                    _showCustomSettingsDialog(context);
                  }
                },
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 32,
                      height: 40,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.transparent,
                      ),
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Image(
                          fit: BoxFit.fill,
                          width: 24,
                          height: 24,
                          image: AdvancedNetworkImage(
                            avatarUrl,
                            useDiskCache: true,
                            timeoutDuration: const Duration(seconds: 10),
                            loadedCallback: () {},
                            loadFailedCallback: () {},
                          )),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      characterName,
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
              titleSpacing: -4, // 增加负值使标题更靠左
              actions: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  textDirection: TextDirection.ltr,
                  children: [
                    Visibility(
                        visible: _mode != CustomMode.finance &&
                            _mode != CustomMode.selfSettleIn, // 定制模式下不展示
                        child: GestureDetector(
                            child: Image(
                              image: AdvancedNetworkImage(
                                "https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/44634900335c0425/new_dialog.png",
                                useDiskCache: true,
                              ),
                              width: 22,
                              height: 22,
                            ),
                            onTap: () {
                              _handleNewSession();
                            })),
                    const SizedBox(width: 15),
                    // 只有小美角色才显示小喇叭
                    Visibility(
                      visible: avatarId == CharacterConfig.xiaoMeiId &&
                          _mode != CustomMode.selfSettleIn,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          GestureDetector(
                              child: Image(
                                image: isMuted
                                    ? AdvancedNetworkImage(
                                        muteImg,
                                        useDiskCache: true,
                                      )
                                    : AdvancedNetworkImage(
                                        unmuteImg,
                                        useDiskCache: true,
                                      ),
                                width: 22,
                                height: 22,
                              ),
                              onTap: () {
                                setState(() {
                                  isMuted = !isMuted;
                                });
                                pageModel.handleMuteToggle(isMuted);
                                HouseKeeperSPUtils.setMutedState(isMuted);
                                MTFToast.showToast(msg: '切换声音');
                              }),
                          const SizedBox(width: 10),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: _mode == CustomMode.selfSettleIn,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          GestureDetector(
                            child: Image(
                              image: AdvancedNetworkImage(
                                "http://p0.meituan.net/tuling/ec1e57fc5f31eaabe3c20058d3486cbc498.png",
                                useDiskCache: true,
                              ),
                              width: 22,
                              height: 22,
                            ),
                            onTap: () {
                              _showBubbleDialog(); // 调用新的气泡显示方法
                            },
                          ),
                          const SizedBox(width: 10),
                        ],
                      ),
                    ),
                    // 调试按钮
                    // GestureDetector(
                    //     child: Image(
                    //       image: AdvancedNetworkImage(
                    //         "http://p0.meituan.net/tuling/ec1e57fc5f31eaabe3c20058d3486cbc498.png",
                    //         useDiskCache: true,
                    //       ),
                    //       width: 22,
                    //       height: 22,
                    //     ),
                    //     onTap: () {
                    //       _showCustomSettingsDialog(context);
                    //     }),
                    const SizedBox(width: 20),
                  ],
                ),
              ],
            ),
            // 在body外层添加Container并设置背景图
            body: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: const NetworkImage(
                        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/7c88497db3cdf5a7/background.png'),
                    colorFilter: ColorFilter.mode(
                      Colors.white.withOpacity(
                          0.25), // Set the transparency of the background image
                      BlendMode.srcOver,
                    ),
                    fit: BoxFit.cover),
              ),
              child: Column(
                children: [
                  // 顶部空白区域，确保内容不会被AppBar遮挡
                  SizedBox(height: topPadding),

                  Expanded(
                    child: Consumer<HouseKeeperPageVo>(
                      builder: (BuildContext context, HouseKeeperPageVo vo,
                          Widget child) {
                        return SmartRefresher(
                          controller: _refreshController,
                          enablePullDown: true,
                          enablePullUp: false,
                          scrollController: _scrollController,
                          header: vo.hasMoreHistory == false
                              ? const ClassicHeader(
                                  idleText: '已是最早消息',
                                  releaseText: '已是最早消息',
                                  refreshingText: '',
                                  completeText: '已是最早消息',
                                  textStyle: TextStyle(color: Colors.grey),
                                  refreshingIcon:
                                      CupertinoActivityIndicator(radius: 10),
                                  idleIcon: SizedBox.shrink(),
                                  releaseIcon: SizedBox.shrink(),
                                  completeIcon: SizedBox.shrink(),
                                  completeDuration: Duration(milliseconds: 500),
                                )
                              : const ClassicHeader(
                                  idleText: '下拉加载更多',
                                  releaseText: '释放加载更多',
                                  refreshingText: '正在加载中',
                                  completeText: '',
                                  textStyle: TextStyle(color: Colors.grey),
                                  refreshingIcon:
                                      CupertinoActivityIndicator(radius: 10),
                                  completeDuration: Duration(milliseconds: 100),
                                ),
                          onRefresh: (vo.hasMoreHistory ?? true)
                              ? () async {
                                  final listState = _scrollController.position;
                                  final oldOffset = listState.pixels;
                                  final oldMaxScrollExtent =
                                      listState.maxScrollExtent;
                                  await pageModel.loadHistoryMessages();
                                  WidgetsBinding.instance
                                      .addPostFrameCallback((_) {
                                    final newMaxScrollExtent =
                                        listState.maxScrollExtent;
                                    final newOffset = oldOffset +
                                        (newMaxScrollExtent -
                                            oldMaxScrollExtent);
                                    if (listState.pixels < newOffset) {
                                      _scrollController.jumpTo(newOffset);
                                    }
                                    _refreshController.refreshCompleted();
                                  });
                                }
                              : () => _refreshController.refreshCompleted(),
                          child: CustomScrollView(
                            controller: _scrollController,
                            slivers: <Widget>[
                              SliverToBoxAdapter(
                                child: HouseKeeperMessageListWidget(
                                  pageModel: pageModel,
                                  scrollController: _scrollController,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),

                  // 底部导航栏
                  _buildBottomNavBar(),

                  // 底部语音输入
                  _buildInputWidget(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavBar() {
    // 获取默认提示词，如果为空则使用默认列表
    final defaultPrompts = _configManager?.messageDefaultPrompts ?? [];

    // 自助入驻模式下的进度展示可以这个位置
    if (_mode == CustomMode.selfSettleIn) {
      return TaskProgressWidget(pageModel: pageModel);
    }

    if (defaultPrompts.isEmpty) {
      return const SizedBox.shrink();
    }
    // 财务管家模式下不展示
    if (_mode == CustomMode.finance) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Row(
        children: [
          // 导航按钮
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: defaultPrompts.map((button) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 6),
                    child: _buildNavButton(
                      icon: button.icon,
                      label: button.label,
                      onTap: () {
                        FlutterLx.moudleClick('43392360', 'c_waimai_e_jxnzlx1r',
                            'b_waimai_e_tztbymfs_mc',
                            val: {"text": button.prompt ?? button.label ?? ""});
                        // 使用 prompt 而不是 label 发送消息
                        pageModel.sendPlainMessageWithAgent(
                            button.prompt ?? button.label, button.subAgentCode);
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),

          // 添加空隙
          const SizedBox(width: 12),

          // 更多按钮 - 使用"更多 ^"文本样式
          GestureDetector(
            onTap: () => _showPromptSuggestions(context),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                // 使用半透明白色背景
                color: Colors.white.withOpacity(0.8),
                borderRadius: BorderRadius.circular(20), // 圆角
              ),
              child: Row(
                children: const [
                  Text(
                    '更多',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 2),
                  Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Colors.black87,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIcon(String url) {
    if (url == null || url.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 15,
          height: 15,
          child: Image(
            image: AdvancedNetworkImage(
              url,
              useDiskCache: true,
              timeoutDuration: const Duration(seconds: 10),
              loadedCallback: () {},
              loadFailedCallback: () {},
            ),
            errorBuilder: (context, error, stackTrace) {
              return const SizedBox.shrink();
            },
          ),
        ),
        const SizedBox(width: 2),
      ],
    );
  }

  Widget _buildNavButton({String icon, String label, VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildIcon(icon),
            Text(
              label ?? '',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputWidget() {
    return HouseKeeperTextInputWidget(
      mode: _mode,
      pageModel: pageModel,
    );
    // return Selector<HouseKeeperPageVo, HouseKeeperInputType>(
    //   selector: (_, pageVo) => pageVo.inputType,
    //   builder: (context, inputType, child) {
    //     if (inputType == HouseKeeperInputType.voice) {
    //       return HouseKeeperVoiceInputWidget(
    //         pageModel: pageModel,
    //         bottomText: '按住说话',
    //       );
    //     } else {
    //       return HouseKeeperTextInputWidget(
    //         mode: _mode,
    //         pageModel: pageModel,
    //       );
    //     }
    //   },
    // );
  }

  void _handleNewSession() {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_6oy26ha7_mc');
    pageModel.handleNewSession();
    pageModel.setNewSession(true);
  }

  void _showCustomSettingsDialog(BuildContext context) {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_8pponb9i_mc');
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      isDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            final bool isDebugMode = CustomBlockDebugTestSwitch.testMode;
            return Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 顶部把手示意
                    Center(
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    // 标题
                    const Padding(
                      padding: EdgeInsets.only(bottom: 16),
                      child: Text(
                        '设置',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF333333),
                        ),
                      ),
                    ),
                    // 调试开关
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Expanded(
                            child: Text(
                              '卡片调试模式',
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                          Switch(
                            value: isDebugMode,
                            onChanged: (bool value) {
                              setState(() {
                                CustomBlockDebugTestSwitch.testMode = value;
                              });
                              // 刷新整个页面
                              this.setState(() {});
                            },
                            activeColor: const Color(0xFF333333),
                          ),
                        ],
                      ),
                    ),
                    // 底部说明文本
                    Visibility(
                        visible: isDebugMode,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Text(
                            '调试模式已开启，将使用测试数据',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        )),
                    // 底部安全区域
                    SizedBox(height: MediaQuery.of(context).padding.bottom),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // 在弹窗中使用配置的展开提示词
  void _showPromptSuggestions(BuildContext context) {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_wf1vz1y0_mc');
    // 获取展开提示词，如果为空则使用默认列表
    final expandPrompts = _configManager?.messageExpandPrompts ?? [];

    if (expandPrompts.isEmpty) {
      return;
    }

    // 按 subAgentCode 对数据进行分组
    final Map<String, List<dynamic>> groupedPrompts = {};
    for (var prompt in expandPrompts) {
      final context = prompt.subAgentCode;
      String groupKey = context;

      // 将 comment_agent 和 spu_agent 归为一组
      if (context == 'comment_agent' || context == 'spu_agent') {
        groupKey = 'spu_agent';
      }

      if (!groupedPrompts.containsKey(groupKey)) {
        groupedPrompts[groupKey] = [];
      }
      groupedPrompts[groupKey].add(prompt);
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.75,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // 顶部把手示意
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // 使用 Expanded + SingleChildScrollView 实现滚动
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: groupedPrompts.entries.map((entry) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 分类标题
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: Text(
                              _getReadableKey(entry.key),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                          // 分类下的提示词列表
                          Wrap(
                            spacing: 12,
                            runSpacing: 12,
                            children: entry.value.map((item) {
                              return GestureDetector(
                                onTap: () {
                                  FlutterLx.moudleClick(
                                      '43392360',
                                      'c_waimai_e_jxnzlx1r',
                                      'b_waimai_e_oiaqq5qy_mc',
                                      val: {"text": item?.label ?? ''});

                                  pageModel.sendPlainMessageWithAgent(
                                      item.prompt ?? item.label,
                                      item.subAgentCode);
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 10,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[50],
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(color: Colors.grey[200]),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (item.icon != null &&
                                          item.icon.isNotEmpty)
                                        Image.network(
                                          item.icon,
                                          width: 20,
                                          height: 20,
                                          fit: BoxFit.cover,
                                        ),
                                      if (item.icon != null &&
                                          item.icon.isNotEmpty)
                                        const SizedBox(width: 8),
                                      Text(
                                        item.label,
                                        style: const TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFF333333),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                          const SizedBox(height: 20),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ),
              // 底部安全区域
              SizedBox(height: MediaQuery.of(context).padding.bottom),
            ],
          ),
        );
      },
    );
  }

  String _getReadableKey(String key) {
    if (key == 'spu_agent') {
      return '门店运营';
    } else if (key == 'operation_agent') {
      return '经营分析';
    } else if (key == 'governance_agent') {
      return '规则中心';
    } else {
      return '其他';
    }
  }
}
