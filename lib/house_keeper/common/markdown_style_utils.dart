import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

/// Markdown 样式工具类，提供统一的 Markdown 样式
class MarkdownStyleUtils {
  /// 获取标准 Markdown 样式
  static MarkdownStyleSheet getStandardStyle(BuildContext context) {
    return MarkdownStyleSheet(
      // 标题样式
      h1: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.black,
        height: 1.5,
      ),
      h2: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black,
        height: 1.5,
      ),
      h3: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.black,
        height: 1.5,
      ),
      // 段落样式
      p: const TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.normal,
        color: Colors.black87,
        height: 1.5, // 增加行高，提高可读性
      ),
      // 列表样式
      listBullet: const TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.normal,
        color: Colors.black87,
        height: 1.5,
      ),
      // 代码块样式
      code: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: Colors.black87,
        backgroundColor: Color(0xFFF5F5F5),
        fontFamily: 'monospace',
      ),
      codeblockDecoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(4),
      ),
      // 表格样式
      tableBorder: TableBorder.all(
        color: Colors.grey.shade300,
        width: 1,
      ),
      tableHead: const TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
      tableBody: const TextStyle(
        fontSize: 15,
        color: Colors.black87,
      ),
      tableCellsPadding:
          const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      // 引用样式
      blockquote: const TextStyle(
        fontSize: 15,
        fontStyle: FontStyle.normal,
        color: Colors.black87,
        fontWeight: FontWeight.normal,
        height: 1.5,
      ),
      blockquoteDecoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(
            color: Colors.grey.shade400,
            width: 4,
          ),
        ),
      ),
      blockquotePadding: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
      // 水平线样式
      horizontalRuleDecoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
      ),
      // 链接样式
      a: TextStyle(
        color: Colors.blue.shade700,
        decoration: TextDecoration.underline,
      ),
    );
  }
}
