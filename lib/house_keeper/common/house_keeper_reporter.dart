import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_vo.dart';

class HouseKeeperReporter {
  static const String pageInfoKey = "42665810";
  static const String cid = "c_waimai_e_e4ctt150";

  /// 会话管理
  Map<String, dynamic> allMessage = {};

  /// 公共参数
  final Map<String, dynamic> _commonParams = {};

  /// 静态变量保存类的唯一实例
  static final HouseKeeperReporter _instance = HouseKeeperReporter();

  static HouseKeeperReporter get instance => _instance;

  /// 重置公参
  static void resetCommonParams(Map<String, dynamic> commonParams) {
    instance.allMessage = {};
    instance._commonParams.clear();
    instance._commonParams.addAll(commonParams);
  }

  /******************* 助手页面 ******************/

  ///首页曝光
  static void reportPV() {
    FlutterLx.pageView(pageInfoKey, cid, val: instance._commonParams);
  }

  /// 页面init
  static void reportInitMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_i27ogh2b_mc', val: val, name: '页面init');
  }

  /// 按住说话
  static void reportClickVoice({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_h9olnx24_mc', val: val, name: '按住说话');
  }

  /// 语音转文字结果
  static void reportASRResult({String asr}) {
    instance._reportMC('b_waimai_e_o9jiltkw_mc',
        val: {"asr": asr}, name: '语音转文字');
  }

  /// 修改输入方式
  static void reportChangeInputTypeClick({HouseKeeperInputType type}) {
    String name = type == HouseKeeperInputType.text ? '文字' : '语音';
    instance._reportMC('b_waimai_e_lle2cpau_mc',
        val: {"input_type": name}, name: '切换发送方式为：$name');
  }

  /// 发送文字按钮
  static void reportSendTextClick({String content}) {
    instance._reportMC('b_waimai_e_smxov9ie_mc',
        val: {"content": content}, name: '点击发送文字');
  }

  /// 关闭按钮
  static void reportClosePage(int enterMilliseconds) {
    instance.allMessage.addAll({
      'stay_time': DateTime.now().millisecondsSinceEpoch - enterMilliseconds,
    });
    instance._reportMC("b_waimai_e_6sd4na29_mc",
        val: instance.allMessage, name: '关闭页面');
    instance.allMessage.clear();
  }

  /// 一次请求
  static void reportOneQuery(
      List<HouseKeeperMessage> message, HouseKeeperMessage gpt) {
    List<HouseKeeperMessage> list = message ?? [];
    list.add(gpt);
    Map<String, dynamic> json = list?.asMap()?.map((index, item) {
      return MapEntry('${index}_${item?.role}', item?.toJson());
    });
    instance._reportMC("b_waimai_e_pwvpwqtf_mc", val: json, name: '请求GPT');
  }

  /// 直接展示模型返回结果
  static void reportShowLLMResponseMc({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_bhu386ml_mc', val: val, name: '直接展示模型返回结果');
  }

  /// 引导语入口点击
  static void reportGuideEntranceMc({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_d9t9pag3_mc', val: val, name: '引导语入口点击');
  }

  /// 文本输入联想词点击
  static void reportTextInputAssociateMc({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_0qkn7p6j_mc', val: val, name: '文本输入联想词点击');
  }

  /// 继续问一个按钮曝光
  static void reportContinueQuestionMV(String button,
      {Map<String, dynamic> val}) {
    Map<String, dynamic> params = val ?? {};
    params['button'] = button;
    instance._reportMV('b_waimai_e_sbeu3i32_mv', val: val, name: '继续问曝光');
  }

  /// 继续问一个按钮点击
  static void reportContinueQuestionMC(String button,
      {Map<String, dynamic> val}) {
    Map<String, dynamic> params = val ?? {};
    params['button'] = button;
    instance._reportMC('b_waimai_e_sbeu3i32_mc', val: val, name: '继续问点击');
  }

  /// 上报当前对话数据
  static void reportSession({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_y105u8ef_mc', val: val, name: '上报当前会话数据');
  }

  /// Push到达
  static void reportPush({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_onc1r29c_mc', val: val, name: 'Push到达');
  }

  /// 猜你想问曝光
  static void reportGuessMV({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_bztdac1a_mv', val: val, name: '猜你想问曝光');
  }

  /// 猜你想问点击
  static void reportGuessBtnMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_bztdac1a_mc', val: val, name: '猜你想问点击');
  }

  /// 有帮助
  static void reportUseFullClick({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_fcadf4pa_mc', val: val, name: '有帮助');
  }

  /// 无帮助
  static void reportUnUseFullClick({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_9psy5kjo_mc', val: val, name: '无帮助');
  }

  /// 快捷入口点击
  static void reportShortCutMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_3csdp5pq_mc', val: val, name: '点击快捷入口');
  }

  /******************* 商品CUI ******************/

  /// 商品CUI页面曝光
  static void reportGoodsCuiMV({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_16mvywlf_mv', val: val, name: '商品信息CUI页面曝光');
  }

  /// 商品卡片Bid
  static String reportGoodsCardMVBid = 'b_waimai_e_7ciowg5u_mv';

  /// 商品卡片点击
  static void reportGoodsCardMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_7ciowg5u_mc', val: val, name: '商品卡片点击');
  }

  /// 商品路由跳转
  static void reportGoodsRoute({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_hmscj81m_mc', val: val, name: '商品路由跳转');
  }

  /******************* 评价回复 ******************/

  /// 生成评价页面曝光
  static void reportCommentPageMV({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_26kewwg4_mv', val: val, name: '生成评价页面曝光');
  }

  static String reportCommentMVString = "b_waimai_e_26kewwg4_mv";

  /// 生成一条评价曝光
  static void reportCommentMV({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_w0d71wt5_mv', val: val, name: '生成一条评价曝光');
  }

  /// 继续回复评价
  static void reportContinueMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_lbrgtkor_mc', val: val, name: '继续回复评价');
  }

  /// 采纳点击
  static void reportAcceptMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_l1av9cbt_mc', val: val, name: '采纳点击');
  }

  /// 换一换点击
  static void reportSwapMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_t3av70dh_mc', val: val, name: '换一换点击');
  }

  /// 卡片上点击确认/取消
  static void reportCardBtnMc({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_oqq7flw7_mc', val: val, name: '卡片点击确认或者取消');
  }

  /// 命中评价分析意图
  static void reportCommentAnalyzeMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_d9fwqckh_mc', val: val, name: '命中评价分析意图');
  }

  /******************* 查询商品 ******************/

  /// 上报查询商品
  static void reportQueryGoodsMc({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_nvk0zddj_mc', val: val, name: '查询商品');
  }

  /******************* 上下架 ******************/

  /// 上报命中商品上下架意图
  static void reportGoodsSaleStatusHit({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_qx621e32_mc', val: val, name: '商品上下架意图');
  }

  /// 上报执行商品上下架
  static void reportGoodsExecuteSaleStatus({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_h35fynr7_mc', val: val, name: '执行商品上下架');
  }

  /******************* 库存 ******************/

  /// 上报命中商品修改库存意图
  static void reportGoodsModifyStockHit({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_h7ce052f_mc', val: val, name: '商品修改库存意图');
  }

  /// 上报执行商品修改库存意图
  static void reportExecuteGoodsModifyStock({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_nvm226z7_mc', val: val, name: '执行商品修改库存');
  }

  /******************* 修改价格 ******************/

  /// 上报命中商品修改价格意图
  static void reportGoodsModifyPriceHit({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_tyirhbpl_mc', val: val, name: '商品修改价格意图');
  }

  /// 上报执行商品修改价格
  static void reportExecuteGoodsModifyPrice({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_x9aes9mt_mc', val: val, name: '执行商品修改价格');
  }

  /******************* 智能新建商品 ******************/

  /// 上报命中智能建品意图
  static void reportGoodsCreateHit({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_veq8l8re_mc', val: val, name: '智能建品意图');
  }

  /// 上报执行智能建品
  static void reportGoodsCreateExecute({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_eqph8nac_mc', val: val, name: '智能建品执行');
  }

  /// 上报查看更多或去编辑
  static void reportGotoGoodsEdit({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_z7foiac7_mc', val: val, name: '查看更多或去编辑');
  }

  /// 上报智能建品---保存
  static void reportGoodsCreateSave({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_33ti790f_mc', val: val, name: '智能建品保存');
  }

  /// 上报智能建品---取消
  static void reportGoodsCreateCancel({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_gy5rz34n_mc', val: val, name: '智能建品取消');
  }

  /// ***************** 种草 ******************/
  /// 上报命中种草、名称优化、描述优化意图
  static void reportGoodsGrassHit({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_co4aqhzq_mc', val: val, name: '命中种草意图');
  }

  /// 上报执行生成种草文案
  static void reportGoodsGrassExecute({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_vngd7fdq_mc', val: val, name: '生成种草文案执行');
  }

  /// 上报种草文案---复制
  static void reportGoodsGrassCopy({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_ec3owzn9_mc', val: val, name: '种草文案--复制');
  }

  /// ***************** 优化商品描述 ******************/
  /// 上报执行生成描述
  static void reportGoodsOptimizeDesExecute({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_18z5coaf_mc', val: val, name: '优化商品描述执行');
  }

  /// 上报生成描述---采纳
  static void reportGoodsOptimizeDesApply({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_l0h5ke34_mc', val: val, name: '生成描述--采纳');
  }

  /// ***************** 优化名称 ******************/
  /// 上报执行生成名称
  static void reportGoodsOptimizeNameExecute({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_f9gy1b4r_mc', val: val, name: '优化商品名称执行');
  }

  /// 上报生成名称---采纳
  static void reportGoodsOptimizeNameApply({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_t5honfo1_mc', val: val, name: '生成名称--采纳');
  }

  /// ***************** 优化商品图片 ******************/
  /// 命中生成菜品图意图
  static void reportHitGoodsPic({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_ntj836ul_mc', val: val, name: '命中生成菜品图意图');
  }

  /// 执行菜品图片生成
  static void reportExecuteGoodsPic({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_u6vrfqed_mc', val: val, name: '执行菜品图片生成');
  }

  /// 菜品图保存到手机
  static void reportSaveLocalGoodsPic({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_n3tm5zbo_mc', val: val, name: '菜品图保存到手机');
  }

  /// 生成菜品图-确认使用
  // static void reportUseGoodsPic({Map<String, dynamic> val}) {
  //   instance._reportMC('b_waimai_e_fxjphukk_mc', val: val, name: '生成菜品图-确认使用');
  // }

  /// 生成菜品图-保存商品图
  static void reportSaveGoodsPic({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_m5i4dmh7_mc', val: val, name: '生成菜品图-保存商品图');
  }

  /// 生成菜品图-换一换
  static void reportChangeGoodsPic({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_yyxq1hzj_mc', val: val, name: '生成菜品图-换一换');
  }

  /// 生成菜品图-选择示例图
  static void reportChooseGoodsPic({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_317x68ta_mc', val: val, name: '生成菜品图-选择示例图');
  }

  /// 生成菜品图-确认使用示例图
  static void reportConfirmGoodsPic({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_0uhk588v_mc',
        val: val, name: '生成菜品图-确认使用示例图');
  }

  /// 商品图片选择---智能生成
  static void reportUploadGoodsPicCui({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_t7i503g9_mc', val: val, name: '商品图片选择-智能生成');
  }

  /// 菜品图生成-3次未拿到斑马结果
  static void reportGoodsPicNoResult({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_t7nm6hb4_mc',
        val: val, name: '菜品图生成-3次未拿到斑马结果');
  }

  /// ***************** 每日待办 ******************/
  /// 命中每日待办意图
  static void reportHitTodo({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_0vtyegaa_mc', val: val, name: '命中每日待办意图');
  }

  /// 点击去完成
  static void reportGotoComplete({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_1gw0o8go_mc', val: val, name: '每日待办点击去完成');
  }

  /// 命中展示低质商品意图
  static void reportHitLowGoods({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_zzleaqui_mc', val: val, name: '命中展示低质商品意图');
  }

  /// 低质商品去优化
  static void reportGotoOptimize({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_4dm5ihcl_mc', val: val, name: '低质商品去优化');
  }

  /// ***************** 商品推荐 ******************/
  /// 命中商品推荐意图
  static void reportHitGoodsRecommend({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_xjx715oj_mc', val: val, name: '命中商品推荐意图');
  }

  /// 商品推荐去上架
  static void reportGoodsRecommendToSave({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_ik93i4p0_mc', val: val, name: '商品推荐去上架');
  }

  /// ***************** 页面替换 ******************/
  void _reportMV(String bid, {Map<String, dynamic> val, String name}) {
    val ??= {};
    _addEvent(name: name, param: val);
    val.addAll(_commonParams);
    FlutterLx.moudleView(pageInfoKey, cid, bid, val: val);
  }

  void _reportMC(String bid, {Map<String, dynamic> val, String name}) {
    val ??= {};
    _addEvent(name: name, param: val);
    val.addAll(_commonParams);
    FlutterLx.moudleClick(pageInfoKey, cid, bid, val: val);
  }

  void _addEvent({String name, Map<String, dynamic> param}) {
    Map<String, dynamic> temp = param.map((key, value) {
      DateTime now = DateTime.now();
      String event = now.toString() + "_" + name + "_" + key;
      return MapEntry(event, value);
    });
    allMessage.addAll(temp);
  }

  /// ***************** 每日待办 ******************/
  /// 商品信息设置页面曝光
  static void reportCUIPageMV({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_3fcl9owt_mv', val: val, name: '商品信息设置页面曝光');
  }

  static void reportCUIPageAnnounceChange({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_eatcyl3f_mc', val: val, name: '群公告修改');
  }

  static void reportCUIPageOperationChange({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_rnic9bvs_mc', val: val, name: '群运营开关修改');
  }

  static void reportCUIPageChangeToOld({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_zb0qeh0q_mc', val: val, name: '返回旧版本');
  }

  /// ***************** 服务市场 ******************/
  /// 服务市场黄条tip点击
  static void reportServiceTipMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_3i6hfbik_mc', val: val, name: '服务市场黄条tip点击');
  }

  /// 服务市场黄条tip曝光
  static void reportServiceTipMV({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_3i6hfbik_mv', val: val, name: '服务市场黄条tip曝光');
  }

  /// 服务市场商品卡片点击
  static void reportServiceCardMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_mqjonem0_mc', val: val, name: '服务市场商品卡片点击');
  }

  /// 服务市场商品卡片曝光
  static void reportServiceCardMV({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_mqjonem0_mv', val: val, name: '服务市场商品卡片曝光');
  }

  /// ***************** 角色页面 ******************/
  /// 角色页面按钮点击
  static void reportRoleMC({Map<String, dynamic> val}) {
    instance._reportMC('b_waimai_e_nvmn7oqg_mc', val: val, name: '角色页面按钮点击');
  }

  /// 角色页面曝光
  static void reportRoleMV({Map<String, dynamic> val}) {
    instance._reportMV('b_waimai_e_nvmn7oqg_mv', val: val, name: '角色页面曝光');
  }
}
