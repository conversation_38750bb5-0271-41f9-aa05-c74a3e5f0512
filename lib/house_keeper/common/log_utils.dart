import 'package:flutter/foundation.dart';

/// 日志工具类，提供带时间戳的日志输出功能
class LogUtils {
  /// 带时间戳的调试输出
  static void logWithTimestamp(String message, {String tag = 'HouseKeeper'}) {
    final DateTime now = DateTime.now();
    final String timestamp =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}.${now.millisecond.toString().padLeft(3, '0')}';
    debugPrint('[$timestamp] $tag: $message');
  }
}
