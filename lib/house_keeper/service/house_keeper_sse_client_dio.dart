import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/common/log_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/common/utf8.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/common/utf8_safe_decoder.dart';

enum SSEDioRequestType { get, post }

/// Model for representing an SSE event.
class SSEDioModel {
  /// ID of the event.
  String id = '';

  /// Event name.
  String event = '';

  /// Event data.
  String data = '';

  Map<String, dynamic> extraData = {};

  /// Constructor for [SSEDioModel].
  SSEDioModel(
      {this.data = '',
      this.id = '',
      this.event = '',
      Map<String, dynamic> extraData})
      : extraData = extraData ?? {};
}

/// 性能统计类，用于收集和管理SSE连接的性能指标
class SSEPerformance {
  /// 是否启用性能统计
  bool _enabled = false;

  /// 数据统计变量
  int _totalMessagesReceived = 0;
  int _totalJsonDataSize = 0;

  /// 连接时间记录
  DateTime _connectionStartTime;
  DateTime _connectionEndTime;
  DateTime _lastDataTime;

  /// 构造函数，可指定是否启用统计
  SSEPerformance({bool enabled = false}) {
    _enabled = enabled;
  }

  /// 启用或禁用性能统计
  set enabled(bool value) {
    _enabled = value;
  }

  /// 获取当前性能统计状态
  bool get enabled => _enabled;

  /// 记录连接开始
  void recordConnectionStart() {
    if (!_enabled) return;
    _connectionStartTime = DateTime.now();
    _lastDataTime = DateTime.now();
    _totalMessagesReceived = 0;
    _totalJsonDataSize = 0;
    _log('连接开始时间: ${_formatDateTime(_connectionStartTime)}');
  }

  /// 记录数据接收
  void recordDataReceived(int dataSize) {
    if (!_enabled) return;
    _lastDataTime = DateTime.now();
    _totalMessagesReceived++;
    _totalJsonDataSize += dataSize;
  }

  /// 记录连接结束
  void recordConnectionEnd() {
    if (!_enabled) return;
    _connectionEndTime = DateTime.now();

    // 计算连接持续时间
    if (_connectionStartTime != null) {
      Duration connectionDuration =
          _connectionEndTime.difference(_connectionStartTime);

      // 打印连接时间信息
      _log('连接开始时间: ${_formatDateTime(_connectionStartTime)}');
      _log('连接结束时间: ${_formatDateTime(_connectionEndTime)}');
      _log('连接持续时间: ${_formatDuration(connectionDuration)}');

      // 打印统计信息
      _log('总接收消息数: $_totalMessagesReceived 条');
      _log('总JSON数据大小: $_totalJsonDataSize bytes');

      // 如果有数据，计算平均值
      if (_totalMessagesReceived > 0) {
        double avgMessageSize = _totalJsonDataSize / _totalMessagesReceived;
        _log('平均每条消息大小: ${avgMessageSize.toStringAsFixed(2)} bytes');
      }
    }
  }

  /// 获取上次数据接收时间
  DateTime get lastDataTime => _lastDataTime;

  /// 格式化日期时间为易读格式
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${_pad(dateTime.month)}-${_pad(dateTime.day)} '
        '${_pad(dateTime.hour)}:${_pad(dateTime.minute)}:${_pad(dateTime.second)}.'
        '${dateTime.millisecond.toString().padLeft(3, '0')}';
  }

  /// 格式化持续时间为易读格式
  String _formatDuration(Duration duration) {
    int totalSeconds = duration.inSeconds;
    int minutes = totalSeconds ~/ 60;
    int seconds = totalSeconds % 60;
    int milliseconds = duration.inMilliseconds % 1000;
    return '$minutes分$seconds秒$milliseconds毫秒';
  }

  /// 数字补零
  String _pad(int number) {
    return number.toString().padLeft(2, '0');
  }

  /// 带时间戳的日志输出
  void _log(String message) {
    if (!_enabled) return;
    LogUtils.logWithTimestamp(message, tag: 'SSEDioClient');
  }
}

/// A client for subscribing to Server-Sent Events (SSE) using Dio.
class SSEDioClient {
  Dio _dio;

  // 全局缓冲区，用于累积接收到的所有数据
  String _globalBuffer = '';
  bool hasReceivedData = false;

  // 性能统计对象
  final SSEPerformance _performance = SSEPerformance(enabled: false);

  // 正则表达式，用于匹配 data: 开头的 JSON 数据
  final RegExp _dataJsonRegex =
      RegExp(r'data:\s*({.*?})(?=\s*data:|\n|$)', dotAll: true);

  /// 最大缓冲区大小，防止内存泄漏
  static const int maxBufferSize = 1024 * 1024; // 1MB
  /// 大缓冲区阈值，超过此值使用分块处理
  static const int largeBufferThreshold = 100 * 1024; // 100KB
  /// SSE 数据开始标记
  static const String sseStartMarker = 'data: ';

  /// SSE 数据结束标记
  static const String sseEndMarker = '[DONE]';

  static const int connectionTimeout = 10000;
  static const int receiveTimeout = 120000;
  static const int sendTimeout = 120000;

  /// 设置性能统计开关
  set enablePerformanceTracking(bool value) {
    _performance.enabled = value;
  }

  /// 获取性能统计开关状态
  bool get enablePerformanceTracking => _performance.enabled;

  /// 带时间戳的调试输出
  void _logWithTimestamp(String message) {
    LogUtils.logWithTimestamp(message, tag: 'SSEDioClient');
  }

  /// 从缓冲区中提取完整的 JSON 数据
  List<String> _extractJsonFromBuffer(String buffer) {
    List<String> results = [];

    // 使用更高效的策略来处理大缓冲区
    if (buffer.length > largeBufferThreshold) {
      _logWithTimestamp('使用分块处理大缓冲区');
    }
    // 查找所有匹配项
    Iterable<Match> matches = _dataJsonRegex.allMatches(buffer);

    for (Match match in matches) {
      if (match.groupCount >= 1) {
        String jsonStr = match.group(1);
        results.add(jsonStr);
      }
    }

    // 如果找到了匹配项，更新缓冲区，移除已处理的部分
    if (matches.isNotEmpty) {
      Match lastMatch = matches.last;
      int endPos = lastMatch.end;
      if (endPos < _globalBuffer.length) {
        _globalBuffer = _globalBuffer.substring(endPos);
      } else {
        _globalBuffer = '';
      }
    }

    return results;
  }

  /// Subscribe to Server-Sent Events using Dio.
  ///
  /// [method] is the request method (GET or POST).
  /// [url] is the URL of the SSE endpoint.
  /// [headers] is a map of request headers.
  /// [body] is an optional request body for POST requests.
  ///
  /// Returns a [Stream] of [SSEDioModel] representing the SSE events.
  Stream<SSEDioModel> subscribeToSSE(
      {SSEDioRequestType method = SSEDioRequestType.get,
      String baseUrl,
      String path,
      Map<String, String> headers,
      Map<String, dynamic> body,
      int maxReceiveTimeout,
      bool enforceXWwwFormUrlencoded = false,
      String wmPoiId}) {
    // 记录连接开始
    _performance.recordConnectionStart();

    final controller = StreamController<SSEDioModel>.broadcast();
    final decoder = Utf8SafeDecoder();

    // 重置全局缓冲区
    _globalBuffer = '';
    hasReceivedData = false;

    _logWithTimestamp(
        'envUrl=$baseUrl , path=$path , headers=$headers,body=$body');

    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      responseType: ResponseType.stream,
      headers: headers,
      connectTimeout: connectionTimeout,
      receiveTimeout: maxReceiveTimeout ?? receiveTimeout,
      sendTimeout: sendTimeout,
      // 非常诡异的设置，如果不设置成会导致主应用失败
      contentType: enforceXWwwFormUrlencoded
          ? 'application/x-www-form-urlencoded'
          : "application/json",
    ));

    try {
      Future<Response<ResponseBody>> dioResponse;
      if (method == SSEDioRequestType.get) {
        dioResponse = _dio.get<ResponseBody>(path);
      } else {
        dioResponse = _dio.post<ResponseBody>(
          path,
          data: body,
        );
      }

      dioResponse.then((response) {
        // 如果请求失败，则取消定时器，并抛出错误
        if (response.data == null) {
          controller.addError(response.statusMessage.toString());
          return;
        }

        List<int> byteData = [];
        // 添加缓冲区，累积不完整的数据
        var currentSSEModel = SSEDioModel();

        response.data.stream.listen((utf8Data) {
          // 更新最后收到数据的时间
          _performance.recordDataReceived(utf8Data.length);

          try {
            if (utf8Data == null || utf8Data.isEmpty) {
              _logWithTimestamp('SSEDioClient : utf8Data is null or empty');
              return;
            }

            byteData.addAll(utf8Data);

            // 提取有效部分，避免 UTF-8 编码断包
            int validOffset = Utf8Validator.extractValidUtf8Bytes(
                Uint8List.fromList(byteData));

            if (validOffset <= 0) {
              _logWithTimestamp('SSEDioClient : validOffset <= 0');
              return;
            }

            // 提取有效部分
            Uint8List extractedBytes =
                Uint8List.fromList(byteData.sublist(0, validOffset));

            // 保留剩余部分
            byteData = byteData.sublist(validOffset);

            final chunkData = decoder.addChunk(extractedBytes);
            if (chunkData == null || chunkData.isEmpty) {
              _logWithTimestamp('SSEDioClient : decoder return null or empty');
              return;
            }

            // 如果收到数据，则设置标志
            hasReceivedData = true;

            _logWithTimestamp('SSEDioClient : chunkData=$chunkData');

            // 将当前数据添加到全局缓冲区
            _globalBuffer += chunkData;

            // 检查是否包含 [DONE] 标记
            if (_globalBuffer.contains(sseEndMarker)) {
              _logWithTimestamp('SSEDioClient : 收到结束标记 [DONE]');
              List<String> jsonDataList = _extractJsonFromBuffer(_globalBuffer);
              for (String jsonData in jsonDataList) {
                // 解析 JSON
                Map<String, dynamic> jsonMap = json.decode(jsonData);
                // 提取 data 字段，包括 正文 和 Context
                if (jsonMap.containsKey('agentContext')) {
                  Map<String, dynamic> agentContext =
                      jsonMap['agentContext'] ?? '';
                  int userMessageId = agentContext['userMessageId'] ?? 0;
                  int agentMessageId = agentContext['agentMessageId'] ?? 0;
                  Map<String, dynamic> extraData = {
                    'userMessageId': userMessageId,
                    'agentMessageId': agentMessageId
                  };
                  // 创建新的 SSE 模型
                  currentSSEModel = SSEDioModel(data: '', extraData: extraData);
                  controller.add(currentSSEModel);
                }
              }
              // 清空缓冲区
              _globalBuffer = '';
              // 延迟关闭流，确保所有数据都被处理
              Future.delayed(const Duration(milliseconds: 50), () {
                if (!controller.isClosed) {
                  controller.close();
                }
              });
              return;
            }

            // {"code":1,"msg":"分配任务失败","data":""}
            if (chunkData.startsWith('{') && chunkData.endsWith('}')) {
              try {
                Map<String, dynamic> result = json.decode(chunkData);
                _logWithTimestamp('SSEDioClient : 接口调用出错，未进入流式状态: $result');
                if (result.containsKey("code") && result['code'] != 0) {
                  _logWithTimestamp('SSEDioClient : 处理数据块错误: $result');
                  controller.addError(result['msg']);
                  return;
                }
              } catch (e) {
                _logWithTimestamp('SSEDioClient : 误判，费接口出错数据: $e');
              }
            }

            // 从缓冲区中提取完整的 JSON 数据
            List<String> jsonDataList = _extractJsonFromBuffer(_globalBuffer);
            for (String jsonData in jsonDataList) {
              try {
                // 记录数据接收
                _performance.recordDataReceived(jsonData.length);
                _logWithTimestamp('jsonData=$jsonData');

                // 解析 JSON
                Map<String, dynamic> jsonMap = json.decode(jsonData);

                if (jsonMap.containsKey("code") && jsonMap['code'] != 0) {
                  String errorMessage = jsonMap['msg'] ?? '';
                  _logWithTimestamp('Code 状态码错误' + errorMessage);
                  controller
                      .addError("[E00" + jsonMap['code'].toString() + "]");
                  return;
                }
                // 提取 data 字段，包括 正文 和 Context
                if (jsonMap.containsKey('data')) {
                  String data = jsonMap['data'] ?? '';
                  Map<String, dynamic> extraData =
                      jsonMap['subAgentCode'] ?? {};
                  String userMessageId = jsonMap['userMessageId'] ?? '';
                  String agentMessageId = jsonMap['agentMessageId'] ?? '';
                  extraData.addAll({
                    'userMessageId': userMessageId,
                    'agentMessageId': agentMessageId
                  });
                  // 创建新的 SSE 模型
                  currentSSEModel =
                      SSEDioModel(data: data, extraData: extraData);
                  controller.add(currentSSEModel);
                }
              } catch (e) {
                _logWithTimestamp(
                    'SSEDioClient : JSON 解析失败: $e, 数据: $jsonData');
              }
            }
          } catch (e) {
            _logWithTimestamp('SSEDioClient : 处理数据块错误: $e');
            // 不要向控制器发送错误，只记录日志
          }
        }, onDone: () {
          // 记录连接结束
          _performance.recordConnectionEnd();

          // 延迟关闭流，确保所有数据都被处理
          Future.delayed(const Duration(milliseconds: 50), () {
            if (!controller.isClosed) {
              controller.close();
            }
          });
        }, onError: (error, stackTrace) {
          _handleError(error, stackTrace, controller, wmPoiId);
        });
      }).catchError((error, stackTrace) {
        _handleError(error, stackTrace, controller, wmPoiId);
      });
    } catch (error, stackTrace) {
      _handleError(error, stackTrace, controller, wmPoiId);
    }

    return controller.stream;
  }

  /// Unsubscribe from the SSE.
  void unsubscribeFromSSE() {
    // 记录连接结束
    _performance.recordConnectionEnd();

    if (_dio != null) {
      _dio.close(force: true);
      _dio = null;
    }
  }

  String _extractHttpStatusCode(String errorStr) {
    final regex = RegExp(r'\[(\d{3})\]');
    final match = regex.firstMatch(errorStr);
    if (match != null) {
      return match.group(1); // 返回匹配到的错误码
    }
    return ""; // 如果没有匹配到，返回 null
  }

  // 统一错误处理
  void _handleError(dynamic error, StackTrace stackTrace,
      StreamController<SSEDioModel> controller, String wmPoiId) {
    String errorStr = '未知错误';
    try {
      errorStr = error?.toString() ?? '未知错误';
    } catch (e) {
      _logWithTimestamp('错误信息转换失败: $e');
    }
    _logWithTimestamp('错误: $errorStr');

    // 识别不同类型的错误，因为不支持 DioError 类型
    String errorMessage = '[E10]';
    try {
      if (errorStr.contains('timeout')) {
        //连接超时，请检查网络
        errorMessage = '[E11]';
      } else if (errorStr.contains('cancel')) {
        //请求已取消
        errorMessage = '[E12]';
      } else if (errorStr.contains('Http status')) {
        errorMessage = "[E13" + _extractHttpStatusCode(errorStr) + "]";
      } else if (errorStr.contains('response')) {
        //服务器响应异常
        errorMessage = '[E14]';
      } else if (errorStr.contains('Invalid HTTP header field value')) {
        //请求参数格式错误
        errorMessage = '[E15]';
      }

      Map<String, List<double>> values = {
        'shop_assistant_custom_error': [0]
      };
      Map<String, String> tags = {
        'Type': errorMessage,
        'PoiId': wmPoiId,
        'fullMessage': errorStr
      };
      FlutterCat.reportCustomField(values: values, tags: tags);
    } catch (e) {
      _logWithTimestamp('错误类型判断失败: $e');
      Map<String, List<double>> values = {
        'shop_assistant_custom_error': [0]
      };
      Map<String, String> tags = {
        'Type': '错误类型判断失败: $e',
        'PoiId': wmPoiId,
        'fullMessage': errorStr
      };
      FlutterCat.reportCustomField(values: values, tags: tags);
    }

    if (!controller.isClosed) {
      controller
          .addError({'msg': errorMessage, 'fullMessage': errorStr}, stackTrace);
    }
  }
}
