import 'dart:convert';
import 'dart:io';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/common/constant.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/api_utils.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/service/house_keeper_sse_client_dio.dart';

/// Abstract class for request handling
abstract class RequestHandler {
  String encodeBody(Map<String, dynamic> body);
  String getQueryString(Map<String, dynamic> params);
  Map<String, String> getHeaders();
  String getPath();
  int getMaxReceiveTimeout();
  String get mode;
  set mode(String value);
}

/// Handler for form-urlencoded requests
class FormUrlEncodedHandler extends RequestHandler {
  String _mode = '';

  @override
  String get mode => _mode;

  @override
  set mode(String value) {
    _mode = value;
  }

  @override
  String encodeBody(Map<String, dynamic> body) {
    if (body == null) return '';
    List<String> encodedPairs = [];
    body.forEach((key, value) {
      if (value != null) {
        String encodedKey = _percentEncode(key);
        String encodedValue = _percentEncode(value.toString());
        encodedPairs.add('$encodedKey=$encodedValue');
      }
    });
    return encodedPairs.join('&');
  }

  @override
  String getQueryString(Map<String, dynamic> params) => '';

  @override
  Map<String, String> getHeaders() => {
        'contentType': 'application/x-www-form-urlencoded',
      };

  @override
  String getPath() => mode == CustomMode.selfSettleIn
      ? '/api/ai/assistant/talk'
      : '/proxy-gw/api/ai/assistant/master/talk';

  String _percentEncode(String str) {
    if (str == null) return '';
    var encoded = Uri.encodeComponent(str);
    return encoded
        .replaceAll('!', '%21')
        .replaceAll("'", '%27')
        .replaceAll('(', '%28')
        .replaceAll(')', '%29')
        .replaceAll('*', '%2A');
  }

  @override
  int getMaxReceiveTimeout() => 120000;
}

/// Handler for JSON requests with query parameters

class JsonWithQueryHandler extends RequestHandler {
  String _mode = '';

  @override
  String get mode => _mode;

  @override
  set mode(String value) {
    _mode = value;
  }

  @override
  String encodeBody(Map<String, dynamic> body) {
    return jsonEncode(body);
  }

  @override
  String getQueryString(Map<String, dynamic> params) {
    final allowedFields = [
      'appType',
      'token',
      'wm_appversion',
      'wmPoiId',
      'acctId'
    ];

    String paramStr = params.entries
        .where((e) => allowedFields.contains(e.key))
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
        .join('&');

    return "?$paramStr";
  }

  @override
  Map<String, String> getHeaders() => {'contentType': 'application/json'};

  @override
  String getPath() => '/proxy-gw/aimanager/api/v1/bill/stream/analyse';

  @override
  int getMaxReceiveTimeout() => 300000;
}

/// API client for House Keeper LLM interactions
class HouseKeeperLLMApi {
// Configuration constants

  static const String streamRequestWorkFlowId = '1896837167259615266';

  // Session configuration
  String workflowId;
  bool isNewSession;
  String mode; // 主应用还是财务对账

  // SSE client instance
  final SSEDioClient _sseClient;

  // Request handlers
  final RequestHandler _formHandler = FormUrlEncodedHandler();
  final RequestHandler _jsonHandler = JsonWithQueryHandler();
  HouseKeeperLLMApi({
    this.workflowId = streamRequestWorkFlowId,
    this.isNewSession = false,
    SSEDioClient sseClient,
    String mode, // 主应用还是财务对账
  })  : _sseClient = sseClient ?? SSEDioClient(),
        mode = mode ?? '' {
    // 根据mode参数更新_formHandler、_jsonHandler
    if (mode != null && mode.isNotEmpty) {
      _formHandler.mode = mode;
      _jsonHandler.mode = mode;
    }
  }

  /// Process mtgsig from KNB response and add it to headers
  ///
  /// [result] - The response from KNB.addRequestSignature
  /// [headers] - The headers map to add mtgsig to
  /// Returns true if processing was successful, false otherwise
  bool _processMtgsig(
      Map<String, dynamic> result, Map<String, String> headers) {
    if (result == null) {
      return false;
    }

    bool isSuccess = result['status'] == "success";
    dynamic mtgsig = result['mtgsig'];

    if (!isSuccess || mtgsig == null || mtgsig.toString().isEmpty) {
      return false;
    }

    try {
      if (mtgsig is String) {
        // iOS needs parsing
        Map<String, dynamic> parsedMtgsig = jsonDecode(mtgsig);
        if (parsedMtgsig == null || !parsedMtgsig.containsKey('mtgsig')) {
          return false;
        }
        headers['mtgsig'] = parsedMtgsig['mtgsig'];
      } else if (mtgsig is Map) {
        // Android doesn't need parsing
        if (!mtgsig.containsKey('mtgsig')) {
          return false;
        }
        headers['mtgsig'] = mtgsig['mtgsig'].toString();
      } else {
        return false;
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  String getHostUrlApi(String hostType) {
    if (mode == CustomMode.selfSettleIn) {
      String hostUrlApi = 'http://kd.waimai.test.meituan.com';
      if (hostType == 'STAGE') {
        hostUrlApi = "https://kd.waimai.st.meituan.com";
      } else if (hostType == 'RELEASE') {
        hostUrlApi = "https://kd.meituan.com";
      }
      return hostUrlApi;
    }
    String hostUrlApi = 'http://eapi.b.waimai.test.sankuai.com';
    if (hostType == 'STAGE') {
      hostUrlApi = "https://eapi.waimai.st.meituan.com";
    } else if (hostType == 'RELEASE') {
      hostUrlApi = "https://eapi.waimai.meituan.com";
    }
    return hostUrlApi;
  }

  /// Stream data from the AI assistant
  ///
  /// [input] - User input text
  /// [cardKey] - Optional card key identifier
  /// [subAgentCode] - Optional sub-agent code
  /// [onData] - Callback for data events
  /// [onDone] - Callback when stream completes
  /// [onError] - Callback for error handling
  Future<void> streamDataApi({
    String input,
    String cardKey,
    String subAgentCode,
    Map<String, dynamic> extra,
    void Function(dynamic response) onData,
    void Function() onDone,
    void Function(dynamic error) onError,
    bool isWorking = false,
  }) async {
    if (isWorking) {
      onError('Another request is in progress');
      return;
    }
    try {
      PoiInfo poiInfo;
      User userInfo;
      String token;
      Map poiDetailResult;
      String poiToken;
      final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
      if (mode == CustomMode.assistant) {
        PoiInfo poiInfo = await WaimaiENativeBusiness.getPoiInfo();
        User userInfo = await WaimaiENativeBusiness.getUserInfo();
        String token = await WaimaiENativeBusiness.getBizLoginToken();
        poiDetailResult = await KNB.use('waimaibiz.getPoiDetail');
        final Map<Object, Object> data =
            (poiDetailResult['data'] as Map<Object, Object>) ?? {};
        poiToken = (data['token'] as String) ?? '';
      }

      String candidateId = '';
      String acctId = '';
      String epToken = '';
      String wmAppversion = envInfo['appVersion'];
      String hostUrlApi = getHostUrlApi(envInfo['hostType']);

      if (mode == CustomMode.selfSettleIn) {
        Map<String, dynamic> params =
            CommonParamsUtils.getInstance().getCommonParams();
        candidateId = params['candidateId'] ?? '';
        acctId = params['acctId'] ?? '';
        poiToken = params['token'] ?? '';
        epToken = params['epToken'] ?? '';
      }

      // Select the appropriate handler based on the path
      final RequestHandler handler =
          mode == CustomMode.finance ? _jsonHandler : _formHandler;
      // Prepare headers
      final Map<String, String> extraHeaders = {
        'contentEncoding': 'gzip',
        "accept": "text/event-stream",
        "cache-control": "no-cache",
        "connection": "keep-alive",
        'userAgent':
            'com.meituan.itakeawaybiz/31086 (unknown, iOS 17.2, iPhone, Scale/3.000000)',
      };
      extraHeaders.addAll(handler.getHeaders());
      if (subAgentCode != null) {
        extraHeaders['subAgentCode'] = subAgentCode;
      }
      if (cardKey != null) {
        extraHeaders['cardKey'] = cardKey;
      }

      // Prepare request body
      final Map<String, dynamic> requestBody = {
        "input": input,
        "workFlowId": workflowId,
        'appType': Platform.isAndroid ? '4' : '5',
        'token': poiToken,
        'epToken': mode == CustomMode.selfSettleIn ? epToken : '',
        'wm_appversion': wmAppversion ?? '',
        'wmPoiId': poiInfo?.poiId ?? '',
        'candidateId': candidateId ?? '',
        'acctId':
            mode == CustomMode.selfSettleIn ? acctId : userInfo?.acctId ?? '',
        'streamType': 'increment',
        'conversationType': '1', // 智能入驻模式写死为1
        'deviceUUID': envInfo['uuid'] ?? '',
      };
      if (mode == CustomMode.finance) {
        Map<String, dynamic> extraParam = {
          "businessLine": "1",
          "businessMap": {"sceneType": 4},
          "requestId": envInfo['uuid'] ?? ''
        };
        requestBody.addAll(extraParam);
      }
      String encodedBody = handler.encodeBody(requestBody);

      String path = handler.getPath();
      String queryString = handler.getQueryString(requestBody);

      var result = await KNB.addRequestSignature(
          method: "POST",
          url: hostUrlApi + path + queryString,
          body: encodedBody,
          header: extraHeaders);

      if (!_processMtgsig(result, extraHeaders)) {
        onError(result != null && result['message'] != null
            ? result['message']
            : 'Failed to process mtgsig');
        return;
      }

      // Subscribe to SSE stream
      _sseClient
          .subscribeToSSE(
              method: SSEDioRequestType.post,
              baseUrl: hostUrlApi,
              headers: extraHeaders,
              path: path + queryString,
              body: requestBody,
              maxReceiveTimeout: handler.getMaxReceiveTimeout(),
              enforceXWwwFormUrlencoded: mode != CustomMode.finance)
          .listen(
            onData,
            onDone: onDone,
            onError: onError,
          );
      // Reset flags after stream setup
      isNewSession = false;
    } catch (e) {
      onError(e);
    }
  }

  /// Cancel any active stream
  void cancelStream() {
    _sseClient.unsubscribeFromSSE();
  }

  /// Close the SSE client and clean up resources
  void dispose() {
    _sseClient.unsubscribeFromSSE();
  }

  /// Enable or disable performance tracking
  void setPerformanceTracking(bool enabled) {
    _sseClient.enablePerformanceTracking = enabled;
  }
}
