import 'package:flutter/material.dart';

/// 推荐问题项
class RecommendItem {
  final String text;
  final String iconType;
  final String applicationId;

  const RecommendItem({
    @required this.text,
    this.iconType,
    this.applicationId,
  });

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      "text": text,
      "icon": iconType,
      "applicationId": applicationId,
    };
  }
}

const String idBusinessAnalysis = '1895640836096688166';
const String idRule = '1896788320865394756';
const String idProduct = '1900448204080865356';

/// 经营: https://p0.meituan.net/tuling/aebdd449094db3d3528ebcb97932ac061443.png
/// 图片: https://p0.meituan.net/tuling/6503f292c9da7bec3521bf4b3c50d9671465.png
/// 文案: https://p0.meituan.net/tuling/63325f765c85b9d0395bbb23d72ed01e1446.png
/// 商品: https://p0.meituan.net/tuling/69ffb54798d4b6da24d6d0863d7159ec1489.png

const String businessAnalysis =
    'https://p0.meituan.net/tuling/aebdd449094db3d3528ebcb97932ac061443.png';
const String image =
    'https://p0.meituan.net/tuling/6503f292c9da7bec3521bf4b3c50d9671465.png';
const String copywriting =
    'https://p0.meituan.net/tuling/63325f765c85b9d0395bbb23d72ed01e1446.png';
const String product =
    'https://p0.meituan.net/tuling/69ffb54798d4b6da24d6d0863d7159ec1489.png';

class RecommendConfig {
  /// 所有推荐问题列表
  static const List<RecommendItem> allRecommends = [
    // 经营分析
    RecommendItem(
      text: "总结昨日经营数据",
      iconType: businessAnalysis,
      applicationId: idBusinessAnalysis,
    ),
    RecommendItem(
      text: "查询新店待完成任务",
      iconType: businessAnalysis,
      applicationId: idBusinessAnalysis,
    ),
    RecommendItem(
      text: "帮我分析订单趋势",
      iconType: businessAnalysis,
      applicationId: idBusinessAnalysis,
    ),
    RecommendItem(
      text: "如何获取更多流量",
      iconType: businessAnalysis,
      applicationId: idBusinessAnalysis,
    ),
    RecommendItem(
      text: "定制单量提升计划",
      iconType: businessAnalysis,
      applicationId: idBusinessAnalysis,
    ),
    RecommendItem(
      text: "定制新店成长计划",
      iconType: businessAnalysis,
      applicationId: idBusinessAnalysis,
    ),

    // 规则
    RecommendItem(
      text: "最近有哪些违规单?",
      applicationId: idRule,
    ),

    // 商品管理推荐
    RecommendItem(
      text: "帮我上架商品",
      iconType: product,
      applicationId: idProduct,
    ),
    RecommendItem(
      text: "帮我修改商品价格",
      iconType: product,
      applicationId: idProduct,
    ),
    RecommendItem(
      text: "帮我修改商品库存",
      iconType: product,
      applicationId: idProduct,
    ),
    RecommendItem(
      text: "语音录入商品",
      iconType: product,
      applicationId: idProduct,
    ),
    RecommendItem(
      text: "拍照录菜",
      iconType: product,
      applicationId: idProduct,
    ),
    RecommendItem(
      text: "设置商品背景图",
      iconType: product,
      applicationId: idProduct,
    ),
    RecommendItem(
      text: "创建套餐拼图",
      iconType: product,
      applicationId: idProduct,
    ),

    // 其他
    RecommendItem(
      text: "帮我回复评价?",
      iconType: copywriting,
      applicationId: idProduct,
    ),
    RecommendItem(
      text: "帮我写一个商品描述",
      iconType: copywriting,
      applicationId: idProduct,
    ),
    RecommendItem(
      text: "帮我写一个商品宣传文案",
      iconType: copywriting,
      applicationId: idProduct,
    ),
  ];
}
