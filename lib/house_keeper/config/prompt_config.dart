import 'package:flutter/foundation.dart';
// ignore: unused_import
import 'package:waimai_e_flutter_house_keeper/house_keeper/service/house_keeper_api.dart';

/// 提示词配置单元
class PromptConfigItem {
  /// 图标URL
  final String icon;

  /// 显示文本
  final String label;

  /// 代理上下文
  final String subAgentCode;

  /// 实际发送的提示词
  final String prompt;

  /// 构造函数
  const PromptConfigItem({
    this.icon,
    @required this.label,
    this.subAgentCode,
    this.prompt,
  });

  /// 从JSON创建配置单元
  factory PromptConfigItem.fromJson(Map<String, dynamic> json) {
    if (json == null) return null;

    return PromptConfigItem(
      icon: json['icon'],
      label: json['label'] ?? '',
      subAgentCode: json['subAgentCode'] ?? '',
      prompt: json['prompt'] ?? json['label'] ?? '', // 如果没有prompt，使用label
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      if (icon != null) 'icon': icon,
      'label': label,
      if (subAgentCode != null) 'subAgentCode': subAgentCode,
      'prompt': prompt ?? label,
    };
  }
}

/// 页面配置
class PageConfig {
  /// 页面类型
  final String page;

  /// 配置行/类型映射
  final Map<String, List<PromptConfigItem>> sections;

  /// 构造函数
  const PageConfig({
    @required this.page,
    @required this.sections,
  });

  /// 从JSON创建页面配置
  factory PageConfig.fromJson(Map<String, dynamic> json) {
    if (json == null) return null;

    // 提取页面类型
    final String page = json['page'] ?? '';

    // 创建配置行/类型映射
    final Map<String, List<PromptConfigItem>> sections = {};

    // 移除page字段，处理其他所有字段作为sections
    json.forEach((key, value) {
      if (key != 'page' && value is List) {
        sections[key] = value
            .map((item) => PromptConfigItem.fromJson(item))
            .where((item) => item != null)
            .toList();
      }
    });

    return PageConfig(
      page: page,
      sections: sections,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = {'page': page};

    sections.forEach((key, value) {
      result[key] = value.map((item) => item.toJson()).toList();
    });

    return result;
  }

  /// 获取特定section的配置项
  List<PromptConfigItem> getSection(String sectionName) {
    return sections[sectionName] ?? [];
  }
}

/// 提示词配置管理器
class PromptConfigData {
  /// 欢迎页配置
  final PageConfig welcomeConfig;

  /// 消息页配置
  final PageConfig messageConfig;

  /// 构造函数
  const PromptConfigData({
    this.welcomeConfig,
    this.messageConfig,
  });

  /// 获取消息页默认提示词
  List<PromptConfigItem> get messageDefaultPrompts {
    return messageConfig?.getSection('defaults') ?? [];
  }

  /// 获取消息页展开提示词
  List<PromptConfigItem> get messageExpandPrompts {
    return messageConfig?.getSection('expands') ?? [];
  }

  /// 获取欢迎页特定行的提示词
  List<PromptConfigItem> getWelcomeLine(String line) {
    return welcomeConfig?.getSection(line) ?? [];
  }

  /// 获取欢迎页第一行提示词
  List<PromptConfigItem> get welcomeLine1 => getWelcomeLine('line1');

  /// 获取欢迎页第二行提示词
  List<PromptConfigItem> get welcomeLine2 => getWelcomeLine('line2');

  /// 获取欢迎页第三行提示词
  List<PromptConfigItem> get welcomeLine3 => getWelcomeLine('line3');
}
