import 'dart:convert';

import 'package:wef_network/wef_request.dart';

class OrderTimeModel {
  String des;
  String data;
  String startTime;
  String endTime;
  int type = 0;
}

class CommentCardItem {
  CommentCardItem.fromJson(Map<String, dynamic> json) {
    orderId = json['orderId'];
    deliveryCommentShow = json['deliveryCommentShow'] ?? true;

    ctime = json['createTime'];
    logisticScore = json['deliveryCommentScore'];
    score = (json['orderCommentScore'] ?? 0).toDouble();
    id = json['id'];
    if (json['pictureUrls'] != null) {
      imgUrls = [];
      json['pictureUrls'].forEach((v) {
        imgUrls.add(v);
      });
    }
    pkgScore = (json['packagingScore'] ?? 0).toDouble();
    foodScore = (json['tasteScore'] ?? 0).toDouble();
    userName = json['userName'];
    userPicUrl = json['userPicUrl'];
    wmPoiId = json['wmPoiId'];
    complained = json['complained'] ?? false;
    comment = json['cleanComment'] ?? "";
    commentSource = json['commentSource'];
  }
  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'deliveryCommentShow': deliveryCommentShow,
      'ctime': ctime,
      'deliveryCommentScore': logisticScore,
      'orderCommentScore': score,
      'id': id,
      'pictureUrls': imgUrls,
      'packagingScore': pkgScore,
      'tasteScore': foodScore,
      'userName': Uri.encodeComponent(userName),
      'userPicUrl': userPicUrl,
      'wmPoiId': wmPoiId,
      'complained': complained,
      'cleanComment': Uri.encodeComponent(comment),
      'commentSource': commentSource,
      'detail': detail,
      'hasSelected': hasSelected,
    };
  }

  @override
  String toString() {
    return json.encode(toJson());
  }

  bool deliveryCommentShow;
  bool complained = false;

  ///头像
  String userPicUrl;

  ///姓名
  String userName;

  ///口味
  num foodScore;

  ///总分
  num poiScore;

  ///包装
  num pkgScore;

  num score;

  /// 评价图片
  List<String> imgUrls;
  String comment;

  ///配送
  num logisticScore;

  num id;
  int wmPoiId;

  num orderId;

  /// 订单详情
  String detail;

  ///评价时间
  String ctime;

  bool hasSelected = false;

  num commentSource;

  String commentInfo() {
    if (comment.isNotEmpty) {
      return "评价内容为:${comment},评价评分为:${score}";
    } else {
      return "评价评分为 ${score}";
    }
  }
}

class CommentResultModel {
  List<CommentCardItem> commentList;
  int total;
  CommentResultModel.fromJson(Map<String, dynamic> json) {
    List orderMapList = json['list'];
    commentList = [];
    orderMapList.forEach((element) {
      commentList.add(CommentCardItem.fromJson(element));
    });
    total = json['total'];
  }
}

class CommentApi {
  static Future<int> fetchCommentGray() {
    return postEApi(path: "/gw/assistant/phf/gray").then((response) {
      if (response != null && response.data != null) {
        return response.data['gray'] ?? false;
      }
      return 0;
    });
  }

  static Future<bool> replyComment(String id, String comment, String cTime) {
    Map<String, dynamic> parmas = {
      'toCommentId': id,
      'comment': comment,
      'userCommentCtime': cTime,
    };
    return postEApi(
            isControlShowToast: true,
            params: parmas,
            path: '/gw/customer/comment/reply')
        .then((value) {
      return true;
    }).catchError((e) {
      return true;
    });
  }

  static Future<CommentResultModel> fetchComment(String path,
      {int pageSize, int pageNum, int commScore, int commType, int source}) {
    Map<String, dynamic> param = {};
    double defaultBeginTime = DateTime(DateTime.now().year,
                DateTime.now().month - 1, DateTime.now().day - 1)
            .millisecondsSinceEpoch /
        1000;
    double defaultEndTime = DateTime(DateTime.now().year, DateTime.now().month,
                DateTime.now().day - 1)
            .millisecondsSinceEpoch /
        1000;
    param.addAll({
      // 用户评价列表参数
      'pageSize': "$pageSize",
      'pageNum': pageNum,
      'commScore': commScore ?? 0, // 好中差评0,1,2,3
      'commType': commType ?? 0, //是否回复 -1,0,1
      'hasContent': -1,
      'periodType': 1,
      'beginTime': defaultBeginTime.toInt(),
      'endTime': defaultEndTime.toInt(),
      'source': source ?? 0,
      "onlyAuditNotPass": 0
    });

    return getApi(
            path: path,
            params: param,
            baseUrl: "https://waimaieapp.meituan.com/")
        .then((response) {
      if (response != null && response.data != null) {
        return CommentResultModel.fromJson(response.data);
      }
      return null;
    });
  }
}
