import 'package:wef_network/wef_request.dart';

class HouseKeeperFinanceApi {
  static Future<bool> confirmCustomization(
    String businessLine,
    String sceneType,
    String requestId,
  ) {
    Map<String, dynamic> params = {
      'businessLine': businessLine ?? 1,
      'sceneType': sceneType ?? 4,
      'requestId': requestId,
    };

    return getEApi(
            path: '/proxy-gw/aimanager/api/v1/bill/customization/submit',
            params: params,
            isControlShowToast: true)
        .then((response) {
      return response?.code == 0;
    }).catchError((error) {
      return false;
    });
  }
}
