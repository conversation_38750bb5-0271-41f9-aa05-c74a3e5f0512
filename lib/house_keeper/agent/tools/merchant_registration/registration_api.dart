import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import 'package:wef_network/wef_request.dart';

class MerchantRegistrationApi {
  /// 获取历史消息
  static Future<HistoryMessages> getHistoryMessages(
      Map<String, dynamic> params) async {
    final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
    String hostType = envInfo['hostType'];
    String baseUrl = 'http://kd.waimai.test.meituan.com';
    if (hostType == 'STAGE') {
      baseUrl = 'https://kd.waimai.st.meituan.com';
    } else if (hostType == 'RELEASE') {
      baseUrl = 'https://kd.meituan.com';
    }

    return getApi(
      baseUrl: baseUrl,
      path: '/api/ai/assistant/history/messages',
      params: params,
      isControlShowToast: true,
    ).then((value) {
      if (value.data == null) {
        return null;
      }

      return HistoryMessages.fromJson(value.data);
    }).catchError((error) {
      return null;
    });
  }
}

class HistoryMessages {
  HistoryMessages({this.items, this.total});

  HistoryMessages.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }

    total = json['total'] ?? 0;
    lastMessageId = json['lastMessageId'] ?? 0;
    if (json['items'] != null) {
      items = [];
      json['items'].forEach((e) {
        items.add(HistoryMessageItem.fromJson(e));
      });
    }
  }

  List<HistoryMessageItem> items;
  int lastMessageId;
  int total;
}

class HistoryMessageItem {
  HistoryMessageItem.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }

    id = json['id'] ?? 0;
    content = json['content'] ?? '';
    role = json['role'] ?? '';
    timestamp = json['timestamp'] ?? '';
  }

  int id;
  String content;
  String role;
  String timestamp;
}
