import 'dart:convert';

class HouseKeeperRecommendSpuNameListInfo {
  HouseKeeperRecommendSpuNameListInfo({
    this.updateTime,
    this.recNames,
  });

  factory HouseKeeperRecommendSpuNameListInfo.fromJson(String str) =>
      HouseKeeperRecommendSpuNameListInfo.fromMap(json.decode(str));

  factory HouseKeeperRecommendSpuNameListInfo.fromMap(
      Map<String, dynamic> json) {
    List list = json["recommendFoodNameVos"] ?? [];
    return HouseKeeperRecommendSpuNameListInfo(
      updateTime: json["updateTime"] ?? '',
      recNames:
          list.map((e) => HouseKeeperRecommendFoodNameVo.fromMap(e)).toList(),
    );
  }

  String updateTime;
  List<HouseKeeperRecommendFoodNameVo> recNames;

  String toJson() => json.encode(toMap());

  Map<String, dynamic> toMap() => {
        "updateTime": updateTime,
        "recommendFoodNameVos": recNames.map((e) => e.toMap()).toList(),
      };
}

class HouseKeeperRecommendFoodNameVo {
  HouseKeeperRecommendFoodNameVo({
    this.picture,
    this.recommendSpuName,
    this.spuId,
    this.spuName,
  });

  factory HouseKeeperRecommendFoodNameVo.fromJson(String str) =>
      HouseKeeperRecommendFoodNameVo.fromMap(json.decode(str));

  factory HouseKeeperRecommendFoodNameVo.fromMap(Map<String, dynamic> json) =>
      HouseKeeperRecommendFoodNameVo(
        picture: json["picture"],
        recommendSpuName: json["recommendSpuName"],
        spuId: json["spuId"],
        spuName: json["spuName"],
      );

  String picture;
  String recommendSpuName;
  int spuId;
  String spuName;

  String toJson() => json.encode(toMap());

  Map<String, dynamic> toMap() => {
        "picture": picture,
        "recommendSpuName": recommendSpuName,
        "spuId": spuId,
        "spuName": spuName,
      };

  Map<String, dynamic> toServerMap(String newName) => <String, dynamic>{
        'spuId': spuId,
        'recommendSpuName': recommendSpuName,
        'spuName': spuName,
        'replacedSpuName': newName
      };
}
