class HouseKeeperProductTagInfo {
  HouseKeeperProductTagInfo();

  HouseKeeperProductTagInfo.fromJson(Map<dynamic, dynamic> dic) {
    isCustomizable =
        (dic['customizable'] ?? customizableDefault) == customizableEnable;

    categoryInfo = dic['category'] != null
        ? ProductCategoryInfo.fromJson(dic['category'])
        : null;

    final List keyItems = dic['propertiesKeys'] ?? [];
    tagKeys = keyItems.map<ProductTagKeyItem>((item) {
      return ProductTagKeyItem.fromJson(item);
    }).toList();

    final Map valueItems = dic['properties_values'] ?? {};
    tagValues = valueItems.map((key, value) {
      List<ProductTagValueItem> items = value.map<ProductTagValueItem>((item) {
        return ProductTagValueItem.fromJson(item);
      }).toList();

      return MapEntry(key, items);
    });

    final List customItems = dic['customPropertiesValues'] ?? [];

    customTagValues = customItems.map<ProductTagValueItem>((item) {
      return ProductTagValueItem.fromJson(item);
    }).toList();
  }

  static const int customizableDefault = 0; // 0:默认值
  static const int customizableEnable = 1; // 1:代表可以自定义
  static const int customizableDisable = 2; // 2:代表不可以自定义

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {};
    map["customizable"] = isCustomizable;
    map["category"] = categoryInfo?.toJson();
    map["propertiesKeys"] = tagKeys?.map((e) => e?.toJson())?.toList();
    map["properties_values"] = tagValues?.map((key, value) =>
        MapEntry(key, value?.map((e) => e?.toJson())?.toList()));
    map["customPropertiesValues"] =
        customTagValues?.map((e) => e?.toJson())?.toList();
    return map;
  }

  Map<String, dynamic> valueToJson() {
    Map<String, dynamic> map = {};
    map["properties_values"] = tagValues?.map((key, value) =>
        MapEntry(key, value?.map((e) => e?.toJson())?.toList()));
    return map;
  }

  Map<String, dynamic> valuesToJson() {
    Map<String, dynamic> result = {};
    tagValues?.forEach((key, valueList) {
      List<String> valueDicList = <String>[];
      for (var element in valueList) {
        if (element.isLeaf) {
          valueDicList.add(element.value ?? '');
        }
      }
      result[key] = valueDicList;
    });
    return result;
  }

  bool isCustomizable;
  ProductCategoryInfo categoryInfo;
  List<ProductTagKeyItem> tagKeys;
  Map<String, List<ProductTagValueItem>> tagValues;
  List<ProductTagValueItem> customTagValues;
}

class ProductTagKeyItem {
  ProductTagKeyItem() {
    tagId = 0;
    tagName = '';
    parentTagId = 0;
    level = 0;
    isLeaf = true;
    sequence = 0;
    isRequired = true;
    inputType = ProductTagInputType();
    children = [];
    promptDocument = '';
    maxLength = -1;
    maxCount = -1;
    inputTypeLimit = '';
  }

  ProductTagKeyItem.fromJson(Map<String, dynamic> dic) {
    tagId = dic['wm_product_lib_tag_id'] ?? 0;
    tagName = dic['wm_product_lib_tag_name'] ?? '';
    parentTagId = dic['parent_tag_id'] ?? 0;
    level = dic['level'] ?? 0;
    isLeaf = (dic['is_leaf'] ?? 0) == 1; // 1 是，2 不是
    sequence = dic['sequence'] ?? 0;
    isRequired = (dic['is_required'] ?? 0) == 1; // 1 是必须，2 非必须
    inputType = ProductTagInputType.fromJson(dic);
    List list = dic['child'] ?? [];
    children = list.map<ProductTagKeyItem>((item) {
      return ProductTagKeyItem.fromJson(item);
    }).toList();
    promptDocument = dic['prompt_document'] ?? '';
    custom = dic['custom'] ?? 2;
    maxCount = dic['enum_limit'] ?? -1;
    maxLength = dic['max_length'] ?? -1;
    inputTypeLimit = dic['input_type_limit'] ?? '';
  }

  Map<String, dynamic> toJson() {
    return {
      'wm_product_lib_tag_id': tagId,
      'wm_product_lib_tag_name': tagName,
      'parent_tag_id': parentTagId,
      'level': level,
      'is_leaf': isLeaf ? 1 : 2,
      'sequence': sequence,
      'is_required': isRequired ? 1 : 2,
      'child': children?.map((e) => e?.toJson())?.toList(),
      'input_type': inputType.value,
      'prompt_document': promptDocument,
      'custom': custom,
      'enum_limit': maxCount,
      'max_length': maxLength,
      'input_type_limit': inputTypeLimit
    };
  }

  bool get isSupportCustomization => custom == 1;

  String get regExpSrting => _inputLimit();

  num tagId;
  String tagName;
  num parentTagId;
  int level;
  bool isLeaf;
  int sequence;
  bool isRequired;
  ProductTagInputType inputType;
  List<ProductTagKeyItem> children;
  String promptDocument;
  String inputTypeLimit; //如'1,2,3'，限制输入内容类型
  int maxLength; //自定义属性值的长度限制
  int maxCount; //多选数量限制
  int custom; //属性值是否可自定义，1代表可以

  bool get isCustomValue => (tagId ?? 0) <= 0;

  String _inputLimit() {
    if (inputTypeLimit == null || inputTypeLimit.isEmpty) {
      return '';
    }
    List<String> list = inputTypeLimit.split(',');
    List<String> result = [];
    for (String subStr in list) {
      if (subStr == '1') {
        result.add('[\u4e00-\u9fa5]');
        //汉字
      } else if (subStr == '2') {
        //英文
        result.add('[a-zA-Z]');
      } else if (subStr == '3') {
        //数字
        result.add('[0-9]');
      }
    }

    if (result.isNotEmpty) {
      return result.join('|');
    } else {
      return '';
    }
  }
}

class ProductTagValueItem {
  ProductTagValueItem() {
    id = 0;
    categoryId = 0;
    valueId = 0;
    value = '';
    tagId = 0;
    tagName = '';
    parentTagId = 0;
    level = 0;
    isLeaf = true;
    sequence = 0;
  }

  ProductTagValueItem.fromJson(Map<String, dynamic> dic) {
    id = dic['id'] ?? 0;
    categoryId = dic['wm_product_property_category_id'] ?? 0;
    valueId = dic['value_id'] ?? 0;
    value = dic['value'] ?? '';
    tagId = dic['wm_product_lib_tag_id'] ?? 0;
    tagName = dic['wm_product_lib_tag_name'] ?? '';
    parentTagId = dic['parent_tag_id'] ?? 0;
    level = dic['level'] ?? 0;
    isLeaf = (dic['is_leaf'] ?? 0) == 1; // 1 是，2 不是
    sequence = dic['sequence'] ?? 0;
  }

  num id;
  num categoryId;
  num valueId;
  String value;
  num tagId;
  String tagName;
  num parentTagId;
  int level;
  bool isLeaf;
  int sequence;

  bool get isCustomValue => (valueId ?? 0) <= 0;

  Map<String, dynamic> toJson() {
    return {
      'wm_product_property_category_id': categoryId,
      'id': id,
      'is_leaf': isLeaf ? 1 : 2,
      'wm_product_lib_tag_id': tagId,
      'wm_product_lib_tag_name': tagName,
      'level': level ?? 0,
      'parent_tag_id': parentTagId,
      'sequence': sequence ?? 0,
      'value': value ?? '',
      'value_id': valueId
    };
  }
}

class ProductCategoryInfo {
  ProductCategoryInfo.fromJson(Map<String, dynamic> dic) {
    id = dic['id'] ?? 0;
    name = dic['name'] ?? '';
    level = dic['level'] ?? 0;
    parentId = dic['parentId'] ?? 0;
    String namePathStr = dic['namePath'] ?? '';
    namePath = namePathStr.split(',');
    String idPathStr = dic['idPath'] ?? '';
    List<String> idPathList = idPathStr.split(',');
    idPath = idPathList.map<num>((item) {
      return num.tryParse(item) ?? 0;
    }).toList();
    overrangeOperationStatus = dic['overrangeOperationStatus'] ?? 0;
  }

  ProductCategoryInfo({
    this.id,
    this.name,
    this.level,
    this.namePath,
    this.idPath,
    this.parentId,
    this.isLeaf,
    this.charIndex,
    this.overrangeOperationStatus,
  });

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'level': level, 'parentId': parentId};
  }

  num id;
  String name;
  num level;
  List<String> namePath;
  List<num> idPath;
  num parentId;
  bool isLeaf;
  String charIndex;
  int overrangeOperationStatus;
}

/// 标签输入类型
class ProductTagInputType {
  ProductTagInputType() {
    _value = none;
  }

  ProductTagInputType.fromJson(Map<String, dynamic> dic) {
    _value = dic['input_type'] ?? none;
  }

  int _value;

  static const int none = 0; // 无
  static const int singleSelect = 1; // 单选
  static const int singleSelectAndInput = 4; // 单选可输入，单层级/多层级，这个是在商品品类树中用到
  static const int singleSelectAndAdd = 7; // 单选可添加
  static const int singleLevelMultiplyChoice = 10002; // 单层级多选
  @Deprecated('old version < 6.10')
  static const int multiSelect = 2; // 多选
  @Deprecated('old version < 6.10')
  static const int input = 3; // 输入大段
  @Deprecated('old version < 6.10')
  static const int multiSelectAppendable = 5; // 多层级多选可添加
  @Deprecated('old version < 6.10')
  static const int inputAppendable = 6; // 输入可添加，已去掉不实现

  int get value => _value;
}

class ProductTagRecommendInfo {
  ProductTagRecommendInfo.fromJson(Map<dynamic, dynamic> dic) {
    final Map valueItems = dic['properties_values'] ?? {};
    tagValues = valueItems.map((key, value) {
      List<ProductTagValueItem> items = value.map<ProductTagValueItem>((item) {
        return ProductTagValueItem.fromJson(item);
      }).toList();
      return MapEntry(key, items);
    });
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {};
    map["properties_values"] = tagValues;
    return map;
  }

  Map<String, dynamic> valuesToJson() {
    Map<String, dynamic> tagValuesDic = {};

    tagValues.forEach((key, valueList) {
      List<String> valueDicList = <String>[];

      for (var e in valueList) {
        if (e.isLeaf) {
          valueDicList.add(e.value ?? '');
        }
      }
      tagValuesDic[key] = valueDicList;
    });

    return tagValuesDic;
  }

  Map<String, List<ProductTagValueItem>> tagValues;
}
