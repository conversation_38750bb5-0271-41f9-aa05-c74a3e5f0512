/// 文案：和顾客看到的优惠不一致？
/// 出现条件:
/// A.商品上的活动处于进行中状态
/// B.满足以下任意条件之一
///   B1.活动开启了流量助手
///   B2.折扣活动-商品同SkuId有提报活动

/// 商品优惠不一致信息
class HouseKeeperProductDiscountDiffInfo {
  HouseKeeperProductDiscountDiffInfo.fromJson(Map<String, dynamic> json) {
    actId = json['actId'];
    actType = json['actType'];
    totalCount = json['totalCount'];
  }

  // 活动id
  int actId;
  // 活动类型
  int actType;
  // 活动总数
  int totalCount;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['actId'] = actId;
    data['actType'] = actType;
    data['totalCount'] = totalCount;
    return data;
  }

  /// 是否展示优惠不一致提示文案
  bool get showDiscountDiffDes => (totalCount ?? 0) > 0;

  /// 是否有多个活动信息
  bool get hasMultiAct => (totalCount ?? 0) > 1;
}
