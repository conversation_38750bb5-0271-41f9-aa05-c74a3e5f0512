import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_goods_spu_attr_info.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_product_ladder_box_price_vo.dart';

class HouseKeeperWmProductSkuVo {
  HouseKeeperWmProductSkuVo(
      {this.id,
      this.spec,
      this.description,
      this.skuCode,
      this.upcCode,
      this.locatorCode,
      this.price,
      this.limitStock,
      this.stock,
      this.boxNum,
      this.boxPrice,
      this.sequence,
      this.specifications,
      this.unit,
      this.weight,
      this.weightUint,
      this.standard,
      this.maxStock,
      this.autoRefresh,
      this.wmProductLadderBoxPriceVo,
      this.haveAttrSendOut,
      this.attrList});

  HouseKeeperWmProductSkuVo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    spec = json['spec'];
    description = json['description'];
    skuCode = json['skuCode'];
    upcCode = json['upcCode'];
    locatorCode = json['locatorCode'];
    price = double.tryParse(json['price'].toString());
    limitStock = json['limitStock'];
    stock = json['stock'];
    boxNum = json['boxNum'];
    boxPrice = json['boxPrice']?.toDouble();
    sequence = json['sequence'];
    specifications = json['specifications'];
    unit = json['unit'];
    weight = json['weight'];
    weightUint = json['weightUint'] ?? '';
    standard = json['standard'];
    maxStock = json['maxStock'];
    autoRefresh = json['autoRefresh'];
    haveAttrSendOut = json['haveAttrSendOut'] ?? 0;
    wmProductLadderBoxPriceVo = json['wmProductLadderBoxPriceVo'] != null
        ? HouseKeeperWmProductLadderBoxPriceVo.fromJson(
            json['wmProductLadderBoxPriceVo'])
        : null;
    List<dynamic> attrs = json['attrList'] ?? [];
    attrList = attrs.map((attrItem) {
      return GoodsSpuAttrValueVo.fromJson(attrItem);
    }).toList();
    skuId = json['sku_id'] ?? '';
  }

  int id;

  String spec;
  String description;
  String skuCode;
  String upcCode;
  String locatorCode;
  double price;
  int limitStock;
  int stock;
  int boxNum;
  double boxPrice;
  int sequence;
  String specifications;
  String unit;
  int weight;
  String weightUint;
  bool standard;
  int maxStock;
  int autoRefresh;
  int haveAttrSendOut;
  HouseKeeperWmProductLadderBoxPriceVo wmProductLadderBoxPriceVo;

  // 组成 SKU 的多规格属性
  List<GoodsSpuAttrValueVo> attrList = [];

  // 套餐商品，标记，不需要序列化
  bool isComboChecked = false;

  // 第三方 SKU CODE
  String skuId;

  // 关联的 spu
  int spuId = 0;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['spec'] = spec;
    data['description'] = description;
    data['skuCode'] = skuCode;
    data['upcCode'] = upcCode;
    data['locatorCode'] = locatorCode;
    data['price'] = price;
    data['limitStock'] = limitStock;
    data['stock'] = stock;
    data['boxNum'] = boxNum;
    data['boxPrice'] = boxPrice;
    data['sequence'] = sequence;
    data['specifications'] = specifications;
    data['unit'] = unit;
    data['weight'] = weight;
    data['weightUint'] = weightUint ?? '克';
    data['standard'] = standard;
    data['maxStock'] = maxStock;
    data['autoRefresh'] = autoRefresh;
    if (wmProductLadderBoxPriceVo != null) {
      data['wmProductLadderBoxPriceVo'] = wmProductLadderBoxPriceVo.toJson();
    }
    return data;
  }

  String getSpec() {
    // 多规格商品，有份量名称则不展示份量值
    GoodsSpuAttrValueVo attrValueVo = attrList
        ?.firstWhere((element) => element.name == "份量", orElse: () => null);
    if (attrValueVo != null && StringUtil.isNotEmpty(attrValueVo.value)) {
      String value = attrValueVo.value;
      for (var attrElement in attrList) {
        if (attrElement != null &&
            attrElement.name != "份量" &&
            StringUtil.isNotEmpty(attrElement.value)) {
          value = value + '、' + attrElement.value;
        }
      }
      return value;
    }
    if (spec == null) {
      return '';
    }
    if (spec.contains('·')) {
      return spec.replaceAll('·', '、');
    }
    return spec;
  }

  // 获取当前 sku 所组成的 attr
  List<num> getRelatedAttrIds() {
    List<num> attrRelatedIds = [];
    for (var element in attrList) {
      attrRelatedIds.add(element.spuAttrId);
    }
    return attrRelatedIds;
  }
}
