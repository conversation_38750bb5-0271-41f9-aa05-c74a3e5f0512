import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/sku_value.dart';

class StockAndBoxPriceInfo {
  StockAndBoxPriceInfo({this.boxType = 2, this.boxInfoList});

  StockAndBoxPriceInfo.fromJson(Map<String, dynamic> json) {
    boxType = json['boxType'];
    List<dynamic> list = json['wmProductBoxInfoVoList'] ?? [];
    boxInfoList = list.map((item) {
      return BoxPriceInfo.fromJson(item);
    }).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['boxType'] = boxType;
    if (boxInfoList != null && boxInfoList.isNotEmpty) {
      List<Map<String, dynamic>> list = boxInfoList.map((item) {
        return item.toJson();
      }).toList();
      data['wmProductBoxInfoVoList'] = list;
    }

    return data;
  }

  num boxType;
  List<BoxPriceInfo> boxInfoList;
}

class BoxPriceInfo {
  BoxPriceInfo(
      {this.boxPrice,
      this.stock = 10000,
      this.maxStock = 10000,
      this.autoRefresh = 1,
      this.boxNum = 1,
      this.limitStock = 1,
      this.skuDetail,
      this.skuSellStatus = 0}) {
    skuSellStatus = calculateSkuSellStatus();
  }

  BoxPriceInfo.fromJson(Map<String, dynamic> json) {
    boxPrice = json['boxPrice'];
    stock = json['stock'] ?? 0;
    maxStock = json['maxStock'] ?? 0;
    autoRefresh = json['autoRefresh'] ?? 1;
    boxNum = json['boxNum'] ?? 1;
    limitStock = json['limitStock'] ?? 0;
    List<dynamic> list = json['wmProductSkuDetailVoList'] ?? [];
    skuDetail = list.map((item) {
      return SkuDetail.fromJson(item);
    }).toList();

    // skuSellStatus会传到库存包装费页，也会回传回来
    if (json['haveAttrSendOut'] != null) {
      skuSellStatus = json['haveAttrSendOut'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['boxPrice'] = boxPrice;
    data['stock'] = stock ?? 0;
    data['maxStock'] = maxStock ?? 0;
    data['autoRefresh'] = autoRefresh ?? 1;
    data['boxNum'] = boxNum ?? 1;
    data['limitStock'] = limitStock ?? 0;
    data['haveAttrSendOut'] = skuSellStatus ?? 0;

    if (skuDetail != null && skuDetail.isNotEmpty) {
      List<Map<String, dynamic>> list = skuDetail.map((item) {
        return item.toJson();
      }).toList();
      data['wmProductSkuDetailVoList'] = list;
    }
    return data;
  }

  List<SkuDetail> skuDetail;
  num boxPrice;
  num stock;
  num maxStock;
  int autoRefresh;
  num boxNum;
  num limitStock;
  int skuSellStatus;

  int detailHashCode() {
    if (skuDetail?.isEmpty ?? true) {
      return 0;
    }
    return skuDetail.map((detail) => detail.value).toList().join().hashCode;
  }

  int calculateSkuSellStatus() {
    int cellStatus = 0;
    for (var item in skuDetail) {
      if (item.sellStatus == 1) {
        cellStatus = 1;
      }
    }
    return cellStatus;
  }

  void setWith(BoxPriceInfo boxPriceInfo) {
    boxPrice = boxPriceInfo.boxPrice;
    stock = boxPriceInfo.stock;
    maxStock = boxPriceInfo.maxStock;
    autoRefresh = boxPriceInfo.autoRefresh;
    boxNum = boxPriceInfo.boxNum;
    limitStock = boxPriceInfo.limitStock;
  }
}

class SkuDetail {
  SkuDetail({this.name, this.nameId = 0, this.value, this.valueId = 0});

  SkuDetail.fromJson(Map<String, dynamic> json) {
    name = json['name'] ?? '';
    nameId = json['name_id'] ?? 0;
    value = json['value'] ?? '';
    valueId = json['value_id'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['value'] = value ?? '';
    data['value_id'] = valueId ?? 0;
    data['name'] = name ?? '';
    data['name_id'] = nameId ?? 0;

    return data;
  }

  @override
  String toString() {
    return value;
  }

  String name;
  num nameId;
  String value;
  num valueId;
  int sellStatus;
}

class WmProductSkuHelper {
  static getSkuValueList(StockAndBoxPriceInfo info) {
    // 已填充打包费的数组
    List<BoxPriceInfo> filledList = info?.boxInfoList?.where((boxInfo) {
      return boxInfo.boxPrice != null;
    })?.toList();

    // 拼接已填充打包费的sku字符串
    List<HouseKeeperSkuValue> valueList = [];
    if (ArrayUtil.isNotEmpty(filledList)) {
      for (var boxInfo in filledList) {
        String values = '';
        for (int i = 0; i < boxInfo.skuDetail.length; i++) {
          SkuDetail detail = boxInfo.skuDetail[i];
          values = values + detail.value;
          if (i != boxInfo.skuDetail.length - 1) {
            values = values + '·';
          } else {
            values = values + '(${boxInfo.boxPrice}元/${boxInfo.boxNum}份)';
          }
        }
        valueList.add(HouseKeeperSkuValue(values));
      }
    }

    return valueList;
  }
}
