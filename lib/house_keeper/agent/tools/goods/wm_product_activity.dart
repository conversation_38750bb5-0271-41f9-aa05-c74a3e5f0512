class HouseKeeperWmProductActivity {
  HouseKeeperWmProductActivity({
    this.actId,
    this.actType,
    this.actInfo,
  });

  HouseKeeperWmProductActivity.fromJson(Map<String, dynamic> json) {
    actId = json['actId'];
    actInfo = json['actInfo'];
    actType = json['actType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['actId'] = actId;
    data['actInfo'] = actInfo;
    data['actType'] = actType;
    return data;
  }

  int actId;
  int actType;
  String actInfo;
}
