class HouseKeeperGoodsOptimizeType {
  static const int all = 0; // 全部
  static const int image = 1; // 图片问题
  static const int weight = 2; //分量未填写
  static const int tag = 3; // 商品标签未填写
  static const int cateogry = 4; // 商品分类未更新
  static const int keyProp = 5; // 建议属性未填写
}

class HouseKeeperGoodsShopAssistantEntity {
  HouseKeeperGoodsShopAssistantEntity(
      {this.tagProduct,
      this.needOptimization,
      this.bizMessage,
      this.bizCode,
      this.spuProduct,
      this.poiProduct});

  HouseKeeperGoodsShopAssistantEntity.fromJson(Map<String, dynamic> json) {
    tagProduct = json['tagProduct'] != null
        ? HouseKeeperShopAssistantTagProduct.fromJson(json['tagProduct'])
        : null;
    needOptimization = json['needOptimization'] != null
        ? HouseKeeperShopAssistantNeedOptimization.fromJson(
            json['needOptimization'])
        : null;
    bizMessage = json['bizMessage'];
    bizCode = json['bizCode'];
    spuProduct = json['spuProduct'] != null
        ? HouseKeeperShopAssistantSpuProduct.fromJson(json['spuProduct'])
        : null;
    poiProduct = json['poiProduct'] != null
        ? HouseKeeperShopAssistantPoiProduct.fromJson(json['poiProduct'])
        : null;
  }

  HouseKeeperShopAssistantTagProduct tagProduct;
  HouseKeeperShopAssistantNeedOptimization needOptimization;
  String bizMessage;
  num bizCode;
  HouseKeeperShopAssistantSpuProduct spuProduct;
  HouseKeeperShopAssistantPoiProduct poiProduct;

  int get optimizePicTotal => needOptimization?.optimizePicTotal ?? 0;

  // 低质商品总数
  int get lowProductCount => poiProduct?.lowCount ?? 0;

  // 建议属性未填写tag下商品数
  int get keyPropProductCount =>
      needOptimization.needOptimizationTitle
          .firstWhere(
            (e) => e.isKeyProp,
            orElse: () => null,
          )
          ?.count ??
      0;

  /// 商品分类需要更新的数量
  int get optimizeCategoryTotal =>
      needOptimization?.categoryNeedUpdateCount ?? 0;

  /// 是否展示商品分类
  bool get categoryUpgradeVisible => optimizeCategoryTotal > 0;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    if (this.tagProduct != null) {
      data['tagProduct'] = this.tagProduct.toJson();
    }
    if (this.needOptimization != null) {
      data['needOptimization'] = this.needOptimization.toJson();
    }
    data['bizMessage'] = this.bizMessage;
    data['bizCode'] = this.bizCode;
    if (this.spuProduct != null) {
      data['spuProduct'] = this.spuProduct.toJson();
    }
    if (this.poiProduct != null) {
      data['poiProduct'] = this.poiProduct.toJson();
    }
    return data;
  }
}

class HouseKeeperShopAssistantTagProduct {
  HouseKeeperShopAssistantTagProduct(
      {this.poiHonorStatus,
      this.poiRankStatus,
      this.freePosterStatus,
      this.freeDisplayStatus});

  HouseKeeperShopAssistantTagProduct.fromJson(Map<String, dynamic> json) {
    poiHonorStatus = json['poiHonorStatus'];
    poiRankStatus = json['poiRankStatus'];
    freePosterStatus = json['freePosterStatus'];
    freeDisplayStatus = json['freeDisplayStatus'];
  }

  bool poiHonorStatus;
  bool poiRankStatus;
  bool freePosterStatus;
  bool freeDisplayStatus;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['poiHonorStatus'] = this.poiHonorStatus;
    data['poiRankStatus'] = this.poiRankStatus;
    data['freePosterStatus'] = this.freePosterStatus;
    data['freeDisplayStatus'] = this.freeDisplayStatus;
    return data;
  }
}

class HouseKeeperShopAssistantNeedOptimization {
  HouseKeeperShopAssistantNeedOptimization(
      {this.needOptimizationTitle,
      this.problemProductoods,
      this.productNameCourseUrl,
      this.productData,
      this.productPicCourseUrl,
      this.optimizePicTotal});

  HouseKeeperShopAssistantNeedOptimization.fromJson(Map<String, dynamic> json) {
    if (json['needOptimizationTitle'] != null) {
      needOptimizationTitle = <ShopAssistantNeedOptimizationTitle>[];
      json['needOptimizationTitle'].forEach((v) {
        needOptimizationTitle
            .add(ShopAssistantNeedOptimizationTitle.fromJson(v));
      });
    }
    problemProductoods = json['problemProductoods'];
    productNameCourseUrl = json['productNameCourseUrl'];
    if (json['productData'] != null) {
      productData = <ShopAssistantProductData>[];
      json['productData'].forEach((v) {
        productData.add(ShopAssistantProductData.fromJson(v));
      });
    }
    productPicCourseUrl = json['productPicCourseUrl'];
    optimizePicTotal = json['optimizePicTotal'] ?? 0;
  }

  List<ShopAssistantNeedOptimizationTitle> needOptimizationTitle;
  num problemProductoods;
  String productNameCourseUrl;
  List<ShopAssistantProductData> productData;
  String productPicCourseUrl;
  int optimizePicTotal;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    if (this.needOptimizationTitle != null) {
      data['needOptimizationTitle'] =
          this.needOptimizationTitle.map((v) => v.toJson()).toList();
    }
    data['problemProductoods'] = this.problemProductoods;
    data['productNameCourseUrl'] = this.productNameCourseUrl;
    if (this.productData != null) {
      data['productData'] = this.productData.map((v) => v.toJson()).toList();
    }
    data['productPicCourseUrl'] = this.productPicCourseUrl;
    data['optimizePicTotal'] = this.optimizePicTotal;
    return data;
  }

  /// 商品分类需要更新的数量
  int get categoryNeedUpdateCount =>
      needOptimizationTitle
          .firstWhere(
              (titleVo) =>
                  titleVo.type == HouseKeeperGoodsOptimizeType.cateogry,
              orElse: (() => null))
          ?.count ??
      0;
}

class ShopAssistantNeedOptimizationTitle {
  ShopAssistantNeedOptimizationTitle(
      {this.level, this.name, this.count, this.type});

  ShopAssistantNeedOptimizationTitle.fromJson(Map<String, dynamic> json) {
    level = json['level'];
    name = json['name'];
    count = json['count'];
    type = json['type'];
  }

  num level;
  String name;
  num count;
  num type;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['level'] = this.level;
    data['name'] = this.name;
    data['count'] = this.count;
    data['type'] = this.type;
    return data;
  }

  bool get isKeyProp => type == HouseKeeperGoodsOptimizeType.keyProp;
}

class ShopAssistantProductData {
  ShopAssistantProductData({
    this.sale,
    this.questionsNum,
    this.productLevel,
    this.price,
    this.spuId,
    this.productMasterPic,
    this.productName,
    this.questionsStr,
    this.isNewCombo,
    this.toppingSpu,
  });

  ShopAssistantProductData.fromJson(Map<String, dynamic> json) {
    sale = json['sale'];
    questionsNum = json['questionsNum'];
    productLevel = json['productLevel'];
    price = json['price'];
    spuId = json['spuId'];
    productMasterPic = json['productMasterPic'];
    productName = json['productName'];
    questionsStr = json['questionsStr']?.cast<String>();
    isNewCombo = json['isNewCombo'] ?? false;
    toppingSpu = json['toppingSpu'] ?? false;
  }

  num sale;
  num questionsNum;
  String productLevel;
  String price;
  num spuId;
  String productMasterPic;
  String productName;
  List<String> questionsStr;
  bool isNewCombo;
  bool toppingSpu;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['sale'] = this.sale;
    data['questionsNum'] = this.questionsNum;
    data['productLevel'] = this.productLevel;
    data['price'] = this.price;
    data['spuId'] = this.spuId;
    data['productMasterPic'] = this.productMasterPic;
    data['productName'] = this.productName;
    data['questionsStr'] = this.questionsStr;
    data['isNewCombo'] = this.isNewCombo;
    data['toppingSpu'] = this.toppingSpu;
    return data;
  }
}

class HouseKeeperShopAssistantSpuProduct {
  HouseKeeperShopAssistantSpuProduct(
      {this.customerTitle,
      this.totalMoneyUnit,
      this.orderDocument,
      this.yesterdayCustomerUnit,
      this.totalOrderUnit,
      this.exposureTitle,
      this.moneyDocument,
      this.totalMoney,
      this.yesterdayExposure,
      this.moneyTitle,
      this.totalExposure,
      this.totalCustomerUnit,
      this.yesterdayOrder,
      this.yesterdayCustomer,
      this.yesterdayOrderUnit,
      this.yesterdayMoneyUnit,
      this.totalCustomer,
      this.totalOrder,
      this.totalExposureUnit,
      this.customerDocument,
      this.yesterdayMoney,
      this.orderTitle,
      this.exposureDocument,
      this.yesterdayExposureUnit});

  HouseKeeperShopAssistantSpuProduct.fromJson(Map<String, dynamic> json) {
    customerTitle = json['customerTitle'];
    totalMoneyUnit = json['totalMoneyUnit'];
    orderDocument = json['orderDocument'];
    yesterdayCustomerUnit = json['yesterdayCustomerUnit'];
    totalOrderUnit = json['totalOrderUnit'];
    exposureTitle = json['exposureTitle'];
    moneyDocument = json['moneyDocument'];
    totalMoney = json['totalMoney'];
    yesterdayExposure = json['yesterdayExposure'];
    moneyTitle = json['moneyTitle'];
    totalExposure = json['totalExposure'];
    totalCustomerUnit = json['totalCustomerUnit'];
    yesterdayOrder = json['yesterdayOrder'];
    yesterdayCustomer = json['yesterdayCustomer'];
    yesterdayOrderUnit = json['yesterdayOrderUnit'];
    yesterdayMoneyUnit = json['yesterdayMoneyUnit'];
    totalCustomer = json['totalCustomer'];
    totalOrder = json['totalOrder'];
    totalExposureUnit = json['totalExposureUnit'];
    customerDocument = json['customerDocument'];
    yesterdayMoney = json['yesterdayMoney'];
    orderTitle = json['orderTitle'];
    exposureDocument = json['exposureDocument'];
    yesterdayExposureUnit = json['yesterdayExposureUnit'];
  }

  String customerTitle;
  String totalMoneyUnit;
  String orderDocument;
  String yesterdayCustomerUnit;
  String totalOrderUnit;
  String exposureTitle;
  String moneyDocument;
  String totalMoney;
  String yesterdayExposure;
  String moneyTitle;
  String totalExposure;
  String totalCustomerUnit;
  String yesterdayOrder;
  String yesterdayCustomer;
  String yesterdayOrderUnit;
  String yesterdayMoneyUnit;
  String totalCustomer;
  String totalOrder;
  String totalExposureUnit;
  String customerDocument;
  String yesterdayMoney;
  String orderTitle;
  String exposureDocument;
  String yesterdayExposureUnit;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['customerTitle'] = this.customerTitle;
    data['totalMoneyUnit'] = this.totalMoneyUnit;
    data['orderDocument'] = this.orderDocument;
    data['yesterdayCustomerUnit'] = this.yesterdayCustomerUnit;
    data['totalOrderUnit'] = this.totalOrderUnit;
    data['exposureTitle'] = this.exposureTitle;
    data['moneyDocument'] = this.moneyDocument;
    data['totalMoney'] = this.totalMoney;
    data['yesterdayExposure'] = this.yesterdayExposure;
    data['moneyTitle'] = this.moneyTitle;
    data['totalExposure'] = this.totalExposure;
    data['totalCustomerUnit'] = this.totalCustomerUnit;
    data['yesterdayOrder'] = this.yesterdayOrder;
    data['yesterdayCustomer'] = this.yesterdayCustomer;
    data['yesterdayOrderUnit'] = this.yesterdayOrderUnit;
    data['yesterdayMoneyUnit'] = this.yesterdayMoneyUnit;
    data['totalCustomer'] = this.totalCustomer;
    data['totalOrder'] = this.totalOrder;
    data['totalExposureUnit'] = this.totalExposureUnit;
    data['customerDocument'] = this.customerDocument;
    data['yesterdayMoney'] = this.yesterdayMoney;
    data['orderTitle'] = this.orderTitle;
    data['exposureDocument'] = this.exposureDocument;
    data['yesterdayExposureUnit'] = this.yesterdayExposureUnit;
    return data;
  }
}

class HouseKeeperShopAssistantPoiProduct {
  HouseKeeperShopAssistantPoiProduct(
      this.hightTitle,
      this.hightCount,
      this.hightPercent,
      this.lowTitle,
      this.lowCount,
      this.lowPercent,
      this.betterPoiCount);

  HouseKeeperShopAssistantPoiProduct.fromJson(Map<String, dynamic> json) {
    lowTitle = json['lowTitle'];
    hightCount = json['hightCount'];
    lowPercent = json['lowPercent'];
    hightPercent = json['hightPercent'];
    betterPoiCount = json['betterPoiCount'];
    lowCount = json['lowCount'];
    hightTitle = json['hightTitle'];
  }

  String hightTitle;
  num hightCount;
  num hightPercent;
  String lowTitle;
  num lowCount;
  num lowPercent;

  String betterPoiCount;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['lowTitle'] = this.lowTitle;
    data['hightCount'] = this.hightCount;
    data['lowPercent'] = this.lowPercent;
    data['hightPercent'] = this.hightPercent;
    data['betterPoiCount'] = this.betterPoiCount;
    data['lowCount'] = this.lowCount;
    data['hightTitle'] = this.hightTitle;
    return data;
  }
}
