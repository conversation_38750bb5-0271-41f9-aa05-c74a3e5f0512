class HouseKeeperFoodSellStatus {
  //null 为首次进入尚未初始化的情况。
  static bool isAllOrDefault(int sellStatus) {
    return sellStatus == null || sellStatus == all;
  }

  // 是否为不可售或折扣Tab
  static bool isOffSellOrDiscountTab(int sellStatus) {
    return sellStatus == notInSell ||
        sellStatus == soldOut ||
        sellStatus == discount ||
        sellStatus == buyGift;
  }

  // -1 全部; 0 售卖中; 1 已下架;2 信息不全; 3 售罄; 4 无图; 5 待添加标签; 6 问题图片; 7 折扣 8 买赠 9单点不送
  // 保证数据一致性，将 WMBFoodSellStatus 映射为 WMBSelectButtonDataItemType
  static const int all = -1;
  static const int selling = 0;
  static const int notInSell = 1;
  static const int infoIncomplete = 2;
  static const int soldOut = 3;
  static const int noPicture = 4;
  static const int needAddTag = 5;
  static const int errorImage = 6;
  static const int discount = 7;
  static const int buyGift = 8;
  static const int noSingleDelivery = 9;
}
