class HouseKeeperWmProductSpuAttrVos {
  HouseKeeperWmProductSpuAttrVos({this.name, this.wmProductSpuSubAttrVos});

  HouseKeeperWmProductSpuAttrVos.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    if (json['wmProductSpuSubAttrVos'] != null) {
      wmProductSpuSubAttrVos = <HouseKeeperWmProductSpuSubAttrVos>[];
      json['wmProductSpuSubAttrVos'].forEach((v) {
        wmProductSpuSubAttrVos
            .add(HouseKeeperWmProductSpuSubAttrVos.fromJson(v));
      });
    }
  }

  String name;
  List<HouseKeeperWmProductSpuSubAttrVos> wmProductSpuSubAttrVos;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    if (wmProductSpuSubAttrVos != null) {
      data['wmProductSpuSubAttrVos'] =
          wmProductSpuSubAttrVos.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HouseKeeperWmProductSpuSubAttrVos {
  HouseKeeperWmProductSpuSubAttrVos({this.id, this.value});

  HouseKeeperWmProductSpuSubAttrVos.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    value = json['value'];
  }

  int id;
  String value;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['value'] = value;
    return data;
  }
}
