import 'dart:math';

import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/goods_common_constants.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/risk_mana_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_goods_spu_attr_info.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_product_activity.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_product_discount_diff_info.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_product_label_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_product_pic_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_product_sku_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_product_spu_attr_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_product_tag_info.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_product_video.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/wm_stock_box_price_info.dart';

class HouseKeeperWmProductSpuVo {
  HouseKeeperWmProductSpuVo({
    this.id,
    this.name,
    this.tagId,
    this.tagName,
    this.price,
    this.wmProductSkuVos,
    this.wmProductLabelVos,
    this.wmProductSpuAttrVos,
    this.unit,
    this.minOrderCount,
    this.sellStatus,
    this.description,
    this.monthSale,
    this.stock,
    this.wmProductPicVos,
    this.shippingTimeX,
    this.discountPrice,
    this.secondTagId,
    this.secondTagName,
    this.level,
    this.offSellType,
    this.riskManaVo,
    this.spTagId,
    this.categoryName,
    this.skuMaxPrice,
    this.searchTerms,
    this.example,
    this.labelValues,
    this.allSoldOut,
    this.someSoldOut,
    this.needBindTag,
    this.wmProductVideo,
    this.wmProductVideoId,
    this.wmProductActivities,
    this.isChecked = false,
    this.wmProductSpuAttrVoV2List,
    this.recommendName,
    this.overrangeOperationStatus,
    this.stockAndBoxPriceInfo,
    this.onlySellInCombo,
    this.comboPriceIllegal,
    this.hasBindCoupon,
    this.cannotRecover,
    this.isNewCombo,
    this.discountDiffInfo,
    this.showViolationPic,
  });

  HouseKeeperWmProductSpuVo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    tagId = json['tagId'];
    tagName = json['tagName'];
    price = json['price']?.toDouble() ?? 0.0;
    if (json['wmProductSkuVos'] != null) {
      wmProductSkuVos = <HouseKeeperWmProductSkuVo>[];
      json['wmProductSkuVos'].forEach((v) {
        wmProductSkuVos.add(HouseKeeperWmProductSkuVo.fromJson(v));
      });
    }
    if (json['wmProductLabelVos'] != null) {
      wmProductLabelVos = <HouseKeeperWmProductLabelVo>[];
      json['wmProductLabelVos'].forEach((v) {
        wmProductLabelVos.add(HouseKeeperWmProductLabelVo.fromJson(v));
      });
    }
    if (json['wmProductSpuAttrVos'] != null) {
      wmProductSpuAttrVos = <HouseKeeperWmProductSpuAttrVos>[];
      json['wmProductSpuAttrVos'].forEach((v) {
        wmProductSpuAttrVos.add(HouseKeeperWmProductSpuAttrVos.fromJson(v));
      });
    }
    unit = json['unit'];
    minOrderCount = json['minOrderCount'];
    sellStatus = json['sellStatus'];
    description = json['description'];
    monthSale = json['monthSale'];
    stock = json['stock'];
    if (json['wmProductPicVos'] != null) {
      wmProductPicVos = <HouseKeeperWmProductPicVo>[];
      json['wmProductPicVos'].forEach((v) {
        wmProductPicVos.add(HouseKeeperWmProductPicVo.fromJson(v));
      });
    }
    shippingTimeX = json['shippingTimeX'];
    discountPrice = json['discountPrice']?.toDouble() ?? 0.0;
    secondTagId = json['secondTagId'];
    secondTagName = json['secondTagName'];
    level = json['level'];
    offSellType = json['offSellType'];
    riskManaVo = json['riskManaVo'] != null
        ? HouseKeeperRiskManaVo.fromJson(json['riskManaVo'])
        : null;
    spTagId = json['spTagId'];
    categoryName = json['categoryName'];
    skuMaxPrice = json['skuMaxPrice']?.toDouble() ?? 0.0;
    searchTerms = json['searchTerms'];
    example = json['example'];
    if (json['labelValues'] != null) {
      labelValues = <HouseKeeperLabelValues>[];
      json['labelValues'].forEach((v) {
        labelValues.add(HouseKeeperLabelValues.fromJson(v));
      });
    }
    allSoldOut = json['allSoldOut'];
    someSoldOut = json['someSoldOut'];
    needBindTag = json['needBindTag'];
    wmProductVideo = json['wmProductVideo'] != null
        ? HouseKeeperWmProductVideo.fromJson(json['wmProductVideo'])
        : null;
    wmProductVideoId = json['wmProductVideoId'];
    discountTips = json['discountTips'];
    if (json['wmProductActivities'] != null) {
      wmProductActivities = <HouseKeeperWmProductActivity>[];
      json['wmProductActivities'].forEach((v) {
        wmProductActivities.add(HouseKeeperWmProductActivity.fromJson(v));
      });
    }
    if (json['isChecked'] != null) {
      isChecked = json['isChecked'];
    } else {
      isChecked = false;
    }
    isOutdatedCategory = json['onlineStatus'] == 2;

    List<dynamic> list = json['wmProductSpuAttrVoV2List'] ?? [];
    wmProductSpuAttrVoV2List = list.map((item) {
      return GoodsSpuAttrInfo.fromJson(item);
    }).toList();
    // 对规格进行排序，份量在第一位
    int weightIndex =
        wmProductSpuAttrVoV2List.indexWhere((element) => element.name == '份量');
    if (weightIndex != -1 && weightIndex != 0) {
      wmProductSpuAttrVoV2List.insert(
          0, wmProductSpuAttrVoV2List.removeAt(weightIndex));
    }

    if (json['stockAndBoxPriceInfo'] != null) {
      stockAndBoxPriceInfo =
          StockAndBoxPriceInfo.fromJson(json['stockAndBoxPriceInfo']);
    }
    recommendName = json['recommendNameByIntelligent'] ?? '';

    overrangeOperationStatus = json['overrangeOperationStatus'] ?? 0;
    canBeSelected = json['canBeSelected'] == 0;
    cannotSelectedReason = json['cannotSelectedReason'] ?? '';

    List<dynamic> attrs = json['attrList'] ?? [];
    attrList = attrs.map((attrItem) {
      return GoodsSpuAttrValueVo.fromJson(attrItem);
    }).toList();
    onlySellInCombo = json['onlySellInCombo'] ?? false;
    comboPriceIllegal = json['comboPriceIllegal'] ?? false;
    hasBindCoupon = json['hasBindCoupon'] ?? 0;
    relateComboList = json['relateComboList'] ?? [];
    isNewCombo = json['isNewCombo'] ?? false;

    cannotRecover = json['cannotRecover'] ?? false;
    appFoodCode = json['app_food_code'] ?? '';

    discountDiffInfo = json['discountPriceDiffDesc'] != null
        ? HouseKeeperProductDiscountDiffInfo.fromJson(
            json['discountPriceDiffDesc'])
        : null;
    showViolationPic = json['showDefaultPic'] ?? false;
    if (json.containsKey('defaultPicUrl')) {
      // json中不存在key defaultPicUrl时有特殊意义，表示后端还在灰度中
      violationDefaultPic = json['defaultPicUrl'] ?? '';
    }
  }

  int id;
  String name; // 商品标题
  int tagId; // 商品分类
  String tagName; //商品分类
  double price; //价格
  List<HouseKeeperWmProductSkuVo> wmProductSkuVos; //多规格
  List<HouseKeeperWmProductLabelVo> wmProductLabelVos; //标签,商品特色商品特色
  List<HouseKeeperWmProductSpuAttrVos> wmProductSpuAttrVos; // 商品属性
  String unit; //商品单位
  int minOrderCount; //最小购买数量
  int sellStatus; //上下架状态 0上架 1下架
  String description; //商品描述
  int monthSale; //月售
  int stock;
  List<HouseKeeperWmProductPicVo> wmProductPicVos;
  bool showViolationPic; // 是否展示违规图(当因图片违规被删除，从而导致商品无图时，要展示违规提示图)
  String violationDefaultPic; // 后端给的违规默认图
  String shippingTimeX;
  double discountPrice;
  int secondTagId;
  String secondTagName;
  int level;
  int offSellType; //下架类型，0－正常下架  1－风控下架
  HouseKeeperRiskManaVo riskManaVo;
  int spTagId; //后台分类Id，标准分类ID
  String categoryName;
  double skuMaxPrice;
  List searchTerms;
  int example;
  List<HouseKeeperLabelValues> labelValues; //商品套餐的内容
  bool allSoldOut;
  bool someSoldOut;
  bool needBindTag;
  HouseKeeperWmProductVideo wmProductVideo; // 对应商品视频
  int wmProductVideoId;
  int overrangeOperationStatus; // 是否超范围经营。1是，0否。

  // 活动tag信息
  String discountTips;

  // 活动
  List<HouseKeeperWmProductActivity> wmProductActivities =
      <HouseKeeperWmProductActivity>[];

  // 商品标签信息，数据来自 /reuse2/product/r/template 非 getSpu
  HouseKeeperProductTagInfo tagInfo;
  bool isChecked;

  //v6.13 类目升级，商品类目状态为2时表示旧类目状态。
  bool isOutdatedCategory;

  List<GoodsSpuAttrInfo> wmProductSpuAttrVoV2List;
  StockAndBoxPriceInfo stockAndBoxPriceInfo;
  String recommendName; // 商品信息优化推荐名称
  // 为主图推荐的优化图
  List<String> recommendedPics;

  // 套餐商品，是否能够被选择，0-可以选择  1-不可以选择
  bool canBeSelected;

  // 套餐商品，展示不能被选择的原因
  String cannotSelectedReason = '';

  // 套餐商品，多规格属性
  List<GoodsSpuAttrValueVo> attrList = [];

  // 是否仅在套餐内可售
  bool onlySellInCombo;

  // 套餐价格异常
  bool comboPriceIllegal;

  // 是否有绑定的商品券（0:未绑定 1:绑定了商品券）
  int hasBindCoupon;

  // 商品单品关联的套餐商品名列表
  List<String> relateComboList;

  // 优惠不一致描述信息
  HouseKeeperProductDiscountDiffInfo discountDiffInfo;

  // 是否新套餐
  bool isNewCombo;

  String appFoodCode;

  bool cannotRecover; // 回收站商品是否可恢复

  // 商品信息版本，用于单商品刷新
  int version;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['tagId'] = tagId;
    data['tagName'] = tagName;
    data['price'] = price;
    if (wmProductSkuVos != null) {
      data['wmProductSkuVos'] = wmProductSkuVos.map((v) => v.toJson()).toList();
    }
    if (wmProductLabelVos != null) {
      data['wmProductLabelVos'] =
          wmProductLabelVos.map((v) => v.toJson()).toList();
    }
    if (wmProductSpuAttrVos != null) {
      data['wmProductSpuAttrVos'] =
          wmProductSpuAttrVos.map((v) => v.toJson()).toList();
    }
    data['unit'] = unit;
    data['minOrderCount'] = minOrderCount;
    data['sellStatus'] = sellStatus;
    data['description'] = description;
    data['monthSale'] = monthSale;
    data['stock'] = stock;
    if (wmProductPicVos != null) {
      data['wmProductPicVos'] = wmProductPicVos.map((v) => v.toJson()).toList();
    }
    data['showDefaultPic'] = showViolationPic;
    data['shippingTimeX'] = shippingTimeX;
    data['discountPrice'] = discountPrice;
    data['secondTagId'] = secondTagId;
    data['secondTagName'] = secondTagName;
    data['level'] = level;
    data['offSellType'] = offSellType;
    if (riskManaVo != null) {
      data['riskManaVo'] = riskManaVo.toJson();
    }
    data['spTagId'] = spTagId;
    data['categoryName'] = categoryName;
    data['skuMaxPrice'] = skuMaxPrice;
    data['searchTerms'] = searchTerms;
    data['example'] = example;
    if (labelValues != null) {
      data['labelValues'] = labelValues.map((v) => v.toJson()).toList();
    }
    data['allSoldOut'] = allSoldOut;
    data['someSoldOut'] = someSoldOut;
    data['needBindTag'] = needBindTag;
    if (wmProductVideo != null) {
      data['wmProductVideo'] = wmProductVideo.toJson();
    }
    data['wmProductVideoId'] = wmProductVideoId;
    if (wmProductActivities != null) {
      data['wmProductActivities'] =
          wmProductActivities.map((v) => v.toJson()).toList();
    }
    data['isChecked'] = isChecked;

    List<GoodsSpuAttrInfo> productSpuAttrVoV2List =
        wmProductSpuAttrVoV2List ?? [];
    List<Map> spuAttrVoV2List = productSpuAttrVoV2List.map((item) {
      return item.toJson();
    }).toList();
    data['wmProductSpuAttrVoV2List'] = spuAttrVoV2List;

    if (stockAndBoxPriceInfo != null) {
      Map stockAndBoxPriceInfoJson = stockAndBoxPriceInfo.toJson();
      data['stockAndBoxPriceInfo'] = stockAndBoxPriceInfoJson;
    }
    data['overrangeOperationStatus'] = overrangeOperationStatus;
    data['isNewCombo'] = isNewCombo;
    data['cannotRecover'] = cannotRecover;
    data['onlySellInCombo'] = onlySellInCombo;
    data['comboPriceIllegal'] = comboPriceIllegal;
    data['hasBindCoupon'] = hasBindCoupon;
    data['app_food_code'] = appFoodCode;
    if (discountDiffInfo != null) {
      data['discountPriceDiffDesc'] = discountDiffInfo.toJson();
    }
    return data;
  }

  /// 更新数据版本号
  void updateVersion() {
    version = (version ?? 0) + 1;
  }

  /// 商品标签类型: 招牌|套餐
  bool isLabelType(int labelType) {
    if (null == wmProductLabelVos) {
      return false;
    }
    for (HouseKeeperWmProductLabelVo labelVo in wmProductLabelVos) {
      if (labelVo.groupId == labelType) {
        return true;
      }
    }
    return false;
  }

  /// 创建套餐VO，并添加
  HouseKeeperWmProductLabelVo createComboLabelVo() {
    HouseKeeperWmProductLabelVo result = HouseKeeperWmProductLabelVo();
    result.groupId = 2;
    wmProductLabelVos ??= <HouseKeeperWmProductLabelVo>[];
    wmProductLabelVos.add(result);
    return result;
  }

  /// 移除套餐
  // void removeComboData() {
  //   WmProductLabelVo comboLabelVo = getComboLabelVo();
  //   if (null != comboLabelVo && null != wmProductLabelVos) {
  //     int index = GoodUtils.flapIndexOfWmProductLabelVo(
  //         comboLabelVo, wmProductLabelVos);
  //     wmProductLabelVos.removeAt(index);
  //   }
  //   if (null != labelValues) {
  //     labelValues = null;
  //   }
  // }

  /// 获取套餐VO数据，商品类型不是套餐时返回null
  HouseKeeperWmProductLabelVo getComboLabelVo() {
    if (null == wmProductLabelVos) {
      return null;
    }
    for (HouseKeeperWmProductLabelVo labelVo in wmProductLabelVos) {
      if (labelVo.groupId == 2) {
        return labelVo;
      }
    }
    return null;
  }

  bool get hasImage => (wmProductPicVos?.length ?? 0) > 0;

  /// 是否有能展示的图片
  bool get hasShowImage {
    // 有图 || 需要展示违规图
    return (wmProductPicVos?.length ?? 0) > 0 || showViolationPic == true;
  }

  /// 获取需要展示的图片
  String get showImage {
    if (hasImage) {
      return wmProductPicVos.first.getDisplaySmallImageUrl();
    }

    final picUrl = _checkViolation(GoodsNetImageConstants.bgImageViolation);
    if (StringUtil.isNotEmpty(picUrl)) return picUrl;

    return GoodsNetImageConstants.bgImageDefault;
  }

  /// 获取需要预览的图片
  String get previewImage {
    if (hasImage) {
      return wmProductPicVos.first.getDisplayLargeImageUrl();
    }

    final picUrl =
        _checkViolation(GoodsNetImageConstants.bgImageViolationPreview);
    if (StringUtil.isNotEmpty(picUrl)) return picUrl;

    return GoodsNetImageConstants.bgImageDefault;
  }

  String _checkViolation(String localDefaultPic) {
    if (StringUtil.isEmpty(localDefaultPic)) return null;

    if (showViolationPic == true) {
      if (StringUtil.isNotEmpty(violationDefaultPic)) {
        return violationDefaultPic;
      } else if (violationDefaultPic == null) {
        return localDefaultPic;
      }
    }
    return null;
  }

  bool get hasSignboard => isLabelType(GoodsLabelType.signatureType);

  bool get isRejectedForSingleTake =>
      isLabelType(GoodsLabelType.singleTakeType);

  // -1 代表无限
  String get stockString {
    int minStock = stock;
    for (var sku in wmProductSkuVos) {
      minStock = getMinStock(minStock, sku.stock);
    }
    if (minStock == null) {
      return '';
    }
    if (minStock > -1) {
      return '库存 $minStock';
    }
    return '库存无限';
  }

  int getMinStock(int a, int b) {
    if (a == -1) {
      return b;
    }
    if (b == -1) {
      return a;
    }
    a ??= 0;
    b ??= 0;
    return min(a, b);
  }

  String get priceString {
    // 多规格
    if (isMultiSku) {
      double minPrice = price;
      for (var sku in wmProductSkuVos) {
        minPrice = min(minPrice, sku.price);
      }
      return '$minPrice';
    }

    // 单规格
    return '$price';
  }

  double get minPrice {
    // 多规格
    if (isMultiSku) {
      double minPrice = price;
      for (var sku in wmProductSkuVos) {
        minPrice = min(minPrice, sku.price);
      }
      return minPrice;
    }

    // 单规格
    return price;
  }

  double get maxPrice {
    // 多规格
    if (isMultiSku) {
      double maxPrice = price;
      for (var sku in wmProductSkuVos) {
        maxPrice = max(maxPrice, sku.price);
      }
      return maxPrice;
    }

    // 单规格
    return price;
  }

  @override
  bool operator ==(other) {
    return (other is HouseKeeperWmProductSpuVo && other.id == id);
  }

  /// 获取第一个sku
  HouseKeeperWmProductSkuVo get firstSkuVo =>
      (wmProductSkuVos?.isEmpty ?? true) ? null : wmProductSkuVos.first;

  /// 获取第一个打包费
  BoxPriceInfo get firstBoxPriceInfo =>
      (stockAndBoxPriceInfo?.boxInfoList?.isEmpty ?? true)
          ? null
          : stockAndBoxPriceInfo.boxInfoList.first;

  /// 是否单规格商品
  bool get isSingleSku =>
      wmProductSkuVos != null && wmProductSkuVos.length == 1;

  /// 多型号
  bool get isMultiSku => wmProductSkuVos != null && wmProductSkuVos.length >= 2;

  bool get hasActivity =>
      wmProductActivities != null && wmProductActivities.isNotEmpty;

  // 限时售卖
  bool get isAllTimeSale =>
      shippingTimeX == null || shippingTimeX == '' || shippingTimeX == '-';

  bool needRecommendName() {
    return (recommendName?.isNotEmpty ?? false) && recommendName != name;
  }

  // 是否超范围经营
  bool get hasOverrangeOperation =>
      overrangeOperationStatus != 0 && overrangeOperationStatus != null;

  // 是否单点不送
  bool get noSingleDelivery {
    bool flag = false;
    for (var label in wmProductLabelVos) {
      if (label.groupId ==
          HouseKeeperWmProductLabelHelper.getLabelMap(
              HouseKeeperWmProductLabel.noSingleDelivery)['groupId']) {
        flag = true;
      }
    }
    return flag;
  }

  // 判断当前规格是否为多规格
  bool isMultiSkuV2() {
    // 规格为空是单规格
    if (ArrayUtil.isEmpty(wmProductSpuAttrVoV2List)) {
      return false;
    }

    // 第一个规格（份量）下的规格值为空 也是单规格
    if (ArrayUtil.isEmpty(wmProductSpuAttrVoV2List[0].spuAttrValueVoList)) {
      return false;
    }
    // 只有一个份量规格 且 只有一个规格值  为单规格  其余都为多规格
    if (wmProductSpuAttrVoV2List.length == 1 &&
        wmProductSpuAttrVoV2List[0].spuAttrValueVoList.length == 1) {
      return false;
    }
    return true;
  }

  // 获取当前商品中已选中组成套餐的 SKU 数量
  num getCombineComboSkuCount() {
    final total = wmProductSkuVos
            ?.map((skuVo) => skuVo.isComboChecked ? 1 : 0)
            ?.reduce((pre, next) => pre + next) ??
        0;
    return max(total, 0);
  }

  // 查找 SKU
  HouseKeeperWmProductSkuVo getSku(int skuId) {
    return wmProductSkuVos?.firstWhere((element) => element.id == skuId,
        orElse: () => null);
  }

  // 商品分类是否为套餐
  bool isPackage() {
    return isLabelType(LabelGroupId.packageId);
  }

  // 商品是否包含套餐内容
  bool hasPackageContent() {
    if (isPackage()) {
      return ArrayUtil.isNotEmpty(labelValues);
    }
    return false;
  }

  /// 是否绑定了商品券
  bool get isBindCoupon => hasBindCoupon == 1;

  // 新套餐内单品名称
  List<String> _productNamesInCombo;

  set productNamesInCombo(List<String> names) {
    _productNamesInCombo = names;
  }

  List<String> get productNamesInCombo => _productNamesInCombo;

  @override
  // TODO: implement hashCode
  int get hashCode => super.hashCode;
}
