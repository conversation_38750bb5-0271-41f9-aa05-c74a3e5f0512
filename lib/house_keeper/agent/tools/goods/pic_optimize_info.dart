import 'dart:convert';

class NewPicOptimizeVo {
  NewPicOptimizeVo(
      {this.spuId,
      this.newPicUrl,
      this.originPicId,
      this.originPicUrl,
      this.newPicSourceType,
      this.newSource});

  factory NewPicOptimizeVo.fromJson(String str) =>
      NewPicOptimizeVo.fromMap(json.decode(str));

  factory NewPicOptimizeVo.fromMap(Map<String, dynamic> json) =>
      NewPicOptimizeVo(
          spuId: json["spuId"],
          newPicUrl: json["newPicUrl"],
          originPicId: json["originPicId"],
          originPicUrl: json["originPicUrl"],
          newPicSourceType: json["newPicSourceType"],
          newSource: json["newSource"]);

  int spuId;
  String newPicUrl;
  String originPicUrl;
  int originPicId;
  int newPicSourceType;
  int newSource;

  Map<String, dynamic> toJson() => {
        "spuId": spuId,
        "newPicUrl": newPicUrl,
        "originPicUrl": originPicUrl,
        "originPicId": originPicId,
        "newPicSourceType": newPicSourceType,
      };

  Map<String, dynamic> toServerMap() => {
        "spuId": spuId,
        "newPicUrl": newPicUrl,
        "newPicSourceType": newPicSourceType,
        "originPicUrl": originPicUrl,
        "originPicId": originPicId,
      };
}
