class HouseKeeperWmProductCategoryVo {
  HouseKeeperWmProductCategoryVo(
      {this.id,
      this.name = '',
      this.description = '',
      this.spuCount,
      this.sequence,
      this.level,
      this.parentId,
      this.parentName,
      this.subWmProductTagVos,
      this.status,
      this.recommendSpuCount,
      this.countOfStatus,
      this.topFlag,
      this.timeZone,
      this.topFlagNew,
      this.tagType,
      this.topFlagStatus,
      this.necessaryTagType,
      this.tagContent = '',
      this.discount,
      this.badgeNumber = 0});

  HouseKeeperWmProductCategoryVo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'] ?? '';
    description = json['description'] ?? '';
    spuCount = json['spuCount'];
    sequence = json['sequence'];
    level = json['level'];
    parentId = json['parentId'];
    parentName = json['parentName'];
    if (json['subWmProductTagVos'] != null) {
      subWmProductTagVos = <HouseKeeperWmProductCategoryVo>[];
      json['subWmProductTagVos'].forEach((v) {
        Map<String, dynamic> castMap = Map<String, dynamic>.from(v);
        subWmProductTagVos
            .add(HouseKeeperWmProductCategoryVo.fromJson(castMap));
      });
    }
    status = json['status'];
    recommendSpuCount = json['recommendSpuCount'];
    countOfStatus = json['countOfStatus'];
    topFlag = json['topFlag'];
    timeZone = json['timeZone'];
    topFlagNew = json['topFlagNew'];
    tagType = json['tagType'];
    timeZoneNew = json['timeZoneNew'];
    necessaryTagType = json['necessaryTagType'];
    tagContent = json['tagContent'] ?? '';
    discount = json['discount'];
    topFlagStatus = json['topFlagStatus'];
  }

  int id;
  String name;
  String description;
  int spuCount;
  int sequence;
  int level;
  int parentId;
  String parentName;
  List<HouseKeeperWmProductCategoryVo> subWmProductTagVos;
  int status;
  int recommendSpuCount;
  int countOfStatus;
  int topFlag;
  Null timeZone;
  int topFlagNew;
  String timeZoneNew;
  int tagType;

  //分时置顶状态：0:非置顶时段；1:置顶时段  正在置顶
  int topFlagStatus;

  //门店是否包含必点分组：0 无，1有
  int necessaryTagType;

  //展示必点or可单点内容
  String tagContent;

  //是否包含折扣商品：0-不包含，1-包含
  int discount;

  int badgeNumber = 0;

  //用于套餐商品选择标记
  int comboBadgeNumber = 0;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['spuCount'] = this.spuCount;
    data['sequence'] = this.sequence;
    data['level'] = this.level;
    data['parentId'] = this.parentId;
    data['parentName'] = this.parentName;
    if (this.subWmProductTagVos != null) {
      data['subWmProductTagVos'] =
          this.subWmProductTagVos.map((v) => v.toJson()).toList();
    }
    data['status'] = this.status;
    data['recommendSpuCount'] = this.recommendSpuCount;
    data['countOfStatus'] = this.countOfStatus;
    data['topFlag'] = this.topFlag;
    data['timeZone'] = this.timeZone;
    data['topFlagNew'] = this.topFlagNew;
    data['tagType'] = this.tagType;
    data['timeZoneNew'] = this.timeZoneNew;
    data['necessaryTagType'] = this.necessaryTagType;
    data['tagContent'] = this.tagContent;
    data['discount'] = this.discount;
    data['topFlagStatus'] = this.topFlagStatus;
    return data;
  }

  /// 门店下是否包含下单必选分组
  bool get hasOrderRequiredCategory => necessaryTagType == 1;

  @override
  bool operator ==(other) {
    return (other is HouseKeeperWmProductCategoryVo && other.id == id);
  }
}

class TagTypeByHome {
  static const int normal = 0;
  static const int must = 1;
  static const int canSingle = 2;
}
