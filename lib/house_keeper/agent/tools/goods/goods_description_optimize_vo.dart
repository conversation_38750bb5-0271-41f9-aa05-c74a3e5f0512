import 'dart:convert';

class GoodsDescriptionOptimizeVo {
  GoodsDescriptionOptimizeVo({
    this.description,
    this.id,
    this.inputGuide,
    this.name,
    this.picture,
    this.recommendList,
  });

  factory GoodsDescriptionOptimizeVo.fromRawJson(String str) =>
      GoodsDescriptionOptimizeVo.fromJson(json.decode(str));

  factory GoodsDescriptionOptimizeVo.fromJson(Map<String, dynamic> json) =>
      GoodsDescriptionOptimizeVo(
        description: json["description"],
        id: json["id"].toInt(),
        inputGuide: json['inputGuide'] != null
            ? List<String>.from(json["inputGuide"].map((x) => x))
            : [],
        name: json["name"],
        picture: json["picture"],
        recommendList: json["recommendList"] != null
            ? List<String>.from(json["recommendList"].map((x) => x))
            : [],
      );

  /**
   * 描述
   */
  String description;
  /**
   * int spuId
   */
  int id;
  /**
   * 输入指导列表（烹饪技法、主要原料、口感描述）
   */
  List<String> inputGuide;
  /**
   * 商品名
   */
  String name;
  /**
   * 主图
   */
  String picture;
  /**
   * 推荐理由列表
   */
  List<String> recommendList;

  String toRawJson() => json.encode(toJson());

  Map<String, dynamic> toJson() => {
        "description": description,
        "id": id,
        "inputGuide": List<dynamic>.from(inputGuide.map((x) => x)),
        "name": name,
        "picture": picture,
        "recommendList": List<dynamic>.from(recommendList.map((x) => x)),
      };
}
