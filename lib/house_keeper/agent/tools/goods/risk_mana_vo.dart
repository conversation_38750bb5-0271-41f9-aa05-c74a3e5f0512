/// 风险下架说明
class HouseKeeperRiskManaVo {
  HouseKeeperRiskManaVo(
      {this.shortDescribe, this.longDescribe, this.recoveryTime});

  HouseKeeperRiskManaVo.fromJson(Map<String, dynamic> json) {
    shortDescribe = json['shortDescribe'];
    longDescribe = json['longDescribe'];
    recoveryTime = json['recoveryTime'];
  }

  String shortDescribe;
  String longDescribe;
  String recoveryTime;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['shortDescribe'] = shortDescribe;
    data['longDescribe'] = longDescribe;
    data['recoveryTime'] = recoveryTime;
    return data;
  }
}
