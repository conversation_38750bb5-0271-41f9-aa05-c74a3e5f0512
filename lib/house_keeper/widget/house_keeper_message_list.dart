import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/messages/house_keeper_markdown_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/messages/house_keeper_stream_message_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/messages/house_keeper_text_message_widget.dart';

class HouseKeeperMessageListWidget extends StatelessWidget {
  final HouseKeeperMessagePageModel pageModel;
  final ScrollController scrollController;

  const HouseKeeperMessageListWidget({
    Key key,
    @required this.pageModel,
    @required this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Selector<HouseKeeperPageVo, List<HouseKeeperMessage>>(
      selector: (_, vo) => vo.messages ?? [],
      builder: (context, messages, child) {
        if (messages.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          itemCount: messages.length,
          itemBuilder: (context, index) {
            final message = messages[index];
            if (message == null) {
              return const SizedBox.shrink();
            }

            // 只对最后一个流式消息使用 key 以优化重建
            if (message is HouseKeeperStreamMessageVo &&
                index == messages.length - 1) {
              return Padding(
                key: ValueKey('${message.hashCode}'),
                padding: const EdgeInsets.only(bottom: 1),
                child: _buildMessageItem(context, message),
              );
            }
            if (index == messages.length - 1) {
              return Padding(
                key: ValueKey('${message.hashCode}'),
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildMessageItem(context, message),
              );
            }
            // 如果不是最后一个消息，添加透明遮罩
            return Padding(
              key: ValueKey('${message.hashCode}'),
              padding: const EdgeInsets.only(bottom: 12),
              child: Stack(
                children: [
                  _buildMessageItem(context, message),
                  // Positioned.fill(
                  //   child: Container(
                  //     color: Colors.transparent,
                  //     // 添加AbsorbPointer使点击事件被吸收
                  //     child: const AbsorbPointer(),
                  //   ),
                  // )
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildMessageItem(BuildContext context, HouseKeeperMessage message) {
    if (message is HouseKeeperStreamMessageVo) {
      return HouseKeeperStreamMessageWidget(
        key: ValueKey(message.hashCode),
        message: message,
        model: pageModel,
      );
    }
    // 添加引导卡片
    if (message?.role == "ASSISTANT" &&
        message?.type == HouseKeeperMessageType.guide) {
      return Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.65,
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(10)),
        ),
        child: Text(
          message?.content ?? '',
          style: const TextStyle(
            fontSize: 14.0,
            color: Colors.black,
            fontWeight: FontWeight.w400,
          ),
        ),
      );
    }

    if (message.role == "USER") {
      // if (message.type == HouseKeeperMessageType.commentCardCui) {
      //   return HouseKeeperCommentCardWidget(cardItem: message.data);
      // }
      return Container(
        alignment: Alignment.centerRight,
        child: UserMessageWidget(message: message),
      );
    } else {
      return HouseKeeperMarkdownWidget(
        data: message.content ?? '',
        messageId: message.messageId,
        vo: message,
        model: pageModel,
        padding: const EdgeInsets.all(12),
        backgroundColor: Colors.white,
        borderRadius: BorderRadius.circular(10),
      );
    }
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Text(
        ' 让我们开始对话吧～',
        style: TextStyle(
          fontSize: 14,
          color: Colors.black54,
        ),
      ),
    );
  }
}
