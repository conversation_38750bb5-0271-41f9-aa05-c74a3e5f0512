import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';

/// 静音/取消静音按钮组件
class MuteToggleButton extends StatefulWidget {
  /// 初始是否静音
  final bool initialMuted;

  /// 静音状态改变时的回调
  final Function(bool isMuted) onMuteChanged;

  /// 构造函数
  const MuteToggleButton({
    Key key,
    this.initialMuted = false,
    this.onMuteChanged,
  }) : super(key: key);

  @override
  State<MuteToggleButton> createState() => _MuteToggleButtonState();
}

class _MuteToggleButtonState extends State<MuteToggleButton> {
  bool _isMuted;

  @override
  void initState() {
    super.initState();
    _isMuted = widget.initialMuted;
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
    });

    if (widget.onMuteChanged != null) {
      widget.onMuteChanged(_isMuted);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: _toggleMute,
        child: Image(
          width: 24,
          height: 24,
          fit: BoxFit.fill,
          image: AdvancedNetworkImage(
            (_isMuted
                ? 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/0a59873b4f1a4911/<EMAIL>'
                : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/919c6a4c42153c45/MuteOff.png'),
            useDiskCache: true,
          ),
        ));
  }
}
