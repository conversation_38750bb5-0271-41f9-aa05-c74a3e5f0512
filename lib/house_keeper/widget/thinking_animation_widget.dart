import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';

/// 显示"正在思考中..."的动画组件，省略号会动态变化
class ThinkingAnimationWidget extends StatefulWidget {
  final TextStyle textStyle;
  final int dotCount;
  final Duration animationDuration;

  const ThinkingAnimationWidget({
    Key key,
    this.textStyle = const TextStyle(
      fontSize: 15,
      color: Color(0xFF8145ED),
      fontWeight: FontWeight.w800,
    ),
    this.dotCount = 3,
    this.animationDuration = const Duration(milliseconds: 200),
  }) : super(key: key);

  @override
  _ThinkingAnimationWidgetState createState() =>
      _ThinkingAnimationWidgetState();
}

class _ThinkingAnimationWidgetState extends State<ThinkingAnimationWidget> {
  int _currentDotCount = 0;
  Timer _timer;

  @override
  void initState() {
    super.initState();
    _startAnimation();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startAnimation() {
    _timer = Timer.periodic(widget.animationDuration, (timer) {
      setState(() {
        _currentDotCount = (_currentDotCount + 1) % (widget.dotCount + 1);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      constraints: const BoxConstraints(
        minWidth: 100,
        maxWidth: 200,
        minHeight: 40,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image(
            image: AdvancedNetworkImage(
              'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/0d88dfde889db604/thinking.png',
              useDiskCache: true,
            ),
            height: 24,
            fit: BoxFit.contain,
          ),
          Text(
            '.' * _currentDotCount,
            style: widget.textStyle,
          ),
        ],
      ),
    );
  }
}
