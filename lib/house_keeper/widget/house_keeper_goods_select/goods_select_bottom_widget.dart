import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_goods_select/single_poi_goods_select_page_vo.dart';

/// 通用选品页底部widget
class GoodsSelectBottomWidget extends StatelessWidget {
  GoodsSelectBottomWidget({
    this.onTapGoodsInfo,
    this.onTapConfirm,
  });

  final Function onTapGoodsInfo;
  final Function onTapConfirm;

  /// 点击商品信息
  void _onTapGoodsInfo() {
    if (onTapGoodsInfo != null) {
      onTapGoodsInfo();
    }
  }

  /// 点击确认按钮
  void _onTapConfirm() {
    if (onTapConfirm != null) {
      onTapConfirm();
    }
  }

  /// 构建已选商品信息widget
  Widget _buildSelectedGoodsInfoWidget() {
    return Selector<SinglePoiGoodsSelectPageVo, Tuple3<int, int, bool>>(
      selector: (context, pageVo) {
        return Tuple3(
          pageVo.maxNum,
          pageVo.selectedGoodsCount,
          pageVo.showMask,
        );
      },
      builder: (context, tuple, _) {
        int maxSelectGoodsCount = tuple.item1 ?? 1;
        int selectedGoodsCount = tuple.item2 ?? 0;
        bool showSelectResultDialog = tuple.item3 ?? false;
        return GestureDetector(
          onTap: _onTapGoodsInfo,
          behavior: HitTestBehavior.opaque,
          child: Container(
            height: 40,
            child: Row(
              children: <Widget>[
                const SizedBox(width: 12),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "${selectedGoodsCount}",
                        style: const TextStyle(
                          color: Color(0xFFFF6A00),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          decoration: TextDecoration.none,
                        ),
                      ),
                      TextSpan(
                        text: "/${maxSelectGoodsCount}件商品",
                      )
                    ],
                    text: "已选择",
                    style: const TextStyle(
                      color: const Color(0xFF222222),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      decoration: TextDecoration.none,
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                Image(
                  width: 14,
                  height: 14,
                  image: AdvancedNetworkImage(
                    showSelectResultDialog
                        ? 'http://p0.meituan.net/tuling/c1d5a2aea57eb11604b085b911c35a49572.png'
                        : 'http://p1.meituan.net/tuling/fb890d0cdebd6ef515d948adc84064f4553.png',
                    useDiskCache: true,
                  ),
                  fit: BoxFit.fill,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建确认按钮widget
  Widget _buildConfirmButtonWidget() {
    return Selector<SinglePoiGoodsSelectPageVo, bool>(
      selector: (context, pageVo) {
        int minSelectGoodsCount = pageVo.minNum ?? 1;
        int selectedGoodsCount = pageVo.selectedGoodsCount ?? 0;
        return selectedGoodsCount >= minSelectGoodsCount;
      },
      builder: (context, isClickable, _) {
        return Container(
            width: 100,
            height: 40,
            margin: const EdgeInsets.only(right: 12),
            child: Container(
                margin: const EdgeInsets.only(),
                decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: isClickable
                            ? [const Color(0xFFFFE14D), const Color(0xFFFFC34D)]
                            : [
                                const Color(0xFFCCCCCC),
                                const Color(0xFFCCCCCC)
                              ]),
                    borderRadius: BorderRadius.circular(40),
                    border: const Border()),
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  child: const Padding(
                    padding: EdgeInsets.all(8),
                    child: Center(
                      child: Text(
                        '确认',
                        style: TextStyle(
                          textBaseline: TextBaseline.ideographic,
                          color: Color(0xFF222222),
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                          decoration: TextDecoration.none,
                        ),
                      ),
                    ),
                  ),
                  onTap: _onTapConfirm,
                )));
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 0.5,
          color: const Color(0xFFEEEEEE),
        ),
        Container(
          height: 64,
          color: Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              _buildSelectedGoodsInfoWidget(),
              _buildConfirmButtonWidget(),
            ],
          ),
        ),
        Container(
          color: Colors.white,
          height: MediaQuery.of(context).padding.bottom,
        )
      ],
    );
  }
}
