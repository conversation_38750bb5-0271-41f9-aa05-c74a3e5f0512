import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_goods_select/house_keeper_goods_single_poi_select_product_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/goods_spu_vo.dart';

/// 商品选择页转化器
class CommonGoodsSelectConverter {
  /// 转化已选商品信息
  static List<SinglePoiSelectProductVo> convertSelectedProductList(
      List<GoodsSpuVo> spuList) {
    if (spuList?.isEmpty ?? true) {
      return <SinglePoiSelectProductVo>[];
    }
    return spuList.map<SinglePoiSelectProductVo>((spu) {
      return SinglePoiSelectProductVo(
        spuId: spu.spuId,
        spuName: spu.spuName,
        tagId: spu.groupId,
        tagName: spu.groupName,
        secondTagId: spu.secondGroupId,
        minPrice: spu.minPrice,
        maxPrice: spu.maxPrice,
        monthSale: spu.monthSale,
        stock: spu.minStock,
        picture: spu.picture,
      );
    }).toList();
  }

  /// 转化已选商品信息
  static List<GoodsSpuVo> convertGoodsSpuList(
    List<SinglePoiSelectProductVo> productVoList, {
    bool fromPageParams = false,
  }) {
    if (productVoList?.isEmpty ?? true) {
      return <GoodsSpuVo>[];
    }
    return productVoList.map<GoodsSpuVo>((spu) {
      return GoodsSpuVo(
        spuId: spu.spuId,
        spuName: spu.spuName,
        groupId: spu.tagId,
        groupName: spu.tagName,
        secondGroupId: spu.secondTagId,
        minPrice: spu.minPrice,
        maxPrice: spu.maxPrice,
        monthSale: spu.monthSale,
        minStock: spu.stock,
        picture: spu.picture,
        // 数据是否来自页面参数
        fromPageParams: fromPageParams,
        // 来自页面参数的数据都是已选中的商品
        checked: fromPageParams == true,
      );
    }).toList();
  }
}
