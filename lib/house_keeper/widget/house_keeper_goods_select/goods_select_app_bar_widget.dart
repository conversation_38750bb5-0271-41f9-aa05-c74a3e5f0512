import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';

class GoodsSelectAppBarWidget extends StatelessWidget {
  const GoodsSelectAppBarWidget({
    Key key,
    @required this.title,
    this.rightButtonText,
    this.rightWidget,
    this.onBack,
    this.onTapRightButton,
    this.isCenterTitle = true,
    this.hasAction = true,
  }) : super(key: key);

  final String title;
  final String rightButtonText;
  final Widget rightWidget;
  final Function onBack;
  final Function onTapRightButton;
  final bool isCenterTitle;
  final bool hasAction;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: _backButton(context),
      centerTitle: isCenterTitle,
      title: Text(
        title,
        style: const TextStyle(
          color: Color(0xff222222),
          fontWeight: FontWeight.w500,
          fontSize: 16,
        ),
      ),
      actions: <Widget>[
        Visibility(
          visible: hasAction,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
            child: _finishButton(),
          ),
        )
      ],
    );
  }

  Widget _backButton(BuildContext context) {
    return TextButton(
      onPressed: () {
        if (onBack != null) {
          onBack();
        } else {
          RouteUtils.close(context);
        }
      },
      child: const Image(
        width: 24,
        height: 24,
        image: AssetImage('images/goods_manage/icon_back.png'),
      ),
    );
  }

  Widget _finishButton() {
    if (rightWidget != null) {
      return rightWidget;
    }

    return Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
              colors: [Color(0xFFFFE14D), Color(0xFFFFC34D)]),
          borderRadius: BorderRadius.circular(16),
        ),
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
            child: Center(
              child: Text(
                rightButtonText ?? '完成',
                style: const TextStyle(
                  color: Color(0xff222222),
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          onTap: () {
            if (onTapRightButton != null) {
              onTapRightButton();
            }
          },
        ));
  }
}
