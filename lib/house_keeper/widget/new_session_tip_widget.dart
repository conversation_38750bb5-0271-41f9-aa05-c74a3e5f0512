import 'package:flutter/material.dart';

class NewSessionTipWidget extends StatelessWidget {
  const NewSessionTipWidget({Key key, this.content}) : super(key: key);

  final String content;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24),
      child: Row(
        children: [
          // 左侧横线
          Expanded(
            child: Divider(
              color: Colors.grey[400],
              thickness: 0.5,
              endIndent: 8,
            ),
          ),
          // 中间文字
          Text(
            content ?? '已是新会话，下拉查看历史记录',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 12,
            ),
          ),
          // 右侧横线
          Expanded(
            child: Divider(
              color: Colors.grey[400],
              thickness: 0.5,
              indent: 8,
            ),
          ),
        ],
      ),
    );
  }
}
