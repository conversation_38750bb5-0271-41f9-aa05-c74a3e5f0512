import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/voice_wave_painter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_vo.dart';

class HouseKeeperVoiceInputWidget extends StatelessWidget {
  const HouseKeeperVoiceInputWidget({Key key, this.pageModel, this.bottomText})
      : super(key: key);
  final HouseKeeperMessagePageModel pageModel;
  final String bottomText;

  /// 创建带有渐变描边的容器
  Widget _buildGradientBorderContainer({
    @required Widget child,
    BoxDecoration innerDecoration,
    List<BoxShadow> boxShadow,
  }) {
    return Container(
      width: double.infinity,
      height: 52,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(26),
        gradient: const LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Color(0xFF63EDFD),
            Color(0xFF2A61C1),
            Color(0xFF8542ED),
          ],
        ),
        boxShadow: boxShadow,
      ),
      child: Container(
        margin: const EdgeInsets.all(2), // 2dp 边框宽度
        decoration: innerDecoration ??
            BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(24),
            ),
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
        alignment: Alignment.center,
        child: Stack(
          children: [
            // 主要内容
            Center(child: child),

            // 右侧文本切换按钮
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onTap: () {
                  pageModel.changeInputType(HouseKeeperInputType.text);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  alignment: Alignment.center,
                  child: Image(
                    width: 24,
                    height: 24,
                    image: AdvancedNetworkImage(
                        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/c9d181f84e76ebe3/keyboard.png'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 将 dbSize 转换为振幅值
  double _calculateAmplitude(int dbSize) {
    // dbSize 通常在 0-100 之间
    // 我们将振幅限制在 0.3-1.0 之间
    const double minAmplitude = 0.3;
    const double maxAmplitude = 1.0;
    const int minDb = 0;
    const int maxDb = 100;

    // 确保 dbSize 在有效范围内
    final int clampedDb = dbSize.clamp(minDb, maxDb);

    // 线性映射 dbSize 到振幅范围
    return minAmplitude + (clampedDb / maxDb) * (maxAmplitude - minAmplitude);
  }

  Widget _buildNormal(BuildContext context) {
    return _buildGradientBorderContainer(
      child: Text(
        bottomText ?? '按住说话',
        style: const TextStyle(
          fontSize: 16.0,
          color: Color(0xFFFFFFFF),
          fontWeight: FontWeight.w500,
          decoration: TextDecoration.none,
        ),
      ),
    );
  }

  Widget _buildAnalysing(BuildContext context) {
    return _buildGradientBorderContainer(
      innerDecoration: BoxDecoration(
        color: const Color(0xFF222222),
        borderRadius: BorderRadius.circular(24),
      ),
      child: const Text(
        '说完了',
        style: TextStyle(
          fontSize: 16.0,
          color: Color(0xFFFFFFFF),
          fontWeight: FontWeight.w500,
          decoration: TextDecoration.none,
        ),
      ),
    );
  }

  Widget _buildListening(BuildContext context, int dbSize) {
    // 计算阴影的扩散半径，也使用 dbSize 来决定
    final double spreadRadius = 2.0 + (dbSize / 100.0) * 2.0; // 2.0-4.0之间变化

    return _buildGradientBorderContainer(
      innerDecoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF222222), Color(0xFF222222)],
        ),
        borderRadius: BorderRadius.circular(24),
      ),
      boxShadow: [
        BoxShadow(
          color: const Color(0xFF8542ED).withOpacity(0.3),
          blurRadius: 8,
          spreadRadius: spreadRadius,
          offset: const Offset(0, 2),
        ),
      ],
      child: VoiceWaveWidget(
        amplitude: _calculateAmplitude(dbSize),
        startColor: const Color(0xFF63EDFD),
        endColor: const Color(0xFF8542ED),
      ),
    );
  }

  Widget buildChild(
      BuildContext context, VoiceAssistantState state, int dbSize) {
    switch (state) {
      case VoiceAssistantState.normal:
        return _buildNormal(context);
      case VoiceAssistantState.listening:
        return _buildListening(context, dbSize);
      case VoiceAssistantState.analysing:
        return _buildAnalysing(context);
      default:
        return _buildNormal(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;
    double bottom = isKeyboardVisible
        ? 0.0
        : MediaQuery.of(context).viewPadding.bottom + 12;

    return Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.fromLTRB(16, 12, 16, bottom),
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Selector<HouseKeeperPageVo, Tuple2<VoiceAssistantState, int>>(
              selector: (_, pageVo) {
            return Tuple2(pageVo.voiceInputState, pageVo.dbSize);
          }, builder: (context, data, _) {
            VoiceAssistantState state = data.item1;
            int dbSize = data.item2;
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onLongPressStart: (details) {
                      KNB.sendLog(
                          text: 'open-assistant-voice-input:onLongPressStart');
                      pageModel.startListening();
                    },
                    onLongPressEnd: (details) {
                      KNB.sendLog(
                          text: 'open-assistant-voice-input:onLongPressEnd');
                      pageModel.stopListening();
                    },
                    onLongPressCancel: () {
                      KNB.sendLog(
                          text: 'open-assistant-voice-input:onLongPressCancel');
                      pageModel.stopListening();
                    },
                    child: buildChild(context, state, dbSize),
                  ),
                ),
              ],
            );
          })
        ],
      ),
    );
  }
}
