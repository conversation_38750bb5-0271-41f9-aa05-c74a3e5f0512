import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_goods_select/house_keeper_goods_single_poi_select_product_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/house_keeper_goods_select_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/picture/image_preview.dart';
import 'package:wef_network/wef_request.dart';

/// 选择背景图
class GoodsBackgroundManagerWidget extends BaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const GoodsBackgroundManagerWidget({
    Key key,
    String content,
    this.model,
  }) : super(key: key, content: content);

  @override
  _GoodsBackgroundManagerWidgetState createState() =>
      _GoodsBackgroundManagerWidgetState();
}

class _GoodsBackgroundManagerWidgetState
    extends BaseBlockWidgetState<GoodsBackgroundManagerWidget> {
  bool isReplacing = false;
  String selectedResult;

  void _startReplacing(String selectedResult) {
    setState(() {
      isReplacing = true;
      this.selectedResult = selectedResult;
    });
  }

  @override
  Widget buildContentView() {
    return isReplacing
        ? GoodsReplaceBackgroundWidget(
            content: selectedResult,
            model: widget.model,
          )
        : GoodsSelectBackgroundWidget(
            content: widget.content,
            model: widget.model,
            onStartReplacing: _startReplacing,
          );
  }
}

/// 选择背景图
class GoodsSelectBackgroundWidget extends BaseBlockWidget {
  final HouseKeeperMessagePageModel model;
  final void Function(String) onStartReplacing;

  const GoodsSelectBackgroundWidget({
    Key key,
    String content,
    this.model,
    this.onStartReplacing,
  }) : super(key: key, content: content);

  @override
  _GoodsSelectBackgroundWidgetState createState() =>
      _GoodsSelectBackgroundWidgetState();
}

class _GoodsSelectBackgroundWidgetState
    extends BaseBlockWidgetState<GoodsSelectBackgroundWidget> {
  // 背景图 url
  List<String> backgroundPicList = [
    'https://p0.meituan.net/ingee/b872d4d339c87223612a96e0d3e2ab44450902.png',
    'https://p0.meituan.net/ingee/7e7248b1d10302811ed3e98d5b0ec66e373239.png',
    'https://p0.meituan.net/ingee/c656aceb46c8294a516c6c948c9d6a71490641.png',
    'https://p0.meituan.net/ingee/f41e6a3829c704f16690ab7974a707ef404992.png',
    'https://p0.meituan.net/ingee/0bb731cea9ccebe1cdae771096d49b83204631.png',
    'https://p0.meituan.net/ingee/2f5e52f16d427f0da4cc0577132b7b05194643.png',
    'https://p0.meituan.net/ingee/8b4ba8633aa8b40e59f23264ba10f1c2449986.png',
    'https://p0.meituan.net/ingee/b7820b23010eb69d3d483ccea8a3e905402230.png',
  ];

  String selectedBackgroundPic =
      'https://p0.meituan.net/ingee/8b4ba8633aa8b40e59f23264ba10f1c2449986.png';

  /// 已选择商品
  List<SinglePoiSelectProductVo> selectedGoodsList = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget buildContentView() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const Padding(
            padding: EdgeInsets.only(bottom: 8),
            child: Text(
              '选择背景图',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF222222),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          _buildPicListWidget(),
          Container(
            margin: const EdgeInsets.only(top: 8, bottom: 8),
            child: const Text(
              '选择商品（最多4个）',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF222222),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          _buildSelectedGoodsWidget(),
          Visibility(
            visible: selectedGoodsList.isNotEmpty,
            child: GestureDetector(
              onTap: () {
                beginReplace();
              },
              child: Container(
                alignment: Alignment.center,
                height: 36,
                margin: const EdgeInsets.only(top: 16, bottom: 16, right: 16),
                decoration: BoxDecoration(
                  color: const Color(0xFF222222),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: const Text(
                  '开始生成',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPicListWidget() {
    return GridView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1),
      itemCount: 8,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              selectedBackgroundPic = backgroundPicList[index];
            });
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFFEEEEEE),
                width: 1,
              ),
            ),
            child: _buildPictureItemWidget(backgroundPicList, index,
                selectedBackgroundPic == backgroundPicList[index]),
          ),
        );
      },
    );
  }

  Widget _buildPictureItemWidget(
      List<String> imageList, int index, bool isSelect) {
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      return Stack(
        children: [
          Positioned(
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0xffF5F6FA)),
                child: Image(
                    image: AdvancedNetworkImage(
                      imageList[index],
                      useDiskCache: true,
                    ),
                    fit: BoxFit.cover)),
          ),
          Positioned(
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              child: Visibility(
                visible: isSelect,
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: const Color(0xFF222222),
                        width: 2,
                      ),
                      color: Colors.transparent),
                ),
              )),
          Positioned(
              right: 8,
              top: 8,
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: isSelect ? const Color(0xFF222222) : Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: const Color(0xFF222222),
                    width: 1,
                  ),
                ),
                child: isSelect
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 14,
                      )
                    : null,
              )),
        ],
      );
    });
  }

  Widget _buildSelectedGoodsWidget() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: GridView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        // 如果商品数量小于4，则多显示一个添加按钮
        itemCount: selectedGoodsList.length < 4
            ? selectedGoodsList.length + 1
            : selectedGoodsList.length,
        itemBuilder: (context, index) {
          // 如果是最后一个且商品数量小于4，显示添加按钮
          if (index == selectedGoodsList.length &&
              selectedGoodsList.length < 4) {
            return GestureDetector(
              onTap: selectGoods,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFFEEEEEE),
                    width: 1,
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.add,
                    size: 24,
                    color: Color(0xFF222222),
                  ),
                ),
              ),
            );
          }

          final product = selectedGoodsList[index];
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFFEEEEEE),
                width: 1,
              ),
            ),
            child: Stack(
              children: [
                Positioned.fill(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image(
                      image: AdvancedNetworkImage(
                        product.picture,
                        useDiskCache: true,
                      ),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Positioned(
                  right: 4,
                  top: 4,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedGoodsList.removeAt(index);
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // 选品
  void selectGoods() {
    // 如果已经选择了4个商品，不允许继续选择
    if (selectedGoodsList.length >= 4) {
      MTFToast.showToast(msg: '最多只能选择4个商品');
      return;
    }

    // 计算还可以选择多少个商品
    final remainingCount = 4 - selectedGoodsList.length;

    showSelectedGood(
      context,
      selectedGoods: selectedGoodsList, // 传入已选择的商品列表
      maxCount: 4, // 传入总的最大可选数量
    ).then((value) {
      if (value == null) {
        return;
      }

      List<SinglePoiSelectProductVo> newGoodsList = value;
      if (ArrayUtil.isEmpty(newGoodsList)) {
        return;
      }

      setState(() {
        selectedGoodsList = newGoodsList; // 直接使用返回的列表，因为选择界面会处理合并逻辑
      });
    });
  }

  Future<dynamic> showSelectedGood(BuildContext context,
      {List<SinglePoiSelectProductVo> selectedGoods, int maxCount}) {
    Completer<dynamic> completer = Completer();
    showModalBottomSheet(
        isDismissible: false,
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (ctx) {
          return Material(
            color: Colors.transparent,
            child: Container(
                height: MediaQuery.of(context).size.height * 0.8,
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10))),
                child: CommonGoodsSelectPage(
                  maxSelectGoodsCount: maxCount,
                  goodSelectedCallBack: (val) {
                    completer.complete(val);
                  },
                  selectedGoods: selectedGoods,
                )),
          );
        }).then((value) {
      if (completer != null && !completer.isCompleted) {
        completer.complete(null);
      }
    });
    return completer.future;
  }

  /// 开始生成
  void beginReplace() {
    if (selectedGoodsList.isEmpty) {
      MTFToast.showToast(msg: '请先选择商品');
      return;
    }

    Map map = {
      'selectedBackgroundPic': selectedBackgroundPic,
      'selectedGoodsList':
          selectedGoodsList.map((item) => item.toJson()).toList(),
    };

    String content = jsonEncode(map);

    // 调用回调函数并传递 content
    if (widget.onStartReplacing != null) {
      widget.onStartReplacing(content);
    }
  }
}

/// 更换背景图
class GoodsReplaceBackgroundWidget extends BaseBlockWidget {
  const GoodsReplaceBackgroundWidget({
    Key key,
    String content,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;

  @override
  _GoodsReplaceBackgroundWidgetState createState() =>
      _GoodsReplaceBackgroundWidgetState();
}

class _GoodsReplaceBackgroundWidgetState
    extends BaseBlockWidgetState<GoodsReplaceBackgroundWidget> {
  GenerateBackgroundPageVo pageVo;
  String selectedBackgroundPic = '';
  List<SinglePoiSelectProductVo> selectedGoods = [];
  Map<String, String> errorMessages = {}; // 存储每个商品的错误信息

  @override
  void initState() {
    super.initState();
    pageVo = GenerateBackgroundPageVo();

    Map<String, dynamic> jsonData = jsonDecode(widget.content);
    selectedBackgroundPic = jsonData['selectedBackgroundPic'];

    // 添加空值检查
    final goodsList = jsonData['selectedGoodsList'];
    if (goodsList != null) {
      goodsList.forEach((element) {
        selectedGoods.add(SinglePoiSelectProductVo.fromJson(element));
      });
    }

    pageVo.initParams(selectedBackgroundPic, selectedGoods);
    _start();
  }

  @override
  Widget buildContentView() {
    return ChangeNotifierProvider<GenerateBackgroundPageVo>.value(
      value: pageVo,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(6)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTopWidget(),
            _buildPicListWidget(),
            _buildBottomWidget(),
          ],
        ),
      ),
    );
  }

  void _start() {
    for (SinglePoiSelectProductVo productVo in selectedGoods) {
      String picUrl = productVo.picture;
      postEApi(
        path: '/gw/product_submit_starfish',
        params: {
          'body': jsonEncode({
            'refImgUrl': selectedBackgroundPic,
            'geneNum': 1,
            'url': picUrl,
          })
        },
        isControlShowToast: false,
      ).then((value) {
        pageVo.loadImageCount++;
        if (value?.code == 0) {
          Map<String, dynamic> jsonData = jsonDecode(value.data);
          List<String> urls = List<String>.from(jsonDecode(jsonData['urls']));

          if (ArrayUtil.isEmpty(urls)) {
            errorMessages[productVo.spuName] = '生成失败';
            pageVo.safeNotifyListeners();
            return;
          }

          pageVo.selectedGoods[productVo.spuName].refImgUrl = urls[0];
          pageVo.safeNotifyListeners();
        } else {
          errorMessages[productVo.spuName] = value?.msg ?? '生成失败';
          pageVo.safeNotifyListeners();
        }
      }).catchError((error) {
        pageVo.loadImageCount++;
        errorMessages[productVo.spuName] = '网络错误';
        pageVo.safeNotifyListeners();
      });
    }
  }

  Widget _buildTopWidget() {
    return Selector<GenerateBackgroundPageVo, Tuple2<int, int>>(
      selector: (_, pageVo) =>
          Tuple2(pageVo.spuNames.length, pageVo.loadImageCount),
      builder: (_, tuple, __) {
        int errorCount = errorMessages.length;
        int successCount = tuple.item2 - errorCount;

        String content = (tuple.item1 == tuple.item2)
            ? '已为${tuple.item1}个商品生成背景图，${errorCount > 0 ? '${errorCount}个失败' : '全部成功'}'
            : '正在为${tuple.item1}个商品更换背景图，预计等待${(tuple.item1 - tuple.item2) * 5}s...';

        return Text(
          content,
          style: TextStyle(
            fontSize: 14,
            color: errorCount > 0
                ? const Color(0xFFFF5F59)
                : const Color(0xff222222),
            fontWeight: FontWeight.w500,
          ),
        );
      },
    );
  }

  Widget _buildPicListWidget() {
    return Selector<GenerateBackgroundPageVo,
        Tuple2<int, List<SinglePoiSelectProductVo>>>(
      selector: (_, pageVo) =>
          Tuple2(pageVo.loadImageCount, pageVo.selectedGoods.values.toList()),
      builder: (_, tuple, __) {
        final productList = tuple.item2;
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 0.8,
            ),
            itemCount: selectedGoods.length, // 使用state中的商品列表长度
            itemBuilder: (context, index) {
              final isLoading = index >= productList.length;
              final productVo =
                  isLoading ? selectedGoods[index] : productList[index];

              return Container(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      productVo.spuName,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Color(0xff222222),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Expanded(
                      child: isLoading
                          ? Container(
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(4),
                                ),
                                border: Border.all(
                                  color: const Color(0xFFCCCCCC),
                                  width: 0.5,
                                ),
                                color: Colors.white,
                              ),
                              child: const Text(
                                '生成中',
                                style: TextStyle(
                                    fontSize: 12, color: Color(0xff999999)),
                              ),
                            )
                          : _buildItem(productVo),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildItem(SinglePoiSelectProductVo item) {
    String url = item.refImgUrl;
    bool isSelect = item.select;
    bool hasError = errorMessages.containsKey(item.spuName);

    if (StringUtil.isEmpty(url)) {
      return Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(
            Radius.circular(4),
          ),
          border: Border.all(
            color: const Color(0xFFCCCCCC),
            width: 0.5,
          ),
          color: Colors.white,
        ),
        child: hasError
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Color(0xFFFF5F59),
                    size: 20,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    errorMessages[item.spuName] ?? '生成失败',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFFFF5F59),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              )
            : const Text(
                '生成中...',
                style: TextStyle(fontSize: 12, color: Color(0xff999999)),
              ),
      );
    }

    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      return Stack(
        children: [
          Positioned(
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: const Color(0xffeeeeee)),
                child: Image(
                    image: StringUtil.isEmpty(url)
                        ? const AssetImage(
                            'images/goods_manage/img_default.png')
                        : AdvancedNetworkImage(
                            url,
                            useDiskCache: true,
                          ),
                    fit: BoxFit.fill)),
          ),
          Positioned(
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              child: Visibility(
                visible: (isSelect ?? false),
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: const Color(0xFFffdd00),
                        width: 2,
                      ),
                      color: Colors.transparent),
                ),
              )),
          Positioned(
              left: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.only(top: 8, left: 8),
                child: Image(
                  width: 15,
                  height: 15,
                  image: (isSelect ?? false)
                      ? const AssetImage(
                          'images/user_comment/product_selected.png')
                      : const AssetImage(
                          'images/user_comment/product_no_selected.png'),
                ),
              )),
          Positioned(
              right: 0,
              bottom: 0,
              child: GestureDetector(
                onTap: () {
                  //   图片预览
                  showDialog(
                    context: context,
                    builder: (_) => ImagePreview(
                      url,
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.all(2),
                  child: Image(
                    image: AdvancedNetworkImage(
                      'https://s3.meituan.net/static-prod01/com.sankuai.waimaie.h5.rulecenter-files/preview_pic.png',
                      useDiskCache: true,
                    ),
                    width: 24,
                    height: 24,
                    fit: BoxFit.fill,
                  ),
                ),
              )),
        ],
      );
    });
  }

  Widget _buildBottomWidget() {
    return Selector<GenerateBackgroundPageVo, Tuple2<bool, int>>(
      selector: (_, pageVo) => Tuple2(
          pageVo.spuNames.length == pageVo.loadImageCount,
          pageVo.loadImageCount),
      builder: (_, tuple, __) {
        // 检查是否有选中的成功生成的图片，并统计数量
        int selectedSuccessCount = 0;
        for (SinglePoiSelectProductVo productVo
            in pageVo.selectedGoods.values) {
          if ((productVo.select ?? false) &&
              !StringUtil.isEmpty(productVo.refImgUrl) &&
              !errorMessages.containsKey(productVo.spuName)) {
            selectedSuccessCount++;
          }
        }

        bool canClick = tuple.item1 && selectedSuccessCount > 0;

        return Container(
          margin: const EdgeInsets.only(top: 8, bottom: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: canClick ? () => onConfirm() : null,
                  child: Container(
                    alignment: Alignment.center,
                    height: 36,
                    decoration: BoxDecoration(
                      color: canClick
                          ? const Color(0xFF222222)
                          : const Color(0xFFCCCCCC),
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Text(
                      selectedSuccessCount > 0
                          ? '应用图片($selectedSuccessCount)'
                          : '应用图片',
                      style: TextStyle(
                        fontSize: 14,
                        color:
                            canClick ? Colors.white : const Color(0xFF999999),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void onConfirm() {
    List spuInfos = [];
    for (SinglePoiSelectProductVo productVo in pageVo.selectedGoods.values) {
      // 只处理选中且成功生成的图片
      if ((productVo.select ?? false) &&
          !StringUtil.isEmpty(productVo.refImgUrl) &&
          !errorMessages.containsKey(productVo.spuName)) {
        PicVo picVo = PicVo();
        picVo.spuId = productVo.spuId;
        picVo.picUrl = productVo.refImgUrl;
        picVo.picLargeUrl = productVo.refImgUrl;
        picVo.backgroundPicId = 1;
        spuInfos.add(picVo.toJson());
      }
    }

    if (spuInfos.isEmpty) {
      MTFToast.showToast(msg: '请选择要应用的图片');
      return;
    }

    postEApi(
            path: '/gw/bizproduct/v3/w/batchUpdatePicInfo',
            params: {
              'spuInfoList': jsonEncode(spuInfos),
            },
            isControlShowToast: true)
        .then((result) {
      if (result.code == 0) {
        MTFToast.showToast(msg: '图片更换成功');
      }
    });
  }
}

class GenerateBackgroundPageVo extends ChangeNotifier {
  bool isDisposed = false; // 是否已经使用商品图

  String selectedBackgroundPic = ''; // 选中的背景图，默认选中第一个

  int loadImageCount = 0; //已加载的商品图个数
  List<String> spuNames = [];
  Map<String, SinglePoiSelectProductVo> selectedGoods = {};

  void initParams(
      String backgroundPic, List<SinglePoiSelectProductVo> goodsList) {
    selectedBackgroundPic = backgroundPic;
    for (int i = 0; i < goodsList.length; i++) {
      SinglePoiSelectProductVo productVo = goodsList[i];
      String name = productVo.spuName;

      spuNames.add(name);
      selectedGoods[name] = productVo;
    }

    safeNotifyListeners();
  }

  /// 通知页面刷新
  void safeNotifyListeners() {
    if (isDisposed) return;
    notifyListeners();
  }

  @override
  void dispose() {
    isDisposed = true;
    super.dispose();
  }
}

class PicVo {
  int spuId;
  String picUrl;
  String picLargeUrl;
  int backgroundPicId;

  Map toJson() {
    return {
      'spuId': spuId,
      'picUrl': picUrl,
      'picLargeUrl': picLargeUrl,
      'backgroundPicId': backgroundPicId,
    };
  }
}
