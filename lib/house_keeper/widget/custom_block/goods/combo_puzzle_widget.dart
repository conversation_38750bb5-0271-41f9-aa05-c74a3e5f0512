import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:roo_flutter/tools/string_utils.dart';
import 'package:waimai_e_flutter_house_keeper/common/wme_flutter_util.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/goods/goods_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_goods_select/house_keeper_goods_single_poi_select_product_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/house_keeper_goods_select_widget.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

/// 拼图步骤
enum PuzzleStep {
  step1, // 选商品、去拼图
  step2, // 预览、去应用
}

/// 套餐拼图组件
class ComboPuzzleWidget extends BaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const ComboPuzzleWidget({
    Key key,
    String content,
    this.model,
  }) : super(key: key, content: content);

  @override
  _ComboPuzzleWidgetState createState() => _ComboPuzzleWidgetState();
}

class _ComboPuzzleWidgetState extends BaseBlockWidgetState<ComboPuzzleWidget> {
  /// 已选择商品
  SinglePoiSelectProductVo selectedGoods;

  /// 拼图结果图片URL
  String puzzleResultUrl;

  /// 当前步骤
  PuzzleStep currentStep = PuzzleStep.step1;

  /// 是否已保存
  bool isSaved = false;

  @override
  Widget buildContentView() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStepIndicator(),
          const SizedBox(height: 16),
          _buildPreviewArea(),
          const SizedBox(height: 16),
          _buildButtonRow(),
        ],
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Step 1 区块
        Expanded(
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: 2,
            children: [
              Text(
                'Step1',
                style: TextStyle(
                  fontSize: 12,
                  color: currentStep == PuzzleStep.step1
                      ? const Color(0xFF222222)
                      : const Color(0xFF999999),
                  fontWeight: currentStep == PuzzleStep.step1
                      ? FontWeight.w500
                      : FontWeight.w400,
                ),
              ),
              const Text(
                '：',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              Text(
                '选商品',
                style: TextStyle(
                  fontSize: 12,
                  color: currentStep == PuzzleStep.step1
                      ? const Color(0xFF222222)
                      : const Color(0xFF999999),
                  fontWeight: currentStep == PuzzleStep.step1
                      ? FontWeight.w500
                      : FontWeight.w400,
                ),
              ),
              const Text(
                '、',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              Text(
                '拼图',
                style: TextStyle(
                  fontSize: 12,
                  color: currentStep == PuzzleStep.step1
                      ? const Color(0xFF222222)
                      : const Color(0xFF999999),
                  fontWeight: currentStep == PuzzleStep.step1
                      ? FontWeight.w500
                      : FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
        // 分隔符
        Container(
          width: 16,
          height: 1,
          color: const Color(0xFFEEEEEE),
        ),
        // Step 2 区块
        Expanded(
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: 2,
            children: [
              Text(
                'Step2',
                style: TextStyle(
                  fontSize: 12,
                  color: currentStep == PuzzleStep.step2
                      ? const Color(0xFF222222)
                      : const Color(0xFF999999),
                  fontWeight: currentStep == PuzzleStep.step2
                      ? FontWeight.w500
                      : FontWeight.w400,
                ),
              ),
              const Text(
                '：',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              Text(
                '预览',
                style: TextStyle(
                  fontSize: 12,
                  color: currentStep == PuzzleStep.step2
                      ? const Color(0xFF222222)
                      : const Color(0xFF999999),
                  fontWeight: currentStep == PuzzleStep.step2
                      ? FontWeight.w500
                      : FontWeight.w400,
                ),
              ),
              const Text(
                '、',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              Text(
                '应用',
                style: TextStyle(
                  fontSize: 12,
                  color: currentStep == PuzzleStep.step2
                      ? const Color(0xFF222222)
                      : const Color(0xFF999999),
                  fontWeight: currentStep == PuzzleStep.step2
                      ? FontWeight.w500
                      : FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewArea() {
    return Container(
      width: double.infinity,
      height: 150,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              children: [
                Expanded(
                  child: _buildLeftPreviewArea(),
                ),
                const SizedBox(width: 48),
                Expanded(
                  child: _buildRightPreviewArea(),
                ),
              ],
            ),
          ),
          _buildCenterArrow(),
        ],
      ),
    );
  }

  // 左侧预览区域
  Widget _buildLeftPreviewArea() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8),
        border: _buildBorder(PuzzleStep.step1),
      ),
      child: Stack(
        children: [
          // 选择商品按钮
          Visibility(
            visible: selectedGoods == null,
            child: GestureDetector(
              onTap: _getSelectGoodsCallback(),
              child: const Center(
                child: Icon(
                  Icons.add,
                  size: 40,
                  color: Color(0xFF999999),
                ),
              ),
            ),
          ),
          // 已选商品展示
          Visibility(
            visible: selectedGoods != null,
            child: _buildSelectedGoodsPreview(),
          ),
        ],
      ),
    );
  }

  // 右侧预览区域
  Widget _buildRightPreviewArea() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8),
        border: _buildBorder(PuzzleStep.step2),
      ),
      child: Visibility(
        visible: puzzleResultUrl != null,
        replacement: const Center(
          child: Text(
            '拼图结果预览',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF999999),
            ),
          ),
        ),
        child: _buildPuzzleResultPreview(),
      ),
    );
  }

  // 中间箭头
  Widget _buildCenterArrow() {
    return Positioned(
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      child: Center(
        child: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _buildArrowIcon(),
        ),
      ),
    );
  }

  // 边框样式
  Border _buildBorder(PuzzleStep step) {
    final bool isCurrentStep = currentStep == step;
    return Border.all(
      color: isCurrentStep ? const Color(0xFF222222) : const Color(0xFFEEEEEE),
      width: isCurrentStep ? 2 : 1,
    );
  }

  // 选择商品回调
  VoidCallback _getSelectGoodsCallback() {
    return currentStep == PuzzleStep.step1 ? _selectGoods : null;
  }

  // 箭头图标
  Widget _buildArrowIcon() {
    return Icon(
      Icons.arrow_forward,
      color: _getArrowColor(),
      size: 20,
    );
  }

  // 箭头颜色
  Color _getArrowColor() {
    return currentStep == PuzzleStep.step2
        ? const Color(0xFF40A9FF)
        : const Color(0xFFCCCCCC);
  }

  // 已选商品预览
  Widget _buildSelectedGoodsPreview() {
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildGoodsImage(),
            ),
            _buildGoodsInfo(),
          ],
        ),
        Visibility(
          visible: currentStep == PuzzleStep.step1,
          child: _buildCloseButton(),
        ),
      ],
    );
  }

  // 商品图片
  Widget _buildGoodsImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.network(
        StringUtil.isNotEmpty(selectedGoods?.picture)
            ? selectedGoods?.picture
            : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/b6ce73ad02219043/img_default-20250324.png',
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
      ),
    );
  }

  // 商品信息
  Widget _buildGoodsInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            selectedGoods?.spuName ?? '',
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          _buildPriceAndSaleInfo(),
        ],
      ),
    );
  }

  // 价格和销售信息
  Widget _buildPriceAndSaleInfo() {
    return Row(
      children: [
        Text(
          '¥${selectedGoods?.minPrice ?? 0}',
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF666666),
          ),
        ),
        Visibility(
          visible: selectedGoods?.minPrice != selectedGoods?.maxPrice,
          child: Text(
            '~¥${selectedGoods?.maxPrice ?? 0}',
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF666666),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '月售${selectedGoods?.monthSale ?? 0}',
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF999999),
          ),
        ),
      ],
    );
  }

  // 关闭按钮
  Widget _buildCloseButton() {
    return Positioned(
      right: 4,
      top: 4,
      child: GestureDetector(
        onTap: () {
          setState(() {
            selectedGoods = null;
          });
        },
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: const BoxDecoration(
            color: Colors.black54,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.close,
            color: Colors.white,
            size: 12,
          ),
        ),
      ),
    );
  }

  // 拼图结果预览
  Widget _buildPuzzleResultPreview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              puzzleResultUrl ?? '',
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
        ),
        _buildGoodsInfo(),
      ],
    );
  }

  Widget _buildButtonRow() {
    final bool isStep1 = currentStep == PuzzleStep.step1;

    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: isStep1 && selectedGoods != null
                ? _navigateToPuzzleMaker
                : null,
            child: Container(
              height: 36,
              decoration: BoxDecoration(
                border: Border.all(
                  color: isStep1 && selectedGoods != null
                      ? const Color(0xFF222222)
                      : const Color(0xFFCCCCCC),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(18),
              ),
              alignment: Alignment.center,
              child: Text(
                '去拼图',
                style: TextStyle(
                  fontSize: 14,
                  color: isStep1 && selectedGoods != null
                      ? const Color(0xFF222222)
                      : const Color(0xFF999999),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: GestureDetector(
            onTap: !isStep1 && !isSaved ? _handleApply : null,
            child: Container(
              height: 36,
              decoration: BoxDecoration(
                color: !isStep1 && !isSaved
                    ? const Color(0xFF222222)
                    : const Color(0xFFCCCCCC),
                borderRadius: BorderRadius.circular(18),
              ),
              alignment: Alignment.center,
              child: Text(
                isSaved ? '已应用' : '去应用',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToPuzzleMaker() async {
    if (selectedGoods == null) {
      MTFToast.showToast(msg: '请先选择商品');
      return;
    }

    final result = await routeToBanmorePicPage(
      goodsId: selectedGoods.spuId,
    );

    if (result != null) {
      setState(() {
        puzzleResultUrl = result['url'];
        currentStep = PuzzleStep.step2;
      });
    }
  }

  /// 跳转到拼图页面
  static Future<Map> routeToBanmorePicPage({int goodsId = 0}) async {
    String poiId = await PoiMananger.getInstance().getPoiInfo().then((poiInfo) {
      return poiInfo?.poiId ?? '';
    });
    final url = WMESchemeUrls.flutterPageUrl(
      'food/banmore/composite',
      params: {'poiId': poiId, 'spuId': '$goodsId'},
      pageChannel: 'waimai_e_flutter_goods',
    );
    return RouteUtils.open(url).then((result) {
      if (result != null && result.data != null && result.data is Map) {
        return result.data;
      }
      return Future.value();
    });
  }

  void _selectGoods() {
    showModalBottomSheet(
      isDismissible: false,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        return Material(
          color: Colors.transparent,
          child: Container(
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            child: CommonGoodsSelectPage(
              maxSelectGoodsCount: 1,
              goodSelectedCallBack: (val) {
                if (val != null && val.isNotEmpty) {
                  setState(() {
                    selectedGoods = val[0];
                    puzzleResultUrl = null;
                  });
                }
              },
              selectedGoods: selectedGoods != null ? [selectedGoods] : [],
            ),
          ),
        );
      },
    );
  }

  void _handleApply() {
    if (puzzleResultUrl == null) {
      MTFToast.showToast(msg: '请先拼图');
      return;
    }

    HouseKeeperGoodsApi.fetchSpu(selectedGoods.spuId).then((spu) {
      if (spu != null) {
        HouseKeeperGoodsApi.saveGoodsPicture(spu, puzzleResultUrl)
            .then((value) {
          if (value.code == 0) {
            setState(() {
              isSaved = true;
            });
            MTFToast.showToast(msg: '套餐拼图设置成功');
          } else {
            MTFToast.showToast(msg: '请返回商品编辑页补充信息：${value.msg}');
          }
        });
      }
    });
  }
}
