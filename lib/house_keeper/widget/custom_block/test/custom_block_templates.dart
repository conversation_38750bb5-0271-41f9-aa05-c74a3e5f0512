/// 自定义区块模板配置
class BlockTemplates {
  static const String ruzhuBdQRCode = """
:::ruzhuBdQRCode
{
}
:::
""";

  static const String ruzhuAudit = """
:::ruzhuAudit
{
}
:::
""";

  static const String comboPuzzle2 = """现在需要您上传店铺门脸图，请点击下方按钮上传。
:::ruzhuPoiPic
{ "cardId": 1200, "subType": 1, "candidateId": 1212121, "snapshotID": 123, "status": 1, "data": { "name": "周记烧烤",

 "url": "" }}
 :::
""";

  static const String ruzhuQuaLicense = """
老板您好，为了继续开店流程，请您填写店铺资质信息。

现在需要您上传营业执照~
:::ruzhuQuaLicense
{
"cardId": 2200,"subType": 1, "candidateId": 1212121, "snapshotID": 123, "status": 0, "data": {

"name":"张三",

"authName":"李四",
 "brandName": "爸爸糖",
 "frontUrl": "https://s3plus.meituan.net/v1/mss_14c672f716b54ef88185e01064deaa20/dist/images/legal_person_card/card_front_eg_3x.png",
 "backUrl": "https://s3plus.meituan.net/v1/mss_14c672f716b54ef88185e01064deaa20/dist/images/legal_person_card/card_back_eg_3x.png",
 "time":"长期有效",

 "url": "" }
}
:::
""";

  static const String merchantIdCardUpload = """
恭喜您已经完成了店铺信息和资质信息上传！现在需要上传法人信息进行实名认证啦~
## 身份证----
请上传您的身份证照片，主要作用是进行实名认证，确保开店人或法人信息的真实性。
:::ruzhuIdCard
{
"cardId": 2200,"subType": 1, "candidateId": 1212121, "snapshotID": 123, "status": 0, "data": {

"name":"张三",

"authName":"李四",
 "brandName": "爸爸糖",
 "frontUrl": "https://s3plus.meituan.net/v1/mss_14c672f716b54ef88185e01064deaa20/dist/images/legal_person_card/card_front_eg_3x.png",
 "backUrl": "https://s3plus.meituan.net/v1/mss_14c672f716b54ef88185e01064deaa20/dist/images/legal_person_card/card_back_eg_3x.png",
 "time":"长期有效",

 "url": "http://" } 
}
:::
""";

  /// 新版成长计划问卷
  static const String newGrowthPlanList = """
:::wm_new_poi_waimaiGrowthPlan 
[
    {
        "featureSelectList": [
            {
                "featureTextValue": "1-4单",
                "featureValue": 1
            },
            {
                "featureTextValue": "4-13单(超越60%同行)",
                "featureValue": 2
            },
            {
                "featureTextValue": "13单以上(超越80%同行)",
                "featureValue": 3
            }
        ],
        "featureTitle": "01 开业30天内，店铺每天单量的预期是?",
        "featureType": 1
    },
    {
        "featureSelectList": [
            {
                "featureTextValue": "我愿意尝试",
                "featureValue": 3
            },
            {
                "featureTextValue": "暂不考虑",
                "featureValue": 1
            }
        ],
        "featureTitle": "02 开业30天内，是否会参照同行的活动设置，或使用付费推广?",
        "featureType": 2
    },
    {
        "featureSelectList": [
            {
                "featureTextValue": "我愿意尝试",
                "featureValue": 3
            },
            {
                "featureTextValue": "暂不考虑",
                "featureValue": 1
            }
        ],
        "featureTitle": "02 开业30天内，是否会参照同行的活动设置，或使用付费推广?",
        "featureType": 3
    }
]
:::""";

  /// 商品上下架内容模板
  static const String productSellStatusContent = """
:::changeSellUp
{
"operation":"up",
"spus":[{"id": "2207851398", "name": "烤羊肉201685", "price": 23.0, "stock": -1, "description": "这是描述模版", "sellStatus": 0, "defaultPicUrl": ""}]
}
:::
""";

  /// 商家行动建议模板
  static const String actionContent = """:::commonAction
{
"title": "开启点金推广",
"subtitle": "午高峰定向投放白领客群",
"action":{
"label": "去设置",
"url": "http://xxxxxxxx"
}
}
:::""";

  /// 财务管家
  static const String financeOrder = """:::wm_biz_finance_order
{
"content": "若符合您的需求，请点击“确认定制”完成定制。若您对生成的内容不满意，请重新告诉我您的需求。",
"acctId":"16010180",
"businessLine":"1",
"sceneType":"4",
"requestId":"f1ca2d4c-5fdb-4a81-be07-f18f29b44c18"
}
:::""";

  /// 竞品对比模板
  static const String compareContent = """:::competitorComparison
{
  "myShop": {
    "name": "小刺猬咖啡",
    "branch": "望京融新店"
  },
  "competitorShop": {
    "name": "Manner Coffee",
    "branch": "望京店"
  },
  "operation": {
    "validOrders": {
      "title": "有效订单",
      "myValue": "1,375",
      "competitorValue": "365",
      "comparison": "胜"
    },
    "actualPayment": {
      "title": "实付客单",
      "myValue": "365",
      "competitorValue": "365",
      "comparison": "平"
    }
  },
  "traffic": {
    "exposure": {
      "title": "曝光人数",
      "myValue": "3,375",
      "competitorValue": "3,375",
      "comparison": "负"
    },
    "storeConversion": {
      "title": "入店转化率",
      "myValue": "28%",
      "competitorValue": "28%",
      "comparison": "负"
    },
    "orderConversion": {
      "title": "下单转化率",
      "myValue": "26%",
      "competitorValue": "26%",
      "comparison": "平"
    }
  },
  "customer": {
    "rating": {
      "title": "评分",
      "myValue": "4.5",
      "competitorValue": "4.5",
      "comparison": "胜"
    },
    "repurchaseRate": {
      "title": "复购率",
      "myValue": "17%",
      "competitorValue": "17%",
      "comparison": "胜"
    },
    "regularCustomerRate": {
      "title": "老客占比",
      "myValue": "35%",
      "competitorValue": "35%",
      "comparison": "负"
    }
  },
  "transaction": {
    "storeScore": {
      "title": "店铺分",
      "myValue": "4.5",
      "competitorValue": "4.5",
      "comparison": "胜"
    },
    "activityCount": {
      "title": "活动数量",
      "myValue": "17%",
      "competitorValue": "17%",
      "comparison": "胜"
    },
    "activityStrength": {
      "title": "活动力度",
      "myValue": "35%",
      "competitorValue": "35%",
      "comparison": "负"
    }
  }
}
:::""";

  /// 冲单内容模板
  static const String rushOrderContent = """:::wm_biz_new_poi_order_task
{
    "orderTask": {
        "target": "25单",
        "targetDesc": "目标",
        "nowValue": "13单",
        "nowValueDesc": "当前完成"
    }
}
:::""";

  /// 商家概览内容模板
  static const String businessOverviewContent =
      """:::wm_biz_new_poi_summary_diagnosis
{
    "biz": [
        {
            "field": "settleAmount",
            "title": "营业收入",
            "type": "double",
            "base": 102.83,
            "baseDelta":+62.32,
            "desc": "比前日"
        },
        {
            "field": "orderCnt",
            "title": "有效订单",
            "type": "int",
            "base": 6,
            "baseDelta": -1,
            "desc": "比前日"
        },
        {
            "field": "avgPrice",
            "title": "实付单均价",
            "type": "double",
            "base": 33.1,
            "baseDelta": -6.87,
            "desc": "比前日"
        }
    ]
}
:::""";

  /// 成长计划问卷模板
  static const String growthPlanContent = """:::waimaiGrowthPlan
[
    {
        "id": 1,
        "type": 1,
        "title": "您的店铺类型是？",
        "value": [
            "纯外卖店",
            "堂食店铺"
        ],
        "selectedValue":[]
    },
    {
        "id": 2,
        "type": 1,
        "title": "您的外卖经验有多久？",
        "value": [
            "没有经验",
            "1-2年",
            "2年以上",
            "没有经验",
            "没有经验",
            "没有经验",
            "没有经验"
        ],
        "selectedValue":[]
    },
    {
        "id": 3,
        "type": 2,
        "title": "您预期每天有多少订单？",
        "value": [
            "15",
            "50",
            "100",
            "500"
        ],
        "selectedValue":[]
    },
    {
        "id": 4,
        "type": 1,
        "title": "您是否愿意尝试付费推广？",
        "value": [
            "可以尝试",
            "不太愿意"
        ],
        "selectedValue":[]
    }
]
:::""";

  static const String requisitionWeightContent = """:::wmAiRequisitionWeight
{
  "title": "店铺权重",
  "subTitle": "店铺权重店铺权重店铺权重店铺权重店铺权重店铺权重",
  "buttonName": "去申请"
}
:::""";

  static const String ruzhuTagCode = """
  老板，您可以点击下方按钮操作修改经营品类
  :::ruzhuTagCode
  { 
  "cardId": 1100, 
  "subType": 1, 
  "candidateId": 1212121, 
  "snapshotID": 123, 
  "status": 0, 
  "data": {
    "tagId": 22020100,
    "tagName":"川渝火锅",
    "viceTagId":-1,
    "viceTagName": "是的，哈哈哈哈"
    }
  }
  :::""";
}
