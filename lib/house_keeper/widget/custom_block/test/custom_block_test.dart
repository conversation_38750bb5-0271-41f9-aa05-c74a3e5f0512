import 'dart:async';
import 'package:waimai_e_flutter_house_keeper/house_keeper/service/house_keeper_sse_client_dio.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/test/stream_config.dart';
import 'custom_block_templates.dart';

/// 自定义标签测试开关
///
/// 用于决定是否使用测试内容的控制器
class CustomBlockDebugTestSwitch {
  /// 是否开启测试模式
  static bool _testMode = false;

  /// 单例实例
  static final CustomBlockDebugTestSwitch _instance =
      CustomBlockDebugTestSwitch._internal();

  /// 工厂构造函数
  factory CustomBlockDebugTestSwitch() {
    return _instance;
  }

  /// 私有构造函数
  CustomBlockDebugTestSwitch._internal();

  static bool get testMode => _testMode;

  static set testMode(bool value) {
    _testMode = value;
  }

  /// 获取测试内容
  static String getTestContent(String type) {
    if (!_testMode) return '';

    switch (type) {
      case 'wm_new_poi_waimaiGrowthPlan':
        return BlockTemplates.newGrowthPlanList;
      case 'changeSellUp':
        return BlockTemplates.productSellStatusContent;
      case 'businessAction':
        return BlockTemplates.actionContent;
      case 'competitorComparison':
        return BlockTemplates.compareContent;
      case 'rushOrder':
        return BlockTemplates.rushOrderContent;
      case 'businessOverview':
        return BlockTemplates.businessOverviewContent;
      case 'waimaiGrowthPlan':
        return BlockTemplates.growthPlanContent;
      case 'ruzhuIdCard':
        return BlockTemplates.merchantIdCardUpload;
      case 'ruzhuTagCode':
        return BlockTemplates.ruzhuTagCode;
      default:
        return '';
    }
  }

  /// 处理测试模式
  /// [streamController] - 流控制器
  /// [onComplete] - 完成时的回调
  /// [onProgress] - 进度回调
  void handleTestMode(
    StreamController<SSEDioModel> streamController, {
    Function onComplete,
    Function onProgress,
  }) {
    // 使用StreamConfig模拟流式数据
    StreamConfig.mockStream(
      BlockTemplates.ruzhuBdQRCode,
      streamController,
      onComplete: onComplete,
      onProgress: onProgress,
    );
  }
}
