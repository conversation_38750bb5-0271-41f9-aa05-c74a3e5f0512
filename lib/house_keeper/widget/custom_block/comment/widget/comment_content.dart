import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/comment/agent/comment_content.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/comment/agent/comment_head.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/comment/agent/preview.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/comment/comment_api.dart';

class CommentBlockContent extends StatelessWidget {
  CommentBlockContent({Key key, this.simple = false, @required this.data})
      : super(key: key);

  final CommentCardItem data;
  bool simple;
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          child: Container(
            padding: EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommentCardHeadWidget(data: data, isSimple: simple),
                CommentTextWidget(
                  cleanComment: data.comment,
                  isSimple: simple,
                ),
                simple
                    ? data.imgUrls.length > 0
                        ? Container(
                            child: Text('[图片]'),
                          )
                        : Container()
                    : Padding(
                        padding: EdgeInsets.only(top: 12),
                        child: PreviewImagesWidget(
                          imgUrls: data.imgUrls,
                          commentId: data.id,
                          maxCount: 3,
                          screenWidth: MediaQuery.of(context).size.width,
                        ),
                      ),
              ],
            ),
          ),
        ),
        getTopPosition()
      ],
    );
  }

  Widget getTopPosition() {
    if (simple) {
      return Positioned(child: Container());
    }
    if (data.hasSelected && data.hasSelected == true) {
      return Positioned(
          right: 12,
          top: 12,
          child: Container(
            height: 20,
            padding: EdgeInsets.only(left: 6, right: 6),
            alignment: Alignment.center,
            decoration: BoxDecoration(
                color: Color(0xffffcc33),
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomLeft: Radius.circular(12))),
            child: Text(
              '已选择',
              style: TextStyle(
                  fontSize: 12,
                  height: 1.1,
                  color: Color(0xffffffff),
                  fontWeight: FontWeight.w400),
            ),
          ));
    }
    if (data.complained != null && data.complained == true) {
      return Positioned(
          right: 12,
          top: 12,
          child: Container(
            height: 20,
            padding: EdgeInsets.only(
              left: 6,
              right: 6,
            ),
            alignment: Alignment.center,
            decoration: BoxDecoration(
                color: Color(0xff999999),
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomLeft: Radius.circular(12))),
            child: Text(
              '已投诉',
              style: TextStyle(
                  fontSize: 12,
                  height: 1.1,
                  color: Color(0xffffffff),
                  fontWeight: FontWeight.w400),
            ),
          ));
    }

    return Positioned(child: Container());
  }
}
