import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/comment/comment_api.dart';
import 'package:mtf_toast/mtf_toast.dart';

/// 评价模块组件
class CommentCardReplyBlockWidget extends BaseBlockWidget {
  final HouseKeeperMessagePageModel model; // 添加 model 属性

  const CommentCardReplyBlockWidget(
      {Key key, @required String content, this.model})
      : super(key: key, content: content);

  @override
  _CommentCardReplyBlockWidgetState createState() =>
      _CommentCardReplyBlockWidgetState();
}

class _CommentCardReplyBlockWidgetState
    extends BaseBlockWidgetState<CommentCardReplyBlockWidget> {
  String refreshURL =
      "https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/8f39a4f179069936/<EMAIL>";
  bool hasClick = false;
  String commentContent = "";
  String id = "";
  String ctime = "";
  CommentCardItem currentItem;

  @override
  void initState() {
    super.initState();
    try {
      // 预处理 content 字符串，处理可能的控制字符
      String processedContent = widget.content
          .replaceAll(RegExp(r'[\n\r]'), ' ') // 替换换行和回车为空格
          .replaceAll(RegExp(r'\s+'), ' ') // 将多个空白字符替换为单个空格
          .trim(); // 移除首尾空白

      Map<String, dynamic> result = jsonDecode(processedContent);
      commentContent = result != null ? result['content']?.toString() : null;
      id = result['id'];
      ctime = result['ctime'];
    } catch (e) {
      // 设置默认值或错误状态
      commentContent = '解析评价内容出错';
      id = '';
      ctime = '';
    }
  }

  void refreshComment() async {
    if (mounted) {
      setState(() {});
    }
  }

  void sendComment() async {
    bool result = await CommentApi.replyComment(id, commentContent, ctime);
    if (result) {
      setState(() {
        hasClick = true;
      });
      MTFToast.showToast(msg: '回复成功');
    }
  }

  Widget _buildReplyButton() {
    if (hasClick) {
      return Container(
        alignment: Alignment.center,
        margin: const EdgeInsets.only(
          bottom: 8,
          left: 10,
          right: 10,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 10,
          vertical: 8,
        ),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(17)),
          color: Color(0xFFDDDDDD),
        ),
        child: const Text(
          '已回复',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
            color: Color(0xFF999999),
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: sendComment,
      child: Container(
        alignment: Alignment.center,
        margin: const EdgeInsets.only(
          bottom: 8,
          left: 10,
          right: 10,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 10,
          vertical: 8,
        ),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(17)),
          color: Color(0xff222222),
        ),
        child: const Text(
          '确认回复',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
            color: Color(0xffffffff),
          ),
        ),
      ),
    );
  }

  @override
  Widget buildContentView() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: const Text(
                    '小袋已针对该评价，为您生成一条回复：',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff222222),
                    ),
                  ),
                ),
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: const Color(0xfff8f8f8),
                    borderRadius: const BorderRadius.all(Radius.circular(13)),
                    border: Border.all(
                      color: const Color(0xffdfdfdf),
                      width: 0.5,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(left: 10, top: 10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '商家回复：',
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xff999999),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Container(
                              margin: const EdgeInsets.only(
                                top: 5,
                                right: 12,
                                bottom: 12,
                              ),
                              child: Text(
                                commentContent,
                                style: const TextStyle(
                                  color: Color(0xff222222),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  child: Column(
                    children: [
                      _buildReplyButton(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
