import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';

/// 评价模块组件
class CommentCardBlockWidget extends BaseBlockWidget {
  const CommentCardBlockWidget({
    Key key,
    @required String content,
  }) : super(key: key, content: content);

  @override
  _CommentCardBlockWidgetState createState() => _CommentCardBlockWidgetState();
}

class _CommentCardBlockWidgetState
    extends BaseBlockWidgetState<CommentCardBlockWidget> {
  Bool hasChoseComment;
  String _selectedTone = '真诚'; // 默认选中"真诚"语气

  void choseCommentCart() {}

  void _handleToneSelect(String tone) {
    setState(() {
      _selectedTone = tone;
    });
  }

  @override
  Widget buildContentView() {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 100),
      decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(10))),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 16, left: 16),
            child: const Text(
              '请选择您想要回复的评价：',
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color(0xff222222)),
            ),
          ),
          hasChoseComment == true
              ? Container(
                  margin: const EdgeInsets.only(bottom: 15),
                )
              : GestureDetector(
                  onTap: () {
                    choseCommentCart();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 36,
                    margin: const EdgeInsets.only(
                        top: 8, left: 16, bottom: 16, right: 16),
                    decoration: BoxDecoration(
                        color: const Color(0xff222222),
                        borderRadius: BorderRadius.circular(18)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          alignment: Alignment.center,
                          margin: const EdgeInsets.only(left: 70, right: 70),
                          child: const Text(
                            '选择评价',
                            style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                                color: Color(0xffffffff)),
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildToneSelectionSection(),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                )
        ],
      ),
    );
  }

  Widget _buildToneOption(String text, bool isSelected) {
    return GestureDetector(
      onTap: () => _handleToneSelect(text),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF333333) : Colors.transparent,
          border: Border.all(
            color:
                isSelected ? const Color(0xFF333333) : const Color(0xFFFFFFFF),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 13,
            color: const Color(0xFFFFFFFF),
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Row _buildToneSelection() {
    return Row(
      children: <Widget>[
        _buildToneOption('真诚', _selectedTone == '真诚'),
        const SizedBox(width: 12),
        _buildToneOption('专业', _selectedTone == '专业'),
        const SizedBox(width: 12),
        _buildToneOption('幽默', _selectedTone == '幽默'),
      ],
    );
  }

  Container _buildToneSelectionSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const Text(
            '回复语气',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFFFFFFFF),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          _buildToneSelection(),
        ],
      ),
    );
  }
}
