import 'package:flutter/widgets.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/comment/comment_api.dart';

class CommentListProvider extends ChangeNotifier {
  OrderTimeModel timeModel;
  List<CommentCardItem> dataList = [];
  int pageSize = 5;
  int pageNum = 1;
  bool loading = false;
  int total = 0;

  void requestDataList() {
    loading = true;
    pageNum = 1;
    CommentApi.fetchComment(searchPath, pageNum: pageNum, pageSize: pageSize)
        .then((value) {
      total = value.total;
      loading = false;
      if (value == null) {
        return;
      }
      dataList = value.commentList ?? [];
      notifyListeners();
    }).catchError((err) {
      loading = false;
      notifyListeners();
    });
  }

  void loadMore() {
    pageNum = pageNum + 1;
    CommentApi.fetchComment(searchPath, pageNum: pageNum, pageSize: pageSize)
        .then((value) {
      loading = false;
      if (value == null) {
        return;
      }
      dataList.addAll(value.commentList);
      notifyListeners();
    }).catchError((err) {
      loading = false;
      notifyListeners();
    });
  }

  bool hasMore() {
    return dataList.length < total;
  }

  String searchPath;
  num selectedID;
}
