// ignore_for_file: must_be_immutable

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/comment/agent/comment_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/comment/agent/comment_provider.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/comment/comment_api.dart';

Future<dynamic> showSelectedComment(BuildContext context, {num selectedID}) {
  Completer<dynamic> completer = Completer();
  showModalBottomSheet(
      isDismissible: false,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        return Material(
          color: Colors.transparent,
          child: Container(
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10))),
            child: SelectedCommentWidget(
                commentSelectedCallBack: (val) {
                  completer.complete(val);
                  Navigator.of(context).pop();
                },
                selectedID: selectedID),
          ),
        );
      }).then((value) {
    if (completer != null && !completer.isCompleted) {
      completer.complete(null);
    }
  });
  return completer.future;
}

class SelectedCommentWidget extends StatefulWidget {
  Function commentSelectedCallBack;
  num selectedID;
  SelectedCommentWidget(
      {Key key, this.commentSelectedCallBack, this.selectedID})
      : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return SelectedCommentWidgetState();
  }
}

class SelectedCommentWidgetState extends State<SelectedCommentWidget>
    with TickerProviderStateMixin {
  List<String> tabTitileList = [];
  final List<String> _tabs = ["外卖", "拼好饭"];
  TabController _tabController;
  TabController _timeWMTabController;

  bool isGray = false;
  bool isShowTips = true;
  int currentSelected = 0;
  int dayLength = 30;
  String closeIcon =
      "https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/854740fcd4ebbc60/close.png";

  @override
  void initState() {
    _tabController = TabController(
      initialIndex: 0,
      length: 2,
      vsync: this,
    );
    _timeWMTabController = TabController(length: dayLength, vsync: this);
    isGray = false;
    OncePerWeek.shouldShow().then((value) {
      isShowTips = value;
      setState(() {});
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: buildTabs(),
    );
  }

  List<OrderTimeModel> getTotalOrderTimeModel() {
    DateTime dateTime = DateTime.now();
    dateTime = dateTime.subtract(const Duration(days: 1));
    List<OrderTimeModel> result = [];
    for (int i = 0; i < dayLength; i++) {
      OrderTimeModel model = OrderTimeModel();
      model.des = '${dateTime.month}.${dateTime.day}';
      var formatter = DateFormat('yyyyMMdd');
      model.data = formatter.format(dateTime);

      DateTime zero = DateTime(dateTime.year, dateTime.month, dateTime.day);
      int zeroTimestamp = zero.millisecondsSinceEpoch;
      model.startTime = (zeroTimestamp ~/ 1000).toInt().toString();
      model.endTime = (zeroTimestamp ~/ 1000).toInt().toString();
      dateTime = dateTime.subtract(Duration(days: 1));
      result.add(model);
    }
    return result;
  }

  Widget buildTabs() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10), topRight: Radius.circular(10))),
          child: Stack(
            children: [
              Positioned(
                child: GestureDetector(
                    onTap: () {
                      if (widget.commentSelectedCallBack != null) {
                        widget.commentSelectedCallBack(null);
                      }
                    },
                    child: Text('取消')),
                top: 15,
                left: 16,
              ),
              isGray
                  ? Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      alignment: Alignment.center,
                      child: Container(
                        width: 160,
                        height: 32,
                        padding: const EdgeInsets.all(2.0),
                        decoration: const BoxDecoration(
                            borderRadius: BorderRadius.all(
                              Radius.circular(7.5),
                            ),
                            color: Color(0xfff5f6fa)),
                        child: Material(
                          color: Color(0xfff5f6fa),
                          child: TabBar(
                            tabs: _tabs
                                .map((e) => Tab(
                                      text: e,
                                    ))
                                .toList(),
                            onTap: (index) {
                              currentSelected = index;
                              setState(() {
                                _timeWMTabController.animateTo(0);
                              });
                            },
                            controller: _tabController,
                            labelColor: Color(0xFF222222),
                            labelStyle: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            indicator: BoxDecoration(
                              color: Color(0xffffffff),
                              shape: BoxShape.rectangle,
                              borderRadius: BorderRadius.circular((7.5)),
                            ),
                          ),
                        ),
                      ),
                    )
                  : Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      alignment: Alignment.center,
                      child: const Text(
                        '请选择您要回复的评价',
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: Color(0xff222222)),
                      ),
                    )
            ],
          ),
        ),
        Container(
          height: 32,
          color: Color(0xFFFFF8E1),
          alignment: Alignment.centerLeft,
          child: Container(
            margin: EdgeInsets.only(left: 12),
            child: Row(
              children: [
                Text('列表仅呈现目前未回复的评价',
                    style:
                        TextStyle(fontSize: 12, fontWeight: FontWeight.w400)),
              ],
            ),
          ),
        ),
        Expanded(
            child: Container(
          key: UniqueKey(),
          color: Colors.white,
          child: Container(
              padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
              child: Container(
                  color: Colors.white,
                  child: CommentContainerView(
                      searchPath: getSearchPath(),
                      timeModel: null,
                      selectedID: widget.selectedID,
                      onTap: (model) {
                        if (widget.commentSelectedCallBack != null) {
                          widget.commentSelectedCallBack(model);
                        }
                      }))),
        )),
      ],
    );
  }

  String getSearchPath() {
    if (currentSelected == 0) {
      return "/gw/customer/comment/list";
    } else {
      return "/gw/assistant/comment/phf/list";
    }
  }
}

// ignore: must_be_immutable
class CommentContainerView extends StatefulWidget {
  CommentContainerView(
      {Key key, this.timeModel, this.onTap, this.searchPath, this.selectedID})
      : super(key: key);
  OrderTimeModel timeModel;
  Function onTap;
  String searchPath;
  num selectedID;
  @override
  State<StatefulWidget> createState() {
    return CommentConainerViewState();
  }
}

class CommentConainerViewState extends State<CommentContainerView> {
  CommentListProvider _provider = CommentListProvider();
  TextEditingController _controller = TextEditingController();
  @override
  void initState() {
    _provider.searchPath = widget.searchPath;
    _provider.selectedID = widget.selectedID;
    _provider.requestDataList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<CommentListProvider>.value(
        value: _provider,
        child: Consumer<CommentListProvider>(builder: (context, model, child) {
          return _provider.loading
              ? Container(
                  child: Column(
                    children: [
                      Expanded(
                        child: Container(
                          alignment: Alignment.center,
                          child: Column(
                            children: const [
                              Text(
                                '数据加载中....',
                                style: TextStyle(
                                    fontSize: 14, color: Color(0xff666666)),
                              ),
                              Spacer()
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                )
              : Container(
                  child: Column(
                    children: [
                      _provider.dataList.length <= 0
                          ? Expanded(
                              child: Container(
                                alignment: Alignment.center,
                                child: Container(
                                  child: Column(
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(top: 150),
                                        child: Image.network(
                                            'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/27ba5a61f6a6ef70/suggest_order_empty.png'),
                                        width: 125,
                                        height: 88,
                                      ),
                                      const Text(
                                        '无相关评价数据',
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: Color(0xff666666)),
                                      ),
                                      Spacer()
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : Expanded(
                              child: EasyRefresh(
                                onRefresh: () async {
                                  _provider.requestDataList();
                                },
                                onLoad: !_provider.hasMore()
                                    ? null
                                    : () async {
                                        _provider.loadMore();
                                      },
                                footer: MaterialFooter(),
                                header: MaterialHeader(),
                                child: ListView.builder(
                                    itemCount: _provider.dataList.length,
                                    itemBuilder: (context, index) {
                                      CommentCardItem model =
                                          _provider.dataList[index];
                                      return GestureDetector(
                                        onTap: () {
                                          widget.onTap(model);
                                        },
                                        child: index !=
                                                _provider.dataList.length - 1
                                            ? Container(
                                                color: Colors.white,
                                                child: CommentCardWidget(
                                                  data: model,
                                                ),
                                              )
                                            : Column(
                                                children: [
                                                  CommentCardWidget(
                                                    data: model,
                                                  ),
                                                  Container(
                                                    height: 50,
                                                    color: Colors.white,
                                                  )
                                                ],
                                              ),
                                      );
                                    }),
                              ),
                            )
                    ],
                  ),
                );
        }));
  }
}

class OncePerWeek {
  static const String _key = 'comment_header';
  static Future<bool> shouldShow() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int lastShown = prefs.getInt(_key) ?? 0;
    int now = DateTime.now().millisecondsSinceEpoch;
    if (now - lastShown >= 7 * 24 * 60 * 60 * 1000) {
      return true;
    } else if (now - lastShown >= 24 * 60 * 60 * 1000) {
      return true;
    } else {
      return false;
    }
  }

  static Future<bool> setDisTime() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int now = DateTime.now().millisecondsSinceEpoch;
    await prefs.setInt(_key, now);
    return true;
  }
}
