import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:keframe/frame_separate_widget.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/agent/tools/comment/comment_api.dart';

class StarsRating extends StatelessWidget {
  const StarsRating({
    Key key,
    @required this.value,
    this.max = 5.0,
    this.size = 14,
    this.delayBuild = true,
  }) : super(key: key);

  /// 得分
  final double max;

  /// 总分
  final double value;

  /// 星大小
  final double size;

  /// 是否延迟构建
  final bool delayBuild;

  List<Widget> getStars() {
    List<Widget> starsList = <Widget>[];
    for (int i = 1; i <= max; i++) {
      // 添加选中星
      String starUrl = '';
      if (i <= value) {
        starUrl = 'images/user_comment/star_full.png';
      } else if (i.toDouble() > value && i < value + 1) {
        starUrl = 'images/user_comment/star_half.png';
      } else {
        starUrl = 'images/user_comment/star_empty.png';
      }
      starsList.add(Container(
        padding: EdgeInsets.only(right: 2),
        child: Image(
          width: size,
          height: size,
          image: AssetImage(starUrl),
        ),
      ));
    }
    return starsList;
  }

  @override
  Widget build(BuildContext context) {
    if (delayBuild != true) {
      return Row(
        children: getStars(),
      );
    }
    return FrameSeparateWidget(
      child: Row(
        children: getStars(),
      ),
      placeHolder: SizedBox(
        width: 65,
        height: 11,
      ),
    );
  }
}

const _scoreStyle = TextStyle(
  color: Color(0xFF999999),
  fontSize: 11,
  inherit: true,
);

class CommentCardHeadWidget extends StatelessWidget {
  const CommentCardHeadWidget({
    Key key,
    @required this.data,
    @required this.isSimple,
    this.renderAbnormal,
    this.black = false,
  }) : super(key: key);

  final CommentCardItem data;
  final Widget renderAbnormal;
  final bool isSimple;
  final bool black;
  String generateDeliveryTimeDesc(num time) {
    if (time == null || time <= 0) {
      return '';
    }
    if (time < 10) {
      return '超时10分钟内送达';
    }
    return '超时10分钟以上送达';
  }

  @override
  Widget build(BuildContext context) {
    num _score = data.score;
    String _userPicUrl = data.userPicUrl;
    num _foodScore = data.foodScore;
    num _pkgScore = data.pkgScore;
    num _logisticScore = data.logisticScore;
    bool showDefaultImg = _userPicUrl != null && _userPicUrl != '';
    return Stack(
      children: <Widget>[
        Positioned(
          top: 0,
          child: Container(
            child: Column(
              children: <Widget>[
                Container(
                  width: 36,
                  height: 36,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadiusDirectional.circular(18),
                    ),
                    image: DecorationImage(
                      image: showDefaultImg
                          ? AdvancedNetworkImage(
                              _userPicUrl ?? '',
                              useDiskCache: true,
                              width: 96,
                              height: 96,
                            )
                          : AssetImage('images/user_comment/empty_user.jpg'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Row(
          children: <Widget>[
            Expanded(
              flex: 1,
              child: Container(
                margin: EdgeInsets.only(left: 44),
                child: Column(
                  children: <Widget>[
                    Container(
                      alignment: Alignment.centerLeft,
                      margin: EdgeInsets.only(bottom: 3.5),
                      child: Text(
                        data?.userName ?? '',
                        style: TextStyle(
                          color: black == true
                              ? Color(0xffffffff)
                              : renderAbnormal != null
                                  ? Color(0xFF999999)
                                  : ColorUtil.scBlackColor222222,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Row(
                      children: <Widget>[
                        StarsRating(
                          delayBuild: false,
                          value: _score,
                          size: 11,
                        ),
                        isSimple
                            ? Container()
                            : Expanded(
                                flex: 1,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: <Widget>[
                                    Container(
                                      margin: EdgeInsets.only(right: 8),
                                      child:
                                          Text('总分$_score', style: _scoreStyle),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(right: 8),
                                      child: Text('口味$_foodScore',
                                          style: _scoreStyle),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(right: 8),
                                      child: Text('包装$_pkgScore',
                                          style: _scoreStyle),
                                    ),
                                    _logisticScore != 0 &&
                                            data.deliveryCommentShow == true
                                        ? Text(
                                            '配送${_logisticScore == 5 ? '满意' : '不满意'}',
                                            style: _scoreStyle,
                                          )
                                        : SizedBox.shrink(),
                                  ],
                                ),
                              )
                      ],
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
