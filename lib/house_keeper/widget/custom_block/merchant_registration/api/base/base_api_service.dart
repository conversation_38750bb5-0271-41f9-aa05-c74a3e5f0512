import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_network/mt_network.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_screen_utils.dart';
import 'package:wef_network/wef_request.dart';
import 'package:mtf_toast/mtf_toast.dart';

// 导入新的工具类
import '../../utils/api_utils.dart';

const wefChanel = 'waimai_e';

/// 图片上传与处理API服务
///
/// 提供商户注册过程中所需的图片上传、处理等API调用
class ImageUploadApi {
  // 更新类名为更具体的形式
  /// 单例实例
  static final ImageUploadApi _instance = ImageUploadApi._internal();

  /// 工厂构造函数
  factory ImageUploadApi() => _instance;

  /// 内部构造函数
  ImageUploadApi._internal();

  /// 图片上传CDN接口路径
  static const String _uploadCdnPath = '/api/kangaroo/file/upload/cdn';

  /// 图片上传MTCloud接口路径
  static const String _uploadMtcloudPath = '/api/kangaroo/file/upload/mtcloud';

  /// MTCloud重定向接口路径
  static const String _redirectMtcloudPath = '/api/redirect/mtcloud';

  /// 上传图片到CDN
  ///
  /// [filePath] - 本地文件路径
  ///
  /// 返回CDN图片URL，失败返回null
  Future<String> uploadImageToCdn({
    @required String filePath, // 修改参数名，更准确地表示单个文件路径
  }) async {
    // 使用工具类获取API Host
    final apiHost = await ApiUtils.getApiHost();

    final compressedFilePath = await compressImage(
        filePath: filePath,
        screenWidth: RuzhuScreenUtils.getScreenWidth(),
        screenHeight: RuzhuScreenUtils.getScreenHeight());

    // 使用uploadImagesApi上传图片
    final response = await uploadImagesApi(
      path: _uploadCdnPath,
      filePaths: [compressedFilePath], // 使用修改后的参数名
      host: apiHost,
    );

    // 解析响应
    if (response != null && response?.code == 0 && response?.data != null) {
      // 直接返回data字段
      final url = response?.data;
      MTFToast.showToast(msg: '图片上传成功');
      return url;
    }
    String errorMsg = response?.msg ?? '未知错误';
    MTFToast.showToast(msg: '上传图片到CDN失败: $errorMsg');
    return null;
  }

  /// 上传图片到MTCloud
  ///
  /// [filePath] - 本地文件路径
  ///
  /// 返回MTCloud图片URL，失败返回null
  Future<String> uploadImageToMtcloud({
    @required String filePath, // 修改参数名，更准确地表示单个文件路径
    @required String token,
    Map<String, dynamic> params,
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      String tmpToken = token;
      if (tmpToken?.isEmpty ?? true) {
        final commonParams =
            CommonParamsUtils.getInstance().getCommonParams() ?? {};
        tmpToken = commonParams['token'];
      }
      if (params != null) {
        params['token'] = tmpToken;
      }

      final compressedFilePath = await compressImage(
          filePath: filePath,
          screenWidth: RuzhuScreenUtils.getScreenWidth(),
          screenHeight: RuzhuScreenUtils.getScreenHeight());

      // 使用uploadImagesApi上传图片
      final response = await uploadImagesApi(
          path: _uploadMtcloudPath,
          filePaths: [compressedFilePath], // 使用修改后的参数名
          host: apiHost,
          params: params);

      // 解析响应
      if (response != null && response?.code == 0 && response?.data != null) {
        // 直接返回data字段
        final url = response?.data;
        MTFToast.showToast(msg: '图片上传成功');
        return url;
      } else {
        String errorMsg = response?.msg ?? '未知错误';
        MTFToast.showToast(msg: '上传图片到MTCloud失败: $errorMsg');
      }

      return null;
    } catch (error) {
      MTFToast.showToast(msg: '图片上传失败，请重试: $error');
      return null;
    }
  }

  Future<String> compressImage(
      {@required String filePath, int screenWidth, int screenHeight}) async {
    String resultFilePath = filePath;

    final imageInfo = await KNB.getImageInfo(
        sceneToken: 'dj-cd246c6d99ef0f5a', image: resultFilePath);

    if (imageInfo == null ||
        imageInfo['width'] == null ||
        imageInfo['width'] == 0 ||
        imageInfo['height'] == null ||
        imageInfo['height'] == 0 ||
        screenWidth == null ||
        screenWidth <= 0 ||
        screenHeight == null ||
        screenHeight <= 0) {
      return resultFilePath;
    }
    int width = imageInfo['width'];
    int height = imageInfo['height'];

    // 计算缩放比例
    int scale = 100;
    // 设置最大宽高限制为2000x2000
    const int maxWidth = 2000;
    const int maxHeight = 2000;

    // 如果图片尺寸超过屏幕尺寸或最大限制，则进行缩放
    if (width > maxWidth || height > maxHeight) {
      double widthRatio = maxWidth / width;
      double heightRatio = maxHeight / height;
      double ratio = widthRatio < heightRatio ? widthRatio : heightRatio;
      scale = (ratio * 100).toInt();
    }

    final compressedResult = await KNB.compressImage(
      sceneToken: 'dj-cd246c6d99ef0f5a',
      image: resultFilePath,
      scale: scale,
      quality: 80,
    );

    KNB.sendLog(
        text: 'cloud comperss error=' +
            (compressedResult != null ? jsonEncode(compressedResult) : 'null'));
    if (compressedResult != null &&
        compressedResult['status'] == 'success' &&
        compressedResult['outputPath'] != null) {
      String correctedPath = compressedResult['outputPath'] ?? '';
      if (correctedPath.isNotEmpty) {
        resultFilePath = correctedPath;
      }
      // 适配代码
      if (!correctedPath.startsWith('knb-media')) {
        correctedPath =
            'knb-media://client?url=${Uri.encodeComponent(correctedPath)}';
        resultFilePath = correctedPath;
      }
    }
    return resultFilePath;
  }

  /// 获取MTCloud图片URL
  ///
  /// [imageUrl] - 原始图片URL
  /// [taskId] - 任务ID（必填）
  /// [width] - 宽度（选填）
  /// [percentage] - 百分比（选填）
  /// [packageId] - 包ID（选填，默认为0）
  Future<dynamic> getMtcloudImageUrl({
    // 更新方法名，更准确地表示功能
    @required String imageUrl, // 修改参数名，更准确地表示这是一个URL
    @required String token, // 修改参数名，更准确地表示这是一个URL
    @required int taskId,
    @required int snapshotId,
    @required int messageId,
    String width,
    String percentage,
    String packageId = '0',
  }) async {
    try {
      // 提取图片ID
      final imageId = _extractImageId(imageUrl);

      // 获取API主机地址
      final apiHost = await ApiUtils.getApiHost();

      // 构建请求参数
      final params = <String, dynamic>{
        'id': imageId,
        'token': token,
        'taskId': taskId,
        'packageId': packageId,
        'snapshotId': snapshotId,
        'messageId': messageId,
      };

      // 添加可选参数
      if (width != null) params['width'] = width;
      if (percentage != null) params['percentage'] = percentage;

      // 发起请求
      final response = await getApi(
        path: _redirectMtcloudPath,
        baseUrl: apiHost,
        params: params,
        isControlShowToast: false,
      );

      return response;
    } catch (error) {
      MTFToast.showToast(msg: '获取图片URL失败：$error');
      return null;
    }
  }

  /// 从URL中提取图片ID
  /// 例如：从 '/download/mos/xxx.jpeg' 提取 'xxx.jpeg'
  String _extractImageId(String url) {
    if (url == null || url.isEmpty) return '';

    final mosIndex = url.indexOf('mos/');
    if (mosIndex == -1) return url; // 如果没有 'mos/' 返回原始URL

    return url.substring(mosIndex + 4);
  }
}

class RuzhuResponseData {
  int code;
  String msg;
  dynamic data;

  RuzhuResponseData(this.code, this.msg, this.data);

  static RuzhuResponseData fromJson(Map<String, dynamic> response) {
    return RuzhuResponseData(response['code'],
        response['msg'] ?? response['message'], response['data']);
  }
}

class RuzhuResponse {
  RuzhuResponseData data;
  int status;
  dynamic headers; //暂时不用

  RuzhuResponse(this.data, this.status, this.headers);

  static RuzhuResponse fromJson(Map<String, dynamic> response) {
    return RuzhuResponse(RuzhuResponseData.fromJson(response['data']),
        response['status'], response['headers']);
  }
}

/*
   * 入住专用-contentType: application/json
   */
Future<RuzhuResponseData> ruzhuPostApi({
  String baseUrl,
  String path,
  Map<String, dynamic> params,
  String token,
  String type,
}) async {
  params ??= {};
  final config = {
    'url': path,
    'method': 'POST',
    'channel': wefChanel,
    'baseURL': baseUrl,
    'data': params,
    'contentType': 'application/json',
    'headers': {
      'token': token,
      'aiType': type,
    },
  };

  final responseStr = await MtNetwork.request(config);
  if (responseStr == null || responseStr.isEmpty) {
    MTFToast.showToast(msg: '网络请求异常，请稍后重试');
    return null;
  }
  final jsonObj = json.decode(responseStr);

  final RuzhuResponse response = RuzhuResponse.fromJson(Map.from(jsonObj));
  if (response.status == 200) {
    final RuzhuResponseData responseData = response.data;
    return responseData;
  } else {
    KNB.sendLog(text: responseStr);
    MTFToast.showToast(msg: '网络请求异常，请稍后重试');
    return null;
  }
}
