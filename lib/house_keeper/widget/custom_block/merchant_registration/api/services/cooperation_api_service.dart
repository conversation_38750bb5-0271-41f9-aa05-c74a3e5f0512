import 'package:flutter/foundation.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/api/base/base_api_service.dart';
import '../../utils/api_utils.dart';
import 'package:dio/dio.dart';
import 'package:mtf_toast/mtf_toast.dart';

class CooperationApiService {
  static final CooperationApiService _instance =
      CooperationApiService._internal();
  factory CooperationApiService() => _instance;
  CooperationApiService._internal();

  final dio = Dio();

  /// 获取单店自入驻合作方案流程改造灰度
  ///
  /// [taskId] 任务ID
  Future<bool> getCooperationSelector({
    @required String candidateId,
    @required String token,
  }) async {
    const endpoint = '/gw/api/deliveryproduct/selector/gray/settlein/wm';

    final apiHost = await ApiUtils.getApiHost();
    final response = await ruzhuPostApi(
        baseUrl: apiHost,
        path: endpoint + '?token=$token',
        token: token,
        params: {
          'scene': 'wm_dandian_app',
          'bizId': candidateId,
        });

    if (response?.data == null) return false;

    final responseData = response.data;
    if (response.code == 0) {
      final gray = responseData['gray'] ?? {};

      return gray['grayHit'] == 1;
    }
    return false;
  }
}
