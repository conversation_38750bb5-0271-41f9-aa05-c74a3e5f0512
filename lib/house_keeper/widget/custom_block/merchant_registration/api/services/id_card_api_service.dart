import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:wef_network/wef_request.dart';
import 'package:flutter/foundation.dart';
import '../../models/response/bd_info_response.dart';
import '../../utils/api_utils.dart';

class IdCardApiService {
  static final IdCardApiService _instance = IdCardApiService._internal();
  factory IdCardApiService() => _instance;
  IdCardApiService._internal();

  /// 查询BD信息
  ///
  /// [taskId] 任务ID
  /// 返回 [BdInfoResponse] BD信息
  /// 抛出异常时返回 null
  Future<BdInfoResponse> queryBdInfo({@required int taskId}) async {
    const endpoint = '/api/kangaroo/kaidian/recommend/queryBdInfo';

    try {
      final apiHost = await ApiUtils.getApiHost();

      Map<String, dynamic> params = {
        'taskId': taskId,
      };

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      final response = await getApi(
        path: endpoint,
        params: params,
        baseUrl: apiHost,
      );

      // 修改为传统的空值检查
      if (response != null && response.data != null) {
        return BdInfoResponse.fromJson(response.data);
      }

      return null;
    } catch (e) {
      debugPrint('查询BD信息失败: $e');
      return null;
    }
  }

  /// 查询二维码ID类型灰度配置
  ///
  /// 返回是否在灰度中的布尔值
  /// 抛出异常时返回 false
  Future<bool> queryQrCodeIDTypeGray() {
    const endpoint = '/api/kangaroo/gray/qrCodeIDTypeGray';

    return ApiUtils.getApiHost().then((apiHost) {
      Map<String, dynamic> commonParams =
          CommonParamsUtils.getInstance().getCommonParams();

      return getApi(
        path: endpoint,
        params: {
          'token': commonParams['token'],
          'taskId': commonParams['candidateId']
        },
        baseUrl: apiHost,
      ).then((response) {
        if (response != null && response.data != null) {
          // 确保返回布尔值
          return response.data == true;
        }
        return false;
      }).catchError((e) {
        debugPrint('查询二维码ID类型灰度配置失败: $e');
        return false;
      });
    });
  }

  /// 获取法人场景信息
  ///
  /// 返回法人场景信息的code值(number类型)
  /// 抛出异常时返回 0
  Future<int> getLegalPersonScene() {
    const endpoint = '/api/v2/kaidian/restful/legalPerson/legalPersonScene';

    return ApiUtils.getApiHost().then((apiHost) {
      Map<String, dynamic> commonParams =
          CommonParamsUtils.getInstance().getCommonParams();

      return getApi(
        path: endpoint,
        params: {
          'token': commonParams['token'],
          'taskId': commonParams['candidateId']
        },
        baseUrl: apiHost,
      ).then((response) {
        if (response != null && response.data != null) {
          // 使用Map访问方式获取code字段
          return (response.data['code'] ?? -1) as int;
        }
        return -1;
      }).catchError((e) {
        debugPrint('获取法人场景信息失败: $e');
        return -1;
      });
    });
  }

  // ... 其他已有的方法 ...
}
