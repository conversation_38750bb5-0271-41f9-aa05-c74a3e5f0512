import 'package:wef_network/wef_request.dart';
import 'package:flutter/foundation.dart';
import '../../utils/api_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';

/// 许可证API服务
class LicenseApiService {
  static final LicenseApiService _instance = LicenseApiService._internal();
  factory LicenseApiService() => _instance;
  LicenseApiService._internal();

  /// 获取许可证说明列表
  Future<List<String>> getLicenseDescriptions() async {
    try {
      final apiHost = await ApiUtils.getApiHost();

      final params = CommonParamsUtils.getInstance().getCommonParams();

      final endpoint =
          '/api/v2/kaidian/restful/qualification/${params['candidateId']}/permitLicenseList';

      final response = await getApi(
        path: endpoint,
        params: params,
        baseUrl: apiHost,
      );

      if (response != null && response.data is List) {
        return List<String>.from(response.data);
      }

      return [];
    } catch (e) {
      debugPrint('获取许可证说明失败: $e');
      return [];
    }
  }
}
