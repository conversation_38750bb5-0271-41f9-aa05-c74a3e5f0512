import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import '../../utils/api_utils.dart';
import 'package:mtf_toast/mtf_toast.dart';
import '../base/base_api_service.dart';

class TaskSubmitApiService {
  /// 提交任务
  /// [taskId] 任务ID
  /// [token] 用户token
  /// [status] 任务状态
  Future<bool> submitTask(Map<String, dynamic> params) async {
    final commonParams =
        CommonParamsUtils.getInstance().getCommonParams() ?? {};
    final token = commonParams['token'];

    params['type'] = 'AI';

    final String _submitPath =
        '/api/v2/kaidian/restful/task/${params['taskId']}/_commit?token=$token&type=AI';

    final apiHost = await ApiUtils.getApiHost();
    final response = await ruzhuPostApi(
      baseUrl: apiHost,
      path: _submitPath,
      params: params,
      token: token,
      type: 'AI',
    );

    if (response == null) {
      return false;
    }

    // 更严谨的空值判断
    if (response.code != 0) {
      MTFToast.showToast(msg: response.msg ?? '提交失败，请重试');
      return false;
    }
    return true;
  }
}
