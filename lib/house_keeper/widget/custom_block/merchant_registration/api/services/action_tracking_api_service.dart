import 'package:flutter/foundation.dart';
import '../../../../../utils/common_params_utils.dart';
import '../../utils/api_utils.dart';
import '../base/base_api_service.dart';

class ActionTrackingApiService {
  static final ActionTrackingApiService _instance =
      ActionTrackingApiService._internal();
  factory ActionTrackingApiService() => _instance;
  ActionTrackingApiService._internal();

  /// 通用打点API - 记录用户行为到hive
  ///
  /// [candidateId] 入驻任务ID
  /// [keys] 唯一区分的key列表，用于标识不同的行为事件
  /// [token] 令牌
  Future<bool> recordAction({
    @required String candidateId,
    @required List<String> keys,
    String token = '',
  }) async {
    final apiHost = await ApiUtils.getApiHost();
    const path = '/api/ai/card/action/logPoint';
    final Map<String, dynamic> params = {
      'candidateId': candidateId ?? '',
      'keys': keys,
      'token': token
    };

    params.addAll(CommonParamsUtils.getInstance().getCommonParams());
    RuzhuResponseData response =
        await ruzhuPostApi(baseUrl: apiHost, path: path, params: params);
    debugPrint('打点结果: $response');
    if (response != null && response.code == 0) {
      return true;
    }
    return false;
  }
}
