import 'package:mtf_toast/mtf_toast.dart';
import 'package:wef_network/wef_request.dart';
import 'package:flutter/foundation.dart';
import '../../../../../utils/common_params_utils.dart';
import '../../utils/api_utils.dart';

class BDQRcodeApiService {
  static final BDQRcodeApiService _instance = BDQRcodeApiService._internal();
  factory BDQRcodeApiService() => _instance;
  BDQRcodeApiService._internal();

  /// 绑定业务经理
  Future<bool> postModifyBdInfo(
      {@required int taskId,
      @required String link,
      @required snapshotId,
      String token}) async {
    // 获取API Host
    final apiHost = await ApiUtils.getApiHost();

    final path =
        '/api/ai/card/fill/modifyBdInfo?token=$token&snapshotId=$snapshotId';

    // 构建请求参数
    final Map<String, dynamic> params = {
      'taskId': taskId, // 使用卡片ID作为任务ID
      'link': link,
      'token': token,
      'snapshotId': snapshotId,
    };

    params.addAll(CommonParamsUtils.getInstance().getCommonParams());

    // 发送请求
    final response =
        await postApi(path: path, baseUrl: apiHost, params: params);

    if (response == null) {
      return false;
    }

    // 处理响应
    if (response.code == 0) {
      // 获取result值，添加判空处理
      final resultData = response.data;
      if (resultData == null) {
        MTFToast.showToast(msg: '返回数据为空，请重试');
        return false;
      }

      final int result = resultData["result"] ?? -1;

      // 根据result值判断成功或失败
      if (result == 1 || result == 2) {
        // result为1或2时视为失败
        debugPrint('绑定业务经理失败: result=$result');
        String msgError = '绑定失败，请重试';
        if (resultData["content"] != null) {
          msgError = resultData["content"];
        }
        MTFToast.showToast(msg: msgError);
        return false;
      } else {
        final bindState = resultData["bindState"];
        if (result == 0 && bindState == 0) {
          MTFToast.showToast(msg: '绑定成功');
          return true;
        } else {
          String msgError = '绑定失败，请重试';
          if (resultData["bindFailContent"] != null) {
            msgError = resultData["bindFailContent"];
          }
          MTFToast.showToast(msg: msgError);
          return false;
        }
      }
    } else {
      // 请求失败
      debugPrint('绑定业务经理失败: ${response.msg ?? '未知错误'}');
      String msgError = '绑定失败，请重试';
      if (response.data != null) {
        final resData = response.data;
        if (resData["content"] != null) {
          msgError = resData["content"];
        }
      } else if (response.msg != null) {
        msgError = response.msg;
      }
      MTFToast.showToast(msg: msgError);
      return false;
    }
  }

  Future<bool> postSkipBindBD({@required int candidateId, String token}) async {
    try {
      // 获取API Host
      final apiHost = await ApiUtils.getApiHost();
      final path = '/api/ai/card/action/skipCard' + '?token=$token';

      // 构建请求参数
      final Map<String, dynamic> params = {
        'candidateId': candidateId,
        'cardId': '1100', // 根据接口文档，固定填1100
        'token': token,
      };

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      // 发送请求
      final response = await postApi(
        baseUrl: apiHost,
        path: path,
        params: params,
      );

      // 处理响应
      if (response != null && response.code == 0) {
        // 跳过成功
        debugPrint('跳过BD信息成功: ${response.data}');
        return true;
      } else {
        // 处理错误
        debugPrint('跳过BD信息失败: ${response?.msg ?? '未知错误'}');
        return false;
      }
    } catch (e) {
      debugPrint('跳过BD信息请求异常: $e');
      return false;
    }
  }

  /// 解析二维码信息
  Future<Map<String, dynamic>> parseQrCodeByUrl(
      {@required String qrCodeUrl,
      @required int candidateId,
      String token}) async {
    try {
      // 获取API Host
      final apiHost = await ApiUtils.getApiHost();
      const path = '/api/kangaroo/kaidian/recommend/download/parseQrCode';

      // 构建请求参数
      final Map<String, dynamic> params = {
        'qrCodeUrl': qrCodeUrl,
        'candidateId': candidateId,
        'token': token
      };

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      // 发送GET请求
      final response = await getApi(
        baseUrl: apiHost,
        path: path,
        params: params,
      );

      // 处理响应
      if (response != null && response.code == 0) {
        // 解析成功
        debugPrint('二维码解析成功: ${response.data}');
        if (response.data != null) {
          final responseData = response.data;
          final passStatus = responseData['passStatus'];
          final rejectReason = responseData['rejectReason'];
          if (passStatus) {
            final data = responseData['data'];
            // 检查必要字段是否存在
            if (data == null ||
                data['qrCodeInfo'] == null ||
                data['misId'] == null ||
                data['misName'] == null) {
              MTFToast.showToast(msg: '推荐人信息不完整');
              return null;
            } else {
              return data;
            }
          } else {
            String errorMsg = '';
            if (rejectReason == null) {
              errorMsg = '二维码解析失败，请重试！';
            } else if (rejectReason is List) {
              // 如果rejectReason是数组，将多条信息换行展示
              errorMsg = (rejectReason as List).join('\n');
            } else {
              // 如果rejectReason是字符串或其他类型，直接转换为字符串
              errorMsg = rejectReason.toString();
            }
            MTFToast.showToast(msg: errorMsg);
            return null;
          }
        }
        return null;
      } else {
        // 处理错误
        MTFToast.showToast(msg: response?.msg ?? '二维码解析失败，请重试！');
        debugPrint('二维码解析失败: ${response?.msg ?? '未知错误'}');
        return null;
      }
    } catch (e) {
      debugPrint('二维码解析请求异常: $e');
      return null;
    }
  }

  /// 解析二维码链接
  /// data:
  /// {
  /// "qrCodeInfo":"",  // 二维码解析出来的url
  /// "misId":"",
  /// "misName":""
  /// }
  Future<Map<String, dynamic>> parseQrCodeByLink(
      {@required String qrCodeUrl,
      @required int candidateId,
      String token}) async {
    try {
      // 获取API Host
      final apiHost = await ApiUtils.getApiHost();
      const path = '/api/kangaroo/kaidian/recommend/parseQrCode';

      // 构建请求参数
      final Map<String, dynamic> params = {
        'qrCodeUrl': qrCodeUrl,
        'candidateId': candidateId,
        'token': token
      };

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      // 发送GET请求
      final response = await getApi(
        baseUrl: apiHost,
        path: path,
        params: params,
      );

      // 处理响应
      if (response != null && response.code == 0) {
        // 请求成功
        debugPrint('二维码链接解析成功: ${response.data}');
        if (response.data != null) {
          final responseData = response.data;
          final passStatus = responseData['passStatus'];
          final rejectReason = responseData['rejectReason'];
          if (passStatus) {
            final data = responseData['data'];
            // 检查必要字段是否存在
            if (data == null ||
                data['qrCodeInfo'] == null ||
                data['misId'] == null ||
                data['misName'] == null) {
              MTFToast.showToast(msg: '推荐人信息不完整');
              return null;
            } else {
              return data;
            }
          } else {
            String errorMsg = '';
            if (rejectReason == null) {
              errorMsg = '二维码解析失败，请重试！';
            } else if (rejectReason is List) {
              // 如果rejectReason是数组，将多条信息换行展示
              errorMsg = (rejectReason as List).join('\n');
            } else {
              // 如果rejectReason是字符串或其他类型，直接转换为字符串
              errorMsg = rejectReason.toString();
            }
            MTFToast.showToast(msg: errorMsg);
            return null;
          }
        } else {
          MTFToast.showToast(msg: '二维码解析失败，请重试！');
          return null;
        }
      } else {
        // 处理错误
        debugPrint('二维码链接解析失败: ${response?.msg ?? '未知错误'}');
        MTFToast.showToast(msg: response?.msg ?? '未知错误');
        return null;
      }
    } catch (e) {
      debugPrint('二维码链接解析请求异常: $e');
      return null;
    }
  }
}
