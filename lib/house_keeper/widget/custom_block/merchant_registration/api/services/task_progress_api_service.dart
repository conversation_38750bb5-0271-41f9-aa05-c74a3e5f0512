import 'package:wef_network/wef_request.dart';
import 'package:flutter/foundation.dart';
import '../../utils/api_utils.dart';
import '../../models/response/task_progress_response.dart';

class TaskProgressApiService {
  static final TaskProgressApiService _instance =
      TaskProgressApiService._internal();
  factory TaskProgressApiService() => _instance;
  TaskProgressApiService._internal();
  // 封装请求方法
  Future<TaskProgressData> getAllCardStatus(
      {@required int candidateId, String token}) async {
    try {
      // 获取API Host
      final apiHost = await ApiUtils.getApiHost();
      const path = '/api/ai/card/action/queryAllCardStatus';
      final response = await getApi(
          baseUrl: apiHost,
          path: path,
          params: {"candidateId": candidateId, "token": token});

      if (response != null && response.code == 0 && response.data != null) {
        TaskProgressData taskProgressData =
            TaskProgressData.fromJson(response.data);
        return taskProgressData;
      } else {
        return null;
      }
    } catch (e) {
      // 请求失败时使用 mock 数据
      debugPrint('获取任务进度请求失败: $e');
      return null;
    }
  }
}
