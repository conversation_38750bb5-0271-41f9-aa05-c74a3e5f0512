import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/api/base/base_api_service.dart';
import 'package:wef_network/wef_request.dart';
import 'package:flutter/foundation.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import '../../utils/api_utils.dart';

class ContactPersonApiService {
  static final ContactPersonApiService _instance =
      ContactPersonApiService._internal();
  factory ContactPersonApiService() => _instance;
  ContactPersonApiService._internal();

  /// 获取验证请求码
  Future<String> getRequestCode(
      {String phoneNumber, String taskId, String token}) async {

 // 获取API Host
      final apiHost = await ApiUtils.getApiHost();
      const path = '/api/v2/kaidian/restful/shop/encryptContactVerify/code';
      Map envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
      String deviceUuid = '';
      if (envInfo != null && envInfo['uuid'] != null) {
        deviceUuid = envInfo['uuid'] as String;
      }

      String platform = 'h5';
      if (envInfo != null && envInfo['platform'] != null) {
        platform = envInfo['platform'] as String;
      }

      String app = 'unknown';
      if (envInfo != null && envInfo['app'] != null) {
        app = envInfo['app'] as String;
      }

      // 请求参数
      Map<String, dynamic> params = {
        'taskId': taskId ?? '',
        'uuid': deviceUuid,
        'platform': platform,
        'app': app,
        'mobile': phoneNumber ?? '',
        'token': token
      };
      params.addAll(CommonParamsUtils.getInstance().getCommonParams());
      final response = await ruzhuPostApi(
          baseUrl: apiHost,
          path: path + '?token=$token',
          params: params,
          token: token);

      if(response == null){
        return null;
      }

      if(response.code == 0 && response.data != null) {
        return response.data['requestCode'];
      }
      return null;
  }

  /// 上报验证结果
  Future<bool> updateVerificationResult({
    String taskId,
    String requestCode = '',
    String responseCode = '',
    String token = '',
    int errorCode = 0,
  }) async {
    try {
      final apiHost = await ApiUtils.getApiHost();
      const path = '/api/v2/kaidian/restful/shop/contactVerify/result';
      final response =
          await getApi(baseUrl: apiHost, path: path + '?token=$token', params: {
        'taskId': taskId ?? '',
        'requestCode': requestCode,
        'responseCode': responseCode,
        'errorCode': errorCode,
        'token': token,
      });
      debugPrint('上报验证结果成功: $response');
      return true;
    } catch (e) {
      debugPrint('上报验证结果失败: $e');
      return false;
    }
  }

  /// 获取Yoda验证域名
  Future<String> getYodaDomain() async {
    final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
    String hostType = '';
    if (envInfo != null && envInfo['hostType'] != null) {
      hostType = envInfo['hostType'] as String;
    }

    /// Yoda环境列表
    final List<String> kYodaEnvList = [
      'prod',
      'stage',
      'release',
      'dev',
      'ppe',
      'test'
    ];

    /// Yoda域名映射
    final Map<String, String> kYodaDomainMap = {
      'release': 'https://verify.meituan.com',
      'prod': 'https://verify.meituan.com',
      'stage': 'https://verify.inf.st.meituan.com',
      'dev': 'https://verify.inf.test.meituan.com',
      'test': 'https://verify.inf.test.meituan.com',
      'ppe': 'https://verify.inf.test.meituan.com',
    };
    String projectEnv = hostType.toLowerCase();
    String env = kYodaEnvList.contains(projectEnv) ? projectEnv : 'prod';
    String result = kYodaDomainMap['prod'];
    if (kYodaDomainMap[env] != null) {
      result = kYodaDomainMap[env];
    }
    return result;
  }

  /// 提交联系人信息
  Future<dynamic> submitContactInfo(
      {@required String contactName,
      @required String contactPhone,
      @required String candidateId,
      int subType = 0,
      String cardCode = '',
      String signUpPhone = '',
      String token = '',
      int snapshotId}) async {
    try {
      final apiHost = await ApiUtils.getApiHost();
      const path = '/api/ai/card/fill/contractInfo';
      // 获取设备信息
      Map envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
      String deviceUuid = 'unknown';
      if (envInfo != null && envInfo['uuid'] != null) {
        deviceUuid = envInfo['uuid'] as String;
      }

      // 获取用户ID
      String userId = '';
      if (envInfo != null && envInfo['acctId'] != null) {
        userId = envInfo['acctId'] as String;
      }

      // 根据需求，用户ID前面加'-'
      if (userId != null && userId.isNotEmpty && !userId.startsWith('-')) {
        userId = '-$userId';
      }

      // 构建请求头
      Map<String, String> headers = {
        'userId': userId,
      };

      Map<String, dynamic> params = {
        'source': '6', //todo-sw
        'candidateId': candidateId ?? '',
        'cardCode': cardCode,
        'subType': subType,
        'contractName': contactName ?? '',
        'contractTel': contactPhone ?? '',
        'signUpPhone': signUpPhone,
        'device': deviceUuid,
        'userId': userId,
        'snapshotID': snapshotId,
      };
      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      final response = await postApi(
          baseUrl: apiHost, path: path + '?token=$token', params: params);
      debugPrint('提交联系人信息成功: $response');
      return response;
    } catch (e) {
      debugPrint('提交联系人信息失败: $e');
    }
  }

  /// 手机号黑名单验证
  ///
  /// 参数说明:
  /// [userId] - 用户ID
  /// [source] - 渠道标识
  /// [candidateId] - 候选门店ID（原taskId）
  /// [tel] - 需要验证的手机号
  ///
  /// 返回值:
  /// 成功时返回接口响应数据
  /// 失败时抛出异常
  Future<dynamic> checkPhoneBlacklist({
    String userId = '',
    String source = '',
    @required String candidateId,
    @required String tel,
    String token = '',
  }) async {
    try {
      final apiHost = await ApiUtils.getApiHost();
      const path = '/api/ai/card/fill/check/tel';

      // 获取设备信息
      Map envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();

      // 处理用户ID格式
      if (userId == null || userId.isEmpty) {
        if (envInfo != null && envInfo['acctId'] != null) {
          userId = envInfo['acctId'] as String;
        } else {
          userId = '';
        }
      }

      // 根据需求，用户ID前面加'-'
      if (userId != null && userId.isNotEmpty && !userId.startsWith('-')) {
        userId = '-$userId';
      }

      // 构建请求参数
      Map<String, dynamic> queryParams = {
        'userId': userId,
        'source': source,
        'candidateId': candidateId ?? '',
        'tel': tel ?? '',
        'token': token,
      };

      // 发送GET请求
      final response = await getApi(
          baseUrl: apiHost, path: path + '?token=$token', params: queryParams);
      if (response != null && response?.code == 0) {
        return response;
      } else {
        String errorMsg = '未知错误';
        if (response != null && response?.msg != null) {
          errorMsg = response?.msg;
        }
        debugPrint('手机号黑名单验证失败: $errorMsg');
        return null;
      }
    } catch (e) {
      debugPrint('手机号黑名单验证失败: $e');
    }
  }

  /// 获取店铺信息
  ///
  /// 参数说明:
  /// [taskId] - 任务ID，将动态拼接到请求路径中
  /// [token] - 可选的token参数
  ///
  /// 返回值:
  /// 成功时返回包含signUpPhone的数据
  /// 失败时返回null
  Future<String> getSignUpPhone({
    @required String taskId,
    String token = '',
  }) async {
    try {
      // 获取API Host
      final apiHost = await ApiUtils.getApiHost();
      // 动态拼接taskId到路径中
      final path = '/api/v2/kaidian/restful/shop/$taskId';

      // 获取设备信息
      Map envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();

      // 获取用户ID
      String userId = '';
      if (envInfo != null && envInfo['acctId'] != null) {
        userId = envInfo['acctId'] as String;
      }

      // 根据需求，用户ID前面加'-'
      if (userId != null && userId.isNotEmpty && !userId.startsWith('-')) {
        userId = '-$userId';
      }

      // 构建请求参数
      Map<String, dynamic> queryParams = {};
      if (token != null && token.isNotEmpty) {
        queryParams['token'] = token;
      }

      // 发送GET请求
      final response = await getApi(
          baseUrl: apiHost, path: path + '?token=$token', params: queryParams);
      if (response != null && response?.code == 0) {
        if (response.data == null) {
          return null;
        }
        return response?.data['signUpPhone'];
      }
      String errorMsg = '未知错误';
      if (response != null && response.msg != null) {
        errorMsg = response.msg;
      }
      debugPrint('获取登录手机号失败: $errorMsg');
      return null;
    } catch (e) {
      debugPrint('获取登录手机号失败: $e');
      return null;
    }
  }
}
