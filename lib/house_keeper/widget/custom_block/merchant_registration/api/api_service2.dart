import 'dart:convert';
import 'dart:ffi';

import 'package:flutter/foundation.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/next_card_util.dart';
import 'package:wef_network/wef_request.dart';
import 'package:mtf_toast/mtf_toast.dart';

// 导入新的工具类
import '../../../../utils/common_params_utils.dart';
import '../utils/api_utils.dart';
import 'base/base_api_service.dart';

/// OCR识别结果
class OcrResult {
  final String shopName;
  final bool success;

  OcrResult({
    this.shopName = '',
    this.success = false,
  });
}

/// 店铺名称检查结果
class CheckNameResult {
  final int passStatus;
  final String rejectReason;
  final bool success;

  CheckNameResult({
    this.passStatus = 0,
    this.rejectReason = '',
    this.success = false,
  });
}

/// 提交店铺信息结果
class SubmitShopResult {
  final bool success;
  final String message;

  SubmitShopResult({
    this.success = false,
    this.message = '',
  });
}

/// 欢迎语结果
class WelcomeMessageResult {
  final int messageId;
  final String welcomeMsg;

  WelcomeMessageResult({
    this.messageId = 0,
    this.welcomeMsg = '',
  });

  factory WelcomeMessageResult.fromJson(Map<String, dynamic> json) {
    return WelcomeMessageResult(
      messageId: json['messageId'],
      welcomeMsg: json['welcomeMsg'],
    );
  }
}

/// 获取下一个卡片的结果
class NextCardResult {
  final CardData data;
  final bool success;
  final String message;

  NextCardResult({
    this.data,
    this.success = false,
    this.message = '',
  });
}

/// 卡片数据模型
class CardData {
  final int snapshotID;
  final String data;
  final int code;
  final int messageId;
  final int id;
  final String content;

  CardData({
    this.snapshotID,
    this.data,
    this.code,
    this.messageId,
    this.id,
    this.content,
  });

  factory CardData.fromJson(Map<String, dynamic> json) {
    return CardData(
      snapshotID: json['snapshotID'],
      data: json['data'],
      code: json['code'],
      messageId: json['messageId'],
      id: json['id'],
      content: json['content'],
    );
  }
}

/// 城市信息
class CityInfo {
  final int cityId;
  final String cityName;
  final String cityPinYin;

  CityInfo({
    this.cityId = 0,
    this.cityName = '',
    this.cityPinYin = '',
  });

  factory CityInfo.fromJson(Map<String, dynamic> json) {
    return CityInfo(
      cityId: json['cityId'] ?? 0,
      cityName: json['cityName'] ?? '',
      cityPinYin: json['cityPinYin'] ?? '',
    );
  }
}

/// 省份信息
class ProvinceInfo {
  final int provinceId;
  final String provinceName;
  final String provincePinYin;
  final List<CityInfo> cityList;

  ProvinceInfo({
    this.provinceId = 0,
    this.provinceName = '',
    this.provincePinYin = '',
    this.cityList = const [],
  });

  factory ProvinceInfo.fromJson(Map<String, dynamic> json) {
    final cityList = (json['cityList'] as List)
            ?.map((city) => CityInfo.fromJson(city))
            .toList() ??
        [];

    return ProvinceInfo(
      provinceId: json['provinceId'] ?? 0,
      provinceName: json['provinceName'] ?? '',
      provincePinYin: json['provincePinYin'] ?? '',
      cityList: cityList,
    );
  }
}

/// 获取省市列表结果
class GetProvinceListResult {
  final List<ProvinceInfo> provinceList;
  final bool success;
  final String message;

  GetProvinceListResult({
    this.provinceList = const [],
    this.success = false,
    this.message = '',
  });
}

/// 区县信息
class CountyInfo {
  final int countyId;
  final String countyName;
  final String countyPinYin;

  CountyInfo({
    this.countyId = 0,
    this.countyName = '',
    this.countyPinYin = '',
  });

  factory CountyInfo.fromJson(Map<String, dynamic> json) {
    return CountyInfo(
      countyId: json['countyId'] ?? 0,
      countyName: json['countyName'] ?? '',
      countyPinYin: json['countyPinYin'] ?? '',
    );
  }
}

/// 获取区县列表结果
class GetCountyListResult {
  final List<CountyInfo> countyList;
  final bool success;
  final String message;

  GetCountyListResult({
    this.countyList = const [],
    this.success = false,
    this.message = '',
  });
}

/// 提交地址信息结果
class SubmitAddressResult {
  final bool success;
  final String message;

  SubmitAddressResult({
    this.success = false,
    this.message = '',
  });
}

/// 插入历史消息结果
class InsertHistoryResult {
  final bool success;
  final String message;

  InsertHistoryResult({
    this.success = false,
    this.message = '',
  });
}

class CityInfoByLatLng {

  final int provinceId;
  final String provinceName;
  final int cityId;
  final String cityName;
  final int countyId;
  final String countyName;

  CityInfoByLatLng({
    this.provinceId,
    this.provinceName,
    this.cityId,
    this.cityName,
    this.countyId,
    this.countyName,
  });

  factory CityInfoByLatLng.fromJson(Map<String, dynamic> json) {
    return CityInfoByLatLng(
      provinceId: json['provinceId'],
      provinceName: json['provinceName'],
      cityId: json['cityId'],
      cityName: json['cityName'],
      countyId: json['countyId'],
      countyName: json['countyName'],
    );
  }
}

/// 商户注册API服务
///
/// 提供商户注册过程中所需的API调用，包括图片上传、数据提交等
class MerchantApiService {
  /// 单例实例
  static final MerchantApiService _instance = MerchantApiService._internal();

  /// 工厂构造函数
  factory MerchantApiService() => _instance;

  /// 内部构造函数
  MerchantApiService._internal();

  /// OCR识别接口
  static const String ocrShopFrontUrl = '/api/ocr/shopFront';

  /// 店铺名称检查接口
  static const String checkPoiNameUrl = '/api/ai/check/r/checkPoiName';

  /// 获取下一个卡片接口
  static const String nextCardUrl = '/api/ai/card/action/nextCard';

  /// 获取指定类型卡片
  static const String nextCardByType = '/api/ai/card/action/getCardByType';

  /// 根据 经纬度获取城市信息
  static const String cityInfoByLatLng = '/api/kangaroo/geography/getCityNameByEncryptCoordinates';

  /// 更新卡片状态接口
  static const String updateCardUrl = '/api/ai/assistant/history/singleMessage';

  /// 获取省市列表接口
  static const String getProvinceListUrl =
      '/api/kangaroo/geography/province/city/all';

  /// 获取区县列表接口
  static const String getCountyListUrl = '/api/kangaroo/geography/county';

  /// 提交地址信息接口
  static const String submitAddressUrl = '/api/ai/card/fill/address';

  /// 获取欢迎语
  static const String getWelcomeMsgUrl = '/api/ai/card/action/getWelcome';

  /// 插入历史消息接口
  static const String insertHistoryUrl = '/api/ai/assistant/history/insert';

  /// 开始OCR识别
  ///
  /// [imgUrl] - 图片URL
  /// [taskId] - 任务ID
  /// 返回识别结果，包含店铺名称和是否成功
  Future<OcrResult> startOcr({
    @required String imgUrl,
    @required String taskId,
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();
      // 构建请求参数
      final Map<String, dynamic> params = {
        'taskId': taskId,
        'source': '9',
        'device': 'APP',
        'imgUrl': imgUrl,
      };

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      // 发送请求
      final response = await postApi(
        baseUrl: apiHost,
        path: ocrShopFrontUrl + '?token=${params['token']}',
        params: params,
      );

      // 解析响应
      if (response != null && response?.code == 0 && response?.data != null) {
        final shopName = response?.data['shopName'];
        if (shopName != null) {
          MTFToast.showToast(msg: 'OCR识别成功');
          return OcrResult(
            shopName: shopName,
            success: true,
          );
        }
      }

      // OCR 识别失败
      String errorMsg = response?.msg ?? '未知错误';
      MTFToast.showToast(msg: 'OCR识别失败: $errorMsg');
      return OcrResult(success: false);
    } catch (error) {
      MTFToast.showToast(msg: 'OCR识别失败，请重试: $error');
      return OcrResult(success: false);
    }
  }

  /// 检查店铺名称
  ///
  /// [name] - 店铺名称
  /// 返回检查结果，包含通过状态和拒绝原因
  Future<CheckNameResult> checkPoiName({
    @required String name,
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      // 构建请求参数
      final Map<String, dynamic> params = {
        'name': name,
      };

      // 发送请求
      final response = await getApi(
        baseUrl: apiHost,
        path: checkPoiNameUrl,
        params: params,
      );

      // 解析响应
      if (response != null && response?.code == 0 && response?.data != null) {
        final passStatus = response?.data['passStatus'];
        final rejectReason = response?.data['rejectReason'];

        return CheckNameResult(
          passStatus: passStatus ?? 2,
          rejectReason: rejectReason ?? '未知错误',
          success: true,
        );
      }

      // 检查失败
      String errorMsg = response?.msg ?? '未知错误';
      MTFToast.showToast(msg: '检查失败: $errorMsg');
      return CheckNameResult(
        passStatus: 0,
        rejectReason: '未知错误',
        success: true,
      );
    } catch (error) {
      return CheckNameResult(
        passStatus: 0,
        rejectReason: '未知错误',
        success: true,
      );
    }
  }

  /// 提交店铺信息
  ///
  /// [candidateId] - 候选ID
  /// [shopName] - 店铺名称
  /// [shopFrontUrl] - 门脸图URL
  /// [cardCode] - 卡片代码
  /// [subType] - 子类型
  /// [suggestList] - 建议列表
  ///
  /// 返回提交结果
  Future<SubmitShopResult> submitShopInfo({
    @required String candidateId,
    @required String shopName,
    @required String shopFrontUrl,
    @required String cardCode,
    @required int subType,
    @required int snapshotId,
    String originalName,
    String suggestList = null, // 修改为 List<String> 类型并添加默认值
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      // 构建请求参数
      final Map<String, dynamic> params = {
        'candidateId': candidateId,
        'cardCode': cardCode,
        'subType': subType,
        'shopFrontUrl': shopFrontUrl,
        'shopName': shopName,
        'originalName': originalName,
        'suggestList': suggestList, // 直接传入 List
        'snapshotID': snapshotId,
        'shopLogoUrl':
            'https://p1.meituan.net/xianfu/92ce909868967bc438eee95cb3a3e7e315205.png',
        'source': 6, // todo-sw
      };

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      // 发送请求
      final response = await postApi(
        baseUrl: apiHost,
        path: '/api/ai/card/fill/shopBaseInfo?token=${params['token']}',
        params: params,
      );

      // 修改成功判断逻辑
      if (response != null &&
          response?.code == 0 &&
          response?.data != null && // 增加 data 不为 null 的判断
          response?.data['passStatus'] != null &&
          response?.data['passStatus'] == true) {
        MTFToast.showToast(msg: '提交成功');
        return SubmitShopResult(
          success: true,
          message: '提交成功',
        );
      }

      // 提交失败
      String errorMsg = '';
      if (response?.data != null && response?.data['rejectReason'] != null) {
        // 修改获取方式
        if (response.data['rejectReason'] is List) {
          // 如果是数组，拼接所有项
          errorMsg = (response.data['rejectReason'] as List).join('，');
        } else {
          // 如果是单个字符串
          errorMsg = response.data['rejectReason'].toString();
        }
      } else {
        errorMsg = response?.msg ?? '未知错误';
      }

      MTFToast.showToast(msg: '提交失败: $errorMsg');
      return SubmitShopResult(
        success: false,
        message: errorMsg,
      );
    } catch (error) {
      MTFToast.showToast(msg: '提交失败，请重试: $error');
      return SubmitShopResult(
        success: false,
        message: error.toString(),
      );
    }
  }

  /// 更新卡片
  static Future<CardData> updateCard({
    @required int messageId,
  }) async {
    // 使用工具类获取API Host
    final apiHost = await ApiUtils.getApiHost();

    final Map<String, dynamic> params = {
      'messageId': messageId,
    };

    /// 通参处理
    params.addAll(CommonParamsUtils.getInstance().getCommonParams());

    final response = await getApi(
      baseUrl: apiHost,
      path: updateCardUrl,
      params: params,
    );

    CardData parsedData;
    try {
      if (response != null && response?.code == 0) {
        // 解析响应数据
        dynamic cardData = response?.data;
        // 处理不同类型的响应数据
        if (cardData is String) {
          // 如果是字符串，解析成CardData对象
          final Map<String, dynamic> jsonData = jsonDecode(cardData);
          parsedData = CardData.fromJson(jsonData);
        } else if (cardData is Map) {
          // 如果是Map，通过fromJson解析

          parsedData = CardData.fromJson(cardData);
        }

        return parsedData;
      }

      return null;
    } catch (e) {
      debugPrint('更新卡片失败: $e');
      return null;
    }
  }

  /// 获取下一个卡片
  ///
  /// [candidateId] - 候选ID
  /// [cardId] - 卡片ID（可选）
  /// [specifyCardId] - 指定卡片ID（可选）
  /// [specifySubCardId] - 明确哪张卡片的哪个类型（可选）
  /// [token] - 认证令牌（可选）
  ///
  /// 返回下一个卡片信息
  static Future<NextCardResult> getNextCard({
    @required int candidateId,
    String cardId,
    String specifyCardId,
    int specifySubCardId,
    String token,
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      // 构建请求参数
      final Map<String, dynamic> params = {
        'candidateId': candidateId,
      };

      // 添加可选参数
      if (cardId != null && cardId.isNotEmpty) {
        params['cardId'] = cardId;
      }
      if (specifyCardId != null && specifyCardId.isNotEmpty) {
        params['specifyCardId'] = specifyCardId;
      }
      if (specifySubCardId != null) {
        params['specifySubCardId'] = specifySubCardId;
      }
      if (token != null && token.isNotEmpty) {
        params['token'] = token;
      }

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      final response = await getApi(
        baseUrl: apiHost,
        path: nextCardUrl,
        params: params,
      );
      // 解析响应
      if (response != null && response?.code == 0) {
        // 解析响应数据
        dynamic cardData = response?.data;
        CardData parsedData;

        // 处理不同类型的响应数据
        if (cardData is String) {
          // 如果是字符串，解析成CardData对象
          try {
            final Map<String, dynamic> jsonData = jsonDecode(cardData);
            parsedData = CardData.fromJson(jsonData);
          } catch (e) {
            debugPrint('解析字符串到CardData对象失败: $e');
          }
        } else if (cardData is Map) {
          // 如果是Map，通过fromJson解析
          try {
            parsedData = CardData.fromJson(cardData);
          } catch (e) {
            debugPrint('通过fromJson解析Map失败: $e');
          }
        } else {
          debugPrint('获取下一个卡片数据格式异常: $cardData');
          // 如果数据格式不符合预期，返回空字符串
        }

        return NextCardResult(
          data: parsedData,
          success: true,
          message: '',
        );
      }

      // 获取失败
      String errorMsg = response?.msg ?? '未知错误';
      debugPrint('获取下一个卡片失败: $errorMsg');
      return NextCardResult(
        success: false,
        message: errorMsg,
      );
    } catch (error) {
      debugPrint('获取下一个卡片异常: $error');
      return NextCardResult(
        success: false,
        message: error.toString(),
      );
    }
  }

  /// 获取下一个卡片
  /// [type] - 卡片类型
  ///
  /// 返回下一个卡片信息
  static Future<NextCardResult> getCardByType({
    int type,
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      // 构建请求参数
      final Map<String, dynamic> params = {
        'type': type,
      };

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      final response = await getApi(
        baseUrl: apiHost,
        path: nextCardByType,
        params: params,
      );
      // 解析响应
      if (response != null && response?.code == 0) {
        // 解析响应数据
        dynamic cardData = response?.data;
        CardData parsedData;

        // 处理不同类型的响应数据
        if (cardData is String) {
          // 如果是字符串，解析成CardData对象
          try {
            final Map<String, dynamic> jsonData = jsonDecode(cardData);
            parsedData = CardData.fromJson(jsonData);
          } catch (e) {
            debugPrint('解析字符串到CardData对象失败: $e');
          }
        } else if (cardData is Map) {
          // 如果是Map，通过fromJson解析
          try {
            parsedData = CardData.fromJson(cardData);
          } catch (e) {
            debugPrint('通过fromJson解析Map失败: $e');
          }
        } else {
          debugPrint('获取下一个卡片数据格式异常: $cardData');
          // 如果数据格式不符合预期，返回空字符串
        }

        return NextCardResult(
          data: parsedData,
          success: true,
          message: '',
        );
      }

      // 获取失败
      String errorMsg = response?.msg ?? '未知错误';
      debugPrint('获取下一个卡片失败: $errorMsg');
      return NextCardResult(
        success: false,
        message: errorMsg,
      );
    } catch (error) {
      debugPrint('获取下一个卡片异常: $error');
      return NextCardResult(
        success: false,
        message: error.toString(),
      );
    }
  }

  /// 获取省市列表
  ///
  /// 返回省市列表信息
  Future<GetProvinceListResult> getProvinceList() async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      // 发送请求
      final response = await getApi(
        baseUrl: apiHost,
        path: getProvinceListUrl,
      );

      // 解析响应
      if (response != null && response?.code == 0 && response?.data != null) {
        final provinceList = (response?.data as List)
                ?.map((province) => ProvinceInfo.fromJson(province))
                .toList() ??
            [];

        return GetProvinceListResult(
          provinceList: provinceList,
          success: true,
          message: '获取成功',
        );
      }

      // 获取失败
      String errorMsg = response?.msg ?? '未知错误';
      debugPrint('获取省市列表失败: $errorMsg');
      return GetProvinceListResult(
        success: false,
        message: errorMsg,
      );
    } catch (error) {
      debugPrint('获取省市列表异常: $error');
      return GetProvinceListResult(
        success: false,
        message: error.toString(),
      );
    }
  }

  /// 获取区县列表
  ///
  /// [provinceId] - 省份ID
  /// [cityId] - 城市ID
  ///
  /// 返回区县列表信息
  Future<GetCountyListResult> getCountyList({
    @required String provinceId,
    @required String cityId,
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      // 构建请求参数
      final Map<String, dynamic> params = {
        'provinceId': provinceId,
        'cityId': cityId,
      };

      // 发送请求
      final response = await getApi(
        baseUrl: apiHost,
        path: getCountyListUrl,
        params: params,
      );

      // 解析响应
      if (response != null && response?.code == 0 && response?.data != null) {
        final countyList = (response?.data as List)
                ?.map((county) => CountyInfo.fromJson(county))
                .toList() ??
            [];

        return GetCountyListResult(
          countyList: countyList,
          success: true,
          message: '获取成功',
        );
      }

      // 获取失败
      String errorMsg = response?.msg ?? '未知错误';
      debugPrint('获取区县列表失败: $errorMsg');
      return GetCountyListResult(
        success: false,
        message: errorMsg,
      );
    } catch (error) {
      debugPrint('获取区县列表异常: $error');
      return GetCountyListResult(
        success: false,
        message: error.toString(),
      );
    }
  }

  /// 提交地址信息
  ///
  /// [candidateId] - 候选ID
  /// [cardCode] - 卡片代码
  /// [subType] - 子类型
  /// [address] - 地址
  /// [cityId] - 城市ID
  /// [cityName] - 城市名称
  /// [countyId] - 区县ID
  /// [countyName] - 区县名称
  /// [latitude] - 纬度
  /// [longitude] - 经度
  /// [provinceId] - 省份ID
  /// [provinceName] - 省份名称
  /// [structuredAddress] - 结构化地址
  /// [suggestList] - 建议列表（可选）
  /// [addressRemark] - 地址备注（可选）
  ///
  /// 返回提交结果
  Future<SubmitAddressResult> submitAddress({
    @required String candidateId,
    @required String cardCode,
    @required int snapshotId,
    @required int subType,
    @required String address,
    @required int cityId,
    @required String cityName,
    @required int countyId,
    @required String countyName,
    @required int latitude,
    @required int longitude,
    @required int provinceId,
    @required String provinceName,
    @required String structuredAddress,
    String suggestList = '',
    String addressRemark = '',
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      final commonParams = CommonParamsUtils.getInstance().getCommonParams();
      final token = commonParams['token'];

      // 构建请求参数
      final Map<String, dynamic> params = {
        'source': 'APP',
        'candidateId': candidateId,
        'snapshotID': snapshotId,
        'cardCode': cardCode,
        'subType': subType,
        'address': address,
        'cityId': cityId,
        'cityName': cityName,
        'countyId': countyId,
        'countyName': countyName,
        'latitude': latitude,
        'longitude': longitude,
        'provinceId': provinceId,
        'provinceName': provinceName,
        'structuredAddress': structuredAddress,
        'token': token,
      };

      // 添加可选参数
      if (suggestList.isNotEmpty) {
        params['suggestList'] = suggestList;
      }
      if (addressRemark.isNotEmpty) {
        params['addressRemark'] = addressRemark;
      }

      // 发送请求
      final response = await postApi(
        baseUrl: apiHost,
        path: submitAddressUrl + '?token=$token',
        params: params,
      );

      // 解析响应
      if (response != null && response?.code == 0) {
        // 修改成功判断逻辑
        if (response != null &&
            response?.code == 0 &&
            response?.data != null && // 增加 data 不为 null 的判断
            response?.data['passStatus'] != null &&
            response?.data['passStatus'] == true) {
          MTFToast.showToast(msg: '提交成功');
          return SubmitAddressResult(
            success: true,
            message: '提交成功',
          );
        }

        // 提交失败
        String errorMsg = '';
        if (response?.data != null && response?.data['rejectReason'] != null) {
          // 修改获取方式
          if (response.data['rejectReason'] is List) {
            // 如果是数组，拼接所有项
            errorMsg = (response.data['rejectReason'] as List).join('，');
          } else {
            // 如果是单个字符串
            errorMsg = response.data['rejectReason'].toString();
          }
        } else {
          errorMsg = response?.msg ?? '未知错误';
        }

        debugPrint('提交地址信息失败: $errorMsg');
        MTFToast.showToast(msg: '提交失败: $errorMsg');
        return SubmitAddressResult(
          success: false,
          message: errorMsg,
        );
      }

      // 提交失败
      String errorMsg = response?.msg ?? '未知错误';
      debugPrint('提交地址信息失败: $errorMsg');
      MTFToast.showToast(msg: '提交失败: $errorMsg');
      return SubmitAddressResult(
        success: false,
        message: errorMsg,
      );
    } catch (error) {
      debugPrint('提交地址信息异常: $error');
      MTFToast.showToast(msg: '提交失败，请重试: $error');
      return SubmitAddressResult(
        success: false,
        message: error.toString(),
      );
    }
  }

  /// 插入历史消息
  ///
  /// [role] - 消息角色，"User"代表用户发送，"Agent"代表助手发送
  /// [visible] - 1-用户可见，0-用户不可见
  /// [content] - 消息内容
  /// [timeStamp] - 消息时间戳
  /// [candidateId] - 候选门店ID
  /// [epToken] - EP令牌（可选）
  /// [token] - 认证令牌（可选）
  ///
  /// 返回插入结果
  static Future<InsertHistoryResult> insertHistory({
    @required String role,
    @required int visible,
    @required String content,
    @required int timeStamp,
    @required int candidateId,
    String epToken,
    String token,
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      // 构建请求参数
      final Map<String, dynamic> params = {
        'role': role,
        'visible': visible,
        'content': content,
        'timeStamp': timeStamp,
        'candidateId': candidateId
      };

      // 添加可选参数
      if (epToken != null && epToken.isNotEmpty) {
        params['epToken'] = epToken;
      }
      if (token != null && token.isNotEmpty) {
        params['token'] = token;
      }

      // 发送请求
      final response = await postApi(
        baseUrl: apiHost,
        path: insertHistoryUrl + '?token=$token',
        params: params,
      );

      // 解析响应
      if (response != null && response?.code == 0) {
        MTFToast.showToast(msg: '插入成功');
        return InsertHistoryResult(
          success: true,
          message: '插入成功',
        );
      }

      // 插入失败
      String errorMsg = response?.msg ?? '未知错误';
      debugPrint('插入历史消息失败: $errorMsg');
      MTFToast.showToast(msg: '插入失败: $errorMsg');
      return InsertHistoryResult(
        success: false,
        message: errorMsg,
      );
    } catch (error) {
      debugPrint('插入历史消息异常: $error');
      MTFToast.showToast(msg: '插入失败，请重试: $error');
      return InsertHistoryResult(
        success: false,
        message: error.toString(),
      );
    }
  }

  /// 提交标签信息
  ///
  /// [source] - 渠道标识
  /// [candidateId] - 候选门店ID
  /// [cardCode] - 卡片代码
  /// [snapshotID] - 卡片快照ID
  /// [subType] - 卡片子类型
  /// [tagId] - 主营品类
  /// [tagName] - 主营品类名称
  /// [viceTagId] - 辅营品类（可选）
  /// [viceTagName] - 辅营品类名称（可选）
  ///
  /// 返回提交结果
  static Future<SubmitShopResult> submitTagInfo({
    @required String source,
    @required int candidateId,
    @required String cardCode,
    @required int snapshotID,
    @required int subType,
    @required int tagId,
    @required String tagName,
    int viceTagId,
    String viceTagName,
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      // 构建请求参数
      final Map<String, dynamic> params = {
        'source': source,
        'candidateId': candidateId,
        'cardCode': cardCode,
        'snapshotID': snapshotID,
        'subType': subType,
        'tagId': tagId,
        'tagName': tagName,
      };

      final commonParams =
          CommonParamsUtils.getInstance().getCommonParams() ?? {};

      // 获取用户ID
      String userId = '';
      if (commonParams['acctId'] != null) {
        userId = commonParams['acctId'] as String;
      }
      // 根据需求，用户ID前面加'-'
      if (userId != null && userId.isNotEmpty && !userId.startsWith('-')) {
        userId = '-$userId';
      }
      params['userId'] = userId;

      // 添加可选参数
      if (viceTagId != null) {
        params['viceTagId'] = viceTagId;
      }
      if (viceTagName != null && viceTagName.isNotEmpty) {
        params['viceTagName'] = viceTagName;
      }

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      // 发送请求
      final response = await postApi(
        baseUrl: apiHost,
        path: '/api/ai/card/fill/tagInfo?token=${commonParams['token']}',
        params: params,
      );

      // 修改成功判断逻辑
      if (response != null &&
          response?.code == 0 &&
          response?.data != null && // 增加 data 不为 null 的判断
          response?.data['passStatus'] != null &&
          response?.data['passStatus'] == true) {
        MTFToast.showToast(msg: '提交成功');
        return SubmitShopResult(
          success: true,
          message: '提交成功',
        );
      }

      // 提交失败
      String errorMsg = '';
      if (response?.data != null && response?.data['rejectReason'] != null) {
        // 修改获取方式
        if (response.data['rejectReason'] is List) {
          // 如果是数组，拼接所有项
          errorMsg = (response.data['rejectReason'] as List).join('，');
        } else {
          // 如果是单个字符串
          errorMsg = response.data['rejectReason'].toString();
        }
      } else {
        errorMsg = response?.msg ?? '未知错误';
      }

      MTFToast.showToast(msg: '提交失败: $errorMsg');
      return SubmitShopResult(
        success: false,
        message: errorMsg,
      );
    } catch (error) {
      MTFToast.showToast(msg: '提交失败，请重试: $error');
      return SubmitShopResult(
        success: false,
        message: error.toString(),
      );
    }
  }

  /// 获取欢迎语
  /// [candidateId] - 候选ID
  /// 返回提交结果
  static Future<WelcomeMessageResult> getWelcomeMsg({
    @required int candidateId,
  }) async {
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      final commonParams = CommonParamsUtils.getInstance().getCommonParams();
      final token = commonParams['token'];

      // 构建请求参数
      final Map<String, dynamic> params = {
        'source': 'APP',
        'candidateId': candidateId,
        'token': token,
      };

      final response = await getApi(
        baseUrl: apiHost,
        path: getWelcomeMsgUrl + '?token=$token',
        params: params,
      );

      // 解析响应
      if (response != null && response?.code == 0) {
        final result = WelcomeMessageResult.fromJson(response.data);
        return result;
      }
      return null;
    } catch (error) {
      debugPrint('获取欢迎语信息异常: $error');
      return null;
    }
  }


  /// 根据经纬度获取城市信息
  static Future<CityInfoByLatLng> getCityInfoByLatLng({
    int latitude,
    int longitude
  }) async {
    
    try {
      // 使用工具类获取API Host
      final apiHost = await ApiUtils.getApiHost();

      final commonParams = CommonParamsUtils.getInstance().getCommonParams();
      final token = commonParams['token'];

      // 构建请求参数
      final Map<String, dynamic> params = {
        'latitude': latitude,
        'longitude': longitude,
      };

      params.addAll(CommonParamsUtils.getInstance().getCommonParams());

      final response = await ruzhuPostApi(
        baseUrl: apiHost,
        path: cityInfoByLatLng + '?token=$token',
        params: params,
      );
      // 解析响应
      if (response != null && response?.code == 0) {
        // 解析响应数据
        dynamic cardData = response?.data;
        CityInfoByLatLng parsedData;

        // 处理不同类型的响应数据
        if (cardData is String) {
          // 如果是字符串，解析成CardData对象
          try {
            final Map<String, dynamic> jsonData = jsonDecode(cardData);
            parsedData = CityInfoByLatLng.fromJson(jsonData);
          } catch (e) {
            debugPrint('解析字符串到CardData对象失败: $e');
          }
        } else if (cardData is Map) {
          // 如果是Map，通过fromJson解析
          try {
            parsedData = CityInfoByLatLng.fromJson(cardData);
          } catch (e) {
            debugPrint('通过fromJson解析Map失败: $e');
          }
        }

        return parsedData;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /// 保存卡片快照为已编辑
  ///
  /// [candidateId] - 候选门店ID
  /// [snapshotId] - 快照ID
  ///
  /// 返回保存结果
  Future<bool> saveCardSnapshot({
    @required int candidateId,
    @required int snapshotId,
  }) async {
    try {
      final apiHost = await ApiUtils.getApiHost();
      final commonParams = CommonParamsUtils.getInstance().getCommonParams();
      final token = commonParams['token'] as String;

      final Map<String, dynamic> params = {
        'candidateId': candidateId,
        'snapshotId': snapshotId,
      };

      final response = await ruzhuPostApi(
        baseUrl: apiHost,
        path: '/api/ai/card/action/saveCardSnapshot?token=$token',
        params: params,
        token: token,
      );

      if (response != null && response.code == 0 && response.data == true) {
        return true;
      }

      String errorMsg = response?.msg ?? '操作失败';
      MTFToast.showToast(msg: errorMsg);
      return false;
    } catch (error) {
      MTFToast.showToast(msg: '操作失败，请重试: $error');
      return false;
    }
  }
}
