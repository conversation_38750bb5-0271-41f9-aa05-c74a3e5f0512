import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import '../../../../utils/common_params_utils.dart';
import 'halal_certificate_upload_page.dart';
import '../utils/next_card_util.dart';
import '../api/api_service2.dart';

/// ------------------------------- 常量定义 ------------------------------- ///

/// 默认示例图片URL
const _kDefaultExampleImageUrl =
    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/69011673f81038f2/halal_license_example.jpg';

/// ------------------------------- 数据模型 ------------------------------- ///

/// 清真许可证数据模型
class HalalLicenseData {
  final String cardId;
  final int subType;
  final int candidateId;
  final int snapshotId;
  final int status;
  final HalalLicenseInfo data;

  HalalLicenseData({
    this.cardId,
    this.subType,
    this.candidateId,
    this.snapshotId,
    this.status,
    this.data,
  });

  factory HalalLicenseData.fromJson(Map<String, dynamic> json) {
    return HalalLicenseData(
      cardId: json['cardId'] as String,
      subType: json['subType'] as int,
      candidateId: json['candidateId'] as int,
      snapshotId: json['snapshotId'] as int,
      status: json['status'] as int,
      data: json['data'] != null
          ? HalalLicenseInfo.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cardId': cardId,
      'subType': subType,
      'candidateId': candidateId,
      'snapshotId': snapshotId,
      'status': status,
      'data': data?.toJson(),
    };
  }
}

/// 清真许可证信息模型
class HalalLicenseInfo {
  final List<String> url;

  HalalLicenseInfo({
    this.url,
  });

  factory HalalLicenseInfo.fromJson(Map<String, dynamic> json) {
    if (json['url'] == null) return HalalLicenseInfo(url: []);

    // 处理url可能是字符串或数组的情况
    if (json['url'] is String) {
      final String urlStr = json['url'] as String;
      return HalalLicenseInfo(
        url: urlStr.isNotEmpty ? [urlStr] : [],
      );
    } else if (json['url'] is List) {
      return HalalLicenseInfo(
        url: (json['url'] as List<dynamic>)
            .map((e) => e as String)
            .where((url) => url != null && url.isNotEmpty)
            .toList(),
      );
    }

    return HalalLicenseInfo(url: []);
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
    };
  }
}

/// 上传卡片状态
enum HalalLicenseSubType {
  /// 初始状态，首次填写 subType= 1
  initial,

  /// 已编辑状态，主动修改  subType= 4
  edited,

  /// 建议修改状态 subType= 3
  suggested,

  /// 被驳回状态  subType= 5
  rejected,
}

/// 卡片状态枚举
/// status=0:"去修改"禁用 status=1:正常操作 status=2:"已填写"禁用 status=3:"已过期"禁用
enum HalalLicenseStatus {
  /// 禁用修改
  disabledModify, // 0

  /// 正常操作
  normal, // 1

  /// 已填写
  filled, // 2

  /// 已过期
  expired, // 3
}

/// ------------------------------- 主组件 ------------------------------- ///

/// 清真许可证上传组件
/// 用于商户注册流程中上传清真许可证
class HalalCertificateUploadWidget extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;
  const HalalCertificateUploadWidget({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
          key: key,
          content: content,
          messageId: messageId,
          realMessageId: realMessageId,
        );

  @override
  _HalalCertificateUploadWidgetState createState() =>
      _HalalCertificateUploadWidgetState();
}

class _HalalCertificateUploadWidgetState
    extends BaseBlockWidgetState<HalalCertificateUploadWidget> {
  /// 当前清真许可证URL列表
  List<String> _halalCertificateUrls = [];

  /// 当前组件状态
  HalalLicenseSubType _cardState = HalalLicenseSubType.initial;
  HalalLicenseData _halalLicenseData;
  int status;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(HalalCertificateUploadWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  /// 解析传入的内容数据
  void _parseContent() {
    if (_isInvalidContent) return;

    try {
      if (widget.content != null && widget.content.isNotEmpty) {
        final data = json.decode(widget.content);
        if (data == null || data is! Map) return;

        _halalLicenseData = HalalLicenseData.fromJson(data);

        // 更新subType状态
        final subType = _halalLicenseData?.subType;
        if (subType != null) {
          setState(() {
            _cardState = _parseStatusToState(subType);
          });
        }

        // 更新status状态
        status = _halalLicenseData?.status ?? 1; // 默认为1（正常状态）

        _updateFromData(data);
      }
    } catch (e) {
      debugPrint('Failed to parse halal certificate data: $e');
    }
  }

  HalalLicenseStatus getStatus(int statusValue) {
    int status =
        RuzhuCardStatusManager().getStatus(widget.messageId, statusValue);
    HalalLicenseStatus _status;
    if (status >= 0 && status < HalalLicenseStatus.values.length) {
      _status = HalalLicenseStatus.values[status];
    } else {
      _status = HalalLicenseStatus.normal; // 默认为正常状态
    }
    return _status;
  }

  /// 检查内容是否有效
  bool get _isInvalidContent =>
      widget.content == null || widget.content.isEmpty;

  /// 从数据中更新组件状态
  void _updateFromData(Map<String, dynamic> data) {
    if (data['data'] == null) return;

    final innerData = data['data'] as Map<String, dynamic>;

    // 处理url可能是字符串或数组的情况
    if (innerData['url'] is String) {
      final String url = innerData['url'] as String;
      if (_isValidUrl(url)) {
        setState(() => _halalCertificateUrls = [url]);
      }
    } else if (innerData['url'] is List) {
      final List<dynamic> urls = innerData['url'] as List<dynamic>;
      final List<String> validUrls = urls
          .map((e) => e as String)
          .where((url) => _isValidUrl(url))
          .take(2) // 最多取前两张图片
          .toList();

      if (validUrls.isNotEmpty) {
        setState(() => _halalCertificateUrls = validUrls);
      }
    }
  }

  /// 将后端状态转换为前端状态
  HalalLicenseSubType _parseStatusToState(int status) {
    switch (status) {
      case 1:
        return HalalLicenseSubType.initial;
      case 5:
        return HalalLicenseSubType.rejected;
      case 3:
        return HalalLicenseSubType.suggested;
      case 4:
        return HalalLicenseSubType.edited;
      default:
        return HalalLicenseSubType.initial;
    }
  }

  /// 检查URL是否有效
  bool _isValidUrl(String url) =>
      url != null && url.isNotEmpty && url != "http://";

  @override
  Widget buildContentView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildImagePreview(),
        const SizedBox(height: 24),
        _buildActionButton(),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton() {
    HalalLicenseStatus _status = getStatus(status);
    // 根据状态判断显示哪种按钮
    switch (_status) {
      case HalalLicenseStatus.disabledModify:
        return _buildDisabledModifyButton();
      case HalalLicenseStatus.expired:
        return _buildExpiredButton();
      case HalalLicenseStatus.filled:
        return _buildFilledButton();
      case HalalLicenseStatus.normal:
        return _buildNormalButton();
      default:
        // 默认情况，也显示正常按钮
        return _buildNormalButton();
    }
  }

  /// 构建已过期按钮
  Widget _buildDisabledModifyButton() {
    return const CustomButton(text: '去修改', primary: false);
  }

  /// 构建已过期按钮
  Widget _buildExpiredButton() {
    return const CustomButton(text: '已过期', primary: false);
  }

  /// 构建已填写按钮
  Widget _buildFilledButton() {
    return const CustomButton(text: '已填写', primary: false);
  }

  /// 构建正常状态下的按钮
  Widget _buildNormalButton() {
    return _UploadButtons(
      snapshotId: _halalLicenseData?.snapshotId,
      cardState: _cardState,
      halalCertificateUrls: _halalCertificateUrls,
      onImagesUploaded: _handleImagesUploaded,
      onSkip: _handleSkip,
      onKeepCurrent: _handleKeepCurrent,
    );
  }

  /// 处理图片上传成功
  void _handleImagesUploaded(List<String> urls) {
    if (urls == null || urls.isEmpty) return;

    widget.model?.updateCard(widget.realMessageId);

    NextCardUtil.addNextCard(
      widget.messageId,
      model: widget.model,
      candidateId: _halalLicenseData.candidateId ?? 0,
      expectedStatus: 2,
    );
  }

  /// 处理跳过上传
  void _handleSkip() {
    NextCardUtil.addNextCard(widget.messageId,
        model: widget.model,
        candidateId: _halalLicenseData.candidateId ?? 0,
        expectedStatus: 2);
    debugPrint("不是清真店铺，跳下一个卡片");
  }

  /// 处理保持当前图片
  void _handleKeepCurrent() async {
    await MerchantApiService().saveCardSnapshot(
      candidateId: _halalLicenseData.candidateId,
      snapshotId: _halalLicenseData.snapshotId,
    );
    widget.model?.updateCard(widget.realMessageId);
    NextCardUtil.addNextCard(widget.messageId,
        model: widget.model,
        candidateId: _halalLicenseData.candidateId ?? 0,
        expectedStatus: 2);
    debugPrint("坚持使用");
  }

  /// 构建图片预览区域
  Widget _buildImagePreview() {
    // 如果有两张图片，使用并排布局
    if (_halalCertificateUrls.length == 2) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Stack(
              children: [
                ImageContainer(imageUrl: _halalCertificateUrls[0]),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Stack(
              children: [
                ImageContainer(imageUrl: _halalCertificateUrls[1]),
              ],
            ),
          ),
        ],
      );
    }

    // 如果只有一张图片或没有图片，使用单张布局
    final imgUrl = _halalCertificateUrls.isNotEmpty
        ? _halalCertificateUrls[0]
        : _kDefaultExampleImageUrl;
    return Stack(
      children: [
        ImageContainer(imageUrl: imgUrl),
        Visibility(
            visible: _halalCertificateUrls.isEmpty, child: const ExampleLabel())
      ],
    );
  }
}

/// ------------------------------- 辅助组件 ------------------------------- ///

/// 图片容器组件
class ImageContainer extends StatelessWidget {
  final String imageUrl;

  const ImageContainer({Key key, @required this.imageUrl}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 160,
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
        ),
        child: _buildImageWidget(),
      ),
    );
  }

  Widget _buildImageWidget() {
    if (imageUrl == null || imageUrl.isEmpty) {
      return _buildErrorWidget();
    }
    // 网络图片
    return Image.network(
      imageUrl,
      fit: BoxFit.contain,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6E30)),
          ),
        );
      },
      errorBuilder: (_, __, ___) => _buildErrorWidget(),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      // 添加 Center
      child: Image.network(_kDefaultExampleImageUrl, fit: BoxFit.contain),
    );
  }
}

/// 示例标签组件
class ExampleLabel extends StatelessWidget {
  const ExampleLabel({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      child: Container(
        decoration: const BoxDecoration(
          color: Color(0xFFFFE14D),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8),
            bottomRight: Radius.circular(12),
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        child: const Text(
          '示例',
          style: TextStyle(fontSize: 12, color: Color(0xFF333333)),
        ),
      ),
    );
  }
}

/// 上传按钮组件
class _UploadButtons extends StatelessWidget {
  final List<String> halalCertificateUrls;
  final HalalLicenseSubType cardState;
  final Function(List<String>) onImagesUploaded;
  final VoidCallback onSkip;
  final VoidCallback onKeepCurrent;
  final int snapshotId;

  const _UploadButtons({
    Key key,
    @required this.cardState,
    this.halalCertificateUrls = const [],
    this.onImagesUploaded,
    this.onSkip,
    this.onKeepCurrent,
    this.snapshotId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 根据不同状态显示不同的按钮组合
    switch (cardState) {
      case HalalLicenseSubType.rejected:
        return _buildRejectedButtons(context);
      case HalalLicenseSubType.suggested:
      case HalalLicenseSubType.edited:
        return _buildSuggestedOrEditedButtons(context);
      case HalalLicenseSubType.initial:
      default:
        return _buildInitialButtons(context);
    }
  }

  /// 构建初始状态下的按钮
  Widget _buildInitialButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildSkipButton()),
        const SizedBox(width: 12),
        Expanded(child: _buildUploadButton(context, text: '上传清真许可证')),
      ],
    );
  }

  /// 构建建议修改或已编辑状态下的按钮
  Widget _buildSuggestedOrEditedButtons(BuildContext context) {
    final btnText =
        cardState == HalalLicenseSubType.suggested ? '坚持使用' : '取消修改';
    return Row(
      children: [
        Expanded(
            child: CustomButton(
                text: btnText,
                primary: false,
                onPressed: () => onKeepCurrent())),
        const SizedBox(width: 12),
        Expanded(child: _buildUploadButton(context, text: '去修改')),
      ],
    );
  }

  /// 构建被驳回状态下的按钮
  Widget _buildRejectedButtons(BuildContext context) {
    return CustomButton(text: '去修改', onPressed: () => _handleUpload(context));
  }

  /// 构建跳过按钮
  Widget _buildSkipButton() {
    return CustomButton(
        text: '不是清真店铺', primary: false, onPressed: () => onSkip());
  }

  /// 构建上传按钮
  Widget _buildUploadButton(BuildContext context, {String text}) {
    return CustomButton(text: text, onPressed: () => _handleUpload(context));
  }

  /// 处理上传操作
  Future<void> _handleUpload(BuildContext context) async {
    try {
      Map<String, dynamic> params =
          CommonParamsUtils.getInstance().getCommonParams();
      final candidateId =
          int.tryParse(params['candidateId']?.toString() ?? '0') ?? 0;
      // 导航到清真许可证上传页面
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => HalalCertificateUploadPage(
            taskId: candidateId, // 开店任务ID
            snapshotId: snapshotId,
            initialImageUrls: halalCertificateUrls,
          ),
        ),
      );

      // 处理返回结果
      if (result != null) {
        // 确保结果是List<String>类型
        final List<String> resultUrls = result is List
            ? List<String>.from(result.where((item) => item is String))
            : (result is String ? [result] : []);

        if (resultUrls.isNotEmpty) {
          if (onImagesUploaded != null) {
            onImagesUploaded(resultUrls);
          }
          MTFToast.showToast(msg: '上传成功');
          return;
        }
        // 如果resultUrls为空但result不为null，显示错误提示
        MTFToast.showToast(msg: '上传图片失败，请重试');
      }
      // 当result为null时，用户可能是主动取消上传，不显示错误提示
    } catch (e) {
      MTFToast.showToast(msg: '上传图片失败，请重试');
      debugPrint('上传图片失败: $e');
    }
  }
}
