import 'package:flutter/material.dart';
import 'package:mtf_toast/mtf_toast.dart';
import '../api/base/base_api_service.dart';
import '../utils/image_utils.dart';

/// 清真许可证图片工具类
///
/// 提供安全的图片选择和上传功能，避免Navigator断言错误
class HalalImageUtils {
  /// 安全地选择并上传图片
  /// [context] - 上下文
  /// [sceneToken] -
  /// [count] - 选择图片数量，默认为1
  /// 返回上传后的图片URL，取消或失败则返回null
  static Future<String> safeSelectAndUploadImage(
    BuildContext context, {
    String sceneToken,
    int count = 1,
  }) async {
    try {
      // 显示选择图片的弹窗
      final ImageSource source = await _showImageSourceDialog(context);

      // 用户取消选择
      if (source == null) {
        return null;
      }

      // 根据用户选择的来源选择图片
      final String sourceType = source == ImageSource.camera
          ? MerchantImageUtil.sourceCamera
          : MerchantImageUtil.sourceGallery;

      // 选择图片
      final String localId = await MerchantImageUtil.chooseLocalImage(
        source: sourceType,
        sceneToken: sceneToken ?? MerchantImageUtil.defaultSceneToken,
        count: count,
      );

      // 用户取消选择或选择失败
      if (localId == null) {
        return null;
      }

      // 上传图片（这里使用模拟数据，实际项目中应该使用真实的上传逻辑）
      // 在实际项目中，取消注释下面的代码，使用真实的上传API
      // String imageUrl =
      //     "https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/69011673f81038f2/halal_license_example.jpg";
      // imageUrl =
      //     'http://p1.meituan.net/ziruzhu/63d5b40872a6731155c92fea4477524745900.jpg';
      // 实际项目中的上传逻辑示例：
      final ImageUploadApi _apiService = ImageUploadApi();
      final imageUrl = await _apiService.uploadImageToCdn(filePath: localId);
      return imageUrl;
    } catch (e) {
      debugPrint('选择并上传图片失败: $e');
      MTFToast.showToast(msg: '选择图片失败，请重试');
      return null;
    }
  }

  /// 安全地选择并上传多张图片
  ///
  /// 解决了原有方法可能导致的类型错误，确保返回类型为List<String>
  ///
  /// [context] - 上下文
  /// [initialUrls] - 初始图片URL列表
  /// [maxCount] - 最大图片数量，默认为2
  /// [sceneToken] - 场景标识，默认使用MerchantImageUtil.defaultSceneToken
  ///
  /// 返回上传后的图片URL列表，取消或失败则返回空列表
  static Future<List<String>> safeSelectAndUploadMultipleImages(
    BuildContext context, {
    List<String> initialUrls = const [],
    int maxCount = 2,
    String sceneToken,
  }) async {
    try {
      // 如果已经达到最大数量，直接返回
      if (initialUrls.length >= maxCount) {
        MTFToast.showToast(msg: '最多只能上传${maxCount}张图片');
        return List<String>.from(initialUrls);
      }

      // 显示选择图片的弹窗
      final ImageSource source = await _showImageSourceDialog(context);

      // 用户取消选择
      if (source == null) {
        return initialUrls;
      }

      // 根据用户选择的来源选择图片
      final String sourceType = source == ImageSource.camera
          ? MerchantImageUtil.sourceCamera
          : MerchantImageUtil.sourceGallery;

      // 选择图片
      final String localId = await MerchantImageUtil.chooseLocalImage(
        source: sourceType,
        sceneToken: sceneToken ?? MerchantImageUtil.defaultSceneToken,
        count: 1,
      );

      // 用户取消选择或选择失败
      if (localId == null) {
        return initialUrls;
      }

      // 上传图片（这里使用模拟数据，实际项目中应该使用真实的上传逻辑）
      // String imageUrl =
      //     "https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/69011673f81038f2/halal_license_example.jpg";

      // imageUrl =
      //     'http://p1.meituan.net/ziruzhu/63d5b40872a6731155c92fea4477524745900.jpg';
      // 实际项目中的上传逻辑示例：

      final ImageUploadApi _apiService = ImageUploadApi();
      final imageUrl = await _apiService.uploadImageToCdn(filePath: localId);

      // 创建新的列表并添加新图片
      final List<String> updatedUrls = List<String>.from(initialUrls);
      updatedUrls.add(imageUrl);

      // 确保不超过最大数量
      return updatedUrls.take(maxCount).toList();
    } catch (e) {
      debugPrint('选择并上传多张图片失败: $e');
      MTFToast.showToast(msg: '选择图片失败，请重试');
      return initialUrls;
    }
  }

  /// 显示图片来源选择弹窗
  ///
  /// [context] - 上下文
  ///
  /// 返回用户选择的图片来源，取消则返回null
  static Future<ImageSource> _showImageSourceDialog(
      BuildContext context) async {
    return await showModalBottomSheet<ImageSource>(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              // 拍照选项
              InkWell(
                onTap: () {
                  Navigator.pop(context, ImageSource.camera);
                },
                child: Container(
                  height: 56,
                  width: double.infinity,
                  alignment: Alignment.center,
                  child: const Text(
                    '开始拍摄',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ),
              const Divider(height: 1, color: Color(0xFFEEEEEE)),

              // 相册选项
              InkWell(
                onTap: () {
                  Navigator.pop(context, ImageSource.gallery);
                },
                child: Container(
                  height: 56,
                  width: double.infinity,
                  alignment: Alignment.center,
                  child: const Text(
                    '相册上传',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ),
              const Divider(height: 1, color: Color(0xFFEEEEEE)),

              // 取消选项
              InkWell(
                onTap: () {
                  Navigator.pop(context, null);
                },
                child: Container(
                  height: 56,
                  width: double.infinity,
                  alignment: Alignment.center,
                  child: const Text(
                    '取消',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ),

              // 底部装饰线
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }
}

/// 图片来源枚举
enum ImageSource {
  /// 相机
  camera,

  /// 相册
  gallery,
}
