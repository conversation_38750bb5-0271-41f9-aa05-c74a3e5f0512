import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/api/base/base_api_service.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import '../../../../utils/common_params_utils.dart';
import '../api/base/base_api_service.dart';
import 'halal_image_utils.dart';
import '../utils/api_utils.dart';

/// 示例标签组件
/// 在图片左上角显示"示例"标签
class HalalExampleTag extends StatelessWidget {
  const HalalExampleTag({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      child: Container(
        decoration: const BoxDecoration(
          color: Color(0xFFFFE14D),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8),
            bottomRight: Radius.circular(12),
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        child: const Text(
          '示例',
          style: TextStyle(fontSize: 12, color: Color(0xFF333333)),
        ),
      ),
    );
  }
}

/// 清真许可证上传页面
class HalalCertificateUploadPage extends StatefulWidget {
  /// 当前已有的图片URL列表（如果有）
  final List<String> initialImageUrls;

  /// 任务ID
  final int taskId;
  final int snapshotId;

  /// 默认示例图片URL
  static const String defaultExampleImageUrl =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/69011673f81038f2/halal_license_example.jpg';

  const HalalCertificateUploadPage({
    Key key,
    this.initialImageUrls = const [],
    @required this.taskId,
    @required this.snapshotId,
  }) : super(key: key);

  @override
  _HalalCertificateUploadPageState createState() =>
      _HalalCertificateUploadPageState();
}

class _HalalCertificateUploadPageState
    extends State<HalalCertificateUploadPage> {
  /// 当前选择的图片URL列表
  List<String> _imageUrls = [];

  /// 是否正在上传
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    // 初始化图片列表
    _imageUrls = List<String>.from(widget.initialImageUrls);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('上传清真许可证'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0.5,
        centerTitle: true,
        leading: TextButton(
          child: const Text('返回', style: TextStyle(color: Colors.black)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInstructionText(),
                    const SizedBox(height: 24),
                    _buildImageUploadArea(),
                  ],
                ),
              ),
            ),
          ),
          _buildBottomButton(),
        ],
      ),
    );
  }

  /// 构建说明文本
  Widget _buildInstructionText() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: const [
        Text(
          '请上传清真许可证（或其他同等效力证明）',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Text(
          '需真实有效、清晰完整、在有效期内',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  /// 构建图片上传区域
  Widget _buildImageUploadArea() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧图片区域（示例图或第一张上传图）
        Expanded(
          flex: 1,
          child: _buildFirstImageContainer(),
        ),
        const SizedBox(width: 12),
        // 右侧图片区域（上传按钮或第二张上传图）
        Expanded(
          flex: 1,
          child: _buildSecondImageContainer(),
        ),
      ],
    );
  }

  /// 构建第一个图片容器（左侧）
  Widget _buildFirstImageContainer() {
    final bool hasFirstImage = _imageUrls.isNotEmpty;

    return Container(
      height: 160,
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          // 图片内容
          _buildFirstImageContent(hasFirstImage),

          // 示例标签（仅当没有上传第一张图片时显示）
          _buildExampleTag(hasFirstImage),

          // 删除按钮（仅当上传了第一张图片时显示）
          _buildFirstDeleteButton(hasFirstImage),
        ],
      ),
    );
  }

  /// 构建第一张图片内容
  Widget _buildFirstImageContent(bool hasFirstImage) {
    final String imageUrl = hasFirstImage
        ? _imageUrls[0]
        : HalalCertificateUploadPage.defaultExampleImageUrl;
    return _buildImageContent(imageUrl);
  }

  /// 构建示例标签
  Widget _buildExampleTag(bool hasFirstImage) {
    return Visibility(
      visible: !hasFirstImage,
      child: const HalalExampleTag(),
    );
  }

  /// 构建第一张图片的删除按钮
  Widget _buildFirstDeleteButton(bool hasFirstImage) {
    return Visibility(
      visible: hasFirstImage,
      child: _buildDeleteButton(() => _handleDeleteImage(0)),
    );
  }

  /// 构建第二个图片容器（右侧）
  Widget _buildSecondImageContainer() {
    final bool hasSecondImage = _imageUrls.length > 1;

    return GestureDetector(
      onTap: hasSecondImage ? null : _handleSelectImage,
      child: Container(
        height: 160,
        width: double.infinity, // 确保宽度填满父容器
        decoration: BoxDecoration(
          color: const Color(0xFFF5F6FA),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
          children: [
            // 图片内容或上传占位符
            _buildSecondImageContent(hasSecondImage),

            // 删除按钮（仅当上传了第二张图片时显示）
            _buildSecondDeleteButton(hasSecondImage),
          ],
        ),
      ),
    );
  }

  /// 构建第二张图片内容
  Widget _buildSecondImageContent(bool hasSecondImage) {
    return hasSecondImage
        ? _buildImageContent(_imageUrls[1])
        : _buildUploadPlaceholder();
  }

  /// 构建第二张图片的删除按钮
  Widget _buildSecondDeleteButton(bool hasSecondImage) {
    return Visibility(
      visible: hasSecondImage,
      child: _buildDeleteButton(() => _handleDeleteImage(1)),
    );
  }

  /// 构建图片内容
  Widget _buildImageContent(String imageUrl) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: const Color(0xFFF5F6FA),
        child: _buildImageBySource(imageUrl),
      ),
    );
  }

  /// 根据图片来源构建图片
  Widget _buildImageBySource(String imageUrl) {
    final bool isNetworkImage = _isImageFromNetwork(imageUrl);

    if (isNetworkImage) {
      return Image.network(
        imageUrl,
        fit: BoxFit.contain, // 使用contain确保图片完整显示
        errorBuilder: (_, __, ___) => _buildErrorWidget(),
      );
    } else {
      return Image.file(
        File(imageUrl.replaceFirst('file://', '')),
        fit: BoxFit.contain, // 使用contain确保图片完整显示
        errorBuilder: (_, __, ___) => _buildErrorWidget(),
      );
    }
  }

  /// 构建删除按钮
  Widget _buildDeleteButton(VoidCallback onPressed) {
    return Positioned(
      bottom: 10,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: GestureDetector(
            onTap: onPressed,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
                // Icon(Icons.delete_outline, size: 16, color: Colors.black),
                // SizedBox(width: 4),
                Text(
                  '删除',
                  style: TextStyle(fontSize: 14, color: Colors.black),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建上传占位符
  Widget _buildUploadPlaceholder() {
    // 计算已上传的图片数量
    final int uploadedCount = _imageUrls.length;
    final int maxCount = 2;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // const Icon(
          //   Icons.add_photo_alternate_outlined,
          //   size: 48,
          //   color: Color(0xFF999999),
          // ),
          Image(
            width: 48,
            height: 48,
            image: AdvancedNetworkImage(
                'https://p1.meituan.net/waimaieassets/d68ce17316fb0578e84cdc7dc87a2f391666.png'),
          ),
          const SizedBox(height: 12),
          const Text(
            '点击上传图片',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF999999),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '上传图片 $uploadedCount/$maxCount',
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF999999),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片加载错误显示
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: const [
          // Icon(Icons.error_outline, color: Colors.grey, size: 32),
          // SizedBox(height: 8),
          Text(
            '图片加载失败',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// 构建底部保存按钮
  Widget _buildBottomButton() {
    // 只有当至少上传了一张图片时，保存按钮才启用
    final bool canSave = _imageUrls.isNotEmpty && !_isUploading;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: canSave ? _handleSave : null,
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return const Color(0xFFCCCCCC);
              }
              return Colors.black;
            },
          ),
          foregroundColor: MaterialStateProperty.resolveWith<Color>(
            (Set<MaterialState> states) {
              if (states.contains(MaterialState.disabled)) {
                return Colors.white.withOpacity(0.38);
              }
              return Colors.white;
            },
          ),
          padding: MaterialStateProperty.all<EdgeInsetsGeometry>(
            const EdgeInsets.symmetric(vertical: 12),
          ),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
          ),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  /// 构建按钮内容
  Widget _buildButtonContent() {
    return _isUploading
        ? const SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        : const Text(
            '保存',
            style: TextStyle(fontSize: 16, color: Colors.white),
          );
  }

  /// 处理选择图片
  Future<void> _handleSelectImage() async {
    try {
      setState(() => _isUploading = true);

      // 使用新的HalalImageUtils工具类安全地选择并上传多张图片
      final List<String> updatedUrls =
          await HalalImageUtils.safeSelectAndUploadMultipleImages(
        context,
        initialUrls: _imageUrls,
        maxCount: 2,
      );

      // 更新图片列表
      if (updatedUrls != null && updatedUrls.isNotEmpty) {
        setState(() {
          _imageUrls = updatedUrls;
          _isUploading = false;
        });
      } else {
        setState(() => _isUploading = false);
      }
    } catch (e) {
      setState(() => _isUploading = false);
      MTFToast.showToast(msg: '选择图片失败，请重试');
      debugPrint('选择图片失败: $e');
    }
  }

  /// 处理删除图片
  void _handleDeleteImage(int index) {
    setState(() {
      if (index < _imageUrls.length) {
        // 删除指定索引的图片
        _imageUrls.removeAt(index);
      }
    });
  }

  /// 上传清真证书
  Future<bool> _uploadHalalCertificate() async {
    // 获取环境信息
    final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
    final String deviceUuid = envInfo['uuid'] as String ?? 'unknown';
    final String platform = envInfo['platform'] as String ?? 'h5';
    final String app = envInfo['app'] as String ?? 'unknown';

    Map<String, dynamic> commonParams =
        CommonParamsUtils.getInstance().getCommonParams() ?? {};

    final token = commonParams['token'];
    
    // 获取API Host
    final apiHost = await ApiUtils.getApiHost();
    final path =
        '/api/v2/kaidian/restful/qualification/w/halalCertificate/${widget.taskId ?? 0}?token=$token'; // /?token=${commonParams['token']}
    // 构建请求参数
    final Map<String, dynamic> params = {
      'taskId': widget.taskId ?? 0,
      'snapshotId': widget.snapshotId ?? 0,
      'uuid': deviceUuid,
      'platform': platform,
      'app': app,
      'urlList': _imageUrls,
      'action': 'save',
    };
    params.addAll(commonParams);

    // 发送请求
    final response = await ruzhuPostApi(
      baseUrl: apiHost,
      path: path,
      params: params,
      token: token,
    );
    if (response != null && response.code == 0) {
      debugPrint("上传成功:$response");
      return true;
    }
    return false;
  }

  /// 处理保存
  Future<void> _handleSave() async {
    if (_imageUrls.isEmpty) {
      MTFToast.showToast(msg: '请至少上传一张清真证书');
      return;
    }

    setState(() => _isUploading = true);

    try {
      // 调用上传清真证书接口
      final bool success = await _uploadHalalCertificate();

      if (success) {
        MTFToast.showToast(msg: '保存成功');
        // 返回选择的图片URL列表
        Navigator.of(context).pop(_imageUrls);
      } else {
        MTFToast.showToast(msg: '保存失败，请重试');
        setState(() => _isUploading = false);
      }
    } catch (e) {
      debugPrint('保存清真证书时出错: $e');
      MTFToast.showToast(msg: '保存失败，请重试');
      setState(() => _isUploading = false);
    }
  }

  /// 判断图片是否来自网络
  bool _isImageFromNetwork(String url) {
    return url != null &&
        !url.startsWith('file://') &&
        !url.startsWith('/') &&
        (url.startsWith('http://') || url.startsWith('https://'));
  }
}
