import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';

class RuzhuBlockCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const RuzhuBlockCard({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(key: key, content: content, messageId: messageId, realMessageId: realMessageId);

  @override
  _RuzhuBlockCardState createState() => _RuzhuBlockCardState();
}

class _RuzhuBlockCardState extends BaseBlockWidgetState<RuzhuBlockCard> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget buildContentView() {
    return const SizedBox();
  }
}