import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/next_card_util.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import '../../../../model/house_keeper_message_page_model.dart';

class RuzhuContinueCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const RuzhuContinueCard({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(key: key, content: content, messageId: messageId, realMessageId: realMessageId);

  @override
  _RuzhuContinueCardState createState() => _RuzhuContinueCardState();
}

class _RuzhuContinueCardState extends BaseBlockWidgetState<RuzhuContinueCard> {
  Map<String, dynamic> _contentMap;

  int status;

  @override
  void initState() {
    super.initState();
    if(widget.content?.isNotEmpty ?? false) {
      _contentMap = json.decode(widget.content) ?? {};
      status = _contentMap['status'] ?? 1;
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 0), // 修改这里的 padding
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          _buildButtons(),
        ],
      ),
    );
  }

  Widget _buildButtons() {
    String buttonText;
    bool isEnabled = true;

    int status = RuzhuCardStatusManager().getStatus(widget.messageId, this.status);

    if (status == 1) {
      buttonText = '继续开店任务';
      isEnabled = true;
    }else if (status == 3) {
      buttonText = '已过期';
      isEnabled = false;
    }

    if (status == 3) {
      return _buildDisableSingleButton(buttonText);
    } else {
      return Row(
        children: [
          Expanded(
            child: _buildElevatedButton(buttonText, isEnabled: isEnabled),
          ),
        ],
      );
    }
  }

  // 添加单个按钮的构建方法
  Widget _buildDisableSingleButton(String text) {
    return CustomButton(text: text, onPressed: null,);
  }

  Widget _buildElevatedButton(String text, {bool isEnabled = true}) {
    return SizedBox(
      height: 40,
      child: ElevatedButton(
        onPressed: () {
          if(!isEnabled) return;
          final params = CommonParamsUtils.getInstance().getCommonParams() ?? {};
          final candidateId = params['candidateId'];
          final token = params['token'];
          NextCardUtil.addNextCard(widget.messageId,
            model: widget.model, candidateId: candidateId, token: token);
        },
        style: ElevatedButton.styleFrom(
          primary: Colors.black,
          onSurface: Colors.grey, // 禁用时的颜色
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}