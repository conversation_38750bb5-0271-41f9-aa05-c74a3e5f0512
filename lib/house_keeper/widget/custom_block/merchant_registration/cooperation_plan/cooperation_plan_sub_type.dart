/// 营业执照子类型枚举
enum CooperationPlanSubType {
  filling, // 填写
  rejected, // 填写驳回
  suggested, // 建议修改
  modifying // 主动修改
}

///
CooperationPlanSubType getCooperationPlanSubType(int value) {
  switch (value) {
    case 1:
      return CooperationPlanSubType.filling;
    case 5:
      return CooperationPlanSubType.rejected;
    case 3:
      return CooperationPlanSubType.suggested;
    case 4:
      return CooperationPlanSubType.modifying;
    default:
      return CooperationPlanSubType.filling;
  }
}
