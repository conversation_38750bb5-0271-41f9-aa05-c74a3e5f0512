import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/next_card_util.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../api/services/cooperation_api_service.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import '../utils/api_utils.dart';
import 'cooperation_plan_sub_type.dart';
import '../api/api_service2.dart';

class CooperationPlanCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;
  const CooperationPlanCard(
      {Key key,
      @required String content,
      this.model,
      String messageId,
      int realMessageId})
      : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _CooperationPlanCardState createState() => _CooperationPlanCardState();
}

class _CooperationPlanCardState
    extends BaseBlockWidgetState<CooperationPlanCard> {
  // 环境类型常量
  static const String hostTypeTest = 'TEST';
  static const String hostTypeStage = 'STAGE';
  static const String hostTypeRelease = 'RELEASE';
  CooperationPlanSubType _subType = CooperationPlanSubType.filling;
  int _snapshotId = 0;
  int _status = 0;
  int _candidateId = 0;
  String _token = '';

  // URL常量
  static const String urlTest = 'https://shangjia.banma.test.sankuai.com';
  static const String urlStaging = 'https://shangjia.banma.st.sankuai.com';
  static const String urlProduction = 'https://shangjia.peisong.meituan.com';
  static const String coopPath = '/h5/entrant/index';
  static const String defaultScene = 'wm_dandian_app';

  // 上传中，防止重复点击
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  /// 处理选择合作方案按钮点击
  void _handleCooperationPlanSelect() {
    if (_isUploading) {
      return;
    }
    _isUploading = true;

    // 显示加载提示
    EasyLoading.show(status: '加载中...');
    // 获取通用参数
    final params = CommonParamsUtils.getInstance().getCommonParams();
    final String candidateIdStr = params['candidateId'];
    final String userId = params['acctId'];
    final String token = params['token'];

    if (candidateIdStr?.isEmpty ?? true) {
      MTFToast.showToast(msg: 'candidateId不能为空');
      _isUploading = false;
      return;
    }

    _candidateId = int.tryParse(candidateIdStr) ?? 0;
    _token = params['token'];

    final apiService = CooperationApiService();

    // 链式调用处理异步操作
    apiService
        .getCooperationSelector(
      candidateId: candidateIdStr,
      token: token,
    )
        .then((coopMigrateCanary) {
      if (coopMigrateCanary) {
        return _getDeliveryCoopUrl(
          taskId: candidateIdStr,
          userId: userId,
          token: token,
        );
      } else {
        return getShopCoopUrl(_candidateId,
            token: token, snapshotId: _snapshotId);
      }
    }).then((targetUrl) {
      EasyLoading.dismiss();
      _isUploading = false;
      if (targetUrl.isEmpty) {
        MTFToast.showToast(msg: '获取跳转链接失败');
        return null;
      }
      return RouteUtils.open(targetUrl);
    }).then((_) {
      widget.model?.updateCard(widget.realMessageId);
      _addNextCard();
    }).catchError((e) {
      _isUploading = false;
      EasyLoading.dismiss();
      debugPrint('选择合作方案失败: $e');
      MTFToast.showToast(msg: '选择合作方案失败，请重试$e');
    }).whenComplete(() {
      _isUploading = false;
    });
  }

  /// 添加下一张卡片的通用方法
  Future<void> _addNextCard() async {
    try {
      NextCardUtil.addNextCard(widget.messageId,
          model: widget.model, candidateId: _candidateId, token: _token);
    } catch (e) {
      debugPrint('添加下一张卡片失败: $e');
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 24),
          // 选择按钮
          _buildButtons()
        ],
      ),
    );
  }

  /// 获取不同环境的基础URL
  Future<String> _getBaseUrl() async {
    try {
      final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
      final String hostType = envInfo['hostType'] ?? hostTypeRelease;

      switch (hostType) {
        case hostTypeTest:
          return urlTest;
        case hostTypeStage:
          return urlStaging;
        case hostTypeRelease:
        default:
          return urlProduction;
      }
    } catch (e) {
      return urlProduction; // 出错时返回生产环境
    }
  }

  Widget _buildModifyingButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '取消',
            primary: false,
            onPressed: () async {
              await MerchantApiService().saveCardSnapshot(
                candidateId: _candidateId,
                snapshotId: _snapshotId,
              );
              widget.model?.updateCard(widget.realMessageId);
              NextCardUtil.addNextCard(
                widget.messageId,
                model: widget.model,
                candidateId: _candidateId,
                token: _token,
                expectedStatus: 2,
              );
            },
          ),
        ),
        const SizedBox(width: 12), // 按钮之间的水平间距
        Expanded(
          child: CustomButton(
            text: '去修改',
            onPressed: _handleCooperationPlanSelect,
          ),
        ),
      ],
    );
  }

  // 构建主动修改状态按钮
  Widget _buildRejectedButtons() {
    return CustomButton(
      text: '去修改',
      onPressed: _handleCooperationPlanSelect,
    );
  }

  // 构建主动修改状态按钮
  Widget _buildExpiredButton() {
    return const CustomButton(
      text: '已过期',
      onPressed: null,
      primary: false, // 可以根据具体业务逻辑控制按钮状态
    );
  }

  // 构建主动修改状态按钮
  Widget _buildFilledButton() {
    return const CustomButton(
      text: '已填写',
      onPressed: null,
      primary: false, // 可以根据具体业务逻辑控制按钮状态
    );
  }

  // 构建主动修改状态按钮
  Widget _buildFillingButton() {
    return CustomButton(
      text: '选择合作方案',
      onPressed: _handleCooperationPlanSelect,
    );
  }

  // 构建底部按钮
  Widget _buildButtons() {
    int _status =
        RuzhuCardStatusManager().getStatus(widget.messageId, this._status);
    // 首先检查状态
    if (_status == 3) {
      return _buildExpiredButton();
    } else if (_status == 2) {
      return _buildFilledButton();
    } else if (_status == 0) {
      return const CustomButton(
        text: '已修改',
        onPressed: null,
        primary: false, // 可以根据具体业务逻辑控制按钮状态
      );
    }

    // 如果不是特殊状态，则根据subType显示对应按钮
    switch (_subType) {
      case CooperationPlanSubType.modifying:
        return _buildModifyingButtons();
      case CooperationPlanSubType.rejected:
        return _buildRejectedButtons();
      case CooperationPlanSubType.filling:
      default:
        return _buildFillingButton();
    }
  }

  Future<String> _getDeliveryCoopUrl({
    @required String taskId,
    String userId,
    String token,
  }) async {
    if (taskId == null || taskId.isEmpty) {
      return '';
    }

    try {
      final baseUrl = await _getBaseUrl();
      final params = <String, String>{
        'scene': defaultScene, // 使用常量
        'entryType': 'assistant',
        'taskId': taskId,
        'hideNativeNavBar': '1',
      };

      if (userId?.isNotEmpty == true) {
        params['mtUserId'] = userId;
      }
      if (token?.isNotEmpty == true) {
        params['wmbSettleToken'] = token;
      }

      final queryString = Uri(queryParameters: params).query;
      return '$baseUrl$coopPath?$queryString';
    } catch (e) {
      return '';
    }
  }

  /// 获取新版选择器URL
  Future<String> getShopCoopUrl(int taskId,
      {@required String token, @required int snapshotId}) async {
    final apiHost = await ApiUtils.getApiHost();
    return '$apiHost/kd?hideNativeNavBar=1#/pages/cooperation/index?taskId=$taskId&scene=assistant&token=$token&snapshotId=$snapshotId';
  }

  @override
  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;
      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;

      setState(() {
        _snapshotId = data['snapshotId'] as int ?? 0;
        _subType = getCooperationPlanSubType(
            data['subType'] is int ? data['subType'] : 1);

        _status = data['status'] as int ?? 0;
        _candidateId = data['candidateId'] as int ?? 0;
      });
    } catch (e) {
      MTFToast.showToast(msg: '解析操作卡片数据时出错: $e');
    }
  }
}
