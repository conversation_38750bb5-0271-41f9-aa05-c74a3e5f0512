import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import '../api/services/bd_qrcode_api_service.dart';
import '../api/base/base_api_service.dart';
import '../models/response/image_model_info.dart';
import '../utils/next_card_util.dart';
import '../api/api_service2.dart';

/// 推荐人数据模型
class BDUserData {
  final String name;

  BDUserData({this.name});

  factory BDUserData.fromJson(Map<String, dynamic> json) {
    return BDUserData(
      name: json['name'] as String,
    );
  }
}

/// 商户绑定推荐人组件
class MerchantBindBDWidget extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;
  const MerchantBindBDWidget({
    Key key,
    String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _MerchantBindBDWidgetState createState() => _MerchantBindBDWidgetState();
}

/// subType=1:填写 subType=2:用于后端区分填写卡片的文案（是第一次填写，还是已绑定的修改）  subType=3 建议修改   subType=4 主动修改
/// 生成枚举类型
enum BindBDSubType {
  fill,
  toModified,
  suggestModify,
  activeModify,
}

/// status=0:"去修改"禁用 status=1:正常操作 status=2:"已填写"禁用 status=3:"已过期"禁用
enum BindBDStatus {
  disabledModify, // 0: 禁用修改
  normal, // 1: 正常操作
  filled, // 2: 已填写
  expired, // 3: 已过期
}

class _MerchantBindBDWidgetState
    extends BaseBlockWidgetState<MerchantBindBDWidget> {
  // 卡片相关数据
  String _cardId;
  int _subType;
  int _candidateId;
  int _snapshotID;
  int status;
  BDUserData _data;
  BindBDSubType _bindBDsubType = BindBDSubType.fill;
  bool _isSkipping = false; // 添加跳过状态标记

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(MerchantBindBDWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  /// 解析卡片内容数据
  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;
      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;

      setState(() {
        _cardId = data['cardId'] as String;
        _subType = data['subType'] as int;
        _candidateId = data['candidateId'] as int;
        _snapshotID = data['snapshotId'] as int;

        status = data['status'] as int ?? 1; // 默认为1（正常状态）

        if (_subType == 1) {
          _bindBDsubType = BindBDSubType.fill;
        } else if (_subType == 2) {
          _bindBDsubType = BindBDSubType.toModified;
        } else if (_subType == 3) {
          _bindBDsubType = BindBDSubType.suggestModify;
        } else if (_subType == 4) {
          _bindBDsubType = BindBDSubType.activeModify;
        }

        if (data['data'] != null) {
          _data = BDUserData.fromJson(data['data']);
        }
      });
      debugPrint(
          '解析绑定推荐人卡片数据成功：$_cardId, $_subType, $_candidateId, $_snapshotID, $status, $_data');
    } catch (e) {
      MTFToast.showToast(msg: '解析绑定推荐人卡片数据时出错: $e');
    }
  }

  BindBDStatus getStatusEnum(final int statusValue) {
    int status =
        RuzhuCardStatusManager().getStatus(widget.messageId, statusValue);
    BindBDStatus statusEnum;
    if (status != null && status >= 0 && status < BindBDStatus.values.length) {
      statusEnum = BindBDStatus.values[status];
    } else {
      statusEnum = BindBDStatus.normal; // 默认为正常状态
    }
    return statusEnum;
  }

  @override
  Widget buildContentView() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: _buildCardContent(),
    );
  }

  /// 构建卡片内容
  Widget _buildCardContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 14),
        // 底部按钮
        _buildButtons(),
      ],
    );
  }

  /// 构建底部按钮
  Widget _buildButtons() {
    BindBDStatus _status = getStatusEnum(status);

    // 使用switch语句根据卡片状态构建不同的按钮
    switch (_status) {
      case BindBDStatus.disabledModify:
        // 禁用修改状态 - 显示置灰的"去修改"按钮
        return _buildDisabledModifyButton();

      case BindBDStatus.expired:
        // 已过期状态 - 显示置灰的"已过期"按钮
        return _buildExpiredButton();

      case BindBDStatus.filled:
        // 已填写状态 - 显示置灰的"已填写"按钮
        return _buildFilledButton();

      case BindBDStatus.normal:
        // 正常状态 - 根据subType显示对应按钮
        return _buildNormalStatusButtons();

      default:
        // 默认情况 - 返回空容器
        return Container();
    }
  }

  /// 构建正常状态下的按钮
  Widget _buildNormalStatusButtons() {
    // 根据subType显示对应按钮
    switch (_bindBDsubType) {
      case BindBDSubType.suggestModify:
        // 建议修改状态 - 显示"更换推荐人"和"更换营业执照"按钮
        return _buildSuggestModifyButtons();

      case BindBDSubType.activeModify:
        // 主动修改状态 - 显示"取消修改"和"去修改"按钮
        return _buildActiveModifyButtons();

      case BindBDSubType.fill:
        return _buildFillButtons();
      case BindBDSubType.toModified:
      default:
        // 默认情况 - 返回空容器
        return Container();
    }
  }

  /// 添加已过期按钮
  Widget _buildExpiredButton() {
    return const CustomButton(text: '已过期', primary: false);
  }

  /// 添加已填写禁用按钮
  Widget _buildFilledButton() {
    return const CustomButton(text: '已填写', primary: false);
  }

  /// 构建禁用的去修改按钮
  Widget _buildDisabledModifyButton() {
    return const CustomButton(text: '去修改', primary: false);
  }

  /// 构建填写状态按钮
  Widget _buildFillButtons() {
    // 根据不同的状态构建不同的UI
    final bool isSuggestModify = _bindBDsubType == BindBDSubType.suggestModify;
    final String skipText = isSuggestModify ? "暂不绑定 >" : "暂无推荐人，跳过 >";

    return Column(
      children: [
        // 第一行按钮
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: "相册上传",
                onPressed: () => _handleChooseImage(),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomButton(
                text: "扫一扫",
                onPressed: () => _handleScanQRCode(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // 第二行跳过按钮
        Center(
          child: GestureDetector(
            onTap: _isSkipping ? null : _handleSkip, // 防止重复点击
            child: Text(
              _isSkipping ? "处理中..." : skipText,
              style: TextStyle(
                fontSize: 14,
                color: _isSkipping
                    ? const Color(0xFFCCCCCC)
                    : const Color(0xFF999999),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建建议修改状态按钮
  Widget _buildSuggestModifyButtons() {
    return Column(
      children: [
        // 第一行按钮
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: "更换推荐人",
                onPressed: () => _handleChangeBD(),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomButton(
                text: "更换营业执照",
                onPressed: () => _handleChangeLicense(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // 第二行跳过按钮
        Center(
          child: GestureDetector(
            onTap: _isSkipping ? null : _handleSkip, // 防止重复点击
            child: Text(
              _isSkipping ? "处理中..." : "暂不绑定 >",
              style: TextStyle(
                fontSize: 14,
                color: _isSkipping ? Color(0xFFCCCCCC) : Color(0xFF999999),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建主动修改状态按钮
  Widget _buildActiveModifyButtons() {
    return Container(
      width: double.infinity,
      child: Column(
        // crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: "取消修改",
                  primary: false,
                  onPressed: () => _handleCancelModify(),
                ),
              ),
              const Padding(padding: EdgeInsets.only(left: 16)),
              Expanded(
                child: CustomButton(
                  text: "去修改",
                  onPressed: () => _handleGoModify(),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  /// 处理跳过操作
  Future<void> _handleSkip() async {
    if (_isSkipping) return; // 防止重复点击
    setState(() {
      _isSkipping = true; // 设置跳过状态为处理中
    });
    try {
      // 调用跳过BD信息的异步请求方法
      BDQRcodeApiService apiService = BDQRcodeApiService();
      Map<String, dynamic> params =
          CommonParamsUtils.getInstance().getCommonParams();

      final candidateId = int.tryParse(params['candidateId']?.toString() ?? '');
      final bool success = await apiService.postSkipBindBD(
          candidateId: candidateId, token: params['token']);
      if (success) {
        widget.model?.updateCard(widget.realMessageId);

        NextCardUtil.addNextCard(
          widget.messageId,
          model: widget.model,
          candidateId: candidateId ?? 0,
          token: params['token'],
          expectedStatus: 2,
        );
      } else {
        MTFToast.showToast(msg: '跳过失败，请重试');
      }
    } catch (e) {
      debugPrint('处理跳过操作时出错: $e');
      MTFToast.showToast(msg: '处理跳过操作时出错: $e');
    } finally {
      setState(() {
        _isSkipping = false; // 重置跳过状态
      });
    }
  }

  /// 处理更换推荐人
  void _handleChangeBD() {
    NextCardUtil.addNextCard(widget.messageId,
        model: widget.model,
        candidateId: _candidateId ?? 0,
        specifyCardId: "1100");
  }

  /// 处理更换营业执照
  void _handleChangeLicense() {
    NextCardUtil.addNextCard(widget.messageId,
        model: widget.model,
        candidateId: _candidateId ?? 0,
        specifyCardId: "2200");
  }

  /// 处理取消修改
  void _handleCancelModify() async {
    await MerchantApiService().saveCardSnapshot(
      candidateId: _candidateId,
      snapshotId: _snapshotID,
    );
    widget.model?.updateCard(widget.realMessageId);
    NextCardUtil.addNextCard(
      widget.messageId,
      model: widget.model,
      candidateId: _candidateId ?? 0,
      expectedStatus: 2,
    );
  }

  /// 处理去修改
  void _handleGoModify() {
    NextCardUtil.addNextCard(widget.messageId,
        model: widget.model,
        candidateId: _candidateId ?? 0,
        specifyCardId: "1100",
        specifySubCardId: 2);
  }

  /// 从相册选择图片
  Future<void> _handleChooseImage() async {
    final result = await KNB.chooseImage(
      type: 'gallery',
      // source: 'album', //1.6.4 后 type 字段更换为 source，type 依然有效，推荐使用 source
      sceneToken: 'dj-cd246c6d99ef0f5a',
      returnType: 'localId',
      count: 1,
    );
    KNB.sendLog(
        text: 'KNB上传的图片信息=' + (result != null ? jsonEncode(result) : '空'));

    if (result == null || result['errorCode'] != 0) {
      if (result != null && result['errorCode'] == 543) {
        MTFToast.showToast(msg: '请授予相册权限');
      } else {
        MTFToast.showToast(msg: '选择图片失败，请重试');
      }
      return;
    }

    final photos = RuZhuImageModel.fromJson(result);
    if (photos?.photoInfos?.isEmpty ?? true) {
      return;
    }

    final String localId = photos.photoInfos[0].localId;
    if (localId?.isEmpty ?? true) {
      return;
    }

    final ImageUploadApi _apiService = ImageUploadApi();
    final qrCodeUrl = await _apiService.uploadImageToCdn(filePath: localId);
    if (qrCodeUrl?.isEmpty ?? true) {
      return;
    }

    MTFToast.showToast(msg: '上传成功，正在识别...');

    final BDQRcodeApiService bdqRcodeApiService = BDQRcodeApiService();
    final Map<String, dynamic> params =
        CommonParamsUtils.getInstance().getCommonParams();

    final response = await bdqRcodeApiService.parseQrCodeByUrl(
        qrCodeUrl: qrCodeUrl,
        candidateId: _candidateId,
        token: params['token']);
    if (response == null) return;

    final bool confirmed = await _showConfirmationDialog(
      context,
      bdName: response['misName'],
    );

    if (!confirmed) {
      debugPrint('用户选择重新绑定');
      // 与产品确认，重新绑定不需要弹出下一张卡片
      // NextCardUtil.addNextCard(widget.messageId,
      //     model: widget.model,
      //     candidateId: _candidateId ?? 0,
      //     specifyCardId: "1100",
      //     specifySubCardId: 1);
      return;
    }

    final BDQRcodeApiService service = BDQRcodeApiService();
    final bool success = await service.postModifyBdInfo(
        snapshotId: _snapshotID,
        taskId: _candidateId,
        link: response['qrCodeInfo'],
        token: params['token']);

    if (success) {
      widget.model?.updateCard(widget.realMessageId);

      NextCardUtil.addNextCard(widget.messageId,
          model: widget.model,
          candidateId: _candidateId ?? 0,
          expectedStatus: 2);
    }
  }

  /// 扫一扫功能
  Future<void> _handleScanQRCode(BuildContext context) async {
    try {
      final result = await KNB.use('scanQRCode', {
        'needResult': 1,
        'sceneToken': 'dj-8bb37f291981c7c7',
      });

      KNB.sendLog(
          text: 'bd二维码扫码结果=' + (result != null ? jsonEncode(result) : '空'));

      debugPrint('扫码结果: $result');

      if (result != null && result['errorCode'] == 0) {
        // 正确解析扫码结果
        String qrContent;

        // 根据平台类型处理不同的返回格式
        if (Platform.isAndroid) {
          // Android 设备返回格式: data.qrCode
          if (result['data'] != null &&
              result['data'] is Map &&
              result['data']['qrCode'] != null) {
            qrContent = result['data']['qrCode'] as String;
            debugPrint('Android设备扫码结果: $qrContent');
          }
        } else if (Platform.isIOS) {
          // iOS 设备返回格式: scanResult
          if (result['scanResult'] != null) {
            qrContent = result['scanResult'] as String;
            debugPrint('iOS设备扫码结果: $qrContent');
          }
        }

        // 兼容旧版API或其他情况
        // if (qrContent == null || qrContent.isEmpty) {
        //   if (result['resultStr'] != null) {
        //     qrContent = result['resultStr'] as String;
        //     debugPrint('使用备选方式获取扫码结果: $qrContent');
        //   }
        // }

        if (qrContent != null && qrContent.isNotEmpty) {
          debugPrint('解析到二维码内容: $qrContent');
          // 根据二维码链接解析对应的bd名称
          BDQRcodeApiService bdqRcodeApiService = BDQRcodeApiService();
          Map<String, dynamic> params =
              CommonParamsUtils.getInstance().getCommonParams();
          final response = await bdqRcodeApiService.parseQrCodeByLink(
              qrCodeUrl: qrContent,
              candidateId: _candidateId,
              token: params['token']);
          if (response == null) return;
          // 显示确认弹窗
          final bool confirmed = await _showConfirmationDialog(
            context,
            bdName: response['misName'],
          );

          if (confirmed) {
            // 用户点击了"确认无误"，执行绑定操作
            BDQRcodeApiService service = BDQRcodeApiService();
            Map<String, dynamic> params =
                CommonParamsUtils.getInstance().getCommonParams();
            service
                .postModifyBdInfo(
                    snapshotId: _snapshotID,
                    taskId: _candidateId,
                    link: response['qrCodeInfo'],
                    token: params['token'])
                .then((value) {
              if (value) {
                widget.model?.updateCard(widget.realMessageId);

                NextCardUtil.addNextCard(
                  widget.messageId,
                  model: widget.model,
                  candidateId: _candidateId ?? 0,
                  expectedStatus: 2,
                );
              }
            });
          } else {
            // TODO: nextCard()  用户点击了"重新绑定"，不执行任何操作
            debugPrint('用户选择重新绑定');
            NextCardUtil.addNextCard(widget.messageId,
                model: widget.model,
                candidateId: _candidateId ?? 0,
                specifyCardId: "1100",
                specifySubCardId: 1);
          }
        } else {
          MTFToast.showToast(msg: '未获取到有效的二维码内容');
        }
      } else {
        MTFToast.showToast(msg: '扫码失败或已取消');
      }
    } catch (e) {
      debugPrint('扫码出错: $e');
      MTFToast.showToast(msg: '扫码失败，请重试');
    }
  }

  /// 显示确认弹窗
  Future<bool> _showConfirmationDialog(BuildContext context,
      {String bdName}) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    const Center(
                      child: Text(
                        '请确认您绑定的推荐人信息',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // 推荐人信息
                    Row(
                      children: [
                        const Text(
                          '推荐人',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF666666),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          bdName ?? '',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF333333),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // 按钮
                    Row(
                      children: [
                        // 重新绑定按钮
                        Expanded(
                          child: TextButton(
                            onPressed: () {
                              Navigator.of(context).pop(false);
                            },
                            style: TextButton.styleFrom(
                              backgroundColor: const Color(0xFFF5F5F5),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: const Text(
                              '重新绑定',
                              style: TextStyle(
                                fontSize: 16,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),

                        // 确认无误按钮
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.of(context).pop(true);
                            },
                            style: ElevatedButton.styleFrom(
                              primary: Colors.black,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: const Text(
                              '确认无误',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ) ??
        false; // 如果用户以其他方式关闭对话框，默认返回false
  }
}
