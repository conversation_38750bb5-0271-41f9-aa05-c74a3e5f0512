import 'package:flutter/material.dart';
import 'id_card_type.dart';

class OtherIdTypeSheet extends StatelessWidget {
  const OtherIdTypeSheet({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildOption(
            context: context,
            type: IdCardType.passport,
          ),
          _buildOption(
            context: context,
            type: IdCardType.hkMacauPermit,
          ),
          _buildOption(
            context: context,
            type: IdCardType.taiwanPermit,
          ),
          _buildCancelButton(context),
        ],
      ),
    );
  }

  Widget _buildOption({
    @required BuildContext context,
    @required IdCardType type,
  }) {
    return Column(
      children: [
        InkWell(
          onTap: () => Navigator.pop(context, type),
          child: Container(
            height: 56,
            alignment: Alignment.center,
            child: Text(
              getIdCardTypeName(type),
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF222222),
              ),
            ),
          ),
        ),
        Container(
          height: 1,
          color: const Color(0xFFEEEEEE),
        ),
      ],
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 56,
      margin: const EdgeInsets.all(16),
      child: TextButton(
        onPressed: () => Navigator.pop(context),
        style: TextButton.styleFrom(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
        ),
        child: const Text(
          '取消',
          style: TextStyle(
            fontSize: 16,
            color: Color(0xFF222222),
          ),
        ),
      ),
    );
  }
}
