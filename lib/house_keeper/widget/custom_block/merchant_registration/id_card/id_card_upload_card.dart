import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import 'id_card_type.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'id_card_sub_type.dart'; // 将枚举移到单独的文件
import 'other_id_type_sheet.dart';
import '../utils/api_utils.dart';
import '../utils/next_card_util.dart';
import '../api/services/id_card_api_service.dart';
import '../api/base/base_api_service.dart';
import '../api/api_service2.dart';

/// 身份证上传卡片
class IdCardUploadCard extends RuzhuBaseBlockWidget {
  const IdCardUploadCard({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
          key: key,
          content: content,
          messageId: messageId,
          realMessageId: realMessageId,
        );

  final HouseKeeperMessagePageModel model;

  @override
  _IdCardUploadCardState createState() => _IdCardUploadCardState();
}

class _IdCardUploadCardState extends BaseBlockWidgetState<IdCardUploadCard> {
  // 移除 _frontUrl 和 _backUrl
  List<String> _urls = []; // 新增 url 数组
  IdCardType _currentType = IdCardType.idCard;
  IdCardSubType _subType = IdCardSubType.filling;
  String _idCard;
  String _name;
  int _candidateId = 0;
  int _status = 0;
  String _token = '';
  int _snapshotId;
  String _realFrontUrl;
  String _realBackUrl;
  String _phone;

  bool _showOtherDocsButton = false; // 新增

  @override
  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) {
        return;
      }

      final data = json.decode(widget.content);
      if (data == null || data is! Map) {
        return;
      }

      _candidateId = data['candidateId'] as int ?? 0;
      _status = data['status'] ?? 0;
      _snapshotId = data['snapshotId'] as int ?? 0;
      _subType = getIdCardSubType(data['subType'] as int ?? 1);

      final innerData = data['data'] as Map<String, dynamic>;
      if (innerData == null) {
        return;
      }

      setState(() {
        // 解析 url 数组
        final urlList = innerData['url'] as List<dynamic>;
        _urls = urlList?.map((url) => url as String)?.toList() ?? [];

        _idCard = innerData['idCard'] as String ?? '';
        _name = innerData['name'] as String ?? '';
        _phone = innerData['phone'] as String ?? '';
      });
    } catch (e) {
      debugPrint('解析身份证数据出错: $e');
    }
  }

  @override
  void initState() {
    super.initState();

    Map<String, dynamic> params =
        CommonParamsUtils.getInstance().getCommonParams();
    _token = params['token'] ?? '';
    _parseContent();

    // 只在特定子类型时调用_checkBdInfo
    if (_status == 1 &&
        (_subType == IdCardSubType.filling ||
            _subType == IdCardSubType.modifying)) {
      _checkBdInfo();
    }

    _updateRealImageUrls();
  }

  // 新增方法：更新真实图片地址
  Future<void> _updateRealImageUrls() async {
    // 如果 _urls 为空，直接返回
    if (_urls == null || _urls.isEmpty) {
      return;
    }

    try {
      // 获取第一张图片的真实URL
      if (_urls[0]?.isNotEmpty == true) {
        _getRealImageUrl(_urls[0]).then((value) {
          _realFrontUrl = value;
          setState(() {});
        });
      }

      // 获取第二张图片的真实URL（如果存在）
      if (_urls.length > 1 && _urls[1]?.isNotEmpty == true) {
        _getRealImageUrl(_urls[1]).then((value) {
          _realBackUrl = value;
          setState(() {});
        });
      }
    } catch (e) {
      MTFToast.showToast(msg: '获取真实图片URL失败: $e');
    }
  }

  // 修改获取真实图片URL的方法
  Future<String> _getRealImageUrl(String originalUrl) {
    if (originalUrl?.isEmpty ?? true) return null;

    return ImageUploadApi()
        .getMtcloudImageUrl(
            imageUrl: originalUrl,
            taskId: _candidateId,
            messageId: widget.realMessageId,
            token: _token,
            snapshotId: _snapshotId)
        .then((response) => response?.code == 0 ? response.data : null);
  }

  /// 添加下一张卡片的通用方法
  Future<void> _addNextCard(
      {String specifyCardId,
      int specifySubCardId,
      int expectedStatus = 3}) async {
    try {
      NextCardUtil.addNextCard(
        widget.messageId,
        model: widget.model,
        candidateId: _candidateId,
        token: _token,
        specifyCardId: specifyCardId,
        specifySubCardId: specifySubCardId,
        expectedStatus: expectedStatus,
      );
    } catch (e) {
      debugPrint('添加下一张卡片失败: $e');
    }
  }

  // 新增方法
  void _checkBdInfo() {
    // 先查询二维码ID类型灰度配置
    IdCardApiService().queryQrCodeIDTypeGray().then((isGrayEnabled) {
      if (isGrayEnabled) {
        // 查询法人场景信息
        IdCardApiService().getLegalPersonScene().then((scene) {
          setState(() {
            // 只有当场景值等于9时，才显示其他证件按钮
            _showOtherDocsButton = scene == 9;
          });
        }).catchError((e) {
          debugPrint('查询法人场景信息失败: $e');
          setState(() {
            _showOtherDocsButton = false;
          });
        });
      }
    }).catchError((e) {
      debugPrint('查询灰度配置失败: $e');
      setState(() {
        _showOtherDocsButton = false;
      });
    });
  }

  // 处理证件上传
  Future<void> _handleIdCardUpload({IdCardType type}) async {
    try {
      final host = await ApiUtils.getApiHost();

      // 构建基础URL
      final Uri uri = Uri.parse('$host/kd');

      // 构建查询参数
      final Map<String, String> queryParams = {
        'taskId': _candidateId.toString(),
        'scene': 'assistant',
        'token': _token,
        'snapshotId': _snapshotId.toString(),
      };

      // 如果有证件类型，添加到参数中
      if (type != null) {
        queryParams['typeLevelTwo'] = getIdCardTypeValue(type).toString();
      }
      // 构建完整URL
      final String fullUrl =
          '$uri?hideNativeNavBar=1#/pages/legal_person_new/index?${Uri(queryParameters: queryParams).query}';

      RouteUtils.open(fullUrl).then((value) {
        if (value != null && value.code == RouteResult.ok) {
          KNB.sendLog(
              text: '身份证信息 result=' + (value != null ? value.toString() : '空'));
          if (value.data != null) {
            final data = jsonDecode(value.data);
            if (data['type'] == 'save') {
              widget.model?.updateCard(widget.realMessageId);
              _addNextCard(expectedStatus: 2);
            }
          }
        }
      });
    } catch (e) {
      MTFToast.showToast(msg: '页面跳转失败: $e');
    }
  }

  // 处理其他证件类型选择
  Future<void> _handleOtherIdType() async {
    final result = await showModalBottomSheet<IdCardType>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => OtherIdTypeSheet(),
    );

    if (result != null) {
      setState(() {
        _currentType = result;
      });
      // 将选择的类型直接作为参数传递
      _handleIdCardUpload(type: result);
    }
  }

  // 构建主动修改状态按钮
  Widget _buildExpiredButton() {
    return const CustomButton(
      text: '已过期',
      onPressed: null,
      primary: false, // 可以根据具体业务逻辑控制按钮状态
    );
  }

  /// 构建操作按钮列表
  /// 根据状态和子类型返回不同的按钮组合
  /// 构建操作按钮列表
  Widget _buildOperationButtons() {
    int _status =
        RuzhuCardStatusManager().getStatus(widget.messageId, this._status);

    // 处理特殊状态
    if (_status == 3) {
      return _buildExpiredButton();
    } else if (_status == 2) {
      return _buildFilledButton();
    } else if (_status == 0) {
      return const CustomButton(
        text: '已修改',
        onPressed: null,
        primary: false, // 可以根据具体业务逻辑控制按钮状态
      );
    } else if (_status == 1) {
      return _buildSubTypeButtons();
    }
    return Container();
  }

  Widget _buildSubTypeButtons() {
    switch (_subType) {
      case IdCardSubType.filling:
        return _buildFillingButtons();
      case IdCardSubType.rejected:
        return _buildModifyButton();
      case IdCardSubType.suggested:
        return _buildSuggestedButtons();
      case IdCardSubType.modifying:
        return _buildModifyingButton();
      default:
        return Container();
    }
  }

  // 添加已填写状态按钮
  Widget _buildFilledButton() {
    return const CustomButton(
      text: '已填写',
      onPressed: null,
      primary: false,
    );
  }

  /// 填写状态按钮组合
  Widget _buildFillingButtons() {
    return Row(
      children: [
        Visibility(
          visible: _showOtherDocsButton,
          child: Expanded(child: _buildOtherDocsButton()),
        ),
        Visibility(
          visible: _showOtherDocsButton,
          child: const SizedBox(width: 12),
        ),
        Expanded(
          child: CustomButton(
            text: '上传身份证照片',
            onPressed: () => {_handleIdCardUpload(type: IdCardType.idCard)},
          ),
        ),
      ],
    );
  }

  /// 建议修改状态按钮组合
  Widget _buildSuggestedButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '坚持使用',
            primary: false,
            onPressed: () async {
              await MerchantApiService().saveCardSnapshot(
                candidateId: _candidateId,
                snapshotId: _snapshotId,
              );
              widget.model?.updateCard(widget.realMessageId);
              _addNextCard(expectedStatus: 2);
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(child: _buildModifyButton()),
      ],
    );
  }

  /// 主动修改状态按钮组合
  Widget _buildModifyingButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: '取消',
                primary: false,
                onPressed: () async {
                  await MerchantApiService().saveCardSnapshot(
                    candidateId: _candidateId,
                    snapshotId: _snapshotId,
                  );
                  widget.model?.updateCard(widget.realMessageId);
                  _addNextCard(expectedStatus: 2);
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(child: _buildModifyButton()),
          ],
        ),
        Visibility(
          visible: _showOtherDocsButton,
          child: Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Center(
              child: GestureDetector(
                onTap: () {
                  // specifyCardId：3100，身份卡片 specifySubCardId：1，填写类
                  _addNextCard(specifyCardId: '3100', specifySubCardId: 1);
                }, // 防止重复点击
                child: const Text(
                  '使用其他证件 >',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF999999),
                  ),
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  /// 基础按钮组件
  Widget _buildOtherDocsButton() {
    return CustomButton(
      text: '使用其他证件',
      primary: false,
      onPressed: _handleOtherIdType,
    );
  }

  Widget _buildModifyButton() {
    return CustomButton(
      text: '去修改',
      onPressed: () => {_handleIdCardUpload()},
    );
  }

  Widget _buildExampleImages() {
    return _buildExampleImage(
      title: '',
      imagePath:
          'https://s3plus.meituan.net/v1/mss_14c672f716b54ef88185e01064deaa20/dist/images/legal_person_card/card_front_eg_3x.png', // 替换为实际的示例图路径
    );
  }

  Widget _buildExampleImage({
    @required String title,
    @required String imagePath,
  }) {
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF999999),
              ),
            ),
            const SizedBox(height: 8),
            AspectRatio(
              aspectRatio: 309 / 210,
              child: Center(
                // 添加 Center
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Image.network(
                    // 使用 Image.network 替代 DecorationImage
                    imagePath,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ],
        ),
        Positioned(
          top: 32,
          left: 0, // 改为 right: 0
          child: Container(
            decoration: const BoxDecoration(
              color: Color(0xB7000000),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8), // 修改为左上角
                bottomRight: Radius.circular(12), // 修改为右下角
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            child: const Text(
              '示例',
              style: TextStyle(
                fontSize: 12,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget buildContentView() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _buildContentChildren(),
      ),
    );
  }

  List<Widget> _buildContentChildren() {
    final List<Widget> children = [];

    // 根据是否有上传图片显示不同内容
    if (_hasUploadedImages) {
      children.add(_buildUploadedImages());
      children.add(const SizedBox(height: 16));
      children.add(_buildIdCardInfo());
    } else {
      children.add(_buildExampleImages());
    }

    // 添加按钮部分
    children.add(const SizedBox(height: 16));
    children.add(_buildOperationButtons());

    return children;
  }

  bool get _hasUploadedImages => _urls.isNotEmpty;

  Widget _buildUploadedImages() {
    // 如果有两张图片，使用 Row 并列展示
    if (_urls.length > 1) {
      return Row(
        children: [
          Expanded(
            child: _buildUploadedImage(
              title: '${getIdCardTypeName(_currentType)}正面',
              imageUrl: _realFrontUrl ?? '',
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildUploadedImage(
              title: '${getIdCardTypeName(_currentType)}反面',
              imageUrl: _realBackUrl ?? '',
            ),
          ),
        ],
      );
    }

    // 单张图片时保持原有的垂直布局
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: _urls.isNotEmpty,
          child: _buildUploadedImage(
            title: '${getIdCardTypeName(_currentType)}正面',
            imageUrl: _realFrontUrl ?? '',
          ),
        ),
        Visibility(
          visible: _urls.length > 1 && _currentType != IdCardType.passport,
          child: Padding(
            padding: const EdgeInsets.only(top: 16),
            child: _buildUploadedImage(
              title: '${getIdCardTypeName(_currentType)}反面',
              imageUrl: _realBackUrl ?? '',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadedImage({
    @required String title,
    @required String imageUrl,
  }) {
    // 如果图片链接为空，直接返回空容器
    if (imageUrl == null || imageUrl.isEmpty) {
      return Container(
        height: 105,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: AspectRatio(
            aspectRatio: 309 / 210,
            child: Image.network(
              imageUrl,
              fit: BoxFit.cover,
            ),
          ),
        ),
      ],
    );
  }

  // 新增身份证信息展示方法
  Widget _buildIdCardInfo() {
    bool hasAnyInfo =
        (_name?.isNotEmpty == true) || (_idCard?.isNotEmpty == true);
    if (!hasAnyInfo) {
      return Container();
    }

    List<Widget> infoRows = [];

    if (_name?.isNotEmpty == true) {
      infoRows.add(_buildInfoRow('姓名', _name));
      infoRows.add(const SizedBox(height: 8));
    }

    if (_idCard?.isNotEmpty == true) {
      infoRows.add(_buildInfoRow('身份证号', _idCard));
      infoRows.add(const SizedBox(height: 8));
    }

    if (_phone?.isNotEmpty == true) {
      infoRows.add(_buildInfoRow('手机号', _phone));
      infoRows.add(const SizedBox(height: 8));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: infoRows,
    );
  }

  // 信息行展示
  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        SizedBox(
          width: 70,
          height: 20, // 设置固定高度
          child: Align(
            alignment: Alignment.centerLeft, // 左对齐但垂直居中
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF222222),
              ),
            ),
          ),
        ),
        Expanded(
          child: Align(
            alignment: Alignment.centerLeft, // 左对齐但垂直居中
            child: Text(
              value ?? '',
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF222222),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
