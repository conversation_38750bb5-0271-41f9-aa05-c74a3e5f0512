/// 身份证子类型枚举
enum IdCardSubType {
  filling, // 填写
  rejected, // 填写驳回
  suggested, // 建议修改
  modifying // 主动修改
}

/// 获取身份证子类型
IdCardSubType getIdCardSubType(int value) {
  switch (value) {
    case 1:
      return IdCardSubType.filling;
    case 5:
      return IdCardSubType.rejected;
    case 3:
      return IdCardSubType.suggested;
    case 4:
      return IdCardSubType.modifying;
    default:
      return IdCardSubType.filling;
  }
}

/// 获取身份证子类型名称
String getIdCardSubTypeName(IdCardSubType type) {
  switch (type) {
    case IdCardSubType.filling:
      return '填写';
    case IdCardSubType.rejected:
      return '填写驳回';
    case IdCardSubType.suggested:
      return '建议修改';
    case IdCardSubType.modifying:
      return '主动修改';
    default:
      return '填写';
  }
}
