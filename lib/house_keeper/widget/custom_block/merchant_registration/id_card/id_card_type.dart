/// 证件类型枚举
enum IdCardType {
  idCard, // 身份证
  passport, // 护照
  hkMacauPermit, // 港澳通行证
  taiwanPermit // 台胞证
}

/// 获取证件类型
IdCardType getIdCardType(int value) {
  switch (value) {
    case 0:
      return IdCardType.idCard;
    case 24:
      return IdCardType.passport;
    case 25:
      return IdCardType.hkMacauPermit;
    case 26:
      return IdCardType.taiwanPermit;
    default:
      return IdCardType.idCard;
  }
}

/// 获取证件类型对应的整数值
int getIdCardTypeValue(IdCardType type) {
  switch (type) {
    case IdCardType.idCard:
      return 0;
    case IdCardType.passport:
      return 24;
    case IdCardType.hkMacauPermit:
      return 25;
    case IdCardType.taiwanPermit:
      return 26;
    default:
      return 0;
  }
}

/// 获取证件类型名称
String getIdCardTypeName(IdCardType type) {
  switch (type) {
    case IdCardType.idCard:
      return '身份证';
    case IdCardType.passport:
      return '护照';
    case IdCardType.hkMacauPermit:
      return '港澳居民往来内地通行证';
    case IdCardType.taiwanPermit:
      return '台湾居民往来大陆通行证';
    default:
      return '身份证';
  }
}
