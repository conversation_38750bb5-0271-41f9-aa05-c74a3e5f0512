import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/next_card_util.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'poi_pic_modal/position_modal.dart'; // 添加导入
import 'api/api_service2.dart';

class PositionCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const PositionCard({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
          key: key,
          content: content,
          messageId: messageId,
          realMessageId: realMessageId,
        );

  @override
  _PositionCardState createState() => _PositionCardState();
}

class _PositionCardState extends BaseBlockWidgetState<PositionCard> {
  Map<String, dynamic> _contentMap;
  int status;

  @override
  void initState() {
    super.initState();
    _contentMap = json.decode(widget.content);
  }

  // 构建主动修改状态按钮
  Widget _buildExpiredButton() {
    return const CustomButton(
      text: '已过期',
      onPressed: null,
      primary: false, // 可以根据具体业务逻辑控制按钮状态
    );
  }

  // 构建主动修改状态按钮
  Widget _buildFilledButton() {
    return const CustomButton(
      text: '已填写',
      onPressed: null,
      primary: false, // 可以根据具体业务逻辑控制按钮状态
    );
  }

  Widget _buildButtons() {
    status = _contentMap['status'] ?? 1;
    final _status =
        RuzhuCardStatusManager().getStatus(widget.messageId, status);
    // 首先检查状态
    if (_status == 3) {
      return _buildExpiredButton();
    } else if (_status == 2) {
      return _buildFilledButton();
    } else if (_status == 0) {
      return const CustomButton(
        text: '去修改',
        onPressed: null,
        primary: false, // 可以根据具体业务逻辑控制按钮状态
      );
    } else if (_status == 1) {
      return _buildSubTypeButtons();
    }

    return const SizedBox.shrink();
  }

  /// 添加下一张卡片的通用方法
  Future<void> _addNextCard({int expectedStatus = 3}) async {
    try {
      final params = CommonParamsUtils.getInstance().getCommonParams();
      NextCardUtil.addNextCard(widget.messageId,
          model: widget.model,
          token: params['token'],
          candidateId: _contentMap['candidateId'],
          expectedStatus: expectedStatus);
    } catch (e) {
      debugPrint('添加下一张卡片失败: $e');
    }
  }

  // 构建主动修改状态按钮
  Widget _buildModifyingButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '取消',
            primary: false,
            onPressed: () async {
              await MerchantApiService().saveCardSnapshot(
                candidateId: _contentMap['candidateId'],
                snapshotId: _contentMap['snapshotId'],
              );
              widget.model?.updateCard(widget.realMessageId);
              _addNextCard(expectedStatus: 2);
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(child: _buildModifyButton())
      ],
    );
  }

  // 构建驳回状态按钮
  Widget _buildModifyButton() {
    return CustomButton(
      text: '去修改',
      onPressed: () => _showPositionModal(),
    );
  }

  // 构建驳回状态按钮
  Widget _buildFillingButtons() {
    return CustomButton(
      text: '确认店铺地址',
      onPressed: () => _showPositionModal(),
    );
  }

  // 构建驳回状态按钮
  Widget _buildSuggestedButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '坚持使用',
            onPressed: () async {
              await MerchantApiService().saveCardSnapshot(
                candidateId: _contentMap['candidateId'],
                snapshotId: _contentMap['snapshotId'],
              );
              widget.model?.updateCard(widget.realMessageId);
              _addNextCard(expectedStatus: 2);
            },
          ),
        ),
        const SizedBox(width: 12), // 添加间距
        Expanded(
          child: CustomButton(
            text: '去修改',
            onPressed: () => _showPositionModal(),
          ),
        ),
      ],
    );
  }

  Widget _buildSubTypeButtons() {
    final _subType = _contentMap['subType'] ?? 1;
    switch (_subType) {
      case 4:
        return _buildModifyingButtons();
      case 3:
        return _buildSuggestedButtons();
      case 5:
        return _buildModifyButton();
      case 1:
        return _buildFillingButtons();
      default:
        return Container();
    }
  }

  // 封装 showModalBottomSheet 逻辑为单独函数
  Future<void> _showPositionModal() async {
    final data = _contentMap['data'] ?? {};
    final result = await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true, // 允许内容滚动
      builder: (context) => PositionModal(
        cityName: data['cityName'] ?? '',
        countyName: data['countyName'] ?? '',
        address: data['address'] ?? '',
        addressRemark: data['addressRemark'] ?? '',
        structuredAddress: data['structuredAddress'] ?? '',
        longitude: data['longitude'] ?? 0,
        latitude: data['latitude'] ?? 0,
        provinceName: data['provinceName'] ?? '',
        provinceId: data['provinceId'] ?? 0,
        cityId: data['cityId'] ?? 0,
        countyId: data['countyId'] ?? 0,
        candidateId: _contentMap['candidateId']?.toString() ?? '',
        cardCode: _contentMap['cardId']?.toString() ?? '',
        subType: _contentMap['subType'] ?? 1,
        snapshotId: _contentMap['snapshotId'] ?? 0,
      ),
    );

    // 处理返回结果
    if (result != null) {
      widget.model?.updateCard(widget.realMessageId);
      // 获取下一张卡片
      NextCardUtil.addNextCard(
        widget.messageId,
        model: widget.model,
        candidateId: _contentMap['candidateId'],
        expectedStatus: 2,
      );
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      padding: const EdgeInsets.only(top: 16),
      width: double.infinity,
      child: _buildButtons(),
    );
  }
}
