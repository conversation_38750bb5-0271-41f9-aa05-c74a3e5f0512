import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/api/api_service2.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/next_card_util.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';

import '../../../../model/house_keeper_message_page_model.dart';
import '../utils/api_utils.dart';

class SelectCategoryCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const SelectCategoryCard({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _SelectCategoryCardState createState() => _SelectCategoryCardState();
}

class _SelectCategoryCardState
    extends BaseBlockWidgetState<SelectCategoryCard> {
  Map<String, dynamic> _contentMap;

  int tagId;
  String tagName;
  int viceTagId;
  String viceTagName;

  int snapshotId;
  int candidateId;
  String cardId;
  int subType;
  int status;

  @override
  void initState() {
    super.initState();
    if (widget.content?.isNotEmpty ?? false) {
      _contentMap = json.decode(widget.content) ?? {};

      snapshotId = _contentMap['snapshotId'] ?? 0;
      candidateId = _contentMap['candidateId'] ?? 0;
      cardId = _contentMap['cardId'] ?? '';
      subType = _contentMap['subType'] ?? 0;
      status = _contentMap['status'] ?? 0;

      final data = _contentMap['data'];
      if (data != null) {
        tagId = data['tagId'];
        tagName = data['tagName'];
        viceTagId = data['viceTagId'];
        viceTagName = data['viceTagName'];
      }
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      padding: const EdgeInsets.symmetric(
          vertical: 12, horizontal: 0), // 修改这里的 padding
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 形式要求
          const Text(
            '您当前选择的经营品类是:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 8),

          Row(
            children: [
              Text(
                tagName ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF333333),
                  height: 1.5,
                ),
              ),
              const Spacer(),
              const Text(
                '-主营品类',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF333333),
                  height: 1.5,
                ),
              ),
            ],
          ),

          // 授权人要求
          Visibility(
            visible: viceTagName?.isNotEmpty ?? false,
            child: Column(
              children: [
                const SizedBox(height: 16),
                Row(
                  children: [
                    Text(
                      viceTagName ?? '',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF333333),
                      ),
                    ),
                    const Spacer(),
                    const Text(
                      '-辅营品类',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          _buildButtons(),
        ],
      ),
    );
  }

  Widget _buildButtons() {
    String buttonText;
    bool isEnabled = true;
    int status =
        RuzhuCardStatusManager().getStatus(widget.messageId, this.status);

    if (status == 0) {
      buttonText = '去修改';
      isEnabled = false;
    } else if (status == 1) {
      buttonText = '去修改';
      isEnabled = true;
    } else if (status == 2) {
      buttonText = '已填写';
      isEnabled = false;
    } else if (status == 3) {
      buttonText = '已过期';
      isEnabled = false;
    }

    // status为3时只有一个按钮
    if (status == 0 || status == 2 || status == 3) {
      return _buildDisableSingleButton(buttonText);
    } else {
      return Row(
        children: [
          Visibility(
            visible: subType != 5, // 驳回，只显示一个按钮
            child: Expanded(
              child: CustomButton(
                text: '取消',
                primary: false,
                onPressed: () async {
                  final params =
                      CommonParamsUtils.getInstance().getCommonParams() ?? {};
                  final token = params['token'];
                  await MerchantApiService().saveCardSnapshot(
                    candidateId: candidateId,
                    snapshotId: snapshotId,
                  );
                  widget.model?.updateCard(widget.realMessageId);
                  NextCardUtil.addNextCard(
                    widget.messageId,
                    model: widget.model,
                    candidateId: candidateId,
                    token: token,
                    expectedStatus: 2,
                  );
                },
              ),
            ),
          ),
          SizedBox(width: subType != 5 ? 12 : 0),
          Expanded(
            child: _buildElevatedButton(buttonText, isEnabled: isEnabled),
          ),
        ],
      );
    }
  }

  // 添加单个按钮的构建方法
  Widget _buildDisableSingleButton(String text) {
    return CustomButton(
      text: text,
      onPressed: null,
    );
  }

  Widget _buildElevatedButton(String text, {bool isEnabled = true}) {
    return CustomButton(
      text: text,
      primary: isEnabled,
      onPressed: isEnabled
          ? () async {
              try {
                // 等待获取 host
                final host = await ApiUtils.getApiHost();
                if (host?.isEmpty ?? true) {
                  MTFToast.showToast(msg: '获取域名失败');
                  return;
                }

                final params =
                    CommonParamsUtils.getInstance().getCommonParams() ?? {};

                final fullUrl = Uri.encodeFull(
                    '$host/kd?hideNativeNavBar=1#/pages/category/index'
                    '?taskId=$candidateId'
                    '&source=9'
                    '&scene=assistant'
                    '&tagType=1'
                    '&hideNativeNavBar=1&token=${params['token']}');

                RouteUtils.open(fullUrl).then((value) {
                  debugPrint('经营品类页面关闭，返回结果: $value');

                  if (value == null) return;

                  if (value.code == RouteResult.ok && value.data != null) {
                    final data = jsonDecode(value.data);
                    if (data is Map && data['type'] == "save") {
                      final result = Map.from(data['data']);
                      final tagId = result['tagId'] ?? 0;
                      final viceTagId = result['viceTagId'] ?? 0;
                      final cateName = result['cateName'] ?? [];

                      this.tagId = tagId;
                      this.viceTagId = viceTagId;

                      if (cateName.isNotEmpty) {
                        tagName = cateName[0];
                        if (cateName.length > 1) {
                          viceTagName = cateName[1];
                        }
                      }

                      MerchantApiService.submitTagInfo(
                              source: '6',
                              candidateId: candidateId,
                              cardCode: cardId,
                              snapshotID: snapshotId,
                              subType: subType,
                              tagId: tagId,
                              tagName: tagName,
                              viceTagId: viceTagId,
                              viceTagName: viceTagName)
                          .then((value) {
                        if (value?.success ?? false) {
                          widget.model.updateCard(widget.realMessageId);

                          NextCardUtil.addNextCard(
                            widget.messageId,
                            model: widget.model,
                            candidateId: candidateId,
                            token: params['token'],
                            expectedStatus: 2,
                          );
                        } else {
                          MTFToast.showToast(msg: '保存失败');
                        }
                      });
                    }
                  }
                });
              } catch (e) {
                MTFToast.showToast(msg: '页面跳转失败: $e');
              }
            }
          : null,
    );
  }
}
