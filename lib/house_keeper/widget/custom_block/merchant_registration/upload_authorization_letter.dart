import 'dart:convert';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:flutter/material.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:flutter/services.dart'; // 添加这行导入
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/poi_pic_modal/trademark_license_templete.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/next_card_util.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import 'utils/api_utils.dart';
import 'api/api_service2.dart';

class UploadAuthorizationLetterCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const UploadAuthorizationLetterCard({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _UploadAuthorizationLetterCardState createState() =>
      _UploadAuthorizationLetterCardState();
}

class _UploadAuthorizationLetterCardState
    extends BaseBlockWidgetState<UploadAuthorizationLetterCard> {
  Map<String, dynamic> _contentMap;
  String _uploadedImageUrl;
  int _currentPage = 0; // 添加当前页面索引状态

  @override
  void initState() {
    super.initState();
    _contentMap = json.decode(widget.content);
    final data = _contentMap['data'];
    if (data != null) {
      final url = data['url'];
      if (url != null) {
        if (url is List) {
          // 如果是数组，取第一个元素
          _uploadedImageUrl = url.isNotEmpty ? url[0].toString() : null;
        } else {
          // 如果是字符串，直接使用
          _uploadedImageUrl = url.toString();
        }
      }
    }
  }

  @override
  Widget buildContentView() {
    final int subType = _contentMap['subType'] ?? 1;

    // 创建子组件列表
    final List<Widget> children = <Widget>[];

    // 根据 subType 添加不同的组件
    if (subType == 1) {
      children.add(_buildUploadSection());
    } else {
      children.add(_buildPreviewSection());
    }

    // 添加间距和按钮
    children.add(const SizedBox(height: 24));
    children.add(_buildButtons(subType));

    return Container(
      padding: const EdgeInsets.symmetric(
          vertical: 12, horizontal: 0), // 修改这里的 padding
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  ///'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/auth_template.png'
  Widget _buildUploadSection() {
    return Container(
      width: double.infinity,
      height: 160,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Center(
          child: Stack(
            children: [
              // 背景图片
              Image.network(
                'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/auth_template.png',
                fit: BoxFit.contain,
                height: 160,
              ),
              // 示例标签 - 定位在图片的左上角
              Positioned(
                left: 0,
                top: 0,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: const BoxDecoration(
                    color: Color(0xB8000000), // 72% 的黑色
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: const Text(
                    '示例',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPreviewSection() {
    final data = _contentMap['data'] ?? {};
    final List<String> imageUrls = (data['url'] as List)?.cast<String>() ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图片轮播
        Container(
          height: 160,
          child: Stack(
            children: [
              PageView.builder(
                itemCount: imageUrls.length,
                onPageChanged: (index) {
                  // 添加页面切换回调
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(imageUrls[index]),
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              ),
              // 指示器
              Positioned(
                bottom: 8,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    imageUrls.length,
                    (index) => Container(
                      width: 8,
                      height: 8,
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index == _currentPage // 使用当前页面索引
                            ? const Color(0xFFFF6A00)
                            : Colors.black.withOpacity(0.5),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // 授权信息
        _buildInfoRow('品牌名称', data['brandNames'] ?? ''),
        _buildInfoRow('授权人名称', data['name'] ?? ''),
        _buildInfoRow('被授权人名称', data['authName'] ?? ''),
        _buildInfoRow('有效期', data['time'] ?? ''),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF222222), // 改为 222
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF222222), // 改为 222
                fontWeight: FontWeight.bold, // 添加加粗
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建已过期按钮
  Widget _buildExpiredButton() {
    return const CustomButton(text: '已过期', primary: false, onPressed: null);
  }

  /// 构建已过期按钮
  Widget _buildFilledButton() {
    return const CustomButton(text: '已填写', primary: false, onPressed: null);
  }

  /// 构建禁用的去修改按钮
  Widget _buildDisabledModifyButton() {
    return const CustomButton(
      text: '去修改',
      primary: false,
      onPressed: null,
    );
  }

  Widget _buildButtons(int subType) {
    int status = _contentMap['status'] ?? 1;
    status = RuzhuCardStatusManager().getStatus(widget.messageId, status);

    // 根据状态判断显示哪种按钮
    switch (status) {
      case 0: // 按钮为【去修改】置灰，不可点
        return _buildDisabledModifyButton();
      case 1: // 可以按按钮，正常按钮
        return _buildFillButton(subType);
      case 2: // 按钮为【已填写】置灰，不可点
        return _buildFilledButton();
      case 3: // 按钮为【已过期】置灰，不可点
        return _buildExpiredButton();
      default:
        // 默认情况，也显示填写按钮
        return const SizedBox.shrink();
    }
  }

  /// 构建填写按钮
  Widget _buildFillButton(int subType) {
    // 根据不同的subType返回对应的按钮组合
    switch (subType) {
      case 1: // 初始填写状态
        return Row(
          children: [
            Expanded(child: _buildDownloadButton()),
            const SizedBox(width: 12),
            Expanded(child: _buildUploadButton('上传授权书')),
          ],
        );
      case 5: // 审核驳回状态
        return _buildUploadButton('重新上传');
      case 3: // 建议修改状态
        return Row(
          children: [
            Expanded(child: _buildSkipButton('坚持使用')),
            const SizedBox(width: 12),
            Expanded(child: _buildUploadButton('重新上传')),
          ],
        );
      case 4: // 主动修改状态
        return Row(
          children: [
            Expanded(child: _buildSkipButton('取消修改')),
            const SizedBox(width: 12),
            Expanded(child: _buildUploadButton('重新上传')),
          ],
        );
      default: // 其他状态
        return const SizedBox.shrink();
    }
  }

  // 创建上传按钮
  Widget _buildUploadButton(String text) {
    return CustomButton(
      text: text,
      onPressed: () => _handleUpload(),
    );
  }

  // 创建下载模板按钮
  Widget _buildDownloadButton() {
    return CustomButton(
      text: '下载模板',
      primary: false,
      onPressed: () => _handleDownloadTemplate(),
    );
  }

  // 创建跳过按钮
  Widget _buildSkipButton(String text) {
    return CustomButton(
      text: text,
      primary: false,
      onPressed: () async {
        await MerchantApiService().saveCardSnapshot(
          candidateId: _contentMap['candidateId'],
          snapshotId: _contentMap['snapshotId'],
        );
        widget.model?.updateCard(widget.realMessageId);
        NextCardUtil.addNextCard(
          widget.messageId,
          model: widget.model,
          candidateId: _contentMap['candidateId'],
          expectedStatus: 2,
        );
      },
    );
  }

  // 封装按钮点击处理逻辑
  void _handleUpload() async {
    try {
      final apiHost = await ApiUtils.getApiHost();
      final uri = Uri.parse(apiHost);
      final domain = '${uri.scheme}://${uri.host}';
      final params = CommonParamsUtils.getInstance().getCommonParams() ?? {};
      final token = params['token'];
      final url =
          '$domain/kd?hideNativeNavBar=1#/pages/upload_brand_license/index?taskId=${_contentMap['candidateId']}&token=$token&source=9&scene=assistant&snapshotId=${_contentMap['snapshotId']}';

      // 使用 RouteUtils 打开页面
      RouteUtils.open(url).then((res) {
        if (res != null && res.code == RouteResult.ok) {
          widget.model?.updateCard(widget.realMessageId);
          NextCardUtil.addNextCard(
            widget.messageId,
            model: widget.model,
            candidateId: _contentMap['candidateId'],
            expectedStatus: 2,
          );
        }
      });
    } catch (e) {
      MTFToast.showToast(msg: '获取域名失败: $e');
    }
  }

  // 下载模板处理逻辑
  void _handleDownloadTemplate() async {
    // 复制链接到剪贴板
    await Clipboard.setData(const ClipboardData(
        text:
            'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/example-of-food-plaza-authorization.zip'));

    // 显示模板下载说明弹窗
    if (mounted) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        builder: (context) => const TrademarkLicenseTempleteModal(),
      );
    }
  }
}
