import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/contact/merchant_contact_person_card.dart';

/// 全局手机号验证状态管理类
class PhoneVerificationStateManager {
  /// 单例实例
  static final PhoneVerificationStateManager _instance = PhoneVerificationStateManager._internal();

  /// 工厂构造函数
  factory PhoneVerificationStateManager() => _instance;

  /// 内部构造函数
  PhoneVerificationStateManager._internal();

  /// 存储手机号和对应的验证状态
  final Map<String, PhoneVerificationStatus> _phoneStatusMap = {};

  /// 获取指定手机号的验证状态
  PhoneVerificationStatus getPhoneStatus(String phoneNumber) {
    return _phoneStatusMap[phoneNumber];
  }

  /// 设置状态为验证中
  void setVerifying(String phoneNumber) {
    if(phoneNumber?.isNotEmpty ?? false) {
      _phoneStatusMap[phoneNumber] = PhoneVerificationStatus.verifying;
    }
  }

  /// 设置状态为验证成功
  void setVerified(String phoneNumber) {
    if(phoneNumber?.isNotEmpty ?? false) {
      _phoneStatusMap[phoneNumber] = PhoneVerificationStatus.verified;
    }
  }

  /// 设置状态为验证失败
  void setVerifyFailed(String phoneNumber) {
    if(phoneNumber?.isNotEmpty ?? false) {
      _phoneStatusMap[phoneNumber] = PhoneVerificationStatus.failed;
    }
  }

  /// 重置状态
  void reset(String phoneNumber) {
    if(phoneNumber?.isNotEmpty ?? false) { 
      _phoneStatusMap[phoneNumber] = PhoneVerificationStatus.notVerified;
    }
  }

  /// 重置所有状态
  void resetAll() {
    _phoneStatusMap.clear();

    _phoneStatusMap.forEach((phone, _) {
      reset(phone);
    });
  }
}
