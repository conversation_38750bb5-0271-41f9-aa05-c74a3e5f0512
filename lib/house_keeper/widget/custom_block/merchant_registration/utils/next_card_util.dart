import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/api/api_service2.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';

import '../progress/task_progress_widget.dart';

typedef InsertNextCardSuccess = void Function();

/// 添加下一张卡片的工具类
class NextCardUtil {

  static bool _isLoading = false;

  /// 弹出下一张卡片
  ///
  /// [model] - 消息页面模型
  /// [candidateId] - 候选ID
  /// [cardId] - 卡片ID（可选）
  /// [specifyCardId] - 指定卡片ID（可选）
  /// [specifySubCardId] - 指定子卡片ID（可选）
  /// [token] - 认证令牌（可选）
  static Future<void> addNextCard(
    String messageId, {
    @required HouseKeeperMessagePageModel model,
    @required int candidateId,
    String cardId,
    String specifyCardId,
    int specifySubCardId,
    String token,
    bool isNeedUpdateProgress = true,
    int expectedStatus = 3,
  }) async {
    if (_isLoading) {
      return;
    }

    if (model == null || candidateId == null) {
      MTFToast.showToast(msg: '参数错误');
      return;
    }

    _isLoading = true;
    EasyLoading.show();

    try {
      // 调用获取下一张卡片接口
      final response = await MerchantApiService.getNextCard(
        candidateId: candidateId,
        cardId: cardId,
        specifyCardId: specifyCardId,
        specifySubCardId: specifySubCardId,
        token: token,
      );

      _isLoading = false;
      EasyLoading.dismiss();

      if (response.success && (response.data?.data?.isNotEmpty ?? false)) {
        // 更新任务进度
        if (isNeedUpdateProgress) {
          RouteUtils.publish(ruzhuActionUpdateProgress);
        }

        if (messageId != '0') {
          // 更新上一张卡片状态
          RuzhuCardStatusManager()
              .updateStatus(messageId: messageId, status: expectedStatus);
        } else {
          // 批量设置卡片状态为过期
          RuzhuCardStatusManager().batchSetExpired();
        }

        // 创建非流式消息
        model.insertMessage(HouseKeeperMessage(
          content: response.data.data,
          type: HouseKeeperMessageType.text,
          messageId: response.data.messageId,
        ));
      } else {
        if (response.message?.isNotEmpty ?? false) {
          MTFToast.showToast(msg: response.message ?? '');
        }
      }
    } catch (e) {
      debugPrint('获取下一张卡片异常：$e');
      _isLoading = false;
      EasyLoading.dismiss();
    }
  }

  /// 弹出特定类型卡片
  ///
  /// [model] - 消息页面模型
  /// [candidateId] - 候选ID
  /// [type] - 卡片类型
  static Future<void> getCardByType(
    String messageId, {
    @required HouseKeeperMessagePageModel model,
    int type,
    bool isNeedUpdateProgress = true,
    int expectedStatus = 3,
  }) async {
    if (model == null) {
      MTFToast.showToast(msg: '参数错误');
      return;
    }

    try {
      // 调用获取下一张卡片接口
      final response = await MerchantApiService.getCardByType(
        type: type,
      );

      if (response.success && (response.data?.data?.isNotEmpty ?? false)) {
        // 更新任务进度
        if (isNeedUpdateProgress) {
          RouteUtils.publish(ruzhuActionUpdateProgress);
        }

        if (messageId != '0') {
          // 更新上一张卡片状态
          RuzhuCardStatusManager()
              .updateStatus(messageId: messageId, status: expectedStatus);
        } else {
          // 批量设置卡片状态为过期
          RuzhuCardStatusManager().batchSetExpired();
        }

        // 创建非流式消息
        model.insertMessage(HouseKeeperMessage(
          content: response.data.data,
          type: HouseKeeperMessageType.text,
          messageId: response.data.messageId,
        ));
      } else {
        if (response.message?.isNotEmpty ?? false) {
          MTFToast.showToast(msg: response.message ?? '');
        }
      }
    } catch (e) {
      debugPrint('获取下一张卡片异常：$e');
    }
  }


  static void resetLoading() {
    _isLoading = false;
  }

}
