import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../api/base/base_api_service.dart';
import '../models/response/image_model_info.dart';

/// 图片选择回调函数类型
typedef ImagePickerCallback = void Function(String imageUrl);

/// 商户注册图片工具类
class MerchantImageUtil {
  /// 图片来源类型
  static const String sourceGallery = 'gallery';
  static const String sourceCamera = 'camera';

  /// 默认场景token
  static const String defaultSceneToken = 'dj-cd246c6d99ef0f5a';

  /// API服务实例
  static final ImageUploadApi _apiService = ImageUploadApi();

  /// 选择本地图片
  ///
  /// [source] - 图片来源，可以是 gallery(相册) 或 camera(相机)
  /// [sceneToken] - 场景标识，默认为 defaultSceneToken
  /// [count] - 选择图片数量，默认为1
  ///
  /// 返回图片的localId，失败返回null
  static Future<String> chooseLocalImage({
    @required String source,
    String sceneToken = defaultSceneToken,
    int count = 1,
  }) async {
    try {
      final result = await KNB.chooseImage(
          sceneToken: sceneToken,
          returnType: 'localId',
          count: count,
          // source: source, // 1.6.4 后 type 字段更换为 source，type 依然有效，推荐使用 source
          type: source);

      // 用户取消选择
      if (result['errorCode'] == 543) {
        return null;
      }

      // 解析返回结果
      final photos = RuZhuImageModel.fromJson(result);
      if (photos?.photoInfos?.isNotEmpty == true) {
        return photos.photoInfos[0].localId;
      }

      return null;
    } catch (error) {
      MTFToast.showToast(msg: '选择图片失败');
      return null;
    }
  }

  /// 显示图片选择弹窗
  ///
  /// [context] - 上下文
  /// [sceneToken] - 场景标识，默认为 defaultSceneToken
  /// [count] - 选择图片数量，默认为1
  /// [cameraText] - 拍照按钮文本，默认为"拍照"
  /// [galleryText] - 相册按钮文本，默认为"相册"
  /// [cancelText] - 取消按钮文本，默认为"取消"
  /// [uploadType] - 上传类型，可选值为"cdn"或"mtcloud"，默认为"cdn"
  ///
  /// 返回包含图片URL和上传类型的Map，取消或失败则返回null
  static void showImagePickerDialog(
    BuildContext context, {
    String sceneToken = defaultSceneToken,
    int count = 1,
    int candidateId,
    String token = "",
    String cameraText = "拍照",
    String galleryText = "相册",
    String cancelText = "取消",
    String uploadType = "cdn",
    ImagePickerCallback callback,
  }) {
    // 封装选择图片并上传的函数
    void pickImageAndUpload(String source) {
      chooseLocalImage(
        source: source,
        sceneToken: sceneToken,
        count: count,
      ).then((localId) {
        if (localId == null) {
          if (callback != null) {
            callback(null);
          }
          return;
        }

        EasyLoading.show(status: '上传中...');

        final Future<String> uploadFuture =
            uploadType.toLowerCase() == "mtcloud"
                ? _apiService.uploadImageToMtcloud(
                    filePath: localId,
                    token: token,
                    params: {
                      'taskId': candidateId,
                    },
                  )
                : _apiService.uploadImageToCdn(filePath: localId);

        uploadFuture.then((imageUrl) {
          if (callback != null) {
            callback(imageUrl);
          }
          EasyLoading.dismiss();
        }).catchError((e) {
          MTFToast.showToast(msg: '上传图片失败');
          EasyLoading.dismiss();
          if (callback != null) {
            callback(null);
          }
        });
      }).catchError((e) {
        EasyLoading.dismiss();
        MTFToast.showToast(msg: '选择图片失败');
        if (callback != null) {
          callback(null);
        }
      });
    }

    showRooModalPopup(
      context: context,
      action1: cameraText,
      action1Callback: () => pickImageAndUpload(sourceCamera),
      action2: galleryText,
      action2Callback: () => pickImageAndUpload(sourceGallery),
      cancelAction: cancelText,
    );
  }

  /// 选择并上传图片（不显示弹窗）
  ///
  /// [source] - 图片来源，可以是 gallery(相册) 或 camera(相机)
  /// [sceneToken] - 场景标识，默认为 defaultSceneToken
  /// [count] - 选择图片数量，默认为1
  /// [uploadType] - 上传类型，可选值为"cdn"或"mtcloud"，默认为"cdn"
  ///
  /// 返回上传后的图片URL，取消或失败则返回null
  static Future<String> chooseAndUploadImage({
    @required String source,
    String sceneToken = defaultSceneToken,
    int count = 1,
    String uploadType = "cdn",
  }) async {
    try {
      // 选择图片
      final localId = await chooseLocalImage(
        source: source,
        sceneToken: sceneToken,
        count: count,
      );

      if (localId == null) {
        return null;
      }

      // 上传图片
      if (uploadType.toLowerCase() == "mtcloud") {
        return await _apiService.uploadImageToMtcloud(
          filePath: localId,
        );
      } else {
        return await _apiService.uploadImageToCdn(
          filePath: localId,
        );
      }
    } catch (error) {
      return null;
    }
  }
}
