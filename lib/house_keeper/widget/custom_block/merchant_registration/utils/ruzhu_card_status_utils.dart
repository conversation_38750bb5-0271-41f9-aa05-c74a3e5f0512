class RuzhuCardStatus {
  final int status;
  final Map<String, dynamic> data;

  RuzhuCardStatus({
    this.status,
    this.data,
  });
}

/// 卡片状态管理器
class RuzhuCardStatusManager {
  // 单例模式
  static final RuzhuCardStatusManager _instance = RuzhuCardStatusManager._internal();
  factory RuzhuCardStatusManager() => _instance;
  RuzhuCardStatusManager._internal();

  // 存储消息状态的Map
  final Map<String, RuzhuCardStatus> _statusMap = {};

  // 根据id获取状态
  int getStatus(String messageId, int status) {
    if(_statusMap[messageId] == null) {
      updateStatus(messageId: messageId, status: status);
      return status;
    }
    return _statusMap[messageId].status;
  }

  // 根据id获取data
  Map<String, dynamic> getData(String messageId) {
    return _statusMap[messageId] != null ? _statusMap[messageId].data : {};
  }

  // 批量设置过期
  void batchSetExpired({String excludeMessageId}) {
    _statusMap.forEach((key, value) {
      if(key != excludeMessageId) {
        updateStatus(messageId: key, status: 3);
      }
    });
  }

  // 更新状态和数据
  int updateStatus({
    String messageId,
    int status,
    Map<String, dynamic> data,
  }) {

    final oldData = _statusMap[messageId];
    if (oldData != null) {
      int oldStatus = oldData.status;
      // 已经是最终态，不再更新
      if (oldStatus == 2 || oldStatus == 3){
        return oldStatus;
      }
    }

    _statusMap[messageId] = RuzhuCardStatus(
      status: status,
      data: data ?? {},
    );
    return status;
  }

  // 清除指定消息状态
  void removeStatus(String messageId) {
    _statusMap.remove(messageId);
  }

  // 清除所有状态
  void clear() {
    _statusMap.clear();
  }
}