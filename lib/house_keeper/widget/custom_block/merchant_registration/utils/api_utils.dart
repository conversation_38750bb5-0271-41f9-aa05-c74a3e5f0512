import 'package:waimai_e_native_business/waimai_e_native_business.dart';

/// API工具类
///
/// 提供商户注册模块中API相关的通用工具方法
class ApiUtils {
  /// 获取当前环境的API Host
  ///
  /// 根据当前环境返回对应的API Host
  /// - 测试环境: http://kd.waimai.test.meituan.com
  /// - 预发布环境: https://kd.waimai.st.meituan.com
  /// - 正式环境: https://kd.meituan.com
  ///
  /// 返回值:
  /// - 成功时返回对应环境的API Host字符串
  /// - 获取环境信息失败时默认返回测试环境Host
  static Future<String> getApiHost() async {
    try {
      final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
      final String hostType = envInfo['hostType'];

      switch (hostType) {
        case 'QA':
          return 'https://kd.waimai.test.meituan.com';
        case 'TEST':
          return 'https://kd.waimai.test.meituan.com';
        case 'STAGE':
          return 'https://kd.waimai.st.meituan.com';
        case 'RELEASE':
          return 'https://kd.meituan.com';
        default:
          return 'https://kd.meituan.com'; // 默认返回线上域名
      }
    } catch (error) {
      return 'https://kd.meituan.com'; // 出错时返回线上域名
    }
  }
}
