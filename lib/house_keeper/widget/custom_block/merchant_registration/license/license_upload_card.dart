import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import 'license_description_drawer.dart';
import 'package:mtf_toast/mtf_toast.dart';
import '../common/image_upload_guide_bottom_sheet.dart';
import '../utils/api_utils.dart';
import '../api/base/base_api_service.dart';
import '../utils/next_card_util.dart';
import '../../../../utils/common_params_utils.dart';
import '../api/api_service2.dart';

/// 许可证上传卡片
class LicenseUploadCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const LicenseUploadCard({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _LicenseUploadCardState createState() => _LicenseUploadCardState();
}

// 添加枚举定义
enum LicenseSubType {
  filling, // 填写
  rejected, // 填写驳回
  suggested, // 建议修改
  modifying // 主动修改
}

// 扩展方法，放在枚举定义外面
LicenseSubType getLicenseSubType(int value) {
  switch (value) {
    case 1:
      return LicenseSubType.filling;
    case 5:
      return LicenseSubType.rejected;
    case 3:
      return LicenseSubType.suggested;
    case 4:
      return LicenseSubType.modifying;
    default:
      return LicenseSubType.filling;
  }
}

class _LicenseUploadCardState extends BaseBlockWidgetState<LicenseUploadCard> {
  LicenseSubType _subType = LicenseSubType.filling;
  String _uploadedImageUrl = ''; // MTCloud 返回的原始URL
  String _realImageUrl = ''; // 真实可访问的URL
  int _candidateId;
  int _snapshotId;
  int _status;
  bool _isImageLoadFailed = false;

  String number = ''; // 注册号/统一社会信息代码
  String name = ''; // 名称
  String person = ''; // 法定代表人
  String validPeriod = ''; // 有效期
  String address = ''; // 地址
  String _token = ''; // 地址

  static const List<String> exampleImages = [
    'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/cater1.png',
    'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/cater2.png',
    'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/cater3.png',
    'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/licence4.png',
  ];

  // 获取真实图片URL
  void _getRealImageUrl() {
    if (_uploadedImageUrl?.isEmpty ?? true) return;

    ImageUploadApi()
        .getMtcloudImageUrl(
      imageUrl: _uploadedImageUrl,
      taskId: _candidateId,
      messageId: widget.realMessageId,
      token: _token,
      snapshotId: _snapshotId,
    )
        .then((response) {
      if (response?.code == 0) {
        setState(() {
          _realImageUrl = response.data;
        });
      } else {
        setState(() {
          _isImageLoadFailed = true; // 设置失败状态
        });
      }
    }).catchError((error) {
      setState(() {
        _isImageLoadFailed = true; // 设置失败状态
      });
    });
  }

  @override
  void initState() {
    super.initState();
    // 获取通用参数中的 token
    Map<String, dynamic> params =
        CommonParamsUtils.getInstance().getCommonParams();
    _token = params['token'] ?? '';
    _parseContent();
  }

  @override
  void didUpdateWidget(LicenseUploadCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {}
  }

  @override
  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;

      _status = data['status'] as int ?? 0; // 解析candidateId
      _candidateId = data['candidateId'] as int ?? 0; // 解析candidateId
      _snapshotId = data['snapshotId'] as int ?? 0; // 解析candidateId
      _subType = getLicenseSubType(data['subType'] as int ?? 0);

      // 添加对 innerData 的空值检查
      final innerData = data['data'] as Map<String, dynamic>;
      if (innerData == null) {
        return;
      }

      setState(() {
        final innerData = data['data'] as Map<String, dynamic>;
        _uploadedImageUrl = innerData['url'] as String ?? '';

        number = innerData['number'] as String ?? '';
        name = innerData['name'] as String ?? '';
        person = innerData['person'] as String ?? '';
        validPeriod = innerData['validPeriod'] as String ?? '';
        address = innerData['address'] as String ?? '';
      });

      // 解析完数据后获取真实URL
      _getRealImageUrl();
    } catch (e) {
      debugPrint('解析操作卡片数据时出错: $e');
    }
  }

  /// 跳转到餐饮资质页面
  Future<dynamic> _navigateToCaterPage({String imageUrl}) async {
    if (_candidateId == null) {
      MTFToast.showToast(msg: 'taskId不能为空');
      return null;
    }

    final host = await ApiUtils.getApiHost();

    // 构建基础URL
    final Uri uri = Uri.parse('$host/kd');

    // 构建查询参数
    final Map<String, String> queryParams = {
      'hideNativeNavBar': '1',
      'taskId': _candidateId.toString(),
      'scene': 'assistant',
      'token': _token,
      'snapshotId': _snapshotId.toString(),
    };

    // 如果有图片URL，添加到参数中
    if (imageUrl?.isNotEmpty == true) {
      queryParams['imageUrl'] = imageUrl;
    }

    // 构建完整URL
    final String fullUrl =
        '$uri?hideNativeNavBar=1#/pages/cater/index?${Uri(queryParameters: queryParams).query}';

    return RouteUtils.open(fullUrl);
  }

  // 修改原有的图片选择和上传方法
  Future<void> _pickAndUploadImage() async {
    final result = await showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => ImageUploadGuideBottomSheet(
        exampleImages: exampleImages,
        token: _token,
        candidateId: _candidateId,
        uploadType: 'mtcloud',
      ),
    );

    if (result?.isEmpty ?? true) {
      return;
    }

    final imageUrl = result;

    MTFToast.showToast(msg: '图片上传成功');
    final pageResult = await _navigateToCaterPage(imageUrl: imageUrl);

    if (pageResult != null && pageResult.code == RouteResult.ok) {
      debugPrint('H5页面关闭，返回结果: $pageResult');

      widget.model?.updateCard(widget.realMessageId);
      _addNextCard(expectedStatus: 2);
    }
  }

  Future<void> _handleModifyDirectly() async {
    final pageResult = await _navigateToCaterPage();

    if (pageResult != null && pageResult.code == RouteResult.ok) {
      debugPrint('H5页面关闭，返回结果: $pageResult');

      widget.model?.updateCard(widget.realMessageId);
      _addNextCard(expectedStatus: 2);
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 示例区域或已上传图片
          Stack(
            children: [
              Container(
                width: double.infinity,
                height: 160,
                margin: const EdgeInsets.symmetric(horizontal: 39), // 添加左右间距
                child: _buildImageView(), // 修改图片展示部分
              ),
              // 只有在显示示例图片时才显示示例标签
              Visibility(
                visible: _uploadedImageUrl == null,
                child: Positioned(
                  top: 0,
                  left: 39,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Color(0xFFFFDD00),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomRight: Radius.circular(12),
                      ),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    child: const Text(
                      '示例',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF3D3D3D),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          _buildLicenseInfo(),

          const SizedBox(height: 24),

          // 底部按钮
          _buildButtons(),
        ],
      ),
    );
  }

  // 添加信息行构建方法
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 140,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF222222),
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600, // 添加加粗
              color: Color(0xFF333333),
            ),
          ),
        ),
      ],
    );
  }

  // 构建主动修改状态按钮
  Widget _buildExpiredButton() {
    return const CustomButton(
      text: '已过期',
      onPressed: null,
      primary: false,
    );
  }

  // 构建主动修改状态按钮
  Widget _buildFilledButton() {
    return const CustomButton(
      text: '已填写',
      onPressed: null,
      primary: false,
    );
  }

  // 根据subType构建对应按钮
  Widget _buildSubTypeButtons() {
    switch (_subType) {
      case LicenseSubType.modifying:
        return _buildModifyingButtons();
      case LicenseSubType.suggested:
        return _buildSuggestedButtons();
      case LicenseSubType.rejected:
        return _buildModifyButton();
      case LicenseSubType.filling:
        return _buildFillingButtons();
      default:
        return Container();
    }
  }

  // 构建底部按钮
  Widget _buildButtons() {
    int _status =
        RuzhuCardStatusManager().getStatus(widget.messageId, this._status);

    // 首先检查状态
    if (_status == 3) {
      return _buildExpiredButton();
    } else if (_status == 2) {
      return _buildFilledButton();
    } else if (_status == 0) {
      return const CustomButton(
        text: '已修改',
        onPressed: null,
        primary: false,
      );
    } else if (_status == 1) {
      return _buildSubTypeButtons();
    }

    return const SizedBox.shrink();
  }

  // 构建填写状态按钮
  Widget _buildFillingButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '许可证说明',
            primary: false,
            onPressed: () {
              // 显示许可证说明抽屉弹窗
              LicenseDescriptionDrawer.show(context);
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: CustomButton(
            text: '上传许可证', // 修改按钮文案
            onPressed: _pickAndUploadImage,
          ),
        ),
      ],
    );
  }

  /// 添加下一张卡片的通用方法
  Future<void> _addNextCard({int expectedStatus}) async {
    try {
      NextCardUtil.addNextCard(widget.messageId,
          model: widget.model,
          candidateId: _candidateId,
          token: _token,
          expectedStatus: expectedStatus);
    } catch (e) {
      debugPrint('添加下一张卡片失败: $e');
    }
  }

  // 构建建议修改状态按钮
  Widget _buildSuggestedButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '坚持使用',
            onPressed: () async {
              // 处理坚持使用操作
              await MerchantApiService().saveCardSnapshot(
                candidateId: _candidateId,
                snapshotId: _snapshotId,
              );
              widget.model?.updateCard(widget.realMessageId);
              _addNextCard(expectedStatus: 2);
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildModifyButton(), // 使用统一的修改按钮
        ),
      ],
    );
  }

  // 封装统一的修改按钮
  Widget _buildModifyButton() {
    return CustomButton(
      text: '去修改',
      onPressed: _handleModifyDirectly,
    );
  }

  // 构建主动修改状态按钮
  Widget _buildModifyingButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '取消',
            primary: false,
            onPressed: () async {
              // 处理取消操作
              await MerchantApiService().saveCardSnapshot(
                candidateId: _candidateId,
                snapshotId: _snapshotId,
              );
              widget.model?.updateCard(widget.realMessageId);
              _addNextCard(expectedStatus: 2);
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildModifyButton(), // 使用统一的修改按钮
        ),
      ],
    );
  }

  // 修改图片展示部分
  Widget _buildImageView() {
    const String defaultImageUrl =
        'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/images/info_prompt/st06.jpg';

    return Container(
      margin: const EdgeInsets.all(8),
      height: 105,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6), // 修改圆角值为6
      ),
      child: Stack(
        alignment: Alignment.center, // 添加居中对齐
        children: [
          // 默认图片
          Visibility(
            visible: _uploadedImageUrl?.isEmpty ?? true,
            child: Center(
              // 添加 Center
              child: ClipRRect(
                // 添加ClipRRect包裹图片
                borderRadius: BorderRadius.circular(6), // 设置圆角为6
                child: Image.network(
                  defaultImageUrl,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),

          // 加载中状态
          Visibility(
            visible: _uploadedImageUrl?.isNotEmpty == true &&
                _realImageUrl.isEmpty &&
                !_isImageLoadFailed,
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6E30)),
              ),
            ),
          ),

          // 加载失败状态 - 只显示默认图片，不显示失败文本
          Visibility(
            visible: _isImageLoadFailed,
            child: Center(
              child: Image.network(defaultImageUrl, fit: BoxFit.contain),
            ),
          ),

          // 实际图片
          Visibility(
            visible: _uploadedImageUrl?.isNotEmpty == true &&
                _realImageUrl.isNotEmpty &&
                !_isImageLoadFailed,
            child: Center(
              // 添加 Center
              child: ClipRRect(
                // 添加ClipRRect包裹图片
                borderRadius: BorderRadius.circular(6), // 设置圆角为6
                child: Image.network(
                  _realImageUrl,
                  fit: BoxFit.contain,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return const Center(
                      child: CircularProgressIndicator(
                        valueColor:
                            AlwaysStoppedAnimation<Color>(Color(0xFFFF6E30)),
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    // 图片加载错误时只显示默认图片，不显示失败文本
                    return ClipRRect(
                      // 添加ClipRRect包裹错误状态下的图片
                      borderRadius: BorderRadius.circular(6), // 设置圆角为6
                      child:
                          Image.network(defaultImageUrl, fit: BoxFit.contain),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 添加营业执照信息展示
  Widget _buildLicenseInfo() {
    bool hasAnyInfo = (number?.isNotEmpty == true) ||
        (name?.isNotEmpty == true) ||
        (person?.isNotEmpty == true);

    if (!hasAnyInfo) {
      return Container();
    }

    List<Widget> children = [];

    if (person?.isNotEmpty == true) {
      children.add(_buildInfoRow('法定代表人', person));
      children.add(const SizedBox(height: 8));
    }

    if (number?.isNotEmpty == true) {
      children.add(_buildInfoRow('许可证号', number));
      children.add(const SizedBox(height: 8));
    }

    if (address?.isNotEmpty == true) {
      children.add(_buildInfoRow('地址', address));
      children.add(const SizedBox(height: 8));
    }

    if (validPeriod?.isNotEmpty == true) {
      children.add(_buildInfoRow('有效期', validPeriod));
      children.add(const SizedBox(height: 8));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }
}
