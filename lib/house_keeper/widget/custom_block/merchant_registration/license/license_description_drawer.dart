import 'package:flutter/material.dart';
import '../../merchant_registration/api/services/license_api_service.dart';

class LicenseDescriptionDrawer extends StatefulWidget {
  const LicenseDescriptionDrawer({Key key}) : super(key: key);

  static void show(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return const LicenseDescriptionDrawer();
      },
    );
  }

  @override
  _LicenseDescriptionDrawerState createState() =>
      _LicenseDescriptionDrawerState();
}

class _LicenseDescriptionDrawerState extends State<LicenseDescriptionDrawer> {
  final LicenseApiService _apiService = LicenseApiService();
  List<String> _descriptions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchLicenseDescriptions();
  }

  Future<void> _fetchLicenseDescriptions() async {
    try {
      final descriptions = await _apiService.getLicenseDescriptions();
      setState(() {
        _descriptions = descriptions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('获取许可证说明失败: $e');
    }
  }

  Widget _buildTitleBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: 12, vertical: 16), // 将水平间距从16调整为12
      child: Stack(
        alignment: Alignment.center,
        children: [
          const Center(
            child: Text(
              '许可证说明',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
          ),
          Positioned(
            right: 0,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: const Icon(
                Icons.close,
                size: 24,
                color: Color(0xFF999999),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Text(
      _descriptions.asMap().entries.map((entry) {
        return '${entry.key + 1}. ${entry.value}';
      }).join('\n'),
      style: const TextStyle(
        fontSize: 14,
        height: 1.5,
        color: Color(0xFF666666),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      constraints: BoxConstraints(
        // 设置最大高度为屏幕高度的70%
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      child: Padding(
        padding: const EdgeInsets.only(
            left: 12, right: 12, bottom: 16), // 将左右间距从16调整为12
        child: Column(
          mainAxisSize: MainAxisSize.min, // 关键设置：让Column根据内容自适应高度
          crossAxisAlignment: CrossAxisAlignment.start, // 添加此行，使Column中的子组件左对齐
          children: [
            // 标题栏
            _buildTitleBar(context),
            // 分割线
            const Divider(height: 1, color: Color(0xFFEEEEEE)),

            const SizedBox(height: 16), // 添加顶部间距

            _buildContent(),

            const SizedBox(height: 16), // 添加底部间距

            // 底部按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  primary: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: const Text(
                  '我知道了',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
