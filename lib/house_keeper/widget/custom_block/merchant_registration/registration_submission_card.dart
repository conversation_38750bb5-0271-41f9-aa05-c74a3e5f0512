import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/next_card_util.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import './api/services/task_submit_api_service.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'utils/api_utils.dart';

/// 商家入驻提审卡片
/// 显示入驻任务完成的提示信息和提交审核按钮
class RegistrationSubmissionCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const RegistrationSubmissionCard({
    Key key,
    this.model,
    @required String content,
    String messageId,
    int realMessageId,
  }) : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _RegistrationSubmissionCardState createState() =>
      _RegistrationSubmissionCardState();
}

class _RegistrationSubmissionCardState
    extends BaseBlockWidgetState<RegistrationSubmissionCard> {
  final _taskApi = TaskSubmitApiService();
  int _status = 0;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;

      setState(() {
        _status = data['status'] as int ?? 0;
      });
    } catch (e) {
      debugPrint('解析操作卡片数据时出错: $e');
    }
  }

  /// 处理提交按钮点击
  Future<void> _handleSubmit() async {
    try {
      if (_isUploading) {
        return;
      }
      _isUploading = true;
      EasyLoading.show();

      final params = CommonParamsUtils.getInstance().getCommonParams();
      final token = params['token'];
      final _candidateId = params['candidateId'];

      final success = await _taskApi.submitTask({
        'taskId': int.parse(_candidateId),
        'token': token,
        'source': '',
        'device': 'APP',
        'type': 'AI'
      });

      _isUploading = false;
      EasyLoading.dismiss();

      if (!success) {
        return;
      }
      final host = await ApiUtils.getApiHost();

      final fullUrl = '$host/kd?hideNativeNavBar=1#/pages/status/index';
      final queryParams = Uri(queryParameters: {
        'taskId': _candidateId,
        'scene': 'assistant',
        'token': token,
      }).query;
      final finalUrl = '$fullUrl?$queryParams';

      NextCardUtil.getCardByType(widget.messageId,
          model: widget.model, type: 1);

      RouteUtils.open(finalUrl);
      MTFToast.showToast(msg: '提交成功，请等待审核');
    } catch (e) {
      MTFToast.showToast(msg: '提交失败，请重试 $e');
      _isUploading = false;
      EasyLoading.dismiss();
    }
  }

  Widget _buildExpiredButton() {
    return const CustomButton(
      text: '已过期',
      onPressed: null,
      primary: false, // 可以根据具体业务逻辑控制按钮状态
    );
  }

  // 构建底部按钮
  Widget _buildButtons() {
    int _status =
        RuzhuCardStatusManager().getStatus(widget.messageId, this._status);

    // 首先检查状态
    if (_status == 3) {
      return _buildExpiredButton();
    }

    return CustomButton(
      text: '确认提交',
      onPressed: _handleSubmit,
    );
  }

  @override
  Widget buildContentView() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 添加带图片的标题
          Row(
            children: [
              Image.network(
                'http://p0.meituan.net/ziruzhu/08b4793f4627b997b7af30a267b83e163227.png',
                width: 24,
                height: 24,
              ),
              const SizedBox(width: 4),
              const Expanded(
                child: Text(
                  '恭喜老板，您已完成全部入驻任务！',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF222222),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 7),
          // 描述文本
          const Text(
            '如果您没有其他问题了，请点击下方按钮进行提交。提交后系统将对您的资料进行审核，您可在后续页面中随时关注审核进度哦~',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
              height: 1.5,
            ),
          ),

          const SizedBox(height: 14),

          // 提交按钮
          _buildButtons(),
        ],
      ),
    );
  }
}
