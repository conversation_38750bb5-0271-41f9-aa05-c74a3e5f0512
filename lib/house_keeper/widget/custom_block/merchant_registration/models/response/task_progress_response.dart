// 响应数据模型类
class TaskProgressResponse {
  final int code;
  final String msg;
  final TaskProgressData data;

  TaskProgressResponse({
    this.code,
    this.msg,
    this.data,
  });

  factory TaskProgressResponse.fromJson(Map<String, dynamic> json) {
    return TaskProgressResponse(
      code: json['code'],
      msg: json['msg'],
      data:
          json['data'] != null ? TaskProgressData.fromJson(json['data']) : null,
    );
  }
}

class TaskProgressData {
  final TaskStatus taskStatus;
  final List<ModuleStatus> moduleStatus;

  TaskProgressData({
    this.taskStatus,
    this.moduleStatus,
  });

  factory TaskProgressData.fromJson(Map<String, dynamic> json) {
    return TaskProgressData(
      taskStatus: json['taskStatus'] != null
          ? TaskStatus.fromJson(json['taskStatus'])
          : null,
      moduleStatus: json['moduleStatus'] != null
          ? List<ModuleStatus>.from(
              json['moduleStatus'].map((x) => ModuleStatus.fromJson(x)))
          : [],
    );
  }
}

class TaskStatus {
  final String key;
  final String name;
  final String notice;

  TaskStatus({
    this.key,
    this.name,
    this.notice,
  });

  factory TaskStatus.fromJson(Map<String, dynamic> json) {
    return TaskStatus(
      key: json['key'],
      name: json['name'],
      notice: json['notice'],
    );
  }
}

class ModuleStatus {
  final String moduleName;
  final String summaryStatus;
  final String detailStatus;
  final String moduleRemark;
  final String moduleRejectMsg;
  final List<CardStatus> cardList;

  ModuleStatus({
    this.moduleName,
    this.summaryStatus,
    this.detailStatus,
    this.moduleRemark,
    this.moduleRejectMsg,
    this.cardList,
  });

  factory ModuleStatus.fromJson(Map<String, dynamic> json) {
    return ModuleStatus(
      moduleName: json['moduleName'],
      summaryStatus: json['summaryStatus'],
      detailStatus: json['detailStatus'],
      moduleRemark: json['moduleRemark'],
      moduleRejectMsg: json['moduleRejectMsg'],
      cardList: json['cardList'] != null
          ? List<CardStatus>.from(
              json['cardList'].map((x) => CardStatus.fromJson(x)))
          : [],
    );
  }
}

class CardStatus {
  final String cardCode;
  final int status;
  final String rejectMsg;
  final String module;

  CardStatus({
    this.cardCode,
    this.status,
    this.rejectMsg,
    this.module,
  });

  factory CardStatus.fromJson(Map<String, dynamic> json) {
    return CardStatus(
      cardCode: json['cardCode'],
      status: json['status'],
      rejectMsg: json['rejectMsg'],
      module: json['module'],
    );
  }
}
