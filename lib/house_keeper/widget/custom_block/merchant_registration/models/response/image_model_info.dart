class RuZhuImageModel {
  RuZhuImageModel.fromJson(Map<dynamic, dynamic> json) {
    if (json['photoInfos'] != null) {
      photoInfos = <RuZhuImageModelObj>[];
      json['photoInfos'].forEach((v) {
        photoInfos.add(RuZhuImageModelObj.fromJson(v));
      });
    }
  }

  List<RuZhuImageModelObj> photoInfos;
}

class RuZhuImageModelObj {
  RuZhuImageModelObj.fromJson(Map<dynamic, dynamic> json) {
    localId = json['localId'];
  }

  String localId;
}