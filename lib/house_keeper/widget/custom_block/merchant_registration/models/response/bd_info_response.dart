class BdInfoResponse {
  /// 识别结果
  /// 0-识别成功
  /// 1-二维码已失效
  /// 2-无法识别二维码
  int result;

  /// 弹窗标题
  String title;

  /// 弹窗内容
  String content;

  /// BD姓名
  String bdName;

  /// BD ID
  String misId;

  /// 绑定状态
  /// 0-已绑定
  /// 1-被动解绑
  /// 3-未绑定或主动解绑
  int bindState;

  /// 提示文案
  String words;

  /// 任务状态
  int taskStatus;

  BdInfoResponse({
    this.result = 0,
    this.title = '',
    this.content = '',
    this.bdName = '',
    this.misId = '',
    this.bindState = 0,
    this.words = '',
    this.taskStatus = 0,
  });

  factory BdInfoResponse.fromJson(Map<String, dynamic> json) {
    return BdInfoResponse(
      result: (json['result'] ?? 0) as int,
      title: (json['title'] ?? '').toString(),
      content: (json['content'] ?? '').toString(),
      bdName: (json['bdName'] ?? '').toString(),
      misId: (json['misId'] ?? '').toString(),
      bindState: (json['bindState'] ?? 0) as int,
      words: (json['words'] ?? '').toString(),
      taskStatus: (json['taskStatus'] ?? 0) as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'result': result,
      'title': title,
      'content': content,
      'bdName': bdName,
      'misId': misId,
      'bindState': bindState,
      'words': words,
      'taskStatus': taskStatus,
    };
  }

  @override
  String toString() {
    return 'BdInfoResponse('
        'result: $result, '
        'title: $title, '
        'content: $content, '
        'bdName: $bdName, '
        'misId: $misId, '
        'bindState: $bindState, '
        'words: $words, '
        'taskStatus: $taskStatus)';
  }
}
