import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import '../../../../utils/common_params_utils.dart';
import '../utils/next_card_util.dart';
import '../api/services/action_tracking_api_service.dart';

/// 客服信息数据模型
class CustomerServiceData {
  final String qrCodeUrl;
  final String serviceLink;
  final int type;
  int status;

  CustomerServiceData(
      {this.qrCodeUrl, this.serviceLink, this.type, this.status});

  factory CustomerServiceData.fromJson(Map<String, dynamic> json) {
    return CustomerServiceData(
      qrCodeUrl: json['qrCodeUrl'] as String,
      serviceLink: json['serviceLink'] as String,
      type: json['type'] as int,
      status: json['status'] as int,
    );
  }
}

/// 客服二维码组件
class CustomerServiceQRCodeWidget extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const CustomerServiceQRCodeWidget(
      {Key key,
      String content,
      this.model,
      String messageId,
      int realMessageId})
      : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _CustomerServiceQRCodeWidgetState createState() =>
      _CustomerServiceQRCodeWidgetState();
}

class _CustomerServiceQRCodeWidgetState
    extends BaseBlockWidgetState<CustomerServiceQRCodeWidget> {
  // 卡片相关数据
  CustomerServiceData _customerServiceData;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(CustomerServiceQRCodeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  /// 解析卡片内容数据
  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;
      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;
      setState(() {
        _customerServiceData = CustomerServiceData.fromJson(data);
      });

      debugPrint('解析客服二维码卡片数据成功： $_customerServiceData');
    } catch (e) {
      debugPrint('解析客服二维码卡片数据时出错: $e');
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 10),
          // 二维码图片
          _buildQRCodeImage(),
          // 底部按钮
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// 构建二维码图片
  Widget _buildQRCodeImage() {
    if (_customerServiceData?.type == 0) {
      return const SizedBox.shrink(); // 当type为0时不显示任何内容
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 20), // 添加底部间距
      child: Center(
        child: Container(
          width: 180,
          height: 180,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFEEEEEE), width: 1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: _customerServiceData?.qrCodeUrl != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Image.network(
                    _customerServiceData.qrCodeUrl,
                    fit: BoxFit.contain,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return const Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Color(0xFF999999)),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return _buildQRCodeErrorWidget();
                    },
                  ),
                )
              : _buildQRCodeErrorWidget(),
        ),
      ),
    );
  }

  /// 构建二维码错误提示
  Widget _buildQRCodeErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: const [
          Icon(Icons.qr_code_2, color: Colors.grey, size: 32),
          SizedBox(height: 8),
          Text(
            '二维码加载失败',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮行
  Widget _buildActionButtons() {
    int status = RuzhuCardStatusManager()
        .getStatus(widget.messageId, _customerServiceData.status);

    // 先根据 status 判断显示哪种状态按钮
    if (status == 0) {
      //  去修改禁用
      return _buildDisabledModifyButton();
    } else if (status == 2) {
      // 已填写禁用
      return _buildFilledButton();
    } else if (status == 3) {
      // 已过期
      return _buildExpiredButton();
    } else if (status == 1) {
      // 只有在 status = 1 时才根据 type 判断显示按钮
      return _buildActiveButtons();
    }

    // 默认情况返回空容器
    return const SizedBox.shrink();
  }

  /// 添加已过期按钮
  Widget _buildExpiredButton() {
    return Row(
      children: const [
        Expanded(
          child: CustomButton(
            text: "已过期",
            onPressed: null,
          ),
        ),
      ],
    );
  }

  /// 添加已填写禁用按钮
  Widget _buildFilledButton() {
    return Row(
      children: const [
        Expanded(
          child: CustomButton(
            text: "已填写",
            onPressed: null,
          ),
        ),
      ],
    );
  }

  /// 添加"去查看"禁用按钮
  Widget _buildDisabledModifyButton() {
    return Row(
      children: const [
        Expanded(
          child: CustomButton(
            text: "去修改",
            onPressed: null,
          ),
        ),
      ],
    );
  }

  /// 保存二维码到相册
  Future<void> _saveQRCodeToGallery() async {
    if (_customerServiceData?.qrCodeUrl == null) {
      MTFToast.showToast(msg: '二维码不存在，无法保存');
      return;
    }

    try {
      MTFToast.showToast(msg: '正在保存二维码...');

      KNB.downloadImage(
          sceneToken: 'dj-5ea52754bff69d34',
          imageUrl: _customerServiceData.qrCodeUrl);
      MTFToast.showToast(msg: '保存完成!');
    } catch (e) {
      debugPrint('保存二维码到相册失败: $e');
      MTFToast.showToast(msg: '保存失败，请重试');
    }
  }

  /// 联系在线客服
  Future<void> _contactCustomerService() async {
    if (_customerServiceData?.serviceLink == null ||
        _customerServiceData.serviceLink.isEmpty) {
      MTFToast.showToast(msg: '客服链接不存在，无法跳转');
      return;
    }

    Map<String, dynamic> params =
        CommonParamsUtils.getInstance().getCommonParams();

    try {
      ActionTrackingApiService().recordAction(
        candidateId: params['candidateId'],
        keys: ['airuzhuGoToCustomerService'],
      );
    } catch (e) {
      debugPrint('打点失败失败: $e');
    }

    try {
      MTFToast.showToast(msg: '正在连接客服...');

      // 使用RouteUtils.open打开客服链接
      RouteUtils.open(
        _customerServiceData.serviceLink,
        present: true, // 使用模态方式打开
        opaque: false, // 背景透明
      ).then((value) {
        NextCardUtil.addNextCard(
          widget.messageId,
          model: widget.model,
          candidateId: int.parse(params['candidateId']),
          token: params['token'],
        );
      });
    } catch (e) {
      debugPrint('联系在线客服失败: $e');
      MTFToast.showToast(msg: '连接客服失败，请重试');
    }
  }

  /// 构建双按钮
  Widget _buildDualButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: "保存二维码至相册",
            onPressed: () {
              _saveQRCodeToGallery();
            },
            primary: false,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: "联系在线客服",
            onPressed: () => _contactCustomerService(),
          ),
        ),
      ],
    );
  }

  /// 构建活跃按钮
  Widget _buildActiveButtons() {
    if (_customerServiceData?.type == 0) {
      // 仅显示联系在线客服按钮
      return Row(
        children: [
          Expanded(
            child: CustomButton(
              text: "联系在线客服",
              onPressed: () => _contactCustomerService(),
            ),
          ),
        ],
      );
    } else if (_customerServiceData?.type == 1) {
      // 保存二维码至相册 & 联系在线客服
      return _buildDualButtons();
    } else {
      // 其他类型默认显示联系在线客服按钮
      return Row(
        children: [
          Expanded(
            child: CustomButton(
              text: "联系在线客服",
              onPressed: () => _contactCustomerService(),
            ),
          ),
        ],
      );
    }
  }
}
