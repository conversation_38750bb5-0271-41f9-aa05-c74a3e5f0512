import 'package:flutter/material.dart';
import '../utils/image_utils.dart';

/// 图片上传引导底部弹窗
///
/// 用于显示图片上传的引导说明和示例图片，支持自定义标题、图片和按钮文本
class ImageUploadGuideBottomSheet extends StatefulWidget {
  /// 引导标题
  final String title;

  /// 示例图片列表
  final List<String> exampleImages;

  /// 按钮文本
  final String buttonText;

  /// 上传类型
  final String uploadType;
  final String token;
  final int candidateId;

  /// 构造函数
  const ImageUploadGuideBottomSheet(
      {Key key,
      this.title = '上传图片须知',
      this.exampleImages,
      this.buttonText = '我已了解规则，开始上传',
      this.uploadType = 'cdn',
      this.candidateId,
      this.token})
      : super(key: key);

  /// 显示图片上传引导底部弹窗的静态方法
  static Future<String> show(
    BuildContext context, {
    String title = '上传图片须知',
    List<String> exampleImages,
    String buttonText = '我已了解规则，开始上传',
    String uploadType = 'cdn',
    int candidateId,
  }) {
    return showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return ImageUploadGuideBottomSheet(
          title: title,
          exampleImages: exampleImages,
          buttonText: buttonText,
          uploadType: uploadType,
        );
      },
    );
  }

  @override
  _ImageUploadGuideBottomSheetState createState() =>
      _ImageUploadGuideBottomSheetState();
}

class _ImageUploadGuideBottomSheetState
    extends State<ImageUploadGuideBottomSheet> {
  // 添加 PageController
  final PageController _pageController = PageController();
  // 添加当前页面索引
  int _currentPage = 0;

  // 获取实际使用的图片列表
  List<String> get _carouselImages => widget.exampleImages;

  @override
  void initState() {
    super.initState();
    // 添加页面监听
    _pageController.addListener(() {
      int page = _pageController.page?.round() ?? 0;
      if (page != _currentPage) {
        setState(() {
          _currentPage = page;
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _buildCarouselItem(String imageUrl) {
    return Align(
      alignment: Alignment.centerLeft, // 改为左对齐
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          imageUrl,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildIndicatorDot(bool isActive) {
    return Container(
      width: 4,
      height: isActive ? 12 : 4, // 激活状态高度为12，非激活状态保持4
      margin: const EdgeInsets.symmetric(vertical: 3),
      decoration: BoxDecoration(
        color:
            isActive ? const Color(0xFFFFDD00) : Colors.grey.withOpacity(0.5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)), // 添加顶部圆角
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 让 Column 自适应内容高度
        children: [
          _buildTitleBar(context),
          Container(
            height: 270,
            margin: const EdgeInsets.only(bottom: 10),
            padding: const EdgeInsets.only(left: 12, right: 28),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                PageView.builder(
                  controller: _pageController,
                  scrollDirection: Axis.vertical,
                  itemCount: _carouselImages.length,
                  itemBuilder: (context, index) =>
                      _buildCarouselItem(_carouselImages[index]),
                ),
                Positioned(
                  right: -16,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: List.generate(
                        _carouselImages.length,
                        (index) => _buildIndicatorDot(index == _currentPage),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          _buildConfirmButton(context),
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildTitleBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Center(
            child: Text(
              widget.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
          ),
          Positioned(
            right: 0,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: const Icon(
                Icons.close,
                size: 24,
                color: Color(0xFF999999),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(12),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          // 调用图片选择器并上传，传入 uploadType 参数
          MerchantImageUtil.showImagePickerDialog(context,
              candidateId: widget.candidateId,
              token: widget.token,
              uploadType: widget.uploadType, // 传入 uploadType 参数
              callback: (imageUrl) => {
                    // 上传成功，将结果返回给父组件
                    Navigator.pop(context, imageUrl)
                  });
        },
        style: ElevatedButton.styleFrom(
          primary: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        child: Text(
          widget.buttonText,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
