import 'package:flutter/material.dart';

/// 通用按钮组件
///
/// 提供统一的按钮样式，支持主要和次要按钮样式，以及禁用状态

typedef OnPressedCallBack = void Function();

class CustomButton extends StatelessWidget {
  /// 按钮文本
  final String text;

  /// 点击回调
  final OnPressedCallBack onPressed; // 修改为Function类型，以兼容FlapFunction

  /// 是否为主要按钮，默认为true
  /// true: 黑底白字
  /// false: 灰底黑字
  final bool primary;

  /// 按钮宽度，默认为double.infinity
  final double width;

  /// 按钮高度，默认为null
  final double height;

  /// 文字大小，默认为14
  final double fontSize;

  /// 文字粗细，默认为FontWeight.w500
  final FontWeight fontWeight;

  /// 按钮圆角，默认为24
  final double borderRadius;

  /// 垂直内边距，默认为12
  final double verticalPadding;

  const CustomButton({
    Key key,
    @required this.text,
    this.onPressed,
    this.primary = true,
    this.width = double.infinity,
    this.height = 40,
    this.fontSize = 14,
    this.fontWeight = FontWeight.w500,
    this.borderRadius = 24,
    this.verticalPadding = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isDisabled = onPressed == null;

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: () {
          if (onPressed != null) {
            onPressed();
          }
        },
        style: ElevatedButton.styleFrom(
          primary: isDisabled
              ? const Color(0xFFEEEEEE) // 禁用状态背景色
              : (primary ? Colors.black : const Color(0xFFF8F8F8)),
          elevation: 0,
          padding: EdgeInsets.symmetric(vertical: verticalPadding),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: fontWeight,
            color: isDisabled
                ? const Color(0xFFACACAC) // 禁用状态的文字颜色
                : (primary ? Colors.white : const Color(0xFF222222)),
          ),
        ),
      ),
    );
  }
}
