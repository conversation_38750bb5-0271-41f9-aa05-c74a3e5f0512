import 'dart:convert';
import 'dart:ffi';
import 'package:flutter/material.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'city_select_modal.dart';
import '../api/api_service2.dart';
import '../utils/api_utils.dart';

class PositionModal extends StatefulWidget {
  final String cityName;
  final String countyName;
  final String address;
  final String addressRemark;
  final String structuredAddress;
  final int longitude;
  final int latitude;
  final String provinceName;
  final int provinceId;
  final int cityId;
  final int countyId;
  final String candidateId; // 添加参数
  final String cardCode; // 添加参数
  final int subType; // 添加参数
  final int snapshotId; // 添加参数

  const PositionModal({
    Key key,
    @required this.cityName,
    @required this.countyName,
    @required this.address,
    this.addressRemark = '',
    @required this.structuredAddress,
    @required this.longitude,
    @required this.latitude,
    @required this.provinceName,
    @required this.provinceId,
    @required this.cityId,
    @required this.countyId,
    @required this.candidateId, // 添加参数
    @required this.snapshotId, // 添加参数
    @required this.cardCode, // 添加参数
    @required this.subType, // 添加参数
  }) : super(key: key);

  @override
  _PositionModalState createState() => _PositionModalState();
}

class _PositionModalState extends State<PositionModal> {
  final TextEditingController _remarkController = TextEditingController();
  String _structuredAddress; // 添加状态变量存储地图位置

  // 添加状态变量
  String _provinceName;
  String _cityName;
  String _countyName;
  int _provinceId;
  int _cityId;
  int _countyId;
  String _address;
  int _longitude;
  int _latitude;

  // 在类的开头添加 API 服务实例
  final _apiService = MerchantApiService();

  bool _isLoading = false; // 添加 loading 状态

  @override
  void initState() {
    super.initState();
    _remarkController.text = widget.addressRemark;
    _structuredAddress = widget.structuredAddress; // 初始化地图位置

    // 初始化状态变量
    _provinceName = widget.provinceName;
    _cityName = widget.cityName;
    _countyName = widget.countyName;
    _provinceId = widget.provinceId;
    _cityId = widget.cityId;
    _countyId = widget.countyId;
    _address = widget.address;
    _longitude = widget.longitude;
    _latitude = widget.latitude;
    if (widget.subType == 1) {
      _getLocation();
    }
  }

  void _getLocation() async {
    try {
      final permission = await _readLocationPermission();
      if (permission == null || permission["status"] != 'success') {
        KNB.sendLog(text: 'location permission fail permission: $permission');
        return;
      }

      final position = await KNB.getLocation(
        sceneToken: 'dj-1e82f6c1e29b844c',
        type: 'GCJ02',
        cache: true,
      );
      if (position == null || position["status"] != 'success') {
        KNB.sendLog(text: 'location position fail position: $position');
        return;
      }

      // 将经纬度乘以1000000并转为整数，因为你的状态变量是int类型
      int longitude = (position["lng"] * 1000000).toInt();
      int latitude = (position["lat"] * 1000000).toInt();

      CityInfoByLatLng result = await MerchantApiService.getCityInfoByLatLng(
          latitude: latitude, longitude: longitude);

      if (result != null) {
        setState(() {
          _provinceId =
              _provinceId > 0 ? _provinceId : (result.provinceId ?? 0);
          _provinceName = _provinceName?.isNotEmpty == true
              ? _provinceName
              : (result.provinceName ?? '');
          _cityId = _cityId > 0 ? _cityId : (result.cityId ?? 0);
          _cityName = _cityName?.isNotEmpty == true
              ? _cityName
              : (result.cityName ?? '');
          _countyId = _countyId > 0 ? _countyId : (result.countyId ?? 0);
          _countyName = _countyName?.isNotEmpty == true
              ? _countyName
              : (result.countyName ?? '');
          // _latitude = _latitude > 0 ? _latitude : latitude;
          // _longitude = _longitude > 0 ? _longitude : longitude;
        });
      }
    } catch (e) {
      debugPrint('position error: $e');
    }
  }

  Future<Map<String, dynamic>> _readLocationPermission() {
    return KNB.requestPermission(
      sceneToken: 'dj-1e82f6c1e29b844c', //定位
      type: 'location',
      readonly: false,
      forceJump: false,
    );
  }

  @override
  void dispose() {
    _remarkController.dispose();
    super.dispose();
  }

  Widget _buildItem(
    String label,
    String value, {
    bool showArrow = false,
    bool isInput = false,
    VoidCallback onTap, // 添加点击回调
  }) {
    return GestureDetector(
      onTap: () => {if (onTap != null) onTap()},
      behavior: HitTestBehavior.opaque, // 确保整行可点击
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            SizedBox(
              width: 100,
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF333333),
                ),
              ),
            ),
            Expanded(
              child: _buildInputOrText(
                isInput: isInput,
                value: value,
                controller: _remarkController,
              ),
            ),
            Visibility(
              visible: showArrow,
              child: const Icon(
                Icons.chevron_right,
                color: Color(0xFF999999),
                size: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建输入框或文本显示组件
  ///
  /// [isInput] - 是否为输入模式
  /// [value] - 显示的文本值
  /// [controller] - 输入框控制器
  /// [hintText] - 输入框提示文本
  Widget _buildInputOrText({
    bool isInput,
    String value,
    TextEditingController controller,
    String hintText = '填写参考地标，辅助骑手取餐',
  }) {
    if (isInput) {
      return TextField(
        controller: controller,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: const TextStyle(
            fontSize: 14,
            color: Color(0xFF999999),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF333333),
        ),
      );
    } else {
      return Text(
        value,
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF333333),
          fontWeight: FontWeight.w500,
        ),
      );
    }
  }

  // 在 _PositionModalState 类中添加一个获取域名的方法
  Future<String> _getDomain() async {
    try {
      // 使用 ApiUtils 获取 API 主机地址
      final apiHost = await ApiUtils.getApiHost();

      // 从 apiHost 中提取域名部分
      final uri = Uri.parse(apiHost);
      final domain = '${uri.scheme}://${uri.host}';

      return domain;
    } catch (e) {
      return 'https://kd.meituan.com'; // 返回默认域名作为备选
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    const Text(
                      '确认店铺地址',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF333333),
                      ),
                    ),
                    Positioned(
                      right: 0,
                      child: GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: const Icon(
                          Icons.close,
                          size: 24,
                          color: Color(0xFF999999),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 内容区域 - 使用 Expanded 和 SingleChildScrollView
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      _buildItem(
                        '店铺所在地区',
                        '$_cityName$_countyName', // 使用状态变量
                        showArrow: true,
                        onTap: () async {
                          final result =
                              await showModalBottomSheet<Map<String, dynamic>>(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder: (context) => SizedBox(
                              height: MediaQuery.of(context).size.height * 0.8,
                              child: CitySelectModal(
                                selectedProvinceName: _provinceName, // 使用状态变量
                                selectedCityName: _cityName,
                                selectedCountyName: _countyName,
                                selectedProvinceId:
                                    _provinceId?.toString() ?? '',
                                selectedCityId: _cityId?.toString() ?? '',
                                selectedCountyId: _countyId?.toString() ?? '',
                              ),
                            ),
                          );

                          if (result != null) {
                            setState(() {
                              // 更新状态变量
                              _provinceId = result['provinceId'];
                              _provinceName = result['provinceName'];
                              _cityId = result['cityId'];
                              _cityName = result['cityName'];
                              _countyId = result['countyId'];
                              _countyName = result['countyName'];
                              _latitude = 0;
                              _longitude = 0;
                              _structuredAddress = '';
                            });
                          }
                        },
                      ),
                      const Divider(height: 1, color: Color(0xFFEEEEEE)),
                      _buildItem('地址', _address ?? '暂无地址', onTap: () async {
                        // 添加默认值
                        try {
                          // 获取域名
                          final domain = await _getDomain();
                          final commonParams = CommonParamsUtils.getInstance()
                                  .getCommonParams() ??
                              {};
                          final token = commonParams['token'];
                          // 构建URL
                          final url =
                              '$domain/kd/?hideNativeNavBar=1&hideNativeNavBar=1#/pages/licence/index?taskId=${widget.candidateId}&uploadType=1&from=upload&cityId=${_cityId}&token=${token}&scene=assistant&snapshotId=${widget.snapshotId}';
                          // 使用 RouteUtils 打开页面
                          RouteUtils.open(url).then((res) {
                            if (res != null && res.data != null) {
                              Map<String, dynamic> spuMap =
                                  jsonDecode(res.data);
                              if (spuMap['type'] == 'save') {
                                final data =
                                    spuMap['data'] as Map<String, dynamic>;
                                if (data != null) {
                                  setState(() {
                                    _address = data['address'].toString() ?? '';
                                  });
                                }
                              }
                            }
                          });
                        } catch (e) {
                          MTFToast.showToast(msg: '打开页面失败: $e');
                        }
                      }),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 16),
                        child: Text(
                          '提示：地址从您的营业执照获取，不支持修改。若地址有误，请先更新营业执照。',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF999999),
                          ),
                        ),
                      ),
                      _buildItem(
                        '地图位置',
                        (_latitude ?? 0) > 0 && (_longitude ?? 0) > 0
                            ? '已完成'
                            : '请确认店铺在地图上的位置', // 使用状态变量
                        showArrow: true,
                        onTap: () async {
                          // 先检查是否已选择店铺所在地区
                          if (_cityId == null ||
                              _cityId == 0 ||
                              _countyId == null ||
                              _countyId == 0) {
                            MTFToast.showToast(msg: '请您先选择店铺所在地区');
                            return;
                          }

                          // 修改为 async
                          try {
                            // 获取域名
                            final domain = await _getDomain();
                            final commonParams = CommonParamsUtils.getInstance()
                                    .getCommonParams() ??
                                {};
                            final token = commonParams['token'];
                            // 构建基础参数
                            final Map<String, String> params = {
                              'isFixCounty': 'true',
                              'taskId': widget.candidateId,
                              'source': '9', // todo-sw
                              'token': token,
                              'scene': 'assistant',
                            };

                            // 构建查询字符串
                            final queryString = params.entries
                                .map((e) =>
                                    '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
                                .join('&');

                            // 构建URL
                            String url =
                                '$domain/kd?hideNativeNavBar=1#/pages/map_select/index?$queryString';

                            if (_address != null && _address.isNotEmpty) {
                              url += '&name=${Uri.encodeComponent(_address)}';
                            }
                            if (_latitude != null) {
                              url += '&latitude=$_latitude';
                            }
                            if (_longitude != null) {
                              url += '&longitude=$_longitude';
                            }
                            if (_cityId != null) {
                              url += '&cityId=$_cityId';
                            }
                            if (_cityName != null && _cityName.isNotEmpty) {
                              url +=
                                  '&cityName=${Uri.encodeComponent(_cityName)}';
                            }
                            if (_countyId != null) {
                              url += '&countyId=$_countyId';
                            }
                            if (_countyName != null && _countyName.isNotEmpty) {
                              url +=
                                  '&countyName=${Uri.encodeComponent(_countyName)}';
                            }
                            if (_provinceId != null) {
                              url += '&provinceId=$_provinceId';
                            }
                            if (_provinceName != null &&
                                _provinceName.isNotEmpty) {
                              url +=
                                  '&provinceName=${Uri.encodeComponent(_provinceName)}';
                            }

                            // 使用 RouteUtils 打开页面
                            RouteUtils.open(url).then((res) {
                              if (res != null && res.data != null) {
                                Map<String, dynamic> spuMap =
                                    jsonDecode(res.data);
                                if (spuMap['type'] == 'save') {
                                  // 确保 data 是 Map 类型
                                  final data =
                                      spuMap['data'] as Map<String, dynamic>;
                                  setState(() {
                                    _provinceName =
                                        data['provinceName']?.toString() ?? '';
                                    _provinceId = int.tryParse(
                                            data['provinceId']?.toString() ??
                                                '0') ??
                                        0;
                                    _cityId = int.tryParse(
                                            data['cityId']?.toString() ??
                                                '0') ??
                                        0;
                                    _cityName =
                                        data['cityName']?.toString() ?? '';
                                    _countyId = int.tryParse(
                                            data['countyId']?.toString() ??
                                                '0') ??
                                        0;
                                    _countyName =
                                        data['countyName']?.toString() ?? '';
                                    _latitude = int.tryParse(
                                            data['latitude']?.toString() ??
                                                '0') ??
                                        0;
                                    _longitude = int.tryParse(
                                            data['longitude']?.toString() ??
                                                '0') ??
                                        0;
                                  });
                                }
                              }
                            });
                          } catch (e) {
                            MTFToast.showToast(msg: '打开地图失败: $e');
                          }
                        },
                      ),
                      const Divider(height: 1, color: Color(0xFFEEEEEE)),
                      _buildItem('地址补充说明(选填)', '', isInput: true), // 修改这里
                    ],
                  ),
                ),
              ),

              // 底部按钮 - 固定在底部
              Container(
                padding: EdgeInsets.only(
                  left: 16,
                  right: 16,
                  bottom: MediaQuery.of(context).padding.bottom + 16,
                  top: 16,
                ),
                child: SizedBox(
                  width: double.infinity,
                  height: 44,
                  child: ElevatedButton(
                    onPressed: _isLoading
                        ? null
                        : () {
                            // 禁用按钮当正在加载时
                            // 检查所有必要字段是否为空
                            if (_address == null ||
                                _address.isEmpty ||
                                _cityId == null ||
                                _cityId == 0 ||
                                _countyId == null ||
                                _countyId == 0 ||
                                _latitude == null ||
                                _latitude == 0 ||
                                _longitude == null ||
                                _longitude == 0 ||
                                _provinceId == null ||
                                _provinceId == 0) {
                              MTFToast.showToast(msg: '请完善地址信息');
                              return;
                            }

                            setState(() {
                              _isLoading = true; // 开始加载
                            });

                            // 调用提交地址接口
                            _apiService
                                .submitAddress(
                              candidateId: widget.candidateId,
                              cardCode: widget.cardCode,
                              snapshotId: widget.snapshotId,
                              subType: widget.subType,
                              address: _address,
                              cityId: _cityId,
                              cityName: _cityName,
                              countyId: _countyId,
                              countyName: _countyName,
                              latitude: _latitude,
                              longitude: _longitude,
                              provinceId: _provinceId,
                              provinceName: _provinceName,
                              structuredAddress: _structuredAddress,
                              addressRemark: _remarkController.text,
                            )
                                .then((result) {
                              if (result.success) {
                                Map<String, dynamic> data = {
                                  'provinceId': _provinceId,
                                  'provinceName': _provinceName,
                                  'cityId': _cityId,
                                  'cityName': _cityName,
                                  'countyId': _countyId,
                                  'countyName': _countyName,
                                  'addressRemark': _remarkController.text,
                                  'structuredAddress': _structuredAddress,
                                };
                                Navigator.pop(context, data);
                              }
                            }).whenComplete(() {
                              if (mounted) {
                                setState(() {
                                  _isLoading = false; // 完成加载
                                });
                              }
                            });
                          },
                    style: ElevatedButton.styleFrom(
                      primary: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(22),
                      ),
                    ),
                    child: const Text(
                      '提交',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Loading 遮罩
          Visibility(
            visible: _isLoading,
            child: Container(
              color: Colors.black.withOpacity(0.1),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6A00)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
