import 'package:flutter/material.dart';
import '../api/api_service2.dart';

enum SelectLevel {
  province,  // 省份
  city,      // 城市
  county     // 区县
}

class CitySelectModal extends StatefulWidget {
  final String selectedProvinceName;
  final String selectedCityName;
  final String selectedCountyName;
  final String selectedProvinceId;  // 添加 ID 参数
  final String selectedCityId;
  final String selectedCountyId;

  const CitySelectModal({
    Key key,
    this.selectedProvinceName = '',
    this.selectedCityName = '',
    this.selectedCountyName = '',
    this.selectedProvinceId = '',  // 设置默认值
    this.selectedCityId = '',
    this.selectedCountyId = '',
  }) : super(key: key);

  @override
  _CitySelectModalState createState() => _CitySelectModalState();
}

class _CitySelectModalState extends State<CitySelectModal> {
  final MerchantApiService _apiService = MerchantApiService();
  SelectLevel _currentLevel = SelectLevel.province;
  
  List<ProvinceInfo> _provinceList = [];
  List<CityInfo> _cityList = [];
  List<CountyInfo> _countyList = [];
  
  ProvinceInfo _selectedProvince;
  CityInfo _selectedCity;
  CountyInfo _selectedCounty;

  // 添加 loading 状态变量
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadProvinceList().then((_) {
      _restoreSelection();
    });
  }

  // 加载省份列表
  Future<void> _loadProvinceList() {
    setState(() {
      _isLoading = true;  // 开始加载
    });

    return _apiService.getProvinceList().then((result) {
      if (result.success) {
        setState(() {
          _provinceList = result.provinceList;
        });
      }
    }).whenComplete(() {
      setState(() {
        _isLoading = false;  // 加载完成
      });
    });
  }

  // 加载城市列表
  Future<void> _loadCityList(ProvinceInfo province) {
    if (_currentLevel != SelectLevel.province) {
      return Future.value();
    }
    
    setState(() {
      _cityList = province.cityList;
      _currentLevel = SelectLevel.city;
      _selectedProvince = province;
    });
    return Future.value();
  }

  // 加载区县列表
  Future<void> _loadCountyList(CityInfo city) {
    if (_currentLevel != SelectLevel.city) {
      return Future.value();
    }
    
    setState(() {
      _isLoading = true;  // 开始加载
    });

    return _apiService.getCountyList(
      provinceId: _selectedProvince.provinceId.toString(),
      cityId: city.cityId.toString(),
    ).then((result) {
      if (result.success && mounted) {
        setState(() {
          _countyList = result.countyList;
          _currentLevel = SelectLevel.county;
          _selectedCity = city;
        });
      }
    }).whenComplete(() {
      setState(() {
        _isLoading = false;  // 加载完成
      });
    });
  }

  // 恢复选择
  Future<void> _restoreSelection() {
    if (widget.selectedProvinceId.isEmpty || widget.selectedProvinceId == '0') {
      return Future.value();
    }

    final province = _provinceList.firstWhere(
      (p) => p.provinceId.toString() == widget.selectedProvinceId,
      orElse: () => null,
    );
    if (province == null) return Future.value();

    return _loadCityList(province).then((_) {
      if (widget.selectedCityId.isEmpty || widget.selectedCityId == '0') {
        return Future.value();  // 修改这里，添加返回值
      }

      final city = _cityList.firstWhere(
        (c) => c.cityId.toString() == widget.selectedCityId,
        orElse: () => null,
      );
      if (city == null) return Future.value();  // 修改这里，添加返回值

      return _loadCountyList(city).then((_) {
        if (widget.selectedCountyId.isEmpty || widget.selectedCountyId == '0') {
          return Future.value();  // 修改这里，添加返回值
        }

        final county = _countyList.firstWhere(
          (c) => c.countyId.toString() == widget.selectedCountyId,
          orElse: () => null,
        );
        if (county == null) return Future.value();  // 修改这里，添加返回值

        setState(() {
          _selectedProvince = province;
          _selectedCity = city;
          _currentLevel = SelectLevel.county;
        });
        return Future.value();  // 添加返回值
      });
    });
  }

  // 构建标题栏
  Widget _buildHeader() {
    String title = '';
    List<Widget> breadcrumbs = [];

    // 省份导航
    breadcrumbs.addAll([
      Visibility(
        visible: _selectedProvince != null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () {
                setState(() {
                  _currentLevel = SelectLevel.province;
                  _selectedProvince = null;
                  _selectedCity = null;
                  _selectedCounty = null;
                  _cityList = [];
                  _countyList = [];
                });
              },
              child: Text(
                _selectedProvince?.provinceName ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF333333),
                ),
              ),
            ),
            const Text(' > '),
          ],
        ),
      ),
      
      // 城市导航
      Visibility(
        visible: _selectedCity != null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () {
                setState(() {
                  _currentLevel = SelectLevel.city;
                  _selectedCity = null;
                  _selectedCounty = null;
                  _countyList = [];
                });
              },
              child: Text(
                _selectedCity?.cityName ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF333333),
                ),
              ),
            ),
            const Text(' > '),
          ],
        ),
      ),
    ]);

    // 当前级别标题
    switch (_currentLevel) {
      case SelectLevel.province:
        title = '请选择省份';
        break;
      case SelectLevel.city:
        title = '请选择城市';
        break;
      case SelectLevel.county:
        title = '请选择区县';
        break;
    }

    breadcrumbs.add(
      Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFFFF6A00),
        ),
      ),
    );

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: const Icon(Icons.arrow_back_ios, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Row(
              children: breadcrumbs,
            ),
          ),
        ],
      ),
    );
  }

  // 修改列表项构建方法，添加选中状态判断
  Widget _buildListItem(String title, VoidCallback onTap, {bool selected = false}) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: selected ? const Color(0xFFFF6A00) : const Color(0xFF333333),
                  ),
                ),
                Visibility(  // 使用 Visibility 替代 if
                  visible: selected,
                  child: const Icon(
                    Icons.check,
                    color: Color(0xFFFF6A00),
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        ),
        const Divider(height: 1, color: Color(0xFFEEEEEE)),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,  // 添加固定高度
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Stack(  // Stack 作为内部组件
        children: [
          Column(  // 移除外层 Container
            children: [
              _buildHeader(),
              const Divider(height: 1),
              Expanded(
                child: ListView(
                  children: [
                    // 省份列表
                    Visibility(
                      visible: _currentLevel == SelectLevel.province,
                      child: Column(
                        children: _provinceList.map((province) => _buildListItem(
                          province.provinceName,
                          () => _loadCityList(province),
                          selected: province.provinceId.toString() == widget.selectedProvinceId,
                        )).toList(),
                      ),
                    ),
                    
                    // 城市列表
                    Visibility(
                      visible: _currentLevel == SelectLevel.city && _cityList != null,
                      child: Column(
                        children: _cityList.map((city) => _buildListItem(
                          city.cityName,
                          () => _loadCountyList(city),
                          selected: city.cityId.toString() == widget.selectedCityId,
                        )).toList(),
                      ),
                    ),
                    
                    // 区县列表
                    Visibility(
                      visible: _currentLevel == SelectLevel.county && _countyList != null,
                      child: Column(
                        children: _countyList.map((county) => _buildListItem(
                          county.countyName,
                          () {
                            if (_selectedProvince == null || _selectedCity == null) return;
                            Navigator.pop(context, {
                              'provinceId': _selectedProvince.provinceId,
                              'provinceName': _selectedProvince.provinceName,
                              'cityId': _selectedCity.cityId,
                              'cityName': _selectedCity.cityName,
                              'countyId': county.countyId,
                              'countyName': county.countyName,
                            });
                          },
                          selected: county.countyId.toString() == widget.selectedCountyId,
                        )).toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Loading 遮罩
          Visibility(
            visible: _isLoading,
            child: Container(
              color: Colors.black.withOpacity(0.1),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6A00)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}