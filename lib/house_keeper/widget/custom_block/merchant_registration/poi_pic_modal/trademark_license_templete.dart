import 'package:flutter/material.dart';

class TrademarkLicenseTempleteModal extends StatelessWidget {
  const TrademarkLicenseTempleteModal({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Container(
            width: double.infinity,  // 添加这行，使容器宽度占满父容器
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Stack(
              alignment: Alignment.center,
              children: [
                const Text(
                  '下载模板说明',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                Positioned(
                  right: 0,
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: const Icon(
                      Icons.close,
                      size: 24,
                      color: Color(0xFF999999),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 图片区域
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
            ),
            clipBehavior: Clip.hardEdge,
            child: AspectRatio(
              aspectRatio: 335 / 240,  // 根据实际图片比例调整
              child: Image.network(
                'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/7786fd1397cc0c2d/download_example.png',
                fit: BoxFit.cover,
              ),
            ),
          ),

          // 说明文本
          Container(
            margin: const EdgeInsets.all(16),
            child: const Text(
              '模版下载地址已复制，您可以把下载地址发送给微信的【文件传输助手】，再登录电脑版微信，打开网址。\n\n下载模版并完成信息填写后，上传该图片即可授权成功。',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF333333),
                height: 1.5,
              ),
            ),
          ),

          // 底部按钮
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                primary: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
              child: const Text(
                '我知道了',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}