import 'package:flutter/material.dart';
import 'package:mtf_toast/mtf_toast.dart';
import '../utils/image_utils.dart';

/// 通用图片上传引导组件
///
/// 用于显示图片上传的引导说明和示例图片，支持自定义标题、图片和按钮文本
class ImageUploadGuideSheet extends StatefulWidget {
  /// 引导标题
  final String title;

  /// 示例图片列表
  final List<String> exampleImages;

  /// 按钮文本
  final String buttonText;

  /// 上传类型
  final String uploadType;

  /// 构造函数
  const ImageUploadGuideSheet({
    Key key,
    this.title = '上传图片须知',
    this.exampleImages,
    this.buttonText = '我已了解规则，开始上传',
    this.uploadType = 'cdn',
  }) : super(key: key);

  @override
  _ImageUploadGuideSheetState createState() => _ImageUploadGuideSheetState();
}

class _ImageUploadGuideSheetState extends State<ImageUploadGuideSheet> {
  // 添加 PageController
  final PageController _pageController = PageController();
  // 添加当前页面索引
  int _currentPage = 0;

  // 获取实际使用的图片列表
  List<String> get _carouselImages => widget.exampleImages;

  @override
  void initState() {
    super.initState();
    // 添加页面监听
    _pageController.addListener(() {
      int page = _pageController.page?.round() ?? 0;
      if (page != _currentPage) {
        setState(() {
          _currentPage = page;
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _buildCarouselItem(String imageUrl) {
    return Align(
      alignment: Alignment.centerLeft, // 改为左对齐
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          imageUrl,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildIndicatorDot(bool isActive) {
    return Container(
      width: 4,
      height: isActive ? 12 : 4, // 激活状态高度为12，非激活状态保持4
      margin: const EdgeInsets.symmetric(vertical: 3),
      decoration: BoxDecoration(
        color:
            isActive ? const Color(0xFFFFDD00) : Colors.grey.withOpacity(0.5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)), // 添加顶部圆角
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 让 Column 自适应内容高度
        children: [
          _buildTitleBar(context),
          // 添加两行提示文字
          Padding(
            padding: const EdgeInsets.only(left: 12, right: 12), // 保持左右边距一致
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: const [
                    Text(
                      '需要真实、完整、没有遮挡的固定招牌',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: const [
                    Text(
                      '概念图、手机截图、横幅/布条将被驳回',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 18),
          // 原有的图片轮播部分
          Container(
            height: 270,
            margin: const EdgeInsets.only(bottom: 10),
            padding: const EdgeInsets.only(left: 12, right: 28),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                PageView.builder(
                  controller: _pageController,
                  scrollDirection: Axis.vertical,
                  itemCount: _carouselImages.length,
                  itemBuilder: (context, index) =>
                      _buildCarouselItem(_carouselImages[index]),
                ),
                Positioned(
                  right: -16, // 改为0，让指示器贴在最右边
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: List.generate(
                        _carouselImages.length,
                        (index) => _buildIndicatorDot(index == _currentPage),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          _buildConfirmButton(context),
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildTitleBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Center(
            child: Text(
              widget.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
          ),
          Positioned(
            right: 0,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: const Icon(
                Icons.close,
                size: 24,
                color: Color(0xFF999999),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(12),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () async {
          // 调用图片选择器并上传，传入 uploadType 参数
          MerchantImageUtil.showImagePickerDialog(context,
              uploadType: widget.uploadType, // 传入 uploadType 参数
              callback: (imageUrl) => {
                    if (imageUrl?.isNotEmpty ?? false)
                      {Navigator.pop(context, imageUrl)}
                  });
        },
        style: ElevatedButton.styleFrom(
          primary: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        child: Text(
          widget.buttonText,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
