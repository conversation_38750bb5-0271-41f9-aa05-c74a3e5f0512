import 'dart:convert'; // 添加这行
import 'package:flutter/material.dart';
import '../api/api_service2.dart';
import 'package:mtf_toast/mtf_toast.dart';
import './how_to_fill.dart';

class PoiPicCommitModal extends StatefulWidget {
  final String imageUrl;
  final String taskId;
  final String cardCode; // 添加 cardCode 参数
  final int subType; // 添加 subType 参数
  final String name;
  final int snapshotId;

  const PoiPicCommitModal({
    Key key,
    @required this.imageUrl,
    this.taskId = '',
    this.cardCode = '', // 设置默认值
    this.subType = 1, // 设置默认值
    this.name = '',
    this.snapshotId = 0,
  }) : super(key: key);

  @override
  _PoiPicCommitModalState createState() => _PoiPicCommitModalState();
}

class _PoiPicCommitModalState extends State<PoiPicCommitModal> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _suffixController = TextEditingController();
  String _nameError;
  String _suffixError; // 添加补充名错误信息
  String _suggestList = null; // 添加建议列表状态
  bool _isLoading = false; // 添加 loading 状态

  @override
  void initState() {
    super.initState();
    _nameController.addListener(_validateName);
    _suffixController.addListener(_validateSuffix); // 添加补充名监听器
    _startOcr();
  }

  Future<void> _startOcr() async {
    final apiService = MerchantApiService();
    final ocrResult = await apiService.startOcr(
      imgUrl: widget.imageUrl,
      taskId: widget.taskId,
    );

    setState(() {
      if (ocrResult.success && ocrResult.shopName.isNotEmpty) {
        _nameController.text = ocrResult.shopName;
        _suggestList = null; // OCR 成功时清空建议列表
      } else {
        _suggestList = jsonEncode(['图片不清晰']); // 现在可以使用 jsonEncode
      }
    });
  }

  @override
  void dispose() {
    _nameController.removeListener(_validateName);
    _suffixController.removeListener(_validateSuffix); // 移除补充名监听器
    _nameController.dispose();
    _suffixController.dispose();
    super.dispose();
  }

  // 添加补充名验证方法
  bool _validateSuffix() {
    final suffix = _suffixController.text;
    setState(() {
      if (suffix.length > 8) {
        _suffixError = '补充名最多8个字';
      } else {
        _suffixError = null;
      }
    });

    return suffix.isEmpty || suffix.length <= 8;
  }

  // 添加验证方法
  bool _validateName() {
    final name = _nameController.text;
    final suffix = _suffixController.text;
    final maxLength = suffix.isNotEmpty ? 20 : 30; // 根据补充名是否有值决定最大长度

    setState(() {
      if (name.isEmpty) {
        _nameError = '当前项不能为空';
      } else if (name.length > maxLength) {
        _nameError = '当前项最多$maxLength个字';
      } else {
        _nameError = null;
      }
    });

    return name.isNotEmpty && name.length <= maxLength;
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕高度和键盘高度
    final screenHeight = MediaQuery.of(context).size.height;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final statusBarHeight = MediaQuery.of(context).padding.top;

    // 计算弹窗高度，确保露出页面标题
    final titleReservedHeight = statusBarHeight + 100;

    // 计算可用高度（屏幕高度减去键盘高度、底部安全区域和顶部预留空间）
    final availableHeight = screenHeight - keyboardHeight - titleReservedHeight;
    // 设置弹窗的最大高度，避免遮挡页面标题
    double containerHeight;
    final defaultHeight = screenHeight * 0.8;

    if (keyboardHeight > 0) {
      // 键盘弹起时，使用可用高度的85%，确保不会太高
      containerHeight = availableHeight * 0.85;
      // containerHeight = maxHeightWithKeyboard.clamp(300.0, defaultHeight);
    } else {
      // 键盘未显示时使用默认高度
      containerHeight = defaultHeight;
    }

    return Container(
      height: containerHeight,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 固定的标题栏
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    const Text(
                      '请参照门脸图，确认您的门店名称',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF333333),
                      ),
                    ),
                    Positioned(
                      right: 0,
                      child: GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: const Icon(
                          Icons.close,
                          size: 24,
                          color: Color(0xFF999999),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 可滚动的内容区域
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                    bottom: keyboardHeight > 0 ? 20 : 80, // 键盘弹起时减少底部padding
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 提示文本
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '牌匾名称填写需与门脸图牌匾文字内容保持一致',
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF333333),
                              ),
                            ),
                            const SizedBox(height: 4), // 添加垂直间距
                            GestureDetector(
                              onTap: () {
                                // 使用 showModalBottomSheet 替代 showDialog
                                showModalBottomSheet(
                                  context: context,
                                  backgroundColor: Colors.transparent,
                                  isScrollControlled: true,
                                  builder: (context) =>
                                      const HowToFillBottomSheet(),
                                );
                              },
                              child: const Text(
                                '教你填',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFFFFDD00),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // 示例图片
                      Container(
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        clipBehavior: Clip.hardEdge,
                        child: AspectRatio(
                          aspectRatio: 16 / 9,
                          child: Image.network(
                            widget.imageUrl,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),

                      // 输入区域
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 牌匾名称输入行
                            Row(
                              children: [
                                const Text(
                                  '牌匾名称',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF333333),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: TextField(
                                    controller: _nameController,
                                    decoration: InputDecoration(
                                      errorText: _nameError, // 使用动态错误信息
                                      errorStyle: const TextStyle(
                                          color: Color(0xFFFF4B33)),
                                      border: InputBorder.none, // 去掉边框
                                      enabledBorder:
                                          InputBorder.none, // 去掉启用状态的边框
                                      focusedBorder:
                                          InputBorder.none, // 去掉聚焦状态的边框
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // 分隔线
                            const Divider(
                              height: 32, // 上下各16的间距
                              thickness: 1,
                              color: Color(0xFFEEEEEE),
                            ),
                            // 补充名输入行
                            Row(
                              children: [
                                const Text(
                                  '补充名(选填)',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF333333),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: TextField(
                                    controller: _suffixController,
                                    decoration: InputDecoration(
                                      hintText: '示例：中山店、热干面',
                                      errorText: _suffixError, // 使用动态错误信息
                                      errorStyle: const TextStyle(
                                          color: Color(0xFFFF4B33)),
                                      border: InputBorder.none, // 去掉边框
                                      enabledBorder:
                                          InputBorder.none, // 去掉启用状态的边框
                                      focusedBorder:
                                          InputBorder.none, // 去掉聚焦状态的边框
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 固定的底部按钮
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1,
                    ),
                  ),
                ),
                child: SizedBox(
                  height: 40,
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading
                        ? null
                        : () {
                            // 禁用按钮当正在加载时
                            final name = _nameController.text;
                            final suffix = _suffixController.text;

                            if (!_validateName()) {
                              MTFToast.showToast(msg: '请检查牌匾名称');
                              return;
                            }

                            if (!_validateSuffix()) {
                              MTFToast.showToast(msg: '请检查补充名称');
                              return;
                            }

                            final String shopName =
                                suffix.isNotEmpty ? '${name}(${suffix})' : name;

                            setState(() {
                              _isLoading = true; // 开始加载
                            });

                            final apiService = MerchantApiService();
                            apiService
                                .submitShopInfo(
                              candidateId: widget.taskId,
                              shopName: shopName,
                              shopFrontUrl: widget.imageUrl,
                              cardCode: widget.cardCode,
                              subType: widget.subType,
                              originalName: widget.name,
                              snapshotId: widget.snapshotId,
                              suggestList: _suggestList,
                            )
                                .then((submitResult) {
                              if (submitResult.success) {
                                Navigator.pop(context, name);
                              }
                            }).whenComplete(() {
                              if (mounted) {
                                setState(() {
                                  _isLoading = false; // 完成加载
                                });
                              }
                            });
                          },
                    style: ElevatedButton.styleFrom(
                      primary: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: const Text(
                      '完成',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          Visibility(
            visible: _isLoading,
            maintainSize: true,
            maintainAnimation: true,
            maintainState: true,
            child: Container(
              color: Colors.black.withOpacity(0.1),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6A00)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
