import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/poi_pic_modal/image_upload_guide_sheet.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/poi_pic_modal/poi_pic_commit.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/next_card_util.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'api/api_service2.dart';

class UploadStoreStorefrontImageCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const UploadStoreStorefrontImageCard({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _UploadStoreStorefrontImageCardState createState() =>
      _UploadStoreStorefrontImageCardState();
}

class _UploadStoreStorefrontImageCardState
    extends BaseBlockWidgetState<UploadStoreStorefrontImageCard> {
  Map<String, dynamic> _contentMap;
  String _uploadedImageUrl;
  String _storeName; // 添加 storeName 状态

  @override
  void initState() {
    super.initState();
    _contentMap = json.decode(widget.content);
    final url = _contentMap['data'] != null
        ? _contentMap['data']['shopFrontUrl']
        : null;
    _uploadedImageUrl = url != null && url.isNotEmpty ? url : null;
    _storeName = _contentMap['data'] != null
        ? _contentMap['data']['name'] ?? ''
        : ''; // 初始化 storeName
  }

  // 添加图片上传方法
  Future<void> _pickAndUploadImage() async {
    final result = await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => const ImageUploadGuideSheet(
        title: '上传门脸图须知',
        exampleImages: [
          'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/door_face1.png',
          'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/door_face5.png',
          'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/door_face2.png',
          'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/door_face3.png',
          'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/door_face4.png',
        ],
      ),
    );

    // 处理 result 可能是 Map 或 String 的情况
    String imageUrl;
    if (result != null) {
      if (result is Map) {
        imageUrl = result['imageUrl'];
      } else if (result is String) {
        imageUrl = result;
      }
    }

    if (imageUrl != null && imageUrl.isNotEmpty) {
      // 显示确认弹窗
      showModalBottomSheet<String>(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        builder: (context) => PoiPicCommitModal(
          imageUrl: imageUrl, // 使用提取的 imageUrl
          taskId: _contentMap['candidateId']?.toString() ?? '',
          cardCode: _contentMap['cardId']?.toString() ?? '', // 添加 cardCode
          subType: _contentMap['subType'] ?? 1, // 添加 subType
          name: _contentMap['data'] != null
              ? _contentMap['data']['name'] ?? ''
              : '',
          snapshotId: _contentMap['snapshotId'] ?? 0,
        ),
      ).then((commitResult) {
        if (commitResult != null) {
          // 不回显，改弹出一张新卡片
          // setState(() {
          //   _uploadedImageUrl = imageUrl;
          //   _storeName = commitResult;  // 更新 storeName
          // });
          widget.model?.updateCard(widget.realMessageId);
          // 调用下一个卡片的逻辑
          NextCardUtil.addNextCard(
            widget.messageId,
            model: widget.model,
            candidateId: _contentMap['candidateId'],
            expectedStatus: 2,
          );
        }
      });
    }
  }

  @override
  Widget buildContentView() {
    final int subType = _contentMap['subType'] ?? 1;

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          _buildImageSection(subType, _storeName), // 使用 _storeName
          const SizedBox(height: 16),
          _buildButtons(subType),
        ],
      ),
    );
  }

  Widget _buildImageSection(int subType, String storeName) {
    if (subType == 1) {
      return _buildExampleImageSection();
    } else {
      return _buildStoreImageSection(storeName);
    }
  }

  /// https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/images/info_prompt/eg_font_yes.png
  Widget _buildExampleImageSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 35), // 修改左右边距为 35
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            height: 154,
            decoration: BoxDecoration(
              color: const Color(0xFFF8F8F8),
              borderRadius: BorderRadius.circular(6),
              image: const DecorationImage(
                image: NetworkImage(
                    'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/images/info_prompt/eg_font_yes.png'),
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            left: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              height: 22,
              decoration: const BoxDecoration(
                color: Color(0xB8000000),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(6),
                  bottomRight: Radius.circular(6),
                  topRight: Radius.circular(0),
                  bottomLeft: Radius.circular(0),
                ),
              ),
              alignment: Alignment.center,
              child: const Text(
                '示例',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStoreImageSection(String storeName) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: _uploadedImageUrl != null && _uploadedImageUrl.isNotEmpty
                ? Image.network(
                    // 添加时间戳强制刷新图片
                    '$_uploadedImageUrl?t=${DateTime.now().millisecondsSinceEpoch}',
                    fit: BoxFit.cover,
                    // 添加图片缓存配置
                    cacheWidth: null,
                    cacheHeight: null,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: Colors.grey[200],
                      alignment: Alignment.center,
                      child: const Text('图片加载失败'),
                    ),
                  )
                : Container(
                    color: Colors.grey[200],
                    alignment: Alignment.center,
                    child: const Text('请上传门店图片'),
                  ),
          ),
        ),
        const SizedBox(height: 10),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              width: 80,
              child: Text(
                '门店名称',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF222222),
                ),
              ),
            ),
            Expanded(
              child: Text(
                storeName,
                style: const TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  /// 构建已过期按钮
  Widget _buildExpiredButton() {
    return const CustomButton(text: '已过期', primary: false);
  }

  /// 构建已过期按钮
  Widget _buildFilledButton() {
    return const CustomButton(text: '已填写', primary: false);
  }

  /// 构建禁用的去修改按钮
  Widget _buildDisabledModifyButton() {
    return const CustomButton(text: '去修改', primary: false);
  }

  /// 构建填写按钮
  Widget _buildFillButton(int subType) {
    // 创建上传按钮
    Widget createUploadButton({@required String text}) {
      return CustomButton(
        text: text,
        onPressed: () => _pickAndUploadImage(),
      );
    }

    // 创建跳过按钮
    Widget createSkipButton({@required String text, bool primary = false}) {
      return CustomButton(
        text: text,
        primary: primary,
        onPressed: () async {
          await MerchantApiService().saveCardSnapshot(
            candidateId: _contentMap['candidateId'],
            snapshotId: _contentMap['snapshotId'],
          );
          widget.model?.updateCard(widget.realMessageId);
          NextCardUtil.addNextCard(
            widget.messageId,
            model: widget.model,
            candidateId: _contentMap['candidateId'],
            expectedStatus: 2,
          );
        },
      );
    }

    // 创建按钮组合
    Widget createButtonRow(Widget skipButton, Widget uploadButton) {
      return Row(
        children: [
          Expanded(child: skipButton),
          const SizedBox(width: 12),
          Expanded(child: uploadButton),
        ],
      );
    }

    // 根据不同的subType返回对应的按钮组合
    switch (subType) {
      case 1: // 初始填写状态
        // 仅显示上传按钮
        return createUploadButton(text: '上传门脸图');

      case 5: // 审核驳回状态
        // 仅显示重新上传按钮
        return createUploadButton(text: '重新上传');

      case 3: // 建议修改状态
        // 显示坚持使用和重新上传两个按钮
        final skipButton = createSkipButton(text: '坚持使用');
        final uploadButton = createUploadButton(text: '重新上传');
        return createButtonRow(skipButton, uploadButton);

      case 4: // 主动修改状态
        // 显示去修改和重新上传两个按钮
        final skipButton = createSkipButton(text: '取消修改');
        final uploadButton = createUploadButton(text: '重新上传');
        return createButtonRow(skipButton, uploadButton);

      default: // 其他状态
        // 返回空Widget
        return const SizedBox.shrink();
    }
  }

  Widget _buildButtons(int subType) {
    int status = _contentMap['status'] ?? 1;
    status = RuzhuCardStatusManager().getStatus(widget.messageId, status);

    // 根据状态判断显示哪种按钮
    switch (status) {
      case 0: // 按钮为【去修改】置灰，不可点
        return _buildDisabledModifyButton();
      case 1: // 可以按按钮，正常按钮
        return _buildFillButton(subType);
      case 2: // 按钮为【已填写】置灰，不可点
        return _buildFilledButton();
      case 3: // 按钮为【已过期】置灰，不可点
        return _buildExpiredButton();
      default:
        // 默认情况，也显示填写按钮
        return _buildFillButton(subType);
    }
  }
}
