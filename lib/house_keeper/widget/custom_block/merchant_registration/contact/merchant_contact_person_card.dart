import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/phone_verify_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import '../utils/next_card_util.dart';
import '../api/services/contact_person_api_service.dart';
import '../api/api_service2.dart';

/// 手机号验证状态枚举
enum PhoneVerificationStatus {
  /// 未验证
  notVerified,

  /// 验证中
  verifying,

  /// 验证成功
  verified,

  /// 验证失败
  failed,
}

/// 联系人卡片状态枚举
/// status=0:"去修改"禁用 status=1:正常操作 status=2:"已填写"禁用 status=3:"已过期"禁用
enum ContactPersonStatus {
  /// 禁用修改
  disabledModify, // 0

  /// 正常操作
  normal, // 1

  /// 已填写
  filled, // 2

  /// 已过期
  expired, // 3
}

/// subType=1:填写 subType=5:被驳回  subType=3 建议修改   subType=4 主动修改
enum ContactPersonSubType {
  fill,
  rejected,
  suggestModify,
  activeModify,
}

/// ------------------------------- 数据模型 ------------------------------- ///

/// 联系人数据模型
class ContactPersonData {
  final String cardId;
  final int subType;
  final int candidateId;
  final int snapshotId;
  final int status;
  final ContactPersonInfo data;

  const ContactPersonData({
    this.cardId,
    this.subType,
    this.candidateId,
    this.snapshotId,
    this.status,
    this.data,
  });

  factory ContactPersonData.fromJson(Map<String, dynamic> json) {
    return ContactPersonData(
      cardId: json['cardId'] as String,
      subType: json['subType'] as int,
      candidateId: json['candidateId'] as int,
      snapshotId: json['snapshotId'] as int,
      status: json['status'] as int,
      data: json['data'] != null
          ? ContactPersonInfo.fromJson(json['data'])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'cardId': cardId,
        'subType': subType,
        'candidateId': candidateId,
        'snapshotId': snapshotId,
        'status': status,
        'data': data?.toJson(),
      };
}

/// 联系人信息模型
class ContactPersonInfo {
  final String name;
  final String phone;

  const ContactPersonInfo({
    this.name,
    this.phone,
  });

  factory ContactPersonInfo.fromJson(Map<String, dynamic> json) {
    return ContactPersonInfo(
      name: json['name'] as String,
      phone: json['phone'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'phone': phone,
      };
}

/// ------------------------------- 验证工具 ------------------------------- ///

/// 表单验证工具
class FormValidator {
  /// 验证联系人姓名
  static String validateName(String value) {
    if (value.isEmpty) {
      return '请输入联系人';
    }

    if (RegExp(r'[0-9]').hasMatch(value)) {
      return '联系人不能包含数字';
    }

    if (value.length < 2) {
      return '联系人至少2个字';
    }

    if (value.length > 16) {
      return '联系人最多16个字';
    }

    return null;
  }

  /// 验证手机号
  static String validatePhone(String value) {
    if (value.isEmpty) {
      return "请输入手机号";
    }

    if (!RegExp(r'^1\d{10}$').hasMatch(value)) {
      return '请输入正确的手机号';
    }

    return null;
  }

  /// 检查手机号格式是否有效
  static bool isPhoneValid(String phone) {
    if (phone.isEmpty) return false;
    return RegExp(r'^1[0-9]{10}$').hasMatch(phone);
  }
}

/// ------------------------------- 主组件 ------------------------------- ///

/// 商家联系人组件
class MerchantContactPersonWidget extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;
  const MerchantContactPersonWidget({
    Key key,
    String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _MerchantContactPersonWidgetState createState() =>
      _MerchantContactPersonWidgetState();
}

class _MerchantContactPersonWidgetState
    extends BaseBlockWidgetState<MerchantContactPersonWidget> {
  // 联系人信息
  String _contactName = '';
  String _contactPhone = '';
  String _token = '';
  ContactPersonData _contactData;
  ContactPersonSubType _contactPersonSubType = ContactPersonSubType.fill;
  int status;

  @override
  void initState() {
    super.initState();
    _parseContactData();

    Map<String, dynamic> params =
        CommonParamsUtils.getInstance().getCommonParams();

    _token = params['token'];
  }

  @override
  void didUpdateWidget(MerchantContactPersonWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContactData();
    }
  }

  /// 解析联系人数据
  void _parseContactData() {
    try {
      if (widget.content != null && widget.content.isNotEmpty) {
        _contactData = ContactPersonData.fromJson(json.decode(widget.content));
        _contactName = _contactData?.data?.name ?? '';
        _contactPhone = _contactData?.data?.phone?.toString() ?? '';
        int _subType = _contactData?.subType;
        if (_subType == 1) {
          _contactPersonSubType = ContactPersonSubType.fill;
        } else if (_subType == 3) {
          _contactPersonSubType = ContactPersonSubType.suggestModify;
        } else if (_subType == 4) {
          _contactPersonSubType = ContactPersonSubType.activeModify;
        } else if (_subType == 5) {
          _contactPersonSubType = ContactPersonSubType.rejected;
        }
        // 将整数状态转换为枚举值
        status = _contactData?.status ?? 1; // 默认为1（正常状态）
      }
    } catch (e) {
      debugPrint('解析联系人数据失败: $e');
    }
  }

  ContactPersonStatus getStatus(final int statusValue) {
    final int status =
        RuzhuCardStatusManager().getStatus(widget.messageId, statusValue);

    ContactPersonStatus _status = ContactPersonStatus.normal; // 默认为正常状态

    if (status >= 0 && status < ContactPersonStatus.values.length) {
      _status = ContactPersonStatus.values[status];
    } else {
      _status = ContactPersonStatus.normal; // 默认为正常状态
    }
    return _status;
  }

  @override
  Widget buildContentView() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          // 添加联系人和联系手机的显示
          _buildContactInfo(),
          // 联系人信息填写按钮
          _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 140,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF222222),
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600, // 添加加粗
              color: Color(0xFF333333),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建联系人和联系手机信息显示
  Widget _buildContactInfo() {
    if (_contactName.isEmpty && _contactPhone.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_contactName.isNotEmpty) _buildInfoRow('联系人', _contactName),
        const SizedBox(height: 8),
        if (_contactPhone.isNotEmpty) _buildInfoRow('联系手机', _contactPhone),
        const SizedBox(height: 10),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton() {
    ContactPersonStatus _status = getStatus(status);
    // 根据状态判断显示哪种按钮
    switch (_status) {
      case ContactPersonStatus.disabledModify:
        return _buildDisabledModifyButton();
      case ContactPersonStatus.expired:
        return _buildExpiredButton();
      case ContactPersonStatus.filled:
        return _buildFilledButton();
      case ContactPersonStatus.normal:
        return _buildFillButton();
      default:
        // 默认情况，也显示填写按钮
        return _buildFillButton();
    }
  }

  /// 构建禁用的去修改按钮
  Widget _buildDisabledModifyButton() {
    return const CustomButton(text: '去修改', primary: false);
  }

  /// 构建已过期按钮
  Widget _buildExpiredButton() {
    return const CustomButton(text: '已过期', primary: false);
  }

  /// 构建已填写按钮
  Widget _buildFilledButton() {
    return const CustomButton(text: '已填写', primary: false);
  }

  /// 构建填写按钮
  Widget _buildFillButton() {
    if (_contactPersonSubType == ContactPersonSubType.suggestModify) {
      return Row(
        children: [
          Expanded(
            child: CustomButton(
                text: '坚持使用',
                primary: false,
                onPressed: () async {
                  await MerchantApiService().saveCardSnapshot(
                    candidateId: _contactData?.candidateId,
                    snapshotId: _contactData?.snapshotId,
                  );
                  widget.model?.updateCard(widget.realMessageId);
                  NextCardUtil.addNextCard(widget.messageId,
                      model: widget.model,
                      candidateId: _contactData?.candidateId ?? 0,
                      expectedStatus: 2);
                }),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: CustomButton(
                text: '去修改',
                onPressed: () {
                  _showContactForm(context);
                }),
          ),
        ],
      );
    } else if (_contactPersonSubType == ContactPersonSubType.activeModify) {
      return Row(
        children: [
          Expanded(
            child: CustomButton(
                text: '取消修改',
                primary: false,
                  onPressed: () async {
                  await MerchantApiService().saveCardSnapshot(
                    candidateId: _contactData?.candidateId,
                    snapshotId: _contactData?.snapshotId,
                  );
                  widget.model?.updateCard(widget.realMessageId);
                  NextCardUtil.addNextCard(widget.messageId,
                      model: widget.model,
                      candidateId: _contactData?.candidateId ?? 0,
                      expectedStatus: 2);
                }),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: CustomButton(
                text: '去修改', onPressed: () => _showContactForm(context)),
          ),
        ],
      );
    } else if (_contactPersonSubType == ContactPersonSubType.rejected) {
      return Row(
        children: [
          Expanded(
            child: CustomButton(
                text: '去修改', onPressed: () => _showContactForm(context)),
          )
        ],
      );
    }

    return CustomButton(
      text: '填写联系人',
      onPressed: () => _showContactForm(context),
    );
  }

  /// 显示联系人表单弹窗
  void _showContactForm(BuildContext context) {
    // 显示加载提示
    EasyLoading.show(status: '加载中...');

    // 先获取登录手机号，然后再打开弹窗
    Map<String, dynamic> params =
        CommonParamsUtils.getInstance().getCommonParams();
    String token = params['token'];

    ContactPersonApiService()
        .getSignUpPhone(
            taskId: _contactData?.candidateId.toString() ?? '', token: token)
        .then((signUpPhone) {
      // 关闭加载提示
      EasyLoading.dismiss();

      // 打开弹窗，并传递登录手机号
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (BuildContext context) {
          return ContactFormBottomSheet(
            initialName: _contactName,
            initialPhone: _contactPhone,
            contactData: _contactData,
            signUpPhone: signUpPhone ?? '',
            onSubmit: _handleContactSubmit,
          );
        },
      );
    }).catchError((error) {
      // 关闭加载提示
      EasyLoading.dismiss();

      // 即使获取登录手机号失败，也允许打开弹窗
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (BuildContext context) {
          return ContactFormBottomSheet(
            initialName: _contactName,
            initialPhone: _contactPhone,
            contactData: _contactData,
            signUpPhone: '',
            onSubmit: _handleContactSubmit,
          );
        },
      );

      // 显示错误提示
      MTFToast.showToast(msg: '获取登录手机号失败，可能影响验证流程');
    });
  }

  /// 处理联系人提交
  void _handleContactSubmit(
      String name, String phone, String signUpPhone) async {
    // 显示加载提示
    EasyLoading.show(status: '提交中...');
    // 调用提交接口
    ContactPersonApiService()
        .submitContactInfo(
      contactName: name,
      contactPhone: phone,
      candidateId: _contactData?.candidateId?.toString() ?? '',
      subType: _contactData?.subType ?? 0,
      cardCode: _contactData?.cardId ?? '',
      signUpPhone: signUpPhone,
      token: _token,
      snapshotId: _contactData?.snapshotId ?? 0,
    )
        .then((response) {
      // 关闭加载提示
      EasyLoading.dismiss();

      // 处理响应
      if (response != null && response.code == 0) {
        if (response.data != null) {
          final passStatus = response?.data['passStatus'];
          final rejectReason = response?.data['rejectReason'];
          if (passStatus) {
            // 提交成功
            MTFToast.showToast(msg: '提交成功');
            Navigator.pop(context);
            final candidateId =
                int.tryParse(_contactData?.candidateId?.toString() ?? '');

            widget.model?.updateCard(widget.realMessageId);

            NextCardUtil.addNextCard(
              widget.messageId,
              model: widget.model,
              token: _token,
              candidateId: candidateId,
              expectedStatus: 2,
            );
          } else {
            // 提交失败，处理rejectReason
            String errorMsg = '';
            if (rejectReason == null) {
              errorMsg = '提交失败，请重试';
            } else if (rejectReason is List) {
              // 如果rejectReason是数组，将多条信息换行展示
              errorMsg = (rejectReason as List).join('\n');
            } else {
              // 如果rejectReason是字符串或其他类型，直接转换为字符串
              errorMsg = rejectReason.toString();
            }
            MTFToast.showToast(msg: errorMsg);
          }
        } else {
          // 提交失败
          MTFToast.showToast(msg: '提交失败，请重试');
        }
      }
    }).catchError((error) {
      // 关闭加载提示
      EasyLoading.dismiss();

      // 显示错误信息
      MTFToast.showToast(msg: '提交失败: ${error.toString()}');
    });
  }
}

/// ------------------------------- 表单组件 ------------------------------- ///

/// 联系人表单底部弹窗
class ContactFormBottomSheet extends StatefulWidget {
  final String initialName;
  final String initialPhone;
  final ContactPersonData contactData;
  final String signUpPhone;
  final Function(String name, String phone, String signUpPhone) onSubmit;

  const ContactFormBottomSheet({
    Key key,
    this.contactData,
    this.initialName = '',
    this.initialPhone = '',
    this.signUpPhone = '',
    @required this.onSubmit,
  }) : super(key: key);

  @override
  _ContactFormBottomSheetState createState() => _ContactFormBottomSheetState();
}

class _ContactFormBottomSheetState extends State<ContactFormBottomSheet> {
  /// 文本控制器
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  /// 错误信息
  String _nameError;
  String _phoneError;
  String _verificationErrorMsg = '';

  /// 业务状态
  bool _isPhoneValid = false;
  PhoneVerificationStatus _verificationStatus =
      PhoneVerificationStatus.notVerified;

  /// 按钮状态
  bool _isSubmitting = false;
  bool _isVerifying = false;

  /// 滚动控制器
  final ScrollController _scrollController = ScrollController();

  /// 焦点节点
  final FocusNode _nameFocusNode = FocusNode();
  final FocusNode _phoneFocusNode = FocusNode();

  /// 登录手机号
  String _signUpPhone = '';

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.initialName;
    _phoneController.text = widget.initialPhone;
    _signUpPhone = widget.signUpPhone;

    // 初始化时检查手机号是否有效
    if (widget.initialPhone.isNotEmpty) {
      _isPhoneValid = FormValidator.isPhoneValid(widget.initialPhone);

      // 如果初始手机号格式有效，检查是否与登录手机号一致
      if (_isPhoneValid && _signUpPhone.isNotEmpty) {
        if (PhoneVerificationStateManager()
                .getPhoneStatus(widget.initialPhone) ==
            PhoneVerificationStatus.verified) {
          _verificationStatus = PhoneVerificationStatus.verified;
          _verificationErrorMsg = '';
        } else {
          if (widget.initialPhone != _signUpPhone) {
            _verificationErrorMsg = '该号码与注册手机号不一致，请验证该手机号，或更换为注册手机号';
          }
        }
      }
    }

    // 添加焦点监听
    _nameFocusNode.addListener(_handleFocusChange);
    _phoneFocusNode.addListener(_handleFocusChange);

    // 添加输入监听，实时校验格式
    _nameController.addListener(_validateNameFormat);
    _phoneController.addListener(_validatePhoneFormat);
  }

  /// 实时校验联系人姓名格式
  void _validateNameFormat() {
    final name = _nameController.text;
    if (name.isNotEmpty) {
      setState(() {
        _nameError = FormValidator.validateName(name);
      });
    } else {
      setState(() {
        _nameError = null;
      });
    }
  }

  /// 实时校验手机号格式
  void _validatePhoneFormat() {
    final phone = _phoneController.text;
    if (phone.isNotEmpty) {
      setState(() {
        _phoneError = FormValidator.validatePhone(phone);
        _isPhoneValid = _phoneError == null;

        // 如果格式不正确，重置验证状态
        if (!_isPhoneValid) {
          _verificationStatus = PhoneVerificationStatus.notVerified;
          _verificationErrorMsg = '';
          PhoneVerificationStateManager().reset(phone);
        } else if (_signUpPhone.isNotEmpty && phone == _signUpPhone) {
          // 格式正确且与登录手机号一致
          _verificationErrorMsg = '';
          _verificationStatus = PhoneVerificationStatus.notVerified;
          PhoneVerificationStateManager().reset(phone);
        } else if (_signUpPhone.isNotEmpty && phone != _signUpPhone) {
          if (PhoneVerificationStateManager().getPhoneStatus(phone) ==
              PhoneVerificationStatus.verified) {
            _verificationErrorMsg = '';
            _verificationStatus = PhoneVerificationStatus.verified;
          } else {
            // 格式正确但与登录手机号不一致
            _verificationErrorMsg = '该号码与注册手机号不一致，请验证该手机号，或更换为注册手机号';
            _verificationStatus = PhoneVerificationStatus.notVerified;
            PhoneVerificationStateManager().reset(phone);
          }
        }
      });
    } else {
      setState(() {
        _phoneError = null;
        _isPhoneValid = false;
        _verificationStatus = PhoneVerificationStatus.notVerified;
        _verificationErrorMsg = '';
        PhoneVerificationStateManager().reset(phone);
      });
    }
  }

  @override
  void dispose() {
    _nameFocusNode.removeListener(_handleFocusChange);
    _phoneFocusNode.removeListener(_handleFocusChange);
    _nameController.removeListener(_validateNameFormat);
    _phoneController.removeListener(_validatePhoneFormat);
    _nameFocusNode.dispose();
    _phoneFocusNode.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 处理焦点变化
  void _handleFocusChange() {
    // 当手机号输入框失去焦点时，检查手机号格式并与登录手机号比较
    if (!_phoneFocusNode.hasFocus && _phoneController.text.isNotEmpty) {
      final currentPhone = _phoneController.text;
      // 验证手机号格式
      final phoneError = FormValidator.validatePhone(currentPhone);
      if (phoneError == null) {
        // 手机号格式正确
        if (_signUpPhone.isNotEmpty) {
          // 已有登录手机号，直接比较
          if (currentPhone != _signUpPhone) {
            if (PhoneVerificationStateManager().getPhoneStatus(currentPhone) ==
                PhoneVerificationStatus.verified) {
              setState(() {
                _verificationErrorMsg = '';
                _isPhoneValid = true; // 格式正确，允许验证
                _verificationStatus = PhoneVerificationStatus.verified;
              });
            } else {
              setState(() {
                _verificationErrorMsg = '该号码与注册手机号不一致，请验证该手机号，或更换为注册手机号';
                _isPhoneValid = true; // 格式正确，允许验证
                _verificationStatus = PhoneVerificationStatus.notVerified;
                PhoneVerificationStateManager().reset(currentPhone);
              });
            }
          } else {
            setState(() {
              _verificationErrorMsg = ''; // 清除错误信息
              _isPhoneValid = true;

              if (PhoneVerificationStateManager()
                      .getPhoneStatus(currentPhone) ==
                  PhoneVerificationStatus.verified) {
                _verificationStatus = PhoneVerificationStatus.verified;
              } else {
                _verificationStatus = PhoneVerificationStatus.notVerified;
                PhoneVerificationStateManager().reset(currentPhone);
              }
            });
          }
        } else {
          // 没有登录手机号，仍然允许验证
          setState(() {
            _verificationErrorMsg = ''; // 清除错误信息
            _isPhoneValid = true;
            _verificationStatus = PhoneVerificationStatus.notVerified;
            PhoneVerificationStateManager().reset(currentPhone);
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return _ContactFormLayout(
      isKeyboardVisible: MediaQuery.of(context).viewInsets.bottom > 0,
      viewPaddingBottom: MediaQuery.of(context).viewPadding.bottom,
      viewInsetBottom: MediaQuery.of(context).viewInsets.bottom,
      header: _buildHeader(),
      form: _buildForm(),
      actionButton: _buildActionButton(),
      scrollController: _scrollController,
    );
  }

  /// 构建表单头部
  Widget _buildHeader() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 居中标题
        const Center(
          child: Text(
            '填写联系人',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        // 右侧关闭按钮
        Positioned(
          right: 0,
          child: GestureDetector(
            onTap: () => Navigator.pop(context),
            child: const Icon(Icons.close, size: 20),
          ),
        ),
      ],
    );
  }

  /// 构建表单内容
  Widget _buildForm() {
    return Column(
      children: [
        _ContactFormField(
          label: '联系人',
          controller: _nameController,
          focusNode: _nameFocusNode,
          hintText: '请输入联系人姓名',
          errorText: _nameError,
          onChanged: (value) {
            // 这里可以留空，因为已经通过controller.addListener处理了
          },
        ),
        const SizedBox(height: 16),
        _buildPhoneField(),
      ],
    );
  }

  /// 构建手机号码输入字段
  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(
              width: 70,
              child: Text(
                '联系手机',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                ),
              ),
            ),
            Expanded(
              child: TextField(
                controller: _phoneController,
                focusNode: _phoneFocusNode,
                keyboardType: TextInputType.phone,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(11),
                ],
                decoration: const InputDecoration(
                  hintText: '请输入联系人手机号',
                  hintStyle: TextStyle(
                    color: Color(0xFFCCCCCC),
                    fontSize: 14,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ),
            _buildVerifyWidget(),
          ],
        ),
        const Divider(
          height: 1,
          thickness: 1,
          color: Color(0xFFEEEEEE),
        ),
        _buildErrorWidget(),
      ],
    );
  }

  Widget _buildErrorWidget() {
    if (_phoneError != null) {
      return Column(
        children: [
          const SizedBox(height: 4),
          Text(
            _phoneError,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFFFF4B33),
            ),
            textAlign: TextAlign.left,
          )
        ],
      );
    } else if (_verificationErrorMsg.isNotEmpty) {
      return Column(
        children: [
          const SizedBox(height: 4),
          Text(
            _verificationErrorMsg,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFFFF4B33),
            ),
            textAlign: TextAlign.left,
          ),
        ],
      );
    }
    return const SizedBox();
  }

  Widget _buildVerifyWidget() {
    if (_isVerifying) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6E30)),
        ),
      );
    }
    return _VerifyPhoneButton(
      isPhoneValid: _isPhoneValid,
      verificationStatus: _verificationStatus,
      onVerify: _verifyPhone,
    );
  }

  /// 构建操作按钮（验证手机或提交）
  Widget _buildActionButton() {
    bool enable = !_isSubmitting &&
        _isPhoneValid &&
        _verificationStatus == PhoneVerificationStatus.verified;
    return _ContactFormButton(
      text: _isSubmitting ? '提交中...' : '提交',
      onPressed: enable ? _submit : null,
    );
  }

  /// 验证手机号
  Future<void> _verifyPhone() async {
    // 验证手机号格式
    final phoneError = FormValidator.validatePhone(_phoneController.text);
    if (phoneError != null) {
      setState(() {
        _phoneError = phoneError;
      });
      return;
    }

    // 如果已经在验证中，不重复验证
    if (_isVerifying) return;

    setState(() {
      _isVerifying = true;
      _verificationStatus = PhoneVerificationStatus.verifying;
      _verificationErrorMsg = '';
      PhoneVerificationStateManager().setVerifying(_phoneController.text);
    });

    String currentPhone = _phoneController.text;

    try {
      Map<String, dynamic> params =
          CommonParamsUtils.getInstance().getCommonParams();
      String token = params['token'];

      // 检查是否与登录手机号一致
      if (_signUpPhone.isNotEmpty && currentPhone == _signUpPhone) {
        debugPrint('手机号一致，直接调用check/tel接口验证');
        // 手机号一致，直接调用check/tel接口验证
        _checkPhoneBlacklist(currentPhone, token);
      } else {
        debugPrint('手机号不一致或登录手机号为空，跳转Yoda平台验证');
        // 手机号不一致，跳转Yoda平台验证
        _verifyPhoneWithYoda(currentPhone, token);
      }
    } catch (e) {
      debugPrint('验证手机号过程中出错: $e');
      MTFToast.showToast(msg: '验证过程出错，请重试： $e');
      setState(() {
        _isVerifying = false;
        _verificationStatus = PhoneVerificationStatus.failed;
        _verificationErrorMsg = '验证过程出错，请重试';
        PhoneVerificationStateManager().setVerifyFailed(currentPhone);
      });
    }
  }

  /// 检查手机号是否在黑名单中
  Future<void> _checkPhoneBlacklist(String phone, String token) async {
    try {
      final result = await ContactPersonApiService().checkPhoneBlacklist(
        candidateId: widget.contactData?.candidateId?.toString() ?? '',
        tel: phone,
        token: token,
      );

      setState(() {
        _isVerifying = false;

        if (result != null) {
          if (result?.code == 0) {
            if (result?.data != null) {
              final resultData = result.data;
              if (resultData['passStatus']) {
                // 黑名单检查通过
                _verificationStatus = PhoneVerificationStatus.verified;
                _verificationErrorMsg = '';
                PhoneVerificationStateManager().setVerified(phone);
                debugPrint('黑名单检查通过，验证成功');
              } else {
                final rejectReason = resultData['rejectReason'];
                String errorMsg = '';
                if (rejectReason == null) {
                  errorMsg = '提交失败，请重试';
                } else if (rejectReason is List) {
                  // 如果rejectReason是数组，将多条信息换行展示
                  errorMsg = (rejectReason as List).join('\n');
                } else {
                  // 如果rejectReason是字符串或其他类型，直接转换为字符串
                  errorMsg = rejectReason.toString();
                }
                // 黑名单检查不通过
                _verificationStatus = PhoneVerificationStatus.failed;
                _verificationErrorMsg = errorMsg ?? '该手机号无法使用，请更换其他手机号';
                PhoneVerificationStateManager().setVerifyFailed(phone);
                // debugPrint('黑名单检查不通过: ${result['msg']}');
              }
            }
          } else {
            // API调用失败
            _verificationStatus = PhoneVerificationStatus.failed;
            _verificationErrorMsg = '手机号验证失败，请稍后重试';
            PhoneVerificationStateManager().setVerifyFailed(phone);
            debugPrint('API调用失败');
          }
        } else {
          // API调用失败
          _verificationStatus = PhoneVerificationStatus.failed;
          _verificationErrorMsg = '手机号验证失败，请稍后重试';
          PhoneVerificationStateManager().setVerifyFailed(phone);
          debugPrint('API调用失败');
        }
      });
    } catch (e) {
      setState(() {
        _isVerifying = false;
        _verificationStatus = PhoneVerificationStatus.failed;
        _verificationErrorMsg = '手机号验证失败，请稍后重试';
        PhoneVerificationStateManager().setVerifyFailed(phone);
      });
      debugPrint('黑名单检查失败: $e');
    }
  }

  /// 使用Yoda平台验证手机号
  Future<void> _verifyPhoneWithYoda(String phone, String token) async {
    try {
      // 显示加载提示
      EasyLoading.show(status: '验证手机...');

      // 1. 获取验证请求码
      final requestCode = await ContactPersonApiService().getRequestCode(
        phoneNumber: phone,
        token: token,
        taskId: widget.contactData?.candidateId?.toString() ?? '',
      );

      // 关闭加载提示
      EasyLoading.dismiss();

      if (requestCode == null) {
        MTFToast.showToast(msg: '验证手机失败');
        setState(() {
          _isVerifying = false;
          _verificationStatus = PhoneVerificationStatus.failed;
          _verificationErrorMsg = '验证手机失败，请重试';
          PhoneVerificationStateManager().setVerifyFailed(phone);
        });
        return;
      }

      // 2. 打开验证页面
      final yodaDomain = await ContactPersonApiService().getYodaDomain();
      //&env=prod
      final verifyUrl =
          '$yodaDomain/v2/app/general_page?requestCode=$requestCode&succCallbackKNBFun=close';
      final result = await RouteUtils.open(verifyUrl);

      // 3. 处理验证结果
      try {
        if (result == null || result.data == null || result.code == 0) {
          setState(() {
            _isVerifying = false;
            _verificationStatus = PhoneVerificationStatus.notVerified;
            _verificationErrorMsg = '请验证联系人手机';
            PhoneVerificationStateManager().reset(phone);
          });
          return;
        }

        Map<String, dynamic> jsonData;
        if (result.data is String) {
          jsonData = json.decode(result.data as String);
        } else if (result.data is Map) {
          jsonData = result.data as Map<String, dynamic>;
        } else {
          debugPrint('无效的验证结果格式');
        }
        // 验证成功{status:1, responseCode:$responseCode, requestCode:$requestCode}
        // 验证失败{status: 0, code:$code, msg:$msg}
        final String responseCode = jsonData['responseCode']?.toString() ?? '';
        final int status = jsonData['status'] as int ?? 0;
        final String errorMsg = jsonData['msg']?.toString() ?? '';
        final int errorCode = jsonData['code'] as int ?? 0;
        // 上报验证结果
        ContactPersonApiService().updateVerificationResult(
          taskId: widget.contactData?.candidateId?.toString() ?? '',
          requestCode: requestCode,
          responseCode: responseCode,
          errorCode: errorCode,
          token: token,
        );

        if (status == 1) {
          debugPrint('Yoda验证通过，调用check/tel接口验证');
          // Yoda验证通过，再调用check/tel接口验证
          _checkPhoneBlacklist(phone, token);
        } else {
          debugPrint('Yoda验证失败: $errorMsg');
          // Yoda验证失败
          setState(() {
            _isVerifying = false;
            _verificationStatus = PhoneVerificationStatus.failed;
            _verificationErrorMsg = errorMsg.isNotEmpty ? errorMsg : '验证失败，请重试';
            PhoneVerificationStateManager().setVerifyFailed(phone);
          });
        }
      } catch (e) {
        debugPrint('解析验证结果失败: $e');
        MTFToast.showToast(msg: '验证过程出错，请重试1: $e');
        setState(() {
          _isVerifying = false;
          _verificationStatus = PhoneVerificationStatus.failed;
          _verificationErrorMsg = '验证过程出错，请重试';
          PhoneVerificationStateManager().setVerifyFailed(phone);
        });
      }
    } catch (e) {
      // 关闭加载提示
      EasyLoading.dismiss();

      debugPrint('验证手机号过程中出错: $e');
      MTFToast.showToast(msg: '验证过程出错，请重试2: $e');
      setState(() {
        _isVerifying = false;
        _verificationStatus = PhoneVerificationStatus.failed;
        _verificationErrorMsg = '验证过程出错，请重试';
        PhoneVerificationStateManager().setVerifyFailed(phone);
      });
    }
  }

  /// 提交表单
  void _submit() {
    if (_isSubmitting) return;

    // 重置错误信息
    setState(() {
      _nameError = null;
      _phoneError = null;
    });

    // 校验联系人
    final nameError = FormValidator.validateName(_nameController.text);
    if (_nameController.text.isEmpty || nameError != null) {
      setState(() {
        _nameError = _nameController.text.isEmpty ? '请输入联系人姓名' : nameError;
      });
      return;
    }

    // 校验手机号
    final phoneError = FormValidator.validatePhone(_phoneController.text);
    if (_phoneController.text.isEmpty || phoneError != null) {
      setState(() {
        _phoneError = _phoneController.text.isEmpty ? '请输入联系电话' : phoneError;
      });
      return;
    }

    // 校验验证状态
    if (_verificationStatus != PhoneVerificationStatus.verified) {
      setState(() {
        _verificationErrorMsg = '请先完成手机号验证';
      });
      return;
    }

    _submitContactInfo();
  }

  /// 提交联系人信息
  Future<void> _submitContactInfo() async {
    try {
      // 调用提交回调
      widget.onSubmit(
        _nameController.text,
        _phoneController.text,
        _signUpPhone,
      );
    } catch (e) {
      // 关闭加载提示
      EasyLoading.dismiss();
      debugPrint('提交联系人信息失败: $e');
      MTFToast.showToast(msg: '提交失败，请重试');
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }
}

/// ------------------------------- UI组件 ------------------------------- ///

/// 联系人表单布局
class _ContactFormLayout extends StatelessWidget {
  final bool isKeyboardVisible;
  final double viewPaddingBottom;
  final double viewInsetBottom;
  final Widget header;
  final Widget form;
  final Widget actionButton;
  final ScrollController scrollController;

  const _ContactFormLayout({
    Key key,
    @required this.isKeyboardVisible,
    @required this.viewPaddingBottom,
    @required this.viewInsetBottom,
    @required this.header,
    @required this.form,
    @required this.actionButton,
    this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取屏幕高度和安全区域信息
    final screenHeight = MediaQuery.of(context).size.height;
    final topSafeAreaHeight = MediaQuery.of(context).viewPadding.top;

    // 计算键盘高度
    final keyboardHeight = viewInsetBottom;

    // 计算底部安全区域高度
    final bottomSafeAreaHeight = viewPaddingBottom;

    // 预留顶部空间，避免遮挡页面标题（状态栏 + 导航栏 + 额外间距）
    final topReservedHeight = topSafeAreaHeight + 44 + 20; // 44是导航栏高度，20是额外间距

    // 计算可用高度（屏幕高度减去键盘高度、底部安全区域和顶部预留空间）
    final availableHeight = screenHeight -
        keyboardHeight -
        bottomSafeAreaHeight -
        topReservedHeight;

    // 设置弹窗的最大高度，避免遮挡页面标题
    double containerHeight;
    final defaultHeight = screenHeight * 0.6;

    if (isKeyboardVisible) {
      // 键盘弹起时，使用可用高度的85%，确保不会太高
      final maxHeightWithKeyboard = availableHeight * 0.85;
      containerHeight = maxHeightWithKeyboard.clamp(200.0, defaultHeight);
    } else {
      // 键盘未显示时使用默认高度
      containerHeight = defaultHeight;
    }

    // 确保最小高度为200，最大高度不超过可用高度的85%
    containerHeight = containerHeight.clamp(200.0, availableHeight * 0.85);

    return Container(
      height: containerHeight,
      padding: EdgeInsets.fromLTRB(
          16, 12, 16, isKeyboardVisible ? 16 : viewPaddingBottom + 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          header,
          const SizedBox(height: 24),
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              child: form,
            ),
          ),
          const SizedBox(height: 8),
          actionButton,
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}

/// 联系人表单字段
class _ContactFormField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final FocusNode focusNode;
  final String hintText;
  final String errorText;
  final Function(bool) onFocusChange;
  final List<TextInputFormatter> inputFormatters;
  final TextInputType keyboardType;
  final ValueChanged<String> onChanged;
  final int maxLength;

  const _ContactFormField({
    Key key,
    @required this.label,
    @required this.controller,
    this.focusNode,
    this.hintText,
    this.errorText,
    this.onFocusChange,
    this.inputFormatters,
    this.keyboardType,
    this.onChanged,
    this.maxLength,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 70,
              child: Text(
                label ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                ),
              ),
            ),
            Expanded(
              child: TextField(
                controller: controller,
                focusNode: focusNode,
                keyboardType: keyboardType,
                inputFormatters: inputFormatters,
                onChanged: onChanged,
                decoration: InputDecoration(
                  counterText: '',
                  hintText: hintText ?? '',
                  hintStyle: const TextStyle(
                    color: Color(0xFFCCCCCC),
                    fontSize: 14,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
                maxLength: maxLength ?? 16,
              ),
            ),
          ],
        ),
        const Divider(
          height: 1,
          thickness: 1,
          color: Color(0xFFEEEEEE),
        ),
        SizedBox(height: errorText != null ? 4 : 0),
        Visibility(
          visible: errorText != null,
          child: Text(
            errorText ?? '',
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFFFF4B33),
            ),
            textAlign: TextAlign.left,
          ),
        ),
      ],
    );
  }
}

/// 验证手机按钮
class _VerifyPhoneButton extends StatelessWidget {
  final bool isPhoneValid;
  final PhoneVerificationStatus verificationStatus;
  final VoidCallback onVerify;

  const _VerifyPhoneButton({
    Key key,
    @required this.isPhoneValid,
    @required this.verificationStatus,
    @required this.onVerify,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 根据验证状态决定按钮文本和颜色
    String buttonText = '';
    Color buttonColor = const Color(0xFFFF6E30);
    bool isEnabled = false;

    if (isPhoneValid) {
      switch (verificationStatus) {
        case PhoneVerificationStatus.notVerified:
          buttonText = '验证手机';
          isEnabled = true;
          break;
        case PhoneVerificationStatus.verifying:
          buttonText = '验证中...';
          isEnabled = false;
          break;
        case PhoneVerificationStatus.verified:
          buttonText = '验证通过';
          buttonColor = const Color(0xFF52C41A); // 成功绿色
          isEnabled = false;
          break;
        case PhoneVerificationStatus.failed:
          buttonText = '重新验证';
          isEnabled = true;
          break;
      }
    }

    return GestureDetector(
      onTap: isEnabled ? onVerify : null,
      child: Container(
        padding: EdgeInsets.zero,
        child: Text(
          buttonText,
          style: TextStyle(
            color: isEnabled ? buttonColor : buttonColor.withOpacity(0.5),
            fontSize: 14,
          ),
        ),
      ),
    );
  }
}

/// 联系人表单按钮
class _ContactFormButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;

  const _ContactFormButton({
    Key key,
    @required this.text,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: GestureDetector(
        onTap: onPressed,
        child: Container(
          decoration: BoxDecoration(
            color: onPressed == null ? Colors.grey : Colors.black,
            borderRadius: BorderRadius.circular(24),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12),
          alignment: Alignment.center,
          child: Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}
