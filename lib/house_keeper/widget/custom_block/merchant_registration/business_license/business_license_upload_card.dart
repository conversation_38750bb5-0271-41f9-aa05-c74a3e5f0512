import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/ruzhu_base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import '../common/image_upload_guide_bottom_sheet.dart';
import 'package:mtf_toast/mtf_toast.dart';
import '../utils/api_utils.dart';
import '../api/base/base_api_service.dart';
import 'business_license_sub_type.dart';
import '../utils/next_card_util.dart';
import 'business_license_choice_card.dart';
import '../api/api_service2.dart';

/// 营业执照上传卡片
class BusinessLicenseUploadCard extends RuzhuBaseBlockWidget {
  final HouseKeeperMessagePageModel model;
  const BusinessLicenseUploadCard({
    Key key,
    @required String content,
    this.model,
    String messageId,
    int realMessageId,
  }) : super(
            key: key,
            content: content,
            messageId: messageId,
            realMessageId: realMessageId);

  @override
  _BusinessLicenseUploadCardState createState() =>
      _BusinessLicenseUploadCardState();
}

class _BusinessLicenseUploadCardState
    extends BaseBlockWidgetState<BusinessLicenseUploadCard> {
  BusinessLicenseSubType _subType = BusinessLicenseSubType.filling;
  String _uploadedImageUrl = ''; // MTCloud 返回的原始URL
  String _realImageUrl = ''; // 真实可访问的URL
  int _candidateId = 0; // 添加candidateId字段
  String _token = ''; // 添加candidateId字段
  int _snapshotId;
  bool _isImageLoadFailed = false; // 添加语义化的图片加载失败状态变量

  // 修改字段命名以匹配接口
  String number = ''; // 注册号/统一社会信息代码
  String name = ''; // 名称
  String person = ''; // 法定代表人
  String address = ''; // 法定代表人
  String time = ''; // 有效期

  static const List<String> exampleImages = [
    'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/licence1.png',
    'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/licence2.png',
    'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/licence3.png',
    'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/new_kd_example_images/licence4.png',
  ];

  // 在类中添加状态字段
  int _status = 1; // 默认状态为1，表示正常状态

  // 获取真实图片URL
  void _getRealImageUrl() {
    if (_uploadedImageUrl == null || _uploadedImageUrl.isEmpty) return;

    setState(() {
      _isImageLoadFailed = false; // 重置失败状态
    });

    ImageUploadApi()
        .getMtcloudImageUrl(
            imageUrl: _uploadedImageUrl,
            taskId: _candidateId,
            token: _token,
            messageId: widget.realMessageId,
            snapshotId: _snapshotId)
        .then((response) {
      if (response != null && response.code == 0) {
        setState(() {
          _realImageUrl = response.data;
        });
      } else {
        setState(() {
          _isImageLoadFailed = true; // 设置失败状态
        });
      }
    }).catchError((error) {
      setState(() {
        _isImageLoadFailed = true; // 设置失败状态
      });
    });
  }

  @override
  void initState() {
    final params = CommonParamsUtils.getInstance().getCommonParams();
    _token = params['token'];
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessLicenseUploadCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {}
  }

  @override
  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;

      setState(() {
        _snapshotId = data['snapshotId'] as int ?? 0; // 解析candidateId
        _candidateId =
            data['candidateId'] is int ? data['candidateId'] as int : 0;
        _subType = getBusinessLicenseSubType(
            data['subType'] is int ? data['subType'] : 1);

        _status =
            data['status'] is int ? data['status'] as int : 1; // 解析status字段

        final innerData = data['data'];
        // 添加空值检查，确保innerData不为null
        if (innerData != null && innerData is Map<String, dynamic>) {
          _uploadedImageUrl =
              innerData['url'] is String ? innerData['url'] as String : '';

          number = innerData['number'] is String
              ? innerData['number'] as String
              : '';
          name = innerData['name'] is String ? innerData['name'] as String : '';
          time = innerData['time'] is String ? innerData['time'] as String : '';
          person = innerData['person'] is String
              ? innerData['person'] as String
              : '';
          address = innerData['address'] is String
              ? innerData['address'] as String
              : '';
        }
      });

      _getRealImageUrl();
    } catch (e) {
      MTFToast.showToast(msg: '解析操作卡片数据时出错: $e');
    }
  }

  /// 跳转到营业执照页面
  Future<dynamic> _navigateToLicenseUploadPage({String imageUrl}) async {
    if (_candidateId == null) {
      MTFToast.showToast(msg: 'taskId不能为空');
      return null;
    }

    final host = await ApiUtils.getApiHost();
    if (host == null || host.isEmpty) {
      debugPrint('获取API host失败');
      MTFToast.showToast(msg: 'host失败');
      return null;
    }

    // 构建查询参数
    final Map<String, String> queryParams = {
      'hideNativeNavBar': '1',
      'taskId': _candidateId.toString(),
      'scene': 'assistant',
      'snapshotId': _snapshotId.toString(),
      'token': _token ?? '',
    };

    // 如果有图片URL，添加到参数中
    if (imageUrl?.isNotEmpty == true) {
      queryParams['imageUrl'] = imageUrl;
    }

    final params = CommonParamsUtils.getInstance().getCommonParams() ?? {};
    queryParams['token'] = params['token'];

    try {
      // 使用 Uri.parse 构建基础 URI
      final baseUri = Uri.parse(host);

      // 构建完整的路径
      final path = '${baseUri.path}/kd';

      // 构建查询字符串
      final queryString = Uri(queryParameters: queryParams).query;

      // 组合最终URL
      final fullUrl =
          '$host$path?hideNativeNavBar=1#/pages/licence/index?$queryString';

      debugPrint('跳转URL: $fullUrl');
      return RouteUtils.open(fullUrl);
    } catch (e) {
      debugPrint('URL构建失败: $e');
      MTFToast.showToast(msg: '页面跳转失败$e');
      return null;
    }
  }

  Future<void> _pickAndUploadImage() async {
    final result = await showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => ImageUploadGuideBottomSheet(
          exampleImages: exampleImages,
          uploadType: 'mtcloud',
          token: _token,
          candidateId: _candidateId),
    );

    if (result?.isEmpty ?? true) {
      return;
    }

    final imageUrl = result;
    final pageResult = await _navigateToLicenseUploadPage(imageUrl: imageUrl);

    if (pageResult != null && pageResult.code == RouteResult.ok) {
      debugPrint('H5页面关闭，返回结果: $pageResult');

      widget.model?.updateCard(widget.realMessageId);
      _addNextCard(expectedStatus: 2);
    }
  }

  @override
  Widget buildContentView() {
    // 如果 subType 为 2，渲染 BusinessLicenseChoiceCard
    if (_subType == BusinessLicenseSubType.registered) {
      return BusinessLicenseChoiceCard(
        candidateId: _candidateId,
        token: _token,
        status: _status,
        snapshotId: _snapshotId,
        messageId: widget.messageId,
        onChoiceComplete: () {
          widget.model?.updateCard(widget.realMessageId);
          _addNextCard(expectedStatus: 2);
        },
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 14),
          // 示例区域或已上传图片
          Container(
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 34.5), // 添加左右间距
            child: _buildImageView(), // 修改图片展示部分
          ),

          const SizedBox(height: 16),

          // 使用新的方法展示营业执照信息
          _buildLicenseInfo(),

          const SizedBox(height: 16),

          // 底部按钮
          _buildButtons(),
        ],
      ),
    );
  }

  // 添加信息行构建方法
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 140,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF222222),
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontFamily: 'PingFang SC',
              fontWeight: FontWeight.w600, // 添加加粗
              color: Color(0xFF333333),
            ),
          ),
        ),
      ],
    );
  }

  // 构建主动修改状态按钮
  Widget _buildExpiredButton() {
    return const CustomButton(
      text: '已过期',
      onPressed: null,
      primary: false, // 可以根据具体业务逻辑控制按钮状态
    );
  }

  // 构建底部按钮
  Widget _buildButtons() {
    int _status =
        RuzhuCardStatusManager().getStatus(widget.messageId, this._status);

    // 首先检查状态
    if (_status == 3) {
      return _buildExpiredButton();
    } else if (_status == 2) {
      return _buildFilledButton();
    } else if (_status == 0) {
      return const CustomButton(
        text: '已修改',
        onPressed: null,
        primary: false, // 可以根据具体业务逻辑控制按钮状态
      );
    } else if (_status == 1) {
      return _buildSubTypeButtons();
    }

    return const SizedBox.shrink();
  }

  // 添加已填写按钮
  Widget _buildFilledButton() {
    return const CustomButton(
      text: '已填写',
      onPressed: null,
      primary: false, // 可以根据具体业务逻辑控制按钮状态
    );
  }

  // 构建填写状态按钮
  Widget _buildFillingButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '上传营业执照',
            onPressed: _pickAndUploadImage,
          ),
        ),
      ],
    );
  }

  // 构建驳回状态按钮
  Widget _buildRejectedButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '去修改',
            onPressed: _handleModifyDirectly,
          ),
        ),
      ],
    );
  }

  /// 添加下一张卡片的通用方法
  Future<void> _addNextCard({int expectedStatus = 3}) async {
    try {
      NextCardUtil.addNextCard(widget.messageId,
          model: widget.model,
          candidateId: _candidateId,
          token: _token,
          expectedStatus: expectedStatus);
    } catch (e) {
      debugPrint('添加下一张卡片失败: $e');
    }
  }

  void _handleCancelAndKeepUsing() async {
    await MerchantApiService().saveCardSnapshot(
      candidateId: _candidateId,
      snapshotId: _snapshotId,
    );
    widget.model?.updateCard(widget.realMessageId);
    _addNextCard(expectedStatus: 2);
  }

  Future<void> _handleModifyDirectly() async {
    final pageResult = await _navigateToLicenseUploadPage();

    if (pageResult != null && pageResult.code == RouteResult.ok) {
      debugPrint('H5页面关闭，返回结果: $pageResult');

      widget.model?.updateCard(widget.realMessageId);
      _addNextCard(expectedStatus: 2);
    }
  }

  // 构建建议修改状态按钮
  Widget _buildSuggestedButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '坚持使用',
            onPressed: _handleCancelAndKeepUsing,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildRejectedButtons(),
        ),
      ],
    );
  }

  // 构建主动修改状态按钮
  Widget _buildModifyingButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '取消',
            primary: false,
            onPressed: () {
              _handleCancelAndKeepUsing();
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: CustomButton(text: '去修改', onPressed: _handleModifyDirectly),
        ),
      ],
    );
  }

  // 图片展示主方法
  Widget _buildImageView() {
    return Container(
      height: 154,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
      ),
      child: Stack(
        children: [
          _buildDefaultImageWithExample(),
          _buildLoadingIndicator(),
          _buildFailedImage(),
          _buildUploadedImage(),
        ],
      ),
    );
  }

  // 构建默认图片和示例标签
  Widget _buildDefaultImageWithExample() {
    const String defaultImageUrl =
        'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/kd_example_images/licence5.png';

    return Visibility(
      visible: _uploadedImageUrl?.isEmpty ?? true,
      child: Stack(
        children: [
          _buildCenteredImage(defaultImageUrl),
          _buildExampleLabel(),
        ],
      ),
    );
  }

  // 构建居中的图片
  Widget _buildCenteredImage(String imageUrl) {
    return Center(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: Image.network(
          imageUrl,
          height: 154,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  // 构建示例标签
  Widget _buildExampleLabel() {
    return (_uploadedImageUrl?.isEmpty ?? true)
        ? Positioned(
            top: 0,
            left: 0,
            child: Container(
              decoration: const BoxDecoration(
                color: Color(0xB7000000),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  bottomRight: Radius.circular(12),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              child: const Text(
                '示例',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          )
        : const SizedBox.shrink();
  }

  // 构建加载指示器
  Widget _buildLoadingIndicator() {
    return Visibility(
      visible: _isImageLoading(),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6E30)),
        ),
      ),
    );
  }

  // 构建加载失败时的图片
  Widget _buildFailedImage() {
    const String defaultImageUrl =
        'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/kd_example_images/licence5.png';

    return Visibility(
      visible: _isImageLoadFailed,
      child: _buildCenteredImage(defaultImageUrl),
    );
  }

  // 构建已上传的图片
  Widget _buildUploadedImage() {
    const String defaultImageUrl =
        'https://s3plus.meituan.net/v1/mss_6b7c26b3db4c4bbebdbb4d7a9bb76633/static-pc/welcome/kd_example_images/licence5.png';

    return Visibility(
      visible: _isUploadedImageVisible(),
      child: Center(
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: Image.network(
            _realImageUrl,
            fit: BoxFit.contain,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF6E30)),
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: Image.network(defaultImageUrl, fit: BoxFit.contain),
              );
            },
          ),
        ),
      ),
    );
  }

  // 判断是否正在加载图片
  bool _isImageLoading() {
    return _uploadedImageUrl?.isNotEmpty == true &&
        _realImageUrl.isEmpty &&
        !_isImageLoadFailed;
  }

  // 判断已上传图片是否可见
  bool _isUploadedImageVisible() {
    return _uploadedImageUrl?.isNotEmpty == true &&
        _realImageUrl.isNotEmpty &&
        !_isImageLoadFailed;
  }

  // 添加营业执照信息展示
  Widget _buildLicenseInfo() {
    bool hasAnyInfo = (number?.isNotEmpty == true) ||
        (name?.isNotEmpty == true) ||
        (person?.isNotEmpty == true);

    if (!hasAnyInfo) {
      return Container();
    }

    List<Widget> children = [];

    if (number?.isNotEmpty == true) {
      children.add(_buildInfoRow('注册号/统一社会信息代码', number));
      children.add(const SizedBox(height: 8));
    }

    if (name?.isNotEmpty == true) {
      children.add(_buildInfoRow('名称', name));
      children.add(const SizedBox(height: 8));
    }

    if (person?.isNotEmpty == true) {
      children.add(_buildInfoRow('法定代表人', person));
      children.add(const SizedBox(height: 8));
    }

    if (address?.isNotEmpty == true) {
      children.add(_buildInfoRow('地址', address));
      children.add(const SizedBox(height: 8));
    }

    if (time?.isNotEmpty == true) {
      children.add(_buildInfoRow('有效期', time));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  // 根据subType构建对应按钮
  Widget _buildSubTypeButtons() {
    switch (_subType) {
      case BusinessLicenseSubType.modifying:
        return _buildModifyingButtons();
      case BusinessLicenseSubType.suggested:
        return _buildSuggestedButtons();
      case BusinessLicenseSubType.rejected:
        return _buildRejectedButtons();
      case BusinessLicenseSubType.filling:
        return _buildFillingButtons();
      default:
        return Container();
    }
  }
}
