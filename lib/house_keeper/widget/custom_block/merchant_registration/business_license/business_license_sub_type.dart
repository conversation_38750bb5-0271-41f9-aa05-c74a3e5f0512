/// 营业执照子类型枚举
enum BusinessLicenseSubType {
  filling, // 填写
  rejected, // 填写驳回
  suggested, // 建议修改
  modifying, // 主动修改
  registered, // 营业执照已经在平台注册
}

/// 获取营业执照子类型
BusinessLicenseSubType getBusinessLicenseSubType(int value) {
  switch (value) {
    case 1:
      return BusinessLicenseSubType.filling;
    case 2:
      return BusinessLicenseSubType.registered;
    case 3:
      return BusinessLicenseSubType.suggested;
    case 4:
      return BusinessLicenseSubType.modifying;
    case 5:
      return BusinessLicenseSubType.rejected;
    default:
      return BusinessLicenseSubType.filling;
  }
}
