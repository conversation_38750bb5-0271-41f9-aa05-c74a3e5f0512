import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/utils/ruzhu_card_status_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/common/custom_button.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/api/services/action_tracking_api_service.dart';
import '../utils/api_utils.dart';

/// 营业执照选择卡片
class BusinessLicenseChoiceCard extends StatelessWidget {
  final int candidateId;
  final String token;
  final String messageId;
  final int snapshotId;
  final int status;
  final VoidCallback onChoiceComplete;

  const BusinessLicenseChoiceCard({
    Key key,
    @required this.candidateId,
    @required this.token,
    @required this.snapshotId,
    @required this.status,
    @required this.messageId,
    this.onChoiceComplete,
  }) : super(key: key);

  // 更换营业执照
  Future<void> _changeLicense() async {
    final host = await ApiUtils.getApiHost();
    final fullUrl =
        '$host/kd?hideNativeNavBar=1#/pages/licence/index?taskId=$candidateId&scene=assistant&snapshotId=$snapshotId&token=$token';

    RouteUtils.open(fullUrl).then((value) {
      if (onChoiceComplete != null) {
        onChoiceComplete();
      }
    });
  }

  // 去开多家店
  Future<void> _openMultipleShops() async {
    final host = await ApiUtils.getApiHost();
    ActionTrackingApiService().recordAction(
      candidateId: '$candidateId',
      keys: ['certRepeatBackToOldPage'],
    ).catchError((e) {
      // 可以选择记录错误但不中断执行
    });

    RouteUtils.open(
        '$host/kd/?hideNativeNavBar=1#/pages/qualification/index?taskId=$candidateId&token=$token&scene=assistant');
  }

  /// 构建按钮区域
  Widget _buildButtonArea() {
    int _status = RuzhuCardStatusManager().getStatus(messageId, status);
    if (_status == 3) {
      return const CustomButton(
        text: '已过期',
        onPressed: null,
        primary: false,
      );
    } else if (_status == 2) {
      return const CustomButton(
        text: '已填写',
        onPressed: null,
        primary: false,
      );
    }

    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '更换营业执照',
            onPressed: _changeLicense,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: CustomButton(
            text: '去开多家店',
            onPressed: _openMultipleShops,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        _buildButtonArea(),
      ],
    );
  }
}
