import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/common_params_utils.dart';
import '../models/response/task_progress_response.dart';
import '../api/services/task_progress_api_service.dart';
import '../utils/next_card_util.dart';
import 'package:roo_flutter/roo_flutter.dart';

// 卡片信息辅助类
class CardInfo {
  final String moduleId;
  final String cardId;

  CardInfo(this.moduleId, this.cardId);
}

const String ruzhuActionUpdateProgress = "ruzhu_action_update_progress";

class TaskProgressWidget extends StatefulWidget {
  final HouseKeeperMessagePageModel pageModel;

  const TaskProgressWidget({
    Key key,
    @required this.pageModel,
  }) : super(key: key);

  @override
  _TaskProgressWidgetState createState() => _TaskProgressWidgetState();
}

class _TaskProgressWidgetState extends State<TaskProgressWidget> {
  List<ModuleStatus> _taskProgressData = [];
  bool _isLoading = false;
  String _taskProgress = "0/0";
  StreamSubscription subscription;
  GlobalKey helpIconKey = GlobalKey();
  bool _isShowingTaskProgress = false; // 添加标志变量，用于防止连续点击

  @override
  void initState() {
    super.initState();

    subscription = RouteUtils.subscribe(ruzhuActionUpdateProgress, (data) {
      _loadTaskProgress(isNeedLoading: false, isNeedResetDataWhenFailed: false);
    });

    _loadTaskProgress();
  }

  @override
  void dispose() {
    subscription?.cancel();
    super.dispose();
  }

  Future<void> _loadTaskProgress(
      {bool isNeedLoading = true,
      bool isNeedResetDataWhenFailed = true}) async {
    if (isNeedLoading) {
      setState(() {
        _isLoading = true;
      });
    }
    // 点击时获取数据并显示弹窗
    TaskProgressApiService service = TaskProgressApiService();
    List<ModuleStatus> modules;
    String taskProgress;
    Map<String, dynamic> params =
        CommonParamsUtils.getInstance().getCommonParams();
    final candidateId = int.tryParse(params['candidateId']?.toString() ?? '');

    TaskProgressData response = await service.getAllCardStatus(
        candidateId: candidateId ?? 0, token: params['token']);
    if (response != null) {
      // 请求成功，使用真实数据
      modules = response.moduleStatus;
      taskProgress = _calculateTaskProgress(modules);
      setState(() {
        _isLoading = false;
        _taskProgressData = modules;
        _taskProgress = taskProgress;
      });
    } else {
      // 请求失败
      setState(() {
        _isLoading = false;
        if (isNeedResetDataWhenFailed) {
          _taskProgressData = [];
          _taskProgress = '0/0';
        }
      });
    }
  }

  // 计算真实数据的进度
  String _calculateTaskProgress(List<ModuleStatus> modules) {
    if (modules == null || modules.isEmpty) return "0/0";

    // 总步骤数
    int total = modules.length;

    // 计算已完成数量：总数 - 未完成 - 已驳回
    int unfinished = modules
        .where((module) =>
            module.detailStatus == "noWrite" || module.detailStatus == "reject")
        .length;
    int finished = total - unfinished;
    return "$finished/$total";
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        // 如果正在显示弹窗或加载数据，则不响应点击
        if (_isShowingTaskProgress || _isLoading) return;
        _showTaskProgress(context, _taskProgressData, _taskProgress);
      },
      child: Container(
        height: 38,
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              "开店进度",
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333),
              ),
            ),
            Row(
              children: [
                _isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Color(0xFF666666)),
                        ),
                      )
                    : Text(
                        "${_taskProgress}",
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF666666),
                        ),
                      ),
                const SizedBox(width: 4),
                const Icon(
                  Icons.keyboard_arrow_up,
                  color: Color(0xFF999999),
                  size: 14,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showTaskProgress(
      BuildContext context, List<ModuleStatus> modules, String taskProgress) {
    // 设置标志，防止重复点击
    setState(() {
      _isShowingTaskProgress = true;
    });

    // 使用封装好的_loadTaskProgress方法获取最新数据
    _loadTaskProgress(isNeedLoading: true, isNeedResetDataWhenFailed: false)
        .then((_) {
      // 数据加载完成后，显示底部弹窗
      _showBottomSheet(context, _taskProgressData, _taskProgress);
    });
  }

  // 抽取显示底部弹窗的逻辑为单独的方法
  void _showBottomSheet(
      BuildContext context, List<ModuleStatus> modules, String taskProgress) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.7,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.only(bottom: 16),
                child: Stack(
                  children: [
                    // 关闭按钮
                    Positioned(
                      right: 0,
                      top: 0,
                      child: GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: const Icon(
                          Icons.close,
                          size: 24,
                          color: Color(0xFF999999),
                        ),
                      ),
                    ),
                    // 居中的标题和进度
                    Container(
                      width: double.infinity,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Text(
                            "开店进度",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF333333),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            "${taskProgress}",
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF666666),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: modules.length,
                  itemBuilder: (context, index) {
                    final module = modules[index];
                    return Theme(
                      data: Theme.of(context).copyWith(
                        dividerColor: Colors.transparent,
                      ),
                      child: ExpansionTile(
                        iconColor: const Color(0xFF000000),
                        textColor: const Color(0xFF000000),
                        collapsedIconColor: Colors.black,
                        collapsedTextColor: Colors.black,
                        // 添加这些属性来去掉边框
                        tilePadding: const EdgeInsets.symmetric(horizontal: 16),
                        childrenPadding: EdgeInsets.zero,
                        title: Row(
                          children: [
                            Text(
                              "第${_getChineseNumber(index + 1)}步： ",
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              _getModuleName(module.moduleName),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            // 为合作方案添加问号图标
                            Visibility(
                              visible: module.moduleName == 'cooperation',
                              child: Padding(
                                padding: const EdgeInsets.only(left: 8.0),
                                child: RooPopoverMenuTheme(
                                  data: RooPopoverMenuTheme.of(context)
                                      .copyWith(borderRadius: 8),
                                  child: GestureDetector(
                                    key: helpIconKey,
                                    child: Image(
                                      width: 14,
                                      height: 14,
                                      image: AdvancedNetworkImage(
                                          'https://p0.meituan.net/waimaieassets/1541c40ccc6f8174fe5e909a13134b152081.png'),
                                    ),
                                    onTap: () {
                                      _showHelpPopover(context, helpIconKey);
                                    },
                                  ),
                                ),
                              ),
                            ),
                            Expanded(child: Container()), // 添加Expanded使后面的元素右对齐
                            _buildStatusWithButton(context, module.moduleName,
                                module.detailStatus),
                          ],
                        ),
                        children: module.cardList.map<Widget>((card) {
                          return Container(
                            padding: const EdgeInsets.fromLTRB(32, 8, 16, 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    _getCardName(card.cardCode),
                                    style: const TextStyle(fontSize: 14),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                _buildStatusWithButton(
                                    context, module.moduleName, '',
                                    isCard: true,
                                    cardId: card.cardCode,
                                    cardStatus: card.status),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    ).then((_) {
      // 弹窗关闭后，重置标志，允许再次点击
      setState(() {
        _isShowingTaskProgress = false;
      });
    });
  }

  void _showHelpPopover(BuildContext context, GlobalKey key) {
    RooPopoverMenu menu = RooPopoverMenu(
      key.currentContext,
      arrowDirection: ArrowDirection.bottomCenter,
      items: [
        PopoverMenuItem(
          title: '', // 清空标题，使用customWidget
          customWidget: Container(
            width: double.infinity, // 占满整个宽度
            alignment: Alignment.center, // 内容居中对齐
            child: const Text(
              '完成前序步骤后开启',
              style: TextStyle(
                color: Color(0xFFFFFFFF),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
      dividerHeight: 0,
      itemPadding: 0,
      maxColumn: 1,
      itemHeight: 38,
      height: 38, // 设置总高度与 itemHeight 相同，去掉下边间距
      highlightColor: Colors.transparent, // 去掉长按高亮效果
      onClickMenu: null,
      stateChanged: null,
      onDismiss: null,
    );
    menu.show(widgetKey: key);
  }

  Widget _buildStatusWithButton(
      BuildContext context, String moduleId, String moduleStatus,
      {bool isCard = false, String cardId, int cardStatus}) {
    if (!isCard) {
      return _buildStatusTag(moduleStatus);
    }
    return _buildActionButton(context, moduleId, cardStatus, cardId);
  }

  Widget _buildStatusTag(String status) {
    Color statusColor;
    String statusText;

    switch (status) {
      case 'noWrite':
        statusColor = const Color(0xFF999999);
        statusText = "未完成";
        break;
      case 'hasRecord':
        statusColor = const Color(0xFF00BF80);
        statusText = "已完成";
        break;
      case 'passed':
        statusColor = const Color(0xFFFF9F00);
        statusText = "已生效";
        break;
      case 'auditing':
        statusColor = const Color(0xFF00BF80);
        statusText = "审核中";
        break;
      case 'reject':
        statusColor = const Color(0xFFFF4B33);
        statusText = "已驳回";
        break;
      case 'revised':
        statusColor = const Color(0xFFFF4B33);
        statusText = "已修改";
        break;
      default:
        statusColor = const Color(0xFF999999);
        statusText = "未知";
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: statusColor,
          width: 1,
        ),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: statusColor,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildActionButton(
      BuildContext context, String moduleId, int cardStatus, String cardId) {
    // 如果卡片状态为已提交审核(5)，则不显示按钮
    if (cardStatus == 5) {
      return Container(); // 返回一个空的Container
    }

    String buttonText;
    bool buttonEnabled = true;

    final buttonTextMap = {
      0: "去填写",
      1: "去修改",
      3: "驳回重填",
      4: "去修改",
      6: "去修改",
    };
    // (0, "未完成"),(1, "录入完成"),(3, "被驳回待修改"),(4, "已修改"),(5, "已提交审核"),(6, "审核通过")
    buttonText = buttonTextMap[cardStatus] ?? "去填写";

    if (moduleId == 'cooperation') {
      if (cardStatus == 0) {
        buttonText = "去填写";
        // } else if (cardStatus == 5) {
        //   buttonText = "去查看"; // 已提交审核状态不显示按钮
      } else {
        buttonText = "去修改";
      }

      // 检查前三个步骤是否都已完成
      bool allPreviousStepsCompleted = _checkPreviousStepsCompleted();

      // 如果前三个步骤中有任何一个未完成，禁用"合作方案"的按钮
      if (!allPreviousStepsCompleted) {
        buttonEnabled = false;
      }
    }

    return GestureDetector(
      onTap: buttonEnabled
          ? () => _handleButtonClick(context, moduleId, cardStatus, cardId)
          : moduleId == 'cooperation'
              ? () => _showIncompleteTaskDialog(context)
              : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color:
              buttonEnabled ? const Color(0xFFFFFFFF) : const Color(0xFFEEEEEE),
          border: Border.all(
            color: buttonEnabled
                ? const Color(0xFFEEEEEE)
                : const Color(0xFFEEEEEE),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Text(
          buttonText,
          style: TextStyle(
            color: buttonEnabled
                ? const Color(0xFF222222)
                : const Color(0xFF999999),
            fontSize: 13,
          ),
        ),
      ),
    );
  }

  void _handleButtonClick(
      BuildContext context, String moduleId, int status, String cardId) {
    // 关闭任务进度弹窗
    Navigator.pop(context);
    // 弹窗关闭后会自动触发then回调，重置_isShowingTaskProgress标志

    Map<String, dynamic> params =
        CommonParamsUtils.getInstance().getCommonParams();
    final candidateId =
        int.tryParse(params['candidateId']?.toString() ?? '0') ?? 0;

    // 添加NextCardUtil调用
    NextCardUtil.addNextCard(
      '0',
      model: widget.pageModel,
      candidateId: candidateId ?? 0,
      specifyCardId: cardId,
      token: params['token'],
      isNeedUpdateProgress: false,
    );
  }

  String _getModuleName(String moduleId) {
    switch (moduleId) {
      case 'base':
        return '店铺信息';
      case 'qualification':
        return '资质信息';
      case 'legalPerson':
        return '身份信息';
      case 'cooperation':
        return '合作方案';
      default:
        return '未知模块';
    }
  }

  String _getCardName(String cardId) {
    switch (cardId) {
      case '1000':
        return '经营品类';
      case '1100':
        return 'BD二维码';
      case '1200':
        return '联系人和联系电话';
      case '1300':
        return '门脸图和门店名称';
      case '2100':
        return '商标授权书';
      case '2200':
        return '营业执照';
      case '2300':
        return '所在区域、地址坐标和补充说明';
      case '2400':
        return '许可证';
      case '2500':
        return '补传其他许可证-清真许可证';
      case '3100':
        return '身份信息';
      case '4100':
        return '合作方案';
      default:
        return '未知模块';
    }
  }

  // 检查前三个步骤是否都已完成
  bool _checkPreviousStepsCompleted() {
    // 前三个步骤的模块名称
    final List<String> previousModules = [
      'base',
      'qualification',
      'legalPerson'
    ];

    // 如果没有任务进度数据，默认返回false
    if (_taskProgressData == null || _taskProgressData.isEmpty) {
      return false;
    }

    // 遍历所有模块状态
    for (var module in _taskProgressData) {
      // 如果是前三个步骤中的一个
      if (previousModules.contains(module.moduleName)) {
        //  查找该模块中第一个未完成的卡片
        CardStatus card = module.cardList
            .firstWhere((c) => c.status == 0, orElse: () => null);

        if (card != null) {
          return false;
        }
      }
    }

    // 所有前三个步骤都已完成
    return true;
  }

  // 显示未完成任务提示弹窗
  void _showIncompleteTaskDialog(BuildContext context) {
    // 查找第一个未完成的卡片
    CardInfo incompleteCard = _findFirstIncompleteCard();
    if (incompleteCard == null) return;

    // 构建提示信息
    String moduleName = _getModuleName(incompleteCard.moduleId);
    String cardName = _getCardName(incompleteCard.cardId);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('提示'),
          content: Text('老板，您的$moduleName填写完成后，方可选择合作方案，请您先填写$cardName。'),
          actions: <Widget>[
            TextButton(
              child: const Text('我知道了'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // 查找第一个未完成的卡片
  CardInfo _findFirstIncompleteCard() {
    // 前三个步骤的模块名称
    final List<String> previousModules = [
      'base',
      'qualification',
      'legalPerson'
    ];

    if (_taskProgressData == null || _taskProgressData.isEmpty) {
      return null;
    }

    // 按模块顺序查找第一个未完成的卡片
    for (String moduleId in previousModules) {
      // 查找对应模块
      ModuleStatus module = _taskProgressData
          .firstWhere((m) => m.moduleName == moduleId, orElse: () => null);

      if (module != null) {
        // 如果模块状态是未完成或被驳回
        // if (module.detailStatus == 'noWrite') {
        // 查找该模块中第一个未完成的卡片
        CardStatus card = module.cardList
            .firstWhere((c) => c.status == 0, orElse: () => null);

        if (card != null) {
          return CardInfo(moduleId, card.cardCode);
        }
        // }
      }
    }

    return null;
  }

  // 将数字转换为中文数字
  String _getChineseNumber(int number) {
    final List<String> chineseNumbers = [
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
      '七',
      '八',
      '九',
      '十'
    ];
    if (number > 0 && number <= chineseNumbers.length) {
      return chineseNumbers[number - 1];
    }
    return number.toString();
  }
}
