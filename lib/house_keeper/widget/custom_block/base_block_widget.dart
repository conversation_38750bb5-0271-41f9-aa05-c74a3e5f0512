import 'package:flutter/material.dart';

/// 自定义块组件基类
/// 提供加载状态处理的通用逻辑
abstract class BaseBlockWidget extends StatefulWidget {
  final String content;

  const BaseBlockWidget({
    Key key,
    @required this.content,
  }) : super(key: key);

  @override
  BaseBlockWidgetState createState();
}

/// 自定义块组件基类状态
abstract class BaseBlockWidgetState<T extends BaseBlockWidget> extends State<T>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true; // 保持状态

  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(T oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用 super.build
    return buildContentView();
  }

  /// 构建内容视图
  /// 子类必须实现此方法以提供正常内容的展示
  Widget buildContentView();
}
