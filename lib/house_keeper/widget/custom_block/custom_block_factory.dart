import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/business/business_action_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/business/business_competitor_comparison_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/business/business_finance_order_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/business/business_growth_plan_old.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/business/business_growth_plan_new.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/business/business_summary_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/business/business_order_task_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/business/business_requisition_weight_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/goods/change_goods_status_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/comment/chose_comment_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/comment/comment_reply.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/goods/goods_select_background_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/goods/combo_puzzle_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/ruzhu_block_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/unknown_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/business_license/business_license_upload_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/license/license_upload_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/id_card/id_card_upload_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/cooperation_plan/cooperation_plan_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/upload_store_storefront_image_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/upload_authorization_letter.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/position_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/bd_qrcode/merchant_bd_bind_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/contact/merchant_contact_person_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/halal_license/halal_certificate_upload_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/customer_service/customer_service_qrcode_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/merchant_registration/registration_submission_card.dart';

import 'merchant_registration/business_category/select_category.dart';
import 'merchant_registration/continue/ruzhu_continue_card.dart';

/// 自定义块工厂类
/// 集中管理各种自定义块的创建逻辑
class CustomBlockFactory {
  /// 创建自定义块小部件
  static Widget createCustomBlockWidget(
      String type, String content, HouseKeeperMessagePageModel model,
      {String messageId, int realMessageId}) {
    // 确保类型不为空
    type = (type ?? '').isEmpty ? 'info' : type;
    content = content ?? ''; // 允许内容为空，由组件内部处理流式加载状态

    // 根据标签类型选择不同的处理方式
    switch (type) {
      /////////////////////////////////////////////
      /// 商品
      /////////////////////////////////////////////
      case 'searchGoods':
        return ChangeGoodsStatusWidget(GoodsStatusType.search,
            content: content);
      case 'changePrice':
        return ChangeGoodsStatusWidget(GoodsStatusType.price, content: content);
      case 'changeStock':
        return ChangeGoodsStatusWidget(GoodsStatusType.stock, content: content);
      case 'changeSellDown':
        return ChangeGoodsStatusWidget(GoodsStatusType.down, content: content);
      case 'changeSellUp':
        return ChangeGoodsStatusWidget(GoodsStatusType.up, content: content);
      /////////////////////////////////////////////
      /// 背景图
      /////////////////////////////////////////////
      case 'batchBackground':
        return GoodsBackgroundManagerWidget(content: content, model: model);
      case 'comoPuzzle':
        return ComboPuzzleWidget(content: content, model: model);
      /////////////////////////////////////////////
      /// 评论
      /////////////////////////////////////////////
      case "commentChose":
        return CommentChoseBlockWidget(model: model, content: content);
      case "commentReply":
        return CommentCardReplyBlockWidget(content: content);
      /////////////////////////////////////////////
      /// 经分
      /////////////////////////////////////////////
      case "waimaiGrowthPlan":
        return BusinessGrowthPlanBlockWidget(content: content, model: model);
      case "wm_new_poi_waimaiGrowthPlan":
        return BusinessGrowthPlanList(content: content, model: model);
      case "wm_biz_new_poi_order_task":
        return BusinessOrderTaskCard(content: content);
      case "wm_biz_new_poi_summary_diagnosis":
        return BusinessSummaryCard(content: content);
      case "competitorComparison":
        return BusinessCompetitorComparisonCard(content: content);
      case "wmAiRequisitionWeight":
        return BusinessRequisitionWeightCard(content: content, model: model);
      case "wm_biz_finance_order":
        return BusinessFinanceOrderCard(content: content, model: model);

      /////////////////////////////////////////////
      /// 商家自入驻
      /////////////////////////////////////////////
      case "ruzhuQuaLicense":
        return BusinessLicenseUploadCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuLicense":
        return LicenseUploadCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuIdCard":
        return IdCardUploadCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuCooperation": // 新增合作方案卡片类型
        return CooperationPlanCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuPoiPic":
        return UploadStoreStorefrontImageCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuTrademarkLicense":
        return UploadAuthorizationLetterCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuPosition":
        return PositionCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuAudit":
        return RegistrationSubmissionCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );

      /////////////////////////////////////////////
      /// 通用
      /////////////////////////////////////////////
      case "commonAction":
        return BusinessActionCard(content: content);

      /////////////////////////////////////////////
      /// 自助入驻
      /////////////////////////////////////////////
      case "ruzhuBdQRCode":
        return MerchantBindBDWidget(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuContactPerson":
        return MerchantContactPersonWidget(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuQZLicense":
        return HalalCertificateUploadWidget(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuCustomerService":
        return CustomerServiceQRCodeWidget(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuTagCode":
        return SelectCategoryCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuContinue":
        return RuzhuContinueCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      case "ruzhuBlockCard":
        return RuzhuBlockCard(
          content: content,
          model: model,
          messageId: messageId,
          realMessageId: realMessageId,
        );
      default:
        // 处理未知类型
        return UnknownBlockWidget(type: type, content: content);
    }
  }
}
