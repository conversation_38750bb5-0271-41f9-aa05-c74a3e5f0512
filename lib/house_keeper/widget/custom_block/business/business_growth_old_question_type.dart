import 'package:flutter/foundation.dart';

/// 问题类型类（模拟枚举）
class QuestionType {
  /// 类型名称
  final String name;

  /// 类型值
  final int value;

  /// 私有构造函数
  const QuestionType._internal(this.name, this.value);

  /// 单选题
  static const QuestionType singleChoice =
      QuestionType._internal('singleChoice', 1);

  /// 滑块选择
  static const QuestionType slider = QuestionType._internal('slider', 2);

  /// 多选题
  static const QuestionType multipleChoice =
      QuestionType._internal('multipleChoice', 3);

  /// 文本输入
  static const QuestionType textInput = QuestionType._internal('textInput', 4);

  /// 所有类型列表
  static const List<QuestionType> values = [
    singleChoice,
    slider,
    multipleChoice,
    textInput,
  ];

  /// 从值查找类型
  static QuestionType fromValue(int value) {
    if (value == null) return singleChoice;

    try {
      return values.firstWhere(
        (type) => type.value == value,
        orElse: () => singleChoice,
      );
    } catch (e) {
      return singleChoice;
    }
  }

  @override
  String toString() => 'QuestionType.$name';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuestionType && other.value == value;
  }

  @override
  int get hashCode => value.hashCode;
}

/// 问题模型类
class Question {
  /// 问题ID
  final int id;

  /// 问题类型
  final int type;

  /// 问题标题
  final String title;

  /// 可选值列表
  final List<String> value;

  /// 已选值列表
  List<String> selectedValue;

  /// 构造函数
  Question({
    @required this.id,
    @required this.type,
    @required this.title,
    @required this.value,
    List<String> selectedValue,
  }) : this.selectedValue = selectedValue ?? [];

  /// 从JSON创建
  factory Question.fromJson(Map<String, dynamic> json) {
    if (json == null) return null;

    return Question(
      id: json['id'] ?? 0,
      type: json['type'] ?? 1,
      title: json['title'] ?? '',
      value: List<String>.from(json['value'] ?? []),
      selectedValue: List<String>.from(json['selectedValue'] ?? []),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'value': value,
      'selectedValue': selectedValue,
    };
  }
}

/// 选项项模型
class OptionItem {
  /// 选项ID
  final String id;

  /// 选项值
  final String value;

  /// 附加数据
  final Map<String, dynamic> extra;

  /// 构造函数
  const OptionItem({
    this.id,
    @required this.value,
    this.extra,
  });

  /// 从JSON创建
  factory OptionItem.fromJson(dynamic json) {
    if (json == null) return null;

    // 处理简单字符串情况
    if (json is String) {
      return OptionItem(value: json);
    }

    return OptionItem(
      id: json['id'],
      value: json['value'] ?? '',
      extra: json['extra'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'value': value,
      if (extra != null) 'extra': extra,
    };
  }
}
