import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';

/// 同行对比卡片
class BusinessCompetitorComparisonCard extends BaseBlockWidget {
  const BusinessCompetitorComparisonCard({
    Key key,
    @required String content,
  }) : super(key: key, content: content);

  @override
  _BusinessCompetitorComparisonCardState createState() =>
      _BusinessCompetitorComparisonCardState();
}

class _BusinessCompetitorComparisonCardState
    extends BaseBlockWidgetState<BusinessCompetitorComparisonCard> {
  // 我的店铺信息
  String _myShopName = '';
  String _myShopBranch = '';

  // 对比店铺信息
  String _competitorShopName = '';
  String _competitorShopBranch = '';

  // 经营数据
  Map<String, ComparisonData> _operationData = {};
  // 流量数据
  Map<String, ComparisonData> _trafficData = {};
  // 顾客数据
  Map<String, ComparisonData> _customerData = {};
  // 交易数据
  Map<String, ComparisonData> _transactionData = {};

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessCompetitorComparisonCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final data = json.decode(widget.content);

      setState(() {
        // 解析店铺信息
        final myShop = data['myShop'] ?? {};
        _myShopName = myShop['name'] ?? '';
        _myShopBranch = myShop['branch'] ?? '';

        final competitorShop = data['competitorShop'] ?? {};
        _competitorShopName = competitorShop['name'] ?? '';
        _competitorShopBranch = competitorShop['branch'] ?? '';

        // 解析经营数据
        _operationData = _parseComparisonSection(data['operation'] ?? {});
        // 解析流量数据
        _trafficData = _parseComparisonSection(data['traffic'] ?? {});
        // 解析顾客数据
        _customerData = _parseComparisonSection(data['customer'] ?? {});
        // 解析交易数据
        _transactionData = _parseComparisonSection(data['transaction'] ?? {});
      });
    } catch (e) {
      debugPrint('解析同行对比数据时出错: $e');
    }
  }

  Map<String, ComparisonData> _parseComparisonSection(
      Map<String, dynamic> section) {
    final result = <String, ComparisonData>{};
    section.forEach((key, value) {
      result[key] = ComparisonData(
        title: value['title'] ?? '',
        myValue: value['myValue'] ?? '',
        competitorValue: value['competitorValue'] ?? '',
        comparison: _parseComparison(value['comparison']),
      );
    });
    return result;
  }

  ComparisonResult _parseComparison(String value) {
    switch (value) {
      case '胜':
        return ComparisonResult.win;
      case '负':
        return ComparisonResult.lose;
      case '平':
        return ComparisonResult.draw;
      default:
        return ComparisonResult.draw;
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 6,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildShopComparison(),
          const SizedBox(height: 12),
          _buildComparisonSection('比经营', _operationData),
          const SizedBox(height: 8),
          _buildComparisonSection('比流量', _trafficData),
          const SizedBox(height: 8),
          _buildComparisonSection('比顾客', _customerData),
          const SizedBox(height: 8),
          _buildComparisonSection('比交易', _transactionData),
        ],
      ),
    );
  }

  Widget _buildShopComparison() {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFF7FBFF),
              borderRadius: const BorderRadius.horizontal(
                left: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '您的门店',
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFF999999),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _myShopName,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  _myShopBranch,
                  style: const TextStyle(
                    fontSize: 11,
                    color: Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            color: const Color(0xFFFF4B33).withOpacity(0.1),
            borderRadius: BorderRadius.circular(2),
          ),
          child: const Text(
            'PK',
            style: TextStyle(
              fontSize: 11,
              color: Color(0xFFFF4B33),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF7F7),
              borderRadius: const BorderRadius.horizontal(
                right: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text(
                  '对比门店',
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFF999999),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _competitorShopName,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  _competitorShopBranch,
                  style: const TextStyle(
                    fontSize: 11,
                    color: Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildComparisonSection(
      String title, Map<String, ComparisonData> data) {
    List<Widget> items =
        data.entries.map((entry) => _buildComparisonItem(entry.value)).toList();

    List<Widget> columnChildren = <Widget>[
      Text(
        title,
        style: const TextStyle(
          fontSize: 12,
          color: Color(0xFF666666),
          fontWeight: FontWeight.w600,
        ),
      ),
      const SizedBox(height: 4),
    ];
    columnChildren.addAll(items);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: columnChildren,
    );
  }

  Widget _buildComparisonItem(ComparisonData data) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4),
              decoration: const BoxDecoration(
                color: Color(0xFFF7FBFF),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    data.myValue,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF333333),
                      fontWeight: FontWeight.w600,
                      height: 1.1,
                    ),
                  ),
                  _buildComparisonTag(data.comparison),
                ],
              ),
            ),
          ),
          Container(
            width: 100,
            padding: const EdgeInsets.symmetric(vertical: 4),
            alignment: Alignment.center,
            color: Colors.white,
            child: Text(
              data.title,
              style: const TextStyle(
                fontSize: 11,
                color: Color(0xFF666666),
                height: 1.1,
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4),
              decoration: const BoxDecoration(
                color: Color(0xFFFFF7F7),
              ),
              child: Text(
                data.competitorValue,
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF333333),
                  fontWeight: FontWeight.w600,
                  height: 1.1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonTag(ComparisonResult result) {
    final color = result == ComparisonResult.win
        ? const Color(0xFF333333)
        : result == ComparisonResult.lose
            ? const Color(0xFF333333)
            : const Color(0xFF999999);

    return Container(
      margin: const EdgeInsets.only(left: 2),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
      decoration: BoxDecoration(
        border: Border.all(color: color, width: 0.5),
        borderRadius: BorderRadius.circular(2),
        color: Colors.white,
      ),
      child: Text(
        result.label,
        style: TextStyle(
          fontSize: 9,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

/// 对比数据模型
class ComparisonData {
  final String title;
  final String myValue;
  final String competitorValue;
  final ComparisonResult comparison;

  ComparisonData({
    this.title = '',
    this.myValue = '',
    this.competitorValue = '',
    this.comparison = ComparisonResult.draw,
  });
}

/// 对比结果枚举
enum ComparisonResult {
  win,
  lose,
  draw,
}

extension ComparisonResultExtension on ComparisonResult {
  String get label {
    switch (this) {
      case ComparisonResult.win:
        return '胜';
      case ComparisonResult.lose:
        return '负';
      case ComparisonResult.draw:
        return '平';
    }
  }
}
