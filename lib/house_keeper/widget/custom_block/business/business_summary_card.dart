import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';

/// 经营数据总览卡片
class BusinessSummaryCard extends BaseBlockWidget {
  const BusinessSummaryCard({
    Key key,
    @required String content,
  }) : super(key: key, content: content);

  @override
  _BusinessSummaryCardState createState() => _BusinessSummaryCardState();
}

class _BusinessSummaryCardState
    extends BaseBlockWidgetState<BusinessSummaryCard> {
  // 数据项列表
  List<Map<String, dynamic>> _bizItems = [];

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessSummaryCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      // 预处理 JSON 字符串，处理带+号的数值
      String processedContent = widget.content.replaceAll('":+', '":');

      final data = json.decode(processedContent);
      final bizList = data['biz'] as List;

      if (bizList != null) {
        setState(() {
          _bizItems = List<Map<String, dynamic>>.from(bizList);
        });
      }
    } catch (e) {
      debugPrint('解析经营数据时出错: $e');
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const Divider(
            height: 2,
            color: Color(0xFFEEEEEE),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              const Text(
                '经营数据',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF333333),
                  fontWeight: FontWeight.w500,
                ),
              ),
              GestureDetector(
                onTap: () {
                  RouteUtils.open(
                      'itakeawaybiz://waimaieapi.meituan.com/mrn?mrn_biz=waimaibiz&mrn_entry=feBizdata&mrn_component=bizdata&init_route=Business');
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: const <Widget>[
                    Text(
                      '全部',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      size: 16,
                      color: Color(0xFF999999),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildDataGrid()
        ],
      ),
    );
  }

  Widget _buildDataGrid() {
    // 将数据项分成三列
    List<List<Map<String, dynamic>>> columns = [[], [], []];

    for (var i = 0; i < _bizItems.length; i++) {
      columns[i].add(_bizItems[i]);
    }

    // 预先构建所有列的 Widget
    List<Widget> columnWidgets = [];

    // 第一列
    columnWidgets.add(
      Expanded(
        child: Padding(
          padding: const EdgeInsets.only(left: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: columns[0].map((item) {
              final baseDelta = _parseNumber(item['baseDelta']);
              return _buildDataItem(
                title: item['title'] ?? '',
                value: _formatValue(item['base'], item['type']),
                compare: baseDelta,
                desc: item['desc'] ?? '比前日',
              );
            }).toList(),
          ),
        ),
      ),
    );

    // 第二列
    columnWidgets.add(
      Expanded(
        child: Padding(
          padding: const EdgeInsets.only(left: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: columns[1].map((item) {
              final baseDelta = _parseNumber(item['baseDelta']);
              return _buildDataItem(
                title: item['title'] ?? '',
                value: _formatValue(item['base'], item['type']),
                compare: baseDelta,
                desc: item['desc'] ?? '比前日',
              );
            }).toList(),
          ),
        ),
      ),
    );

    // 第三列
    columnWidgets.add(
      Expanded(
        child: Padding(
          padding: const EdgeInsets.only(left: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: columns[2].map((item) {
              final baseDelta = _parseNumber(item['baseDelta']);
              return _buildDataItem(
                title: item['title'] ?? '',
                value: _formatValue(item['base'], item['type']),
                compare: baseDelta,
                desc: item['desc'] ?? '比前日',
              );
            }).toList(),
          ),
        ),
      ),
    );

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: columnWidgets,
    );
  }

  double _parseNumber(dynamic value) {
    if (value == null) return 0.0;
    if (value is int) return value.toDouble();
    if (value is double) return value;
    if (value is String) {
      String cleanValue = value.startsWith('+') ? value.substring(1) : value;
      return double.tryParse(cleanValue) ?? 0.0;
    }
    return 0.0;
  }

  String _formatValue(dynamic value, String type) {
    if (value == null) return '0';

    if (type == 'double') {
      final doubleValue = _parseNumber(value);
      return doubleValue.toStringAsFixed(2);
    }

    if (type == 'int') {
      final intValue =
          value is int ? value : int.tryParse(value.toString()) ?? 0;
      return intValue.toString();
    }

    return value.toString();
  }

  Widget _buildDataItem({
    @required String title,
    @required String value,
    @required double compare,
    @required String desc,
  }) {
    // 预先计算比较值的文本和颜色
    String compareText;
    Color compareColor;

    if (compare > 0) {
      compareText = '↑${compare.abs().toStringAsFixed(2)}';
      compareColor = const Color(0xFFFF4B33);
    } else if (compare < 0) {
      compareText = '↓${compare.abs().toStringAsFixed(2)}';
      compareColor = const Color(0xFF52C41A);
    } else {
      compareText = '无变化';
      compareColor = const Color(0xFF333333);
    }

    List<Widget> rowChildren = [
      Text(
        desc,
        style: const TextStyle(
          fontSize: 10,
          color: Color(0xFF999999),
        ),
      ),
      const SizedBox(width: 2),
      Text(
        compareText,
        style: TextStyle(
          fontSize: 10,
          color: compareColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    ];

    List<Widget> columnChildren = [
      Text(
        title,
        style: const TextStyle(
          fontSize: 12,
          color: Color(0xFF999999),
        ),
      ),
      const SizedBox(height: 8),
      Text(
        value,
        style: const TextStyle(
          fontSize: 24,
          height: 1.1,
          fontWeight: FontWeight.w600,
          color: Color(0xFF333333),
        ),
      ),
      const SizedBox(height: 4),
      Row(
        mainAxisSize: MainAxisSize.min,
        children: rowChildren,
      ),
    ];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: columnChildren,
      ),
    );
  }
}
