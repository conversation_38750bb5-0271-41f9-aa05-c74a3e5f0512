import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/common/log_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/business/business_growth_old_question_type.dart';

/// 商家成长计划问卷组件
/// INPUT：
// [
//     {
//         "id": 1,
//         "type": 1,
//         "title": "1.您的店铺类型是？",
//         "value": [
//             "纯外卖店",
//             "堂食店铺"
//         ],
//         "selectedValue":[]
//     },
//     {
//         "id": 2,
//         "type": 1,
//         "title": "2.您的外卖经验有多久？",
//         "value": [
//             "没有经验",
//             "1-2年",
//             "2年以上"
//         ],
//         "selectedValue":[]
//     },
//     {
//         "id": 3,
//         "type": 1,
//         "title": "3.您预期每天有多少订单？",
//         "value": [
//             "15",
//             "50",
//             "100",
//             "500"
//         ],
//         "selectedValue":[]
//     },
//     {
//         "id": 4,
//         "type": 1,
//         "title": "4.您是否愿意尝试付费推广？",
//         "value": [
//             "可以尝试",
//             "不太愿意"
//         ],
//         "selectedValue":[]
//     }
// ]
/// SUBMIT:
// {
//  "cardKey":"waimaiGrowthPlan",
//  "data":[
//     {
//         "id": 2,
//         "type": 1,
//         "title": "1.您的店铺类型是？",
//         "value": [
//             "纯外卖店",
//             "堂食店铺"
//         ],
//         "selectedValue":[
//            "纯外卖店"
//         ]
//     },
//     {
//         "id": 2,
//         "type": 1,
//         "title": "1.您的外卖经验有多久？",
//         "value": [
//             "没有经验",
//             "1-2年",
//             "2年以上"
//         ],
//         "selectedValue":[
//             "2年以上"
//         ]
//     },
//     {
//         "id": 3,
//         "type": 1,
//         "title": "1.您预期每天有多少订单？",
//         "value": [
//             "15",
//             "50",
//             "100",
//             "500"
//         ],
//         "selectedValue":[
//         "223"
//         ]
//     },
//     {
//         "id": 4,
//         "type": 1,
//         "title": "1.您是否愿意尝试付费推广？",
//         "value": [
//             "可以尝试",
//             "不太愿意"
//         ],
//         "selectedValue":[
//              "可以尝试"
//         ]
//     }
// 	]
// }
///

/// 商家成长计划问卷组件
class BusinessGrowthPlanBlockWidget extends BaseBlockWidget {
  const BusinessGrowthPlanBlockWidget({
    Key key,
    @required String content,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;

  @override
  _BusinessGrowthPlanBlockWidgetState createState() =>
      _BusinessGrowthPlanBlockWidgetState();
}

class _BusinessGrowthPlanBlockWidgetState
    extends BaseBlockWidgetState<BusinessGrowthPlanBlockWidget> {
  List<OldGrowthPlanQuestionItem> _questions = [];
  bool _hasSubmitted = false;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final List<dynamic> data = json.decode(widget.content);
      setState(() {
        _questions = data
            .map((item) => OldGrowthPlanQuestionItem.fromJson(item))
            .toList();
      });
    } catch (e) {
      LogUtils.logWithTimestamp('解析问卷数据出错: $e');
    }
  }

  void _handleOptionSelect(int questionIndex, String value) {
    if (_hasSubmitted) return;
    setState(() {
      final question = _questions[questionIndex];
      if (!question.selectedValue.contains(value)) {
        question.selectedValue = [value];
      }
    });
  }

  bool _isAllQuestionsAnswered() {
    return _questions.every((q) => q.selectedValue.isNotEmpty);
  }

  void _handleSubmit() {
    if (!_isAllQuestionsAnswered()) {
      MTFToast.showToast(msg: '请回答所有问题');
      return;
    }

    // 构建提交的数据
    final submitData = {
      "cardKey": "waimaiGrowthPlan",
      "data": _questions
          .map((question) => {
                "id": question.id,
                "type": question.type.value,
                "title": question.title,
                "value": question.options.map((o) => o.value).toList(),
                "selectedValue": question.selectedValue,
              })
          .toList(),
    };

    debugPrint('提交数据: ${json.encode(submitData)}');

    // 如果需要，可以在这里调用回调函数通知父组件
    if (widget.model != null) {
      widget.model.sendCardMessage(
        '提交成长计划',
        json.encode(submitData),
        'waimaiGrowthPlan',
      );
    }

    setState(() {
      _hasSubmitted = true;
    });
    MTFToast.showToast(msg: '提交成功');
  }

  @override
  Widget buildContentView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ListView.separated(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _questions.length,
          separatorBuilder: (context, index) => const Divider(
            height: 1,
            color: Color(0xFFEEEEEE),
          ),
          itemBuilder: (context, questionIndex) {
            final question = _questions[questionIndex];
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: const Color(0xFFE8F5FF),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '${questionIndex + 1}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF333333),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          question.title,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF333333),
                            fontWeight: FontWeight.w600,
                            height: 1.3,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  // 根据问题类型显示不同的UI
                  if (question.type == QuestionType.slider)
                    _buildSliderQuestion(question, questionIndex)
                  else
                    _buildOptionsQuestion(question, questionIndex),
                ],
              ),
            );
          },
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: SizedBox(
            height: 40,
            child: TextButton(
              onPressed: _hasSubmitted || !_isAllQuestionsAnswered()
                  ? null
                  : _handleSubmit,
              style: TextButton.styleFrom(
                backgroundColor: _hasSubmitted
                    ? const Color(0xFFCCCCCC)
                    : const Color(0xFF333333),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: EdgeInsets.zero,
              ),
              child: Text(
                _hasSubmitted ? '已提交' : '确定',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建选项问题UI
  Widget _buildOptionsQuestion(
      OldGrowthPlanQuestionItem question, int questionIndex) {
    return Stack(
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children:
                List<Widget>.from(question.options.asMap().entries.map((entry) {
              final option = entry.value;
              final isSelected = question.selectedValue.contains(option.value);
              final isLast = entry.key == question.options.length - 1;

              return Padding(
                padding: EdgeInsets.only(right: isLast ? 16 : 12),
                child: GestureDetector(
                  onTap: _hasSubmitted
                      ? null
                      : () => _handleOptionSelect(questionIndex, option.value),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? const Color(0xFFE8F5FF)
                          : const Color(0xFFF8F8F8),
                      borderRadius: BorderRadius.circular(50),
                      border: Border.all(
                        color: Colors.transparent,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      option.value,
                      style: TextStyle(
                        fontSize: 13,
                        color: isSelected
                            ? const Color(0xFF333333)
                            : const Color(0xFF666666),
                        fontWeight:
                            isSelected ? FontWeight.w500 : FontWeight.normal,
                        height: 1.5,
                      ),
                    ),
                  ),
                ),
              );
            })),
          ),
        ),
        Positioned(
          right: 0,
          top: 0,
          bottom: 0,
          child: Container(
            width: 32,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Colors.white.withOpacity(0.0),
                  Colors.white.withOpacity(1.0),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建滑块问题UI
  Widget _buildSliderQuestion(
      OldGrowthPlanQuestionItem question, int questionIndex) {
    // 解析选项值，获取最小值和最大值
    List<double> numericValues = [];
    for (var option in question.options) {
      try {
        numericValues.add(double.parse(option.value));
      } catch (e) {
        // 忽略非数字值
      }
    }

    // 如果没有有效的数字值，使用默认值
    double minValue = numericValues.isNotEmpty
        ? numericValues.reduce((a, b) => a < b ? a : b)
        : 0;
    double maxValue = numericValues.isNotEmpty
        ? numericValues.reduce((a, b) => a > b ? a : b)
        : 100;

    // 获取当前选中值
    double currentValue = minValue;
    if (question.selectedValue.isNotEmpty) {
      try {
        currentValue = double.parse(question.selectedValue.last);
      } catch (e) {
        // 如果解析失败，使用最小值
      }
    }

    // 创建刻度标签
    List<Widget> tickLabels = [];
    for (var option in question.options) {
      tickLabels.add(
        Text(
          option.value,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF666666),
          ),
        ),
      );
    }

    return Padding(
        padding: const EdgeInsets.only(top: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 60,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  ValueListenableBuilder<double>(
                    valueListenable: ValueNotifier(currentValue),
                    builder: (context, value, child) {
                      final sliderWidth =
                          MediaQuery.of(context).size.width - 64;
                      const indicatorWidth = 50.0;
                      final maxLeft = sliderWidth - indicatorWidth;
                      final position =
                          (value - minValue) / (maxValue - minValue);
                      final left = (position * maxLeft).clamp(0.0, maxLeft);

                      return Positioned(
                        left: left,
                        bottom: 38,
                        child: Container(
                          alignment: Alignment.center,
                          width: 48,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '${value.toInt()}单',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  // Slider 定位在底部
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                        activeTrackColor: Colors.black,
                        inactiveTrackColor: Colors.white,
                        thumbColor: Colors.black,
                        overlayColor: Colors.black.withOpacity(0.2),
                      ),
                      child: Slider(
                        value: currentValue,
                        min: minValue,
                        max: maxValue,
                        onChanged: _hasSubmitted
                            ? null
                            : (value) {
                                setState(() {
                                  question.selectedValue = [
                                    value.toInt().toString()
                                  ];
                                });
                              },
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 刻度标签
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: tickLabels,
              ),
            ),
          ],
        ));
  }
}

/// 问题项模型
class OldGrowthPlanQuestionItem {
  final String id;
  final String title;
  final QuestionType type;
  final List<OptionItem> options;
  final double minValue;
  final double maxValue;
  List<String> selectedValue;

  OldGrowthPlanQuestionItem({
    this.id,
    this.title,
    int typeValue,
    this.options = const [],
    this.minValue = 0,
    this.maxValue = 10,
    this.selectedValue = const [],
  }) : type = typeValue != null
            ? QuestionType.fromValue(typeValue)
            : QuestionType.singleChoice;

  // 从JSON创建
  factory OldGrowthPlanQuestionItem.fromJson(Map<String, dynamic> json) {
    // 处理选项列表
    List<OptionItem> parseOptions(dynamic optionsData) {
      if (optionsData == null) return [];

      if (optionsData is List) {
        return optionsData.map((item) {
          if (item is String) {
            return OptionItem(value: item);
          } else if (item is int || item is double) {
            // 处理数字类型，转换为字符串
            return OptionItem(value: item.toString());
          } else if (item is Map<String, dynamic>) {
            return OptionItem.fromJson(item);
          }
          return OptionItem(value: item.toString());
        }).toList();
      }

      // 如果是单个值，创建单选项列表
      if (optionsData is int || optionsData is double) {
        return [OptionItem(value: optionsData.toString())];
      }
      return [OptionItem(value: optionsData.toString())];
    }

    // 确保 id 是字符串类型
    String id = json['id'] is int ? json['id'].toString() : json['id'];

    // 确保 type 是整数类型
    int typeValue;
    if (json['type'] is int) {
      typeValue = json['type'];
    } else if (json['type'] is String) {
      typeValue = int.tryParse(json['type']) ?? 1;
    } else {
      typeValue = 1; // 默认为单选类型
    }

    // 处理 selectedValue，确保它是字符串列表
    List<String> selectedValues = [];
    if (json['selectedValue'] != null) {
      if (json['selectedValue'] is List) {
        selectedValues = (json['selectedValue'] as List).map((item) {
          if (item is String) return item;
          return item.toString();
        }).toList();
      } else {
        // 如果不是列表，转换为单元素列表
        selectedValues = [json['selectedValue'].toString()];
      }
    }

    return OldGrowthPlanQuestionItem(
      id: id,
      title: json['title'],
      typeValue: typeValue,
      options: parseOptions(json['value']), // 使用 value 字段作为选项
      minValue: json['minValue']?.toDouble() ?? 0,
      maxValue: json['maxValue']?.toDouble() ?? 10,
      selectedValue: selectedValues,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type.value,
      'options': options?.map((e) => e.toJson())?.toList() ?? [],
      'minValue': minValue,
      'maxValue': maxValue,
      'selectedValue': selectedValue ?? [],
    };
  }
}
