import 'package:flutter/material.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'dart:convert';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/base_block_widget.dart';

/// 商家成长计划问卷组件
/// INPUT：
/// [
//     {
//         "featureSelectList": [
//             {
//                 "featureTextValue": "1-4单",
//                 "featureValue": 1
//             },
//             {
//                 "featureTextValue": "4-13单(超越60%同行)",
//                 "featureValue": 2
//             },
//             {
//                 "featureTextValue": "13单以上(超越80%同行)",
//                 "featureValue": 3
//             }
//         ],
//         "featureTitle": "01 开业30天内，店铺每天单量的预期是?",
//         "featureType": 1
//     },
//     {
//         "featureSelectList": [
//             {
//                 "featureTextValue": "我愿意尝试",
//                 "featureValue": 3
//             },
//             {
//                 "featureTextValue": "暂不考虑",
//                 "featureValue": 1
//             }
//         ],
//         "featureTitle": "02 开业30天内，是否会参照同行的活动设置，或使用付费推广?",
//         "featureType": 2
//     }
// ]
///
/// SUBMIT:
///
// {
// 	"cardKey":"wm_new_poi_waimaiGrowthPlan",
//   "data":{
//       "featureSelects": "[{\"featureTextValue\":\"5单以上(超越80%同行)\",\"featureValue\":3,\"featureType\":1},{\"featureTextValue\":\"我愿意尝试\",\"featureValue\":3,\"featureType\":2}]"
//     }
// }
// }
///

class FeatureOption {
  final String featureTextValue;
  final int featureValue;

  FeatureOption({
    this.featureTextValue = '',
    this.featureValue = 0,
  });

  factory FeatureOption.fromJson(Map<String, dynamic> json) {
    return FeatureOption(
      featureTextValue: json['featureTextValue']?.toString() ?? '',
      featureValue: json['featureValue'] as int ?? 0,
    );
  }
}

class NewGrowthPlanQuestionItem {
  final List<FeatureOption> featureSelectList;
  final String featureTitle;
  final int featureType;

  NewGrowthPlanQuestionItem({
    this.featureSelectList = const [],
    this.featureTitle = '',
    this.featureType = 0,
  });

  factory NewGrowthPlanQuestionItem.fromJson(Map<String, dynamic> json) {
    return NewGrowthPlanQuestionItem(
      featureSelectList: (json['featureSelectList'] as List<dynamic>)
              ?.map((option) => FeatureOption.fromJson(option))
              ?.toList() ??
          [],
      featureTitle: json['featureTitle']?.toString() ?? '',
      featureType: json['featureType'] as int ?? 0,
    );
  }
}

class BusinessGrowthPlanList extends BaseBlockWidget {
  const BusinessGrowthPlanList({
    Key key,
    @required String content,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;

  @override
  _BusinessGrowthPlanListState createState() => _BusinessGrowthPlanListState();
}

class _BusinessGrowthPlanListState
    extends BaseBlockWidgetState<BusinessGrowthPlanList> {
  List<NewGrowthPlanQuestionItem> _questions = [];
  Map<int, FeatureOption> _selectedAnswers = {};
  bool _hasSubmitted = false;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final List<dynamic> jsonData = json.decode(widget.content);
      setState(() {
        _questions = jsonData
            .map((item) => NewGrowthPlanQuestionItem.fromJson(item))
            .toList();
      });
    } catch (e) {
      debugPrint('解析问卷数据失败: $e');
      _questions = [];
    }
  }

  void _handleSubmit() {
    if (_selectedAnswers.length != _questions.length) {
      return;
    }

    // 构建提交数据
    List<Map<String, dynamic>> featureSelects = [];
    _selectedAnswers.forEach((index, option) {
      featureSelects.add({
        "featureTextValue": option.featureTextValue,
        "featureValue": option.featureValue,
        "featureType": _questions[index].featureType,
      });
    });

    final submitData = {
      "cardKey": "wm_new_poi_waimaiGrowthPlan",
      "data": {
        "featureSelects": json.encode(featureSelects),
      }
    };

    debugPrint('提交数据:  ${json.encode(submitData)}');

    // 如果需要，可以在这里调用回调函数通知父组件
    if (widget.model != null) {
      widget.model.sendCardMessage(
        '提交成长计划',
        json.encode(submitData),
        'wm_new_poi_waimaiGrowthPlan',
      );
    }

    setState(() {
      _hasSubmitted = true;
    });
    MTFToast.showToast(msg: '提交成功');
  }

  Widget _buildOption(FeatureOption option, int questionIndex) {
    final bool isSelected =
        _selectedAnswers[questionIndex]?.featureValue == option.featureValue;

    return GestureDetector(
      onTap: _hasSubmitted
          ? null
          : () {
              setState(() {
                _selectedAnswers[questionIndex] = option;
              });
            },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE8F5FF) : const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(50),
          border: Border.all(
            color: Colors.transparent,
            width: 1,
          ),
        ),
        child: Text(
          option.featureTextValue,
          style: TextStyle(
            fontSize: 13,
            color:
                isSelected ? const Color(0xFF333333) : const Color(0xFF666666),
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
            height: 1.5,
          ),
        ),
      ),
    );
  }

  @override
  Widget buildContentView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        ListView.separated(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _questions.length,
          separatorBuilder: (context, index) => const Divider(
            height: 1,
            color: Color(0xFFEEEEEE),
          ),
          itemBuilder: (context, index) =>
              _buildQuestion(_questions[index], index),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: SizedBox(
            height: 40,
            child: TextButton(
              onPressed:
                  _hasSubmitted || _selectedAnswers.length != _questions.length
                      ? null
                      : _handleSubmit,
              style: TextButton.styleFrom(
                backgroundColor: _hasSubmitted
                    ? const Color(0xFFCCCCCC)
                    : const Color(0xFF333333),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: EdgeInsets.zero,
              ),
              child: Text(
                _hasSubmitted ? '已提交' : '确定',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuestion(NewGrowthPlanQuestionItem question, int index) {
    final List<Widget> optionWidgets = <Widget>[];

    for (var i = 0; i < question.featureSelectList.length; i++) {
      final entry = question.featureSelectList[i];
      final isLast = i == question.featureSelectList.length - 1;

      optionWidgets.add(
        Padding(
          padding: EdgeInsets.only(right: isLast ? 16.0 : 12.0),
          child: _buildOption(entry, index),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Container(
                width: 20,
                height: 20,
                padding: const EdgeInsets.only(right: 0.5),
                decoration: BoxDecoration(
                  color: const Color(0xFFE8F5FF),
                  borderRadius: BorderRadius.circular(10),
                ),
                alignment: Alignment.center,
                child: Text(
                  '${index + 1}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  question.featureTitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF333333),
                    fontWeight: FontWeight.w600,
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Stack(
            children: <Widget>[
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: optionWidgets,
                ),
              ),
              Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 32,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: <Color>[
                        Colors.white.withOpacity(0.0),
                        Colors.white.withOpacity(1.0),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
