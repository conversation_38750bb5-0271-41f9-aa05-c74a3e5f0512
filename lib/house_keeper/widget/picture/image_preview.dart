import 'package:flutter/cupertino.dart';
import 'package:flutter_advanced_networkimage/provider.dart';

class ImagePreview extends StatelessWidget {
  ImagePreview(this.imageUrl);
  final String imageUrl;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
      },
      child: Center(
        child: Container(
          constraints:
              BoxConstraints(maxWidth: MediaQuery.of(context).size.width),
          color: const Color(0x7F000000),
          child: Image(
            image: AdvancedNetworkImage(
              imageUrl ?? '',
              useDiskCache: true,
            ),
            fit: BoxFit.fitWidth,
          ),
        ),
      ),
    );
  }
}
