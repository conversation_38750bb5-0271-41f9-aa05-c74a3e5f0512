import 'package:flutter/material.dart';
import 'dart:async';

class HouseKeeperPlaceholderMessageWidget extends StatefulWidget {
  const HouseKeeperPlaceholderMessageWidget({Key key, this.isLeft})
      : super(key: key);
  final bool isLeft;

  @override
  _HouseKeeperPlaceholderMessageWidgetState createState() =>
      _HouseKeeperPlaceholderMessageWidgetState();
}

class _HouseKeeperPlaceholderMessageWidgetState
    extends State<HouseKeeperPlaceholderMessageWidget> {
  int _dotCount = 0;
  Timer _timer;

  @override
  void initState() {
    super.initState();
    _startAnimation();
  }

  void _startAnimation() {
    _timer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      setState(() {
        _dotCount = (_dotCount + 1) % 4; // 0-3循环
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: (widget.isLeft ?? true)
          ? const EdgeInsets.only(left: 16, right: 240)
          : const EdgeInsets.only(left: 240, right: 16),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      constraints: const BoxConstraints(
        minWidth: 80,
        minHeight: 30,
      ),
      child: Text(
        '正在思考中${'.' * _dotCount}',
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF666666),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
