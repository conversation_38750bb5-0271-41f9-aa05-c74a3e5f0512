import 'dart:async';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/messages/house_keeper_markdown_widget.dart';

/// 消息容器组件，负责整体布局
class HouseKeeperMessageContainer extends StatelessWidget {
  /// Markdown 内容
  final String data;

  /// 消息数据
  final HouseKeeperStreamMessageVo vo;

  /// 点击链接的回调
  final void Function(String text, String href, String title) onTapLink;

  /// 内边距
  final EdgeInsetsGeometry padding;

  /// 外边距
  final EdgeInsetsGeometry margin;

  /// 背景颜色
  final Color backgroundColor;

  /// 边框圆角
  final BorderRadius borderRadius;

  /// 阴影
  final List<BoxShadow> boxShadow;

  /// 内容预处理函数
  final String Function(String) preprocessor;

  /// 可选的流控制器
  final StreamController<String> streamController;

  /// 是否是流式消息
  final bool isStreaming;

  /// 页面模型
  final HouseKeeperMessagePageModel model;

  /// 底部工具栏
  final Widget bottomWidget;

  /// 底部工具栏
  final Widget errorWidget;

  const HouseKeeperMessageContainer({
    Key key,
    @required this.data,
    @required this.vo,
    this.onTapLink,
    this.padding = const EdgeInsets.all(16),
    this.margin,
    this.backgroundColor = Colors.white,
    this.borderRadius = const BorderRadius.all(Radius.circular(8)),
    this.boxShadow,
    this.streamController,
    this.isStreaming = true,
    this.preprocessor,
    this.model,
    this.bottomWidget,
    this.errorWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 主要内容
        SizedBox(
          width: double.infinity,
          child: HouseKeeperMarkdownWidget(
            data: data,
            vo: vo,
            onTapLink: onTapLink,
            padding: padding,
            margin: margin,
            backgroundColor: backgroundColor,
            borderRadius: borderRadius,
            boxShadow: boxShadow,
            streamController: streamController,
            isStreaming: isStreaming,
            preprocessor: preprocessor,
            model: model,
            excludeGuessBlock: true, // 不在主内容中显示 guess 块
          ),
        ),
        // 底部工具栏
        Visibility(
          visible: bottomWidget != null,
          child: bottomWidget ?? const SizedBox.shrink(),
        ),
        // 猜你想问模块
        Visibility(
          visible: _hasGuessContent(data),
          child: _buildGuessContent(data),
        ),
        // 底部工具栏
        Visibility(
          visible: errorWidget != null,
          child: errorWidget ?? const SizedBox.shrink(),
        ),
      ],
    );
  }

  /// 检查是否包含猜你想问内容
  bool _hasGuessContent(String content) {
    if (content == null || content.isEmpty) return false;
    return content.contains('<Guess>') && content.contains('</Guess>');
  }

  /// 构建猜你想问模块
  Widget _buildGuessContent(String content) {
    // 提取 Guess 内容中的问题列表
    final questions = _extractQuestions(content);
    if (questions.isEmpty) return const SizedBox.shrink();

    final List<Widget> questionWidgets = <Widget>[];

    for (final questionData in questions) {
      final text = questionData['text'] ?? '';
      final link = questionData['link'] ?? '';

      questionWidgets.add(
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: InkWell(
            onTap: () {
              if (link.startsWith('waimaieapi.jump')) {
                model?.sendPlainMessage(text);
              } else if (onTapLink != null) {
                onTapLink(text, link, '');
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFDEF0FF),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    text,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF333333),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(top: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: questionWidgets,
      ),
    );
  }

  /// 从 Guess 内容中提取问题列表
  List<Map<String, String>> _extractQuestions(String content) {
    try {
      final startTag = '<Guess>';
      final endTag = '</Guess>';
      final startIndex = content.indexOf(startTag);
      final endIndex = content.indexOf(endTag);

      if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {
        return [];
      }

      // 提取 Guess 标签中的内容
      final guessContent =
          content.substring(startIndex + startTag.length, endIndex).trim();

      // 分行并过滤空行
      final lines = guessContent
          .split('\n')
          .where((line) => line.trim().isNotEmpty)
          .map((line) => line.trim())
          .toList();

      // 解析每一行，提取文本和链接
      return lines.map((line) {
        // 解析 markdown 链接格式 [text](link)
        final regex = RegExp(r'\[(.*?)\]\((.*?)\)');
        final match = regex.firstMatch(line);
        if (match != null) {
          return {
            'text': match.group(1) ?? '',
            'link': match.group(2) ?? '',
          };
        }
        // 如果不是链接格式，就把整行当作文本
        return {
          'text': line,
          'link': 'waimaieapi.jump', // 默认使用 jump 链接
        };
      }).toList();
    } catch (e) {
      debugPrint('提取Guess问题列表时出错: $e');
      return [];
    }
  }
}
