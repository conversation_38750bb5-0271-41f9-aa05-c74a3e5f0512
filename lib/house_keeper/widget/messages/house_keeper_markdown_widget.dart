import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:markdown/markdown.dart' as md;
import 'package:mtf_toast/mtf_toast.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/common/markdown_style_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/utils/url_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/custom_block_factory.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/custom_block/loading_block_widget.dart';

/// 处理Markdown解析错误的包装组件
class SafeMarkdownBody extends StatelessWidget {
  final String data;
  final MarkdownStyleSheet styleSheet;
  final void Function(String text, String href, String title) onTapLink;
  final Map<String, MarkdownElementBuilder> builders;
  final List<md.BlockSyntax> blockSyntaxes;
  final md.ExtensionSet extensionSet;

  const SafeMarkdownBody({
    Key key,
    @required this.data,
    @required this.styleSheet,
    @required this.onTapLink,
    @required this.builders,
    @required this.blockSyntaxes,
    @required this.extensionSet,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    try {
      // 安全检查输入数据
      final String safeData = data?.isEmpty == true ? '' : (data ?? '');

      // 进一步清理数据，去除可能导致解析错误的格式
      String cleanedData = _cleanMarkdownContent(safeData);

      return MarkdownBody(
        data: cleanedData,
        selectable: false,
        styleSheet: styleSheet,
        onTapLink: onTapLink,
        builders: builders,
        blockSyntaxes: blockSyntaxes,
        extensionSet: extensionSet,
      );
    } catch (e) {
      debugPrint('渲染Markdown时出错: $e');

      // 尝试使用纯文本显示
      try {
        final strippedContent = _stripMarkdownSyntax(data ?? '');
        return Text(
          strippedContent.isNotEmpty ? strippedContent + '纯文本显示' : '无内容',
          style: const TextStyle(color: Colors.black87),
        );
      } catch (e2) {
        return Text(
          '我遇到无法理解的事情啦😭',
          style: TextStyle(color: Colors.red[700]),
        );
      }
    }
  }

  // 清理Markdown内容，移除可能引起解析错误的格式
  String _cleanMarkdownContent(String content) {
    if (content == null || content.isEmpty) return '';

    try {
      // 处理代码块
      content = _fixCodeBlocks(content);

      // 处理自定义自定义块
      content = _fixWarningBlocks(content);

      return content;
    } catch (e) {
      debugPrint('清理Markdown内容时出错: $e');
      return content; // 返回原始内容
    }
  }

  // 修复代码块格式
  String _fixCodeBlocks(String content) {
    // 确保代码块前后有空行
    content = content.replaceAll(RegExp(r'([^\n])```'), r'$1\n\n```');
    content = content.replaceAll(RegExp(r'```([^\n])'), r'```\n\n$1');

    // 确保代码块结束
    final codeBlockStarts = RegExp(r'```\w*').allMatches(content).length;
    final codeBlockEnds =
        RegExp(r'```\s*$', multiLine: true).allMatches(content).length;

    if (codeBlockStarts > codeBlockEnds) {
      content = content + '\n```';
    }

    return content;
  }

  // 修复或移除自定义块
  String _fixWarningBlocks(String content) {
    // 尝试修复自定义块，如果无法修复则移除
    try {
      final result = _attemptToFixWarningBlocks(content);
      if (result != null) return result;
    } catch (e) {
      debugPrint('修复自定义块时出错: $e');
    }

    // 如果无法修复，则移除所有自定义块标签
    return content
        .replaceAll(RegExp(r':::\w+\s*'), '')
        .replaceAll(RegExp(r':::'), '');
  }

  // 尝试修复自定义块格式
  String _attemptToFixWarningBlocks(String content) {
    final lines = content.split('\n');
    final List<String> newLines = [];
    bool insideWarningBlock = false;

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      // 检测自定义块开始
      if (RegExp(r'^:::\w+').hasMatch(line)) {
        if (insideWarningBlock) {
          // 如果已经在自定义块内，先关闭前一个
          newLines.add(':::');
        }

        insideWarningBlock = true;
        newLines.add(line); // 保留开始标记
      }
      // 检测自定义块结束
      else if (line == ':::') {
        insideWarningBlock = false;
        newLines.add(line); // 保留结束标记
      } else {
        newLines.add(lines[i]); // 保持原始格式
      }
    }

    // 确保所有自定义块都已关闭
    if (insideWarningBlock) {
      newLines.add(':::');
    }

    return newLines.join('\n');
  }

  // 去除Markdown语法，显示纯文本
  String _stripMarkdownSyntax(String markdown) {
    if (markdown == null || markdown.isEmpty) return '';

    String result = markdown;

    // 移除代码块
    result =
        result.replaceAll(RegExp(r'```[\s\S]*?```', multiLine: true), '[代码块]');

    // 移除标题
    result = result.replaceAll(RegExp(r'#{1,6}\s+(.+)'), r'$1');

    // 移除粗体和斜体
    result = result.replaceAll(RegExp(r'\*\*(.+?)\*\*'), r'$1');
    result = result.replaceAll(RegExp(r'\*(.+?)\*'), r'$1');
    result = result.replaceAll(RegExp(r'__(.+?)__'), r'$1');
    result = result.replaceAll(RegExp(r'_(.+?)_'), r'$1');

    // 移除链接，保留文本
    result = result.replaceAll(RegExp(r'\[(.+?)\]\(.+?\)'), r'$1');

    // 移除自定义块标记
    result = result.replaceAll(RegExp(r':::\w+'), '');
    result = result.replaceAll(':::', '');

    // 移除列表标记
    result = result.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '');
    result = result.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    return result.trim();
  }
}

/// 通用的 Markdown 显示组件
///
/// 用于在应用的各个部分显示 Markdown 格式的内容，
/// 提供统一的样式和行为。
class HouseKeeperMarkdownWidget extends StatefulWidget {
  /// Markdown 内容
  final String data;

  final HouseKeeperMessage vo;

  /// 点击链接的回调
  final void Function(String text, String href, String title) onTapLink;

  /// 内边距
  final EdgeInsetsGeometry padding;

  /// 外边距
  final EdgeInsetsGeometry margin;

  /// 背景颜色
  final Color backgroundColor;

  /// 边框圆角
  final BorderRadius borderRadius;

  /// 阴影
  final List<BoxShadow> boxShadow;

  /// 内容预处理函数
  final String Function(String) preprocessor;

  /// 可选的流控制器
  final StreamController<String> streamController;

  /// 是否是流式消息
  final bool isStreaming;

  final HouseKeeperMessagePageModel model;

  /// 是否排除猜你想问块
  final bool excludeGuessBlock;

  /// 是否只显示猜你想问块
  final bool onlyGuessBlock;

  final int messageId;

  const HouseKeeperMarkdownWidget({
    Key key,
    @required this.data,
    @required this.vo,
    this.onTapLink,
    this.padding = const EdgeInsets.all(16),
    this.margin,
    this.backgroundColor = Colors.white,
    this.borderRadius = const BorderRadius.all(Radius.circular(8)),
    this.boxShadow,
    this.streamController,
    this.isStreaming = true,
    this.preprocessor,
    this.model,
    this.excludeGuessBlock = false,
    this.onlyGuessBlock = false,
    this.messageId,
  }) : super(key: key);

  @override
  _HouseKeeperMarkdownWidgetState createState() =>
      _HouseKeeperMarkdownWidgetState();
}

/// 内容区块类型
enum ContentBlockType {
  think, // 思考块
  markdown, // 普通 Markdown 内容
  custom, // 自定义块
  guess, // 猜你想问块
}

/// 内容区块基类
abstract class ContentBlock {
  final ContentBlockType type;
  final String content;
  final bool isComplete;

  ContentBlock({
    @required this.type,
    @required this.content,
    @required this.isComplete,
  });
}

/// 思考区块
class ThinkBlock extends ContentBlock {
  ThinkBlock({
    @required String content,
    @required bool isComplete,
  }) : super(
          type: ContentBlockType.think,
          content: content,
          isComplete: isComplete,
        );
}

/// Markdown 区块
class MarkdownBlock extends ContentBlock {
  MarkdownBlock({
    @required String content,
    @required bool isComplete,
  }) : super(
          type: ContentBlockType.markdown,
          content: content,
          isComplete: isComplete,
        );
}

/// 自定义区块
class CustomBlock extends ContentBlock {
  final String blockType;

  CustomBlock({
    @required this.blockType,
    @required String content,
    @required bool isComplete,
  }) : super(
          type: ContentBlockType.custom,
          content: content,
          isComplete: isComplete,
        );
}

/// 猜你想问区块
class GuessBlock extends ContentBlock {
  final List<String> questions;

  GuessBlock({
    @required this.questions,
    @required bool isComplete,
  }) : super(
          type: ContentBlockType.guess,
          content: '',
          isComplete: isComplete,
        );
}

/// 基础区块组件
abstract class BaseMessageBlockWidget extends StatelessWidget {
  final ContentBlock block;
  final bool isVisible;
  final void Function(String text, String href, String title) onTapLink;
  final HouseKeeperMessagePageModel model;

  const BaseMessageBlockWidget({
    Key key,
    @required this.block,
    this.isVisible = true,
    this.onTapLink,
    this.model,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();
    return buildBlock(context);
  }

  Widget buildBlock(BuildContext context);
}

/// 思考区块组件
class ThinkBlockWidget extends BaseMessageBlockWidget {
  final bool showThinking;
  final VoidCallback onToggleThinking;

  const ThinkBlockWidget({
    Key key,
    @required ContentBlock block,
    @required this.showThinking,
    @required this.onToggleThinking,
    bool isVisible = true,
    void Function(String text, String href, String title) onTapLink,
    HouseKeeperMessagePageModel model,
  }) : super(
          key: key,
          block: block,
          isVisible: isVisible,
          onTapLink: onTapLink,
          model: model,
        );

  @override
  Widget buildBlock(BuildContext context) {
    if (block is! ThinkBlock) return const SizedBox.shrink();
    final thinkBlock = block as ThinkBlock;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: onToggleThinking,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  thinkBlock.isComplete ? '深度思考过程' : '深度思考中',
                  style: const TextStyle(
                    color: Color(0xFF222222),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 4),
                Image(
                  image: AdvancedNetworkImage(
                    showThinking
                        ? 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/7874d425f2c5485e/arrow_up.png'
                        : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/edeb84061e9ca377/arrow_down.png',
                    useDiskCache: true,
                  ),
                  width: 12,
                  height: 12,
                ),
              ],
            ),
          ),
        ),
        Visibility(
          visible: showThinking,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.only(left: 8, right: 4),
                decoration: const BoxDecoration(
                  color: Color(0xFFFFFFFF),
                  border: Border(
                    left: BorderSide(
                      color: Color(0xFFE2E2E2),
                      width: 2,
                    ),
                  ),
                ),
                child: MarkdownBody(
                  data: thinkBlock.content,
                  selectable: false,
                  styleSheet:
                      MarkdownStyleUtils.getStandardStyle(context).copyWith(
                    p: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF222222),
                      height: 1.6,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
            ],
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}

/// Markdown区块组件
class MarkdownBlockWidget extends BaseMessageBlockWidget {
  const MarkdownBlockWidget({
    Key key,
    @required ContentBlock block,
    bool isVisible = true,
    void Function(String text, String href, String title) onTapLink,
    HouseKeeperMessagePageModel model,
  }) : super(
          key: key,
          block: block,
          isVisible: isVisible,
          onTapLink: onTapLink,
          model: model,
        );

  @override
  Widget buildBlock(BuildContext context) {
    if (block is! MarkdownBlock) return const SizedBox.shrink();
    final markdownBlock = block as MarkdownBlock;

    return SafeMarkdownBody(
      data: markdownBlock.content,
      styleSheet: MarkdownStyleUtils.getStandardStyle(context),
      onTapLink: onTapLink ??
          (text, href, title) => _defaultOnTapLink(context, text, href, title),
      builders: {},
      blockSyntaxes: [],
    );
  }

  void _defaultOnTapLink(
      BuildContext context, String text, String href, String title) {
    MTFToast.showToast(msg: '点击了链接: $href');
    try {
      if (href != null && href.isNotEmpty) {
        if (isUrlCanOpen(href)) {
          RouteUtils.open(href);
        } else if (href.startsWith('waimaieapi.jump')) {
          model?.sendPlainMessage(text);
        }
      }
    } catch (e) {
      debugPrint('处理链接点击时出错: $e');
    }
  }
}

/// 自定义区块组件
class CustomBlockWidget extends StatefulWidget {
  final String messageId;
  final ContentBlock block;
  final bool isLoading;
  final Map<String, bool> blockLoadingStates;
  final bool isVisible;
  final void Function(String text, String href, String title) onTapLink;
  final HouseKeeperMessagePageModel model;
  final int realMessageId;

  const CustomBlockWidget({
    Key key,
    @required this.block,
    @required this.isLoading,
    @required this.blockLoadingStates,
    this.messageId,
    this.isVisible = true,
    this.onTapLink,
    this.model,
    this.realMessageId,
  }) : super(key: key);

  @override
  _CustomBlockWidgetState createState() => _CustomBlockWidgetState();
}

class _CustomBlockWidgetState extends State<CustomBlockWidget> {
  @override
  void initState() {
    super.initState();
    if (widget.block is CustomBlock &&
        (widget.block as CustomBlock).isComplete) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          widget.blockLoadingStates[(widget.block as CustomBlock).blockType] =
              false;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) return const SizedBox.shrink();

    if (widget.block is! CustomBlock) return const SizedBox.shrink();
    final customBlock = widget.block as CustomBlock;

    if (!customBlock.isComplete) {
      return LoadingBlockWidget(
        blockType: customBlock.blockType,
        isVisible: widget.blockLoadingStates[customBlock.blockType] ?? true,
      );
    }

    final blockWidget = CustomBlockFactory.createCustomBlockWidget(
      customBlock.blockType,
      customBlock.content,
      widget.model,
      messageId: widget.messageId,
      realMessageId: widget.realMessageId,
    );

    return blockWidget ?? const SizedBox.shrink();
  }
}

/// 猜你想问区块组件
class GuessBlockWidget extends BaseMessageBlockWidget {
  const GuessBlockWidget({
    Key key,
    @required ContentBlock block,
    bool isVisible = true,
    void Function(String text, String href, String title) onTapLink,
    HouseKeeperMessagePageModel model,
  }) : super(
          key: key,
          block: block,
          isVisible: isVisible,
          onTapLink: onTapLink,
          model: model,
        );

  /// 解析 markdown 链接格式 [text](link)
  Map<String, String> _parseMarkdownLink(String input) {
    final regex = RegExp(r'\[(.*?)\]\((.*?)\)');
    final match = regex.firstMatch(input);
    if (match != null) {
      return {
        'text': match.group(1) ?? '',
        'link': match.group(2) ?? '',
      };
    }
    return {
      'text': input,
      'link': '',
    };
  }

  @override
  Widget buildBlock(BuildContext context) {
    if (block is! GuessBlock) return const SizedBox.shrink();
    final guessBlock = block as GuessBlock;

    if (guessBlock.questions.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Wrap(
        spacing: 8,
        runSpacing: 12,
        children: guessBlock.questions.map((question) {
          final linkData = _parseMarkdownLink(question);
          return IntrinsicWidth(
            child: InkWell(
              onTap: () {
                if (linkData['link']?.isNotEmpty == true && onTapLink != null) {
                  onTapLink(linkData['text'] ?? '', linkData['link'] ?? '', '');
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFFEDF3FF),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      linkData['text'] ?? '',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF333333),
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: Color(0xFF666666),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

class _HouseKeeperMarkdownWidgetState extends State<HouseKeeperMarkdownWidget>
    with SingleTickerProviderStateMixin {
  /// 内容区块列表
  List<ContentBlock> _contentBlocks = [];
  bool _showThinking = false;
  Map<String, bool> _blockLoadingStates = {};

  @override
  void initState() {
    super.initState();
    _preprocessData();
    _setupMessageListener();
  }

  @override
  void didUpdateWidget(HouseKeeperMarkdownWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.data != oldWidget.data) {
      _preprocessData();
    }
    if (widget.vo != oldWidget.vo) {
      _setupMessageListener();
    }
  }

  void _setupMessageListener() {
    if (widget.vo != null) {
      widget.vo.addListener(_handleMessageStateChanged);
    }
  }

  void _handleMessageStateChanged() {
    if (!mounted) return;

    final state = widget.vo?.state;
    if (state == StreamMessageState.streaming) {
      if (!_showThinking) {
        setState(() => _showThinking = true);
      }
    } else if (state == StreamMessageState.completed) {
      if (_showThinking) {
        setState(() => _showThinking = false);
      }
    }
  }

  @override
  void dispose() {
    if (widget.vo != null) {
      widget.vo.removeListener(_handleMessageStateChanged);
    }
    super.dispose();
  }

  /// 预处理数据，将内容分解成区块
  void _preprocessData() {
    setState(() {
      String originalData = widget.data ?? '';
      final List<ContentBlock> blocks = [];
      String currentContent = '';
      bool inThinkBlock = false;
      bool inCustomBlock = false;
      bool inGuessBlock = false;
      String currentBlockType = '';
      List<String> guessQuestions = [];

      final lines = originalData.split('\n');
      for (int i = 0; i < lines.length; i++) {
        final line = lines[i];

        // 处理 Guess 块
        if (line.contains('<Guess>')) {
          if (currentContent.isNotEmpty && !widget.onlyGuessBlock) {
            blocks.add(MarkdownBlock(
              content: currentContent.trim(),
              isComplete: true,
            ));
            currentContent = '';
          }
          inGuessBlock = true;
          continue;
        } else if (line.contains('</Guess>')) {
          if (currentContent.isNotEmpty) {
            // 解析猜你想问的内容
            if (!widget.excludeGuessBlock) {
              guessQuestions.addAll(
                currentContent
                    .split('\n')
                    .where((line) => line.trim().isNotEmpty)
                    .map((line) => line.trim())
                    .toList(),
              );
            }
            currentContent = '';
          }
          inGuessBlock = false;
          continue;
        }

        // 如果只显示猜你想问块，跳过其他内容的处理
        if (widget.onlyGuessBlock && !inGuessBlock) {
          continue;
        }

        // 如果在猜你想问块中且需要排除，跳过内容收集
        if (inGuessBlock && widget.excludeGuessBlock) {
          continue;
        }

        // 处理 think 块
        if (line.contains('<think>')) {
          if (currentContent.isNotEmpty) {
            blocks.add(MarkdownBlock(
              content: currentContent.trim(),
              isComplete: true,
            ));
            currentContent = '';
          }
          inThinkBlock = true;
          // 处理同一行中 <think> 后面的内容
          final thinkContent =
              line.substring(line.indexOf('<think>') + 7).trim();
          if (thinkContent.isNotEmpty) {
            currentContent = thinkContent + '\n';
          }
          continue;
        } else if (line.contains('</think>')) {
          // 处理同一行中 </think> 前面的内容
          final endIndex = line.indexOf('</think>');
          if (endIndex > 0) {
            currentContent += line.substring(0, endIndex).trim() + '\n';
          }

          if (currentContent.isNotEmpty) {
            blocks.add(ThinkBlock(
              content: currentContent.trim(),
              isComplete: true,
            ));
            currentContent = '';
          }
          inThinkBlock = false;

          // 处理同一行中 </think> 后面的内容
          final remainingContent = line.substring(endIndex + 8).trim();
          if (remainingContent.isNotEmpty) {
            currentContent = remainingContent + '\n';
          }
          continue;
        }

        // 处理自定义块
        final startMatch =
            RegExp(r'^:::(\w+)(\s+(.+))?$').firstMatch(line.trim());
        if (startMatch != null) {
          if (currentContent.isNotEmpty) {
            blocks.add(MarkdownBlock(
              content: currentContent.trim(),
              isComplete: true,
            ));
            currentContent = '';
          }
          inCustomBlock = true;
          currentBlockType = startMatch.group(1);
          _blockLoadingStates[currentBlockType] = true;
          continue;
        } else if ((line.trim() == ':::' ||
                line.trim() == '::' ||
                line.trim() == ':') &&
            inCustomBlock) {
          // 异常情况兜底，当结尾的:::分开下发时，添加保护
          // 如果当前行是 :: 并且下一行是 :，则跳过当前行，等待下一行处理
          if ((line.trim() == '::' &&
                  i + 1 < lines.length &&
                  lines[i + 1].trim() == ':') ||
              (line.trim() == ':' &&
                  i + 1 < lines.length &&
                  lines[i + 1].trim() == '::')) {
            continue;
          }

          if (currentContent.isNotEmpty) {
            blocks.add(CustomBlock(
              blockType: currentBlockType,
              content: currentContent.trim(),
              isComplete: _isCustomBlockContentComplete(
                  currentContent, currentBlockType),
            ));
            currentContent = '';
            currentBlockType = '';
          }
          inCustomBlock = false;
          continue;
        }

        // 收集当前块的内容
        if (inGuessBlock) {
          if (line.trim().isNotEmpty) {
            currentContent += line + '\n';
          }
        } else if (!widget.onlyGuessBlock) {
          currentContent += line + '\n';
        }
      }

      // 处理最后一个未闭合的块
      if (currentContent.isNotEmpty) {
        if (inThinkBlock && !widget.onlyGuessBlock) {
          blocks.add(ThinkBlock(
            content: currentContent.trim(),
            isComplete: false,
          ));
        } else if (inCustomBlock && !widget.onlyGuessBlock) {
          blocks.add(CustomBlock(
            blockType: currentBlockType,
            content: currentContent.trim(),
            isComplete:
                _isCustomBlockContentComplete(currentContent, currentBlockType),
          ));
        } else if (!inGuessBlock && !widget.onlyGuessBlock) {
          blocks.add(MarkdownBlock(
            content: currentContent.trim(),
            isComplete: true,
          ));
        }
      }

      // 如果有猜你想问的内容且不排除，添加到最后
      if (guessQuestions.isNotEmpty && !widget.excludeGuessBlock) {
        blocks.add(GuessBlock(
          questions: guessQuestions,
          isComplete: true,
        ));
      }

      _contentBlocks = blocks;
      _showThinking = widget.vo?.state == StreamMessageState.streaming;
    });
  }

  @override
  Widget build(BuildContext context) {
    try {
      final List<Widget> children = [];

      // 创建一个闭包来捕获 context
      void handleTapLink(String text, String href, String title) {
        try {
          if (href != null && href.isNotEmpty) {
            if (href.startsWith('waimaieapi.jump')) {
              widget.model?.sendPlainMessage(text);
            } else if (_isUrlCanOpen(href)) {
              RouteUtils.open(href);
            }
          }
        } catch (e) {
          debugPrint('处理链接点击时出错: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('无法打开链接: $href'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }

      for (final block in _contentBlocks) {
        switch (block.type) {
          case ContentBlockType.think:
            if (block is ThinkBlock) {
              children.add(ThinkBlockWidget(
                block: block,
                showThinking: _showThinking,
                onToggleThinking: _toggleThinking,
                onTapLink: widget.onTapLink,
                model: widget.model,
              ));
            }
            break;
          case ContentBlockType.markdown:
            if (block is MarkdownBlock) {
              children.add(MarkdownBlockWidget(
                block: block,
                onTapLink: widget.onTapLink,
                model: widget.model,
              ));
            }
            break;
          case ContentBlockType.custom:
            if (block is CustomBlock) {
              children.add(CustomBlockWidget(
                block: block,
                isLoading: _blockLoadingStates[block.blockType] ?? true,
                blockLoadingStates: _blockLoadingStates,
                onTapLink: widget.onTapLink,
                model: widget.model,
                messageId: widget.vo?.id,
                realMessageId: widget.vo?.messageId,
              ));
            }
            break;
          case ContentBlockType.guess:
            if (block is GuessBlock) {
              children.add(GuessBlockWidget(
                block: block,
                onTapLink: widget.onTapLink ?? handleTapLink,
                model: widget.model,
              ));
            }
            break;
        }
      }

      return Container(
        padding: widget.padding,
        margin: widget.margin,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: widget.borderRadius,
          boxShadow: widget.boxShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: children,
        ),
      );
    } catch (e) {
      debugPrint('构建Markdown小部件时出错: $e');
      return Container(
        padding: const EdgeInsets.all(12),
        margin: widget.margin,
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red),
        ),
        child: const Text(
          '我遇到无法理解的事情啦😭',
          style: TextStyle(color: Colors.red),
        ),
      );
    }
  }

  /// 切换思考内容的显示状态
  void _toggleThinking() {
    setState(() => _showThinking = !_showThinking);
  }

  /// 检查自定义块内容是否完整
  bool _isCustomBlockContentComplete(String content, String blockType) {
    if (content == null || content.isEmpty) return false;

    // 检查常见的不完整情况
    if (content.contains('...') || content.contains('…')) return false;

    // 根据不同的块类型进行特定的验证
    switch (blockType.toLowerCase()) {
      case 'warning':
        return _isWarningBlockComplete(content);
      case 'info':
        return _isInfoBlockComplete(content);
      case 'tip':
        return _isTipBlockComplete(content);
      case 'note':
        return _isNoteBlockComplete(content);
      default:
        return _isDefaultBlockComplete(content);
    }
  }

  /// 检查警告块是否完整
  bool _isWarningBlockComplete(String content) {
    // 警告块通常包含标题和内容
    final lines = content.split('\n');
    if (lines.length < 2) return false;

    // 检查标题行是否包含警告图标和标题
    final titleLine = lines[0].trim();
    if (!titleLine.contains('⚠️') && !titleLine.contains('警告')) return false;

    // 检查内容是否完整
    final contentLines = lines.sublist(1);
    return contentLines.any((line) => line.trim().isNotEmpty);
  }

  /// 检查信息块是否完整
  bool _isInfoBlockComplete(String content) {
    // 信息块通常包含标题和内容
    final lines = content.split('\n');
    if (lines.length < 2) return false;

    // 检查标题行是否包含信息图标和标题
    final titleLine = lines[0].trim();
    if (!titleLine.contains('ℹ️') && !titleLine.contains('信息')) return false;

    // 检查内容是否完整
    final contentLines = lines.sublist(1);
    return contentLines.any((line) => line.trim().isNotEmpty);
  }

  /// 检查提示块是否完整
  bool _isTipBlockComplete(String content) {
    // 提示块通常包含标题和内容
    final lines = content.split('\n');
    if (lines.length < 2) return false;

    // 检查标题行是否包含提示图标和标题
    final titleLine = lines[0].trim();
    if (!titleLine.contains('💡') && !titleLine.contains('提示')) return false;

    // 检查内容是否完整
    final contentLines = lines.sublist(1);
    return contentLines.any((line) => line.trim().isNotEmpty);
  }

  /// 检查注释块是否完整
  bool _isNoteBlockComplete(String content) {
    // 注释块通常包含标题和内容
    final lines = content.split('\n');
    if (lines.length < 2) return false;

    // 检查标题行是否包含注释图标和标题
    final titleLine = lines[0].trim();
    if (!titleLine.contains('📝') && !titleLine.contains('注释')) return false;

    // 检查内容是否完整
    final contentLines = lines.sublist(1);
    return contentLines.any((line) => line.trim().isNotEmpty);
  }

  /// 检查默认块是否完整
  bool _isDefaultBlockComplete(String content) {
    // 使用栈来检查花括号和方括号是否正确配对
    List<String> stack = [];
    for (int i = 0; i < content.length; i++) {
      if (content[i] == '{' || content[i] == '[') {
        stack.add(content[i]);
      } else if (content[i] == '}') {
        if (stack.isEmpty || stack.last != '{') return false;
        stack.removeLast();
      } else if (content[i] == ']') {
        if (stack.isEmpty || stack.last != '[') return false;
        stack.removeLast();
      }
    }

    // 检查是否有未闭合的括号
    if (stack.isNotEmpty) return false;

    // 检查内容是否为空
    if (content.trim().isEmpty) return false;

    // 检查是否包含不完整的标记
    if (content.contains('...') || content.contains('…')) return false;

    return true;
  }

  bool _isUrlCanOpen(String href) {
    return href != null &&
        href.isNotEmpty &&
        (href.startsWith('http') ||
            href.startsWith('https') ||
            href.startsWith('itakeawaybiz'));
  }
}
