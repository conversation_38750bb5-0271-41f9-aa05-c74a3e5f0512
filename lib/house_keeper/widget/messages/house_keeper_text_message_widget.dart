import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_vo.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:waimai_e_flutter_house_keeper/house_keeper/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/widget/messages/house_keeper_markdown_widget.dart';

class HouseKeeperTextMessageWidget extends StatelessWidget {
  const HouseKeeperTextMessageWidget({
    Key key,
    @required this.message,
    @required this.model,
  }) : super(key: key);

  final HouseKeeperMessage message;
  final HouseKeeperMessagePageModel model;

  @override
  Widget build(BuildContext context) {
    return Container(
        alignment: message.role == 'USER'
            ? Alignment.centerRight
            : Alignment.centerLeft,
        child: message.role == 'USER'
            ? UserMessageWidget(
                message: message,
              )
            : SystemMessageWidget(
                message: message,
                model: model,
              ));
  }

  // 检查是否是工具响应
  bool _isToolResponse(String content) {
    if (content == null || content.isEmpty) {
      return false;
    }

    try {
      final data = jsonDecode(content);
      return data != null &&
          data is Map &&
          data.containsKey('toolName') &&
          data.containsKey('response');
    } catch (e) {
      return false;
    }
  }
}

class SystemMessageWidget extends StatefulWidget {
  const SystemMessageWidget({
    Key key,
    @required this.message,
    @required this.model,
  }) : super(key: key);

  final HouseKeeperMessage message;
  final HouseKeeperMessagePageModel model;

  @override
  _SystemMessageWidgetState createState() => _SystemMessageWidgetState();
}

class _SystemMessageWidgetState extends State<SystemMessageWidget> {
  bool _showThinking = true;
  String _processedContent = '';
  String _thinkingContent = '';
  String _responseContent = '';
  bool _hasThinking = false;

  @override
  void initState() {
    super.initState();
    _processContent();
  }

  void _processContent() {
    final content = widget.message?.content ?? '';

    if (content.contains('<think/>')) {
      try {
        final thinkIndex = content.indexOf('<think/>');
        final thinkContent = content.substring(0, thinkIndex).trim();
        final responseContent = content.substring(thinkIndex + 8).trim();

        if (thinkContent.isNotEmpty) {
          _hasThinking = true;
          _thinkingContent = thinkContent;
          _responseContent = responseContent;

          // 根据思考内容长度决定是否默认展开
          _showThinking = thinkContent.length < 500; // 如果思考内容较短，默认展开

          // 初始状态只显示回答部分
          _processedContent = _responseContent;
        } else {
          _processedContent = responseContent;
        }
      } catch (e) {
        if (kDebugMode) {
          print('处理 think 标签时出错: $e');
        }
        _processedContent = content.replaceAll('<think/>', '\n\n');
      }
    } else {
      _processedContent = content;
    }

    // 移除其他 HTML 标签
    _processedContent = _processedContent.replaceAll(RegExp(r'<[^>]*>'), '');
    _thinkingContent = _thinkingContent.replaceAll(RegExp(r'<[^>]*>'), '');

    // 处理特殊字符和格式
    _processedContent = _preprocessMarkdownText(_processedContent);
    _thinkingContent = _preprocessMarkdownText(_thinkingContent);
  }

  String _preprocessMarkdownText(String text) {
    if (text == null || text.isEmpty) {
      return '';
    }

    // 确保列表项正确显示
    text = text.replaceAll(RegExp(r'^\* ', multiLine: true), '* ');
    text = text.replaceAll(RegExp(r'^\d+\. ', multiLine: true), '1. ');

    // 确保段落之间有足够的换行
    text = text.replaceAll('\n\n', '\n\n');

    // 处理特殊字符
    text = text.replaceAll('\\', '\\\\');

    // 确保代码块正确显示
    text = text.replaceAll(RegExp(r'```(\w*)\n'), '```\$1\n');

    return text;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
          bottomLeft: Radius.circular(2.0),
          bottomRight: Radius.circular(10.0),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 如果有思考过程，显示思考过程相关UI
          if (_hasThinking)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 思考过程标题和折叠按钮放在同一行
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '思考过程',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    // 只显示箭头的折叠按钮
                    InkWell(
                      onTap: () {
                        setState(() {
                          _showThinking = !_showThinking;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        // child: Icon(
                        //   _showThinking
                        //       ? Icons.keyboard_arrow_up
                        //       : Icons.keyboard_arrow_down,
                        //   size: 20,
                        //   color: Colors.grey.shade700,
                        // ),
                      ),
                    ),
                  ],
                ),

                // 思考过程内容 - 根据折叠状态显示或隐藏
                if (_showThinking) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.only(left: 12),
                    decoration: BoxDecoration(
                      border: Border(
                        left: BorderSide(
                          color: Colors.grey.shade400,
                          width: 1,
                        ),
                      ),
                    ),
                    child: MarkdownBody(
                      data: _thinkingContent,
                      selectable: true,
                      styleSheet:
                          MarkdownStyleSheet.fromTheme(Theme.of(context))
                              .copyWith(
                        p: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          height: 1.5,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 1,
                    color: Colors.grey.shade200,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                  ),
                ],

                const Text(
                  '回答',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),

          // 回答内容 - 使用通用 Markdown 组件
          HouseKeeperMarkdownWidget(
            data: _hasThinking ? _responseContent : _processedContent,
            model: widget.model,
          )
        ],
      ),
    );
  }
}

class UserMessageWidget extends StatelessWidget {
  const UserMessageWidget({Key key, @required this.message}) : super(key: key);
  final HouseKeeperMessage message;

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.65,
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: const BoxDecoration(
        color: Color(0xFF050505),
        borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      child: Text(
        message?.content ?? '',
        style: const TextStyle(
          fontSize: 14.0,
          color: Colors.white,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
