import 'package:flutter/cupertino.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/common/house_keeper_reporter.dart';

class HouseKeeperFeedbackDialog extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _HouseKeeperFeedbackDialogState();
  }
}

class _HouseKeeperFeedbackDialogState extends State<HouseKeeperFeedbackDialog> {
  List<String> tags = [
    '语音识别不准确',
    '理解有误',
    '内容没有帮助',
    '回答得太慢',
    '操作错误',
    '回答的内容看不懂',
  ];

  Map<String, bool> states = {
    '语音识别不准确': false,
    '理解有误': false,
    '内容没有帮助': false,
    '回答得太慢': false,
    '操作错误': false,
    '回答的内容看不懂': false,
  };

  bool _click = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                  child: Container(
                alignment: Alignment.center,
                child: const Text('您的反馈将有助于商家助手的进步',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF222222),
                      fontWeight: FontWeight.w500,
                    )),
              )),
              GestureDetector(
                onTap: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  }
                },
                child: Image(
                  width: 24,
                  height: 24,
                  image: AdvancedNetworkImage(
                      'https://p0.meituan.net/ingee/598cc41286566ac1164a42d0da8c0cef1170.png'),
                ),
              ),
            ],
          ),

          // 标签
          Container(
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 35),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: tags.map((tag) {
                return GestureDetector(
                  onTap: () {
                    states[tag] = !states[tag];
                    _update();
                    setState(() {});
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: states[tag]
                          ? const Color(0xFFFFF3EB)
                          : const Color(0xFFF5F6FA),
                      borderRadius:
                          const BorderRadius.all(Radius.circular(6.5)),
                    ),
                    child: Text(
                      tag,
                      style: TextStyle(
                        fontSize: 12,
                        color: states[tag]
                            ? const Color(0xFFFF6A00)
                            : const Color(0xFF222222),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          GestureDetector(
            onTap: () {
              submit();
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: 20, left: 12, right: 12),
              height: 40,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                    colors: [Color(0xFFFFE74D), Color(0xFFFFDD1A)]),
                // 渐变色
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Center(
                child: Text(
                  '提交',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF222222),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _update() {
    List<String> selects =
        states.keys.where((element) => states[element] == true).toList();

    _click = selects.isNotEmpty;
  }

  void submit() {
    List<String> selects =
        states.keys.where((element) => states[element] == true).toList();
    if (selects.isEmpty) {
      return;
    }

    String content = selects.join(',');
    // 通过埋点保存反馈情况
    HouseKeeperReporter.reportUnUseFullClick(val: {
      'content': content,
    });

    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }
}
