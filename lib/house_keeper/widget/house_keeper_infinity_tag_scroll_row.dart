import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper/config/prompt_config.dart';

class TagItem {
  final String text;
  final String icon;
  final String subAgentCode;
  TagItem({this.text, this.icon, this.subAgentCode});
}

class ScrollTagsWidget extends StatefulWidget {
  final List<PromptConfigItem> dataList;
  final bool autoScroll; // 是否自动滚动
  final double scrollSpeed; // 滚动速度（像素/秒）
  final Function(TagItem) onTagTap; // 标签点击事件回调

  const ScrollTagsWidget({
    Key key,
    this.dataList,
    this.autoScroll = false,
    this.scrollSpeed = 100.0,
    this.onTagTap,
  }) : super(key: key);

  @override
  _ScrollTagsWidgetState createState() => _ScrollTagsWidgetState();
}

class _ScrollTagsWidgetState extends State<ScrollTagsWidget> {
  final ScrollController _scrollController = ScrollController();
  List<TagItem> _tags = [];
  bool _isLoading = false; // 是否正在加载
  int _currentIndex = 0;
  Timer _autoScrollTimer; // 使用Timer替代AnimationController
  bool _isDisposed = false; // 标记组件是否已销毁
  bool _isUserScrolling = false; // 标记用户是否正在手动滑动
  Timer _resumeAutoScrollTimer; // 用于恢复自动滚动的定时器

  @override
  void initState() {
    super.initState();
    _initTagsList();
    _setupScrollListener();

    if (widget.autoScroll) {
      _startAutoScroll();
    }
  }

  // 初始化标签列表
  void _initTagsList() {
    if (widget.dataList != null && widget.dataList.isNotEmpty) {
      _tags = widget.dataList.map((item) {
        return TagItem(
          text: item.label,
          icon: item.icon,
          subAgentCode: item.subAgentCode,
        );
      }).toList();
    }
  }

  // 设置滚动监听器
  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.maxScrollExtent > 0) {
        double remainingDistance = _scrollController.position.maxScrollExtent -
            _scrollController.position.pixels;
        double screenWidth = MediaQuery.of(context).size.width;

        // 如果剩余距离小于一屏宽度x1.5，则加载更多
        if (remainingDistance < screenWidth * 1.5 && !_isLoading) {
          _loadMoreTags();
        }
      }
    });
  }

  // 启动自动滚动
  void _startAutoScroll() {
    // 取消可能存在的定时器
    _autoScrollTimer?.cancel();

    // 计算滚动间隔，确保滚动平滑
    const scrollInterval = Duration(milliseconds: 16); // 约60fps

    // 计算每次滚动的距离
    final pixelsPerFrame = widget.scrollSpeed / 60;

    _autoScrollTimer = Timer.periodic(scrollInterval, (timer) {
      if (!mounted || _isDisposed || _isUserScrolling) {
        return; // 如果用户正在滑动，暂停自动滚动但不取消定时器
      }

      _performAutoScroll(pixelsPerFrame);
    });
  }

  // 执行自动滚动
  void _performAutoScroll(double pixelsPerFrame) {
    if (_scrollController.hasClients) {
      // 计算新的滚动位置
      double newOffset = _scrollController.offset + pixelsPerFrame;

      // 如果到达末尾，重置到开始位置
      if (newOffset >= _scrollController.position.maxScrollExtent) {
        newOffset = 0;
      }

      _scrollController.jumpTo(newOffset);
    }
  }

  // 暂停自动滚动
  void _pauseAutoScroll() {
    _isUserScrolling = true;

    // 取消之前的恢复定时器
    _resumeAutoScrollTimer?.cancel();
  }

  // 恢复自动滚动
  void _resumeAutoScroll() {
    // 设置延迟恢复，给用户一些时间完成滑动操作
    _resumeAutoScrollTimer?.cancel();
    _resumeAutoScrollTimer = Timer(const Duration(seconds: 2), () {
      if (mounted && !_isDisposed && widget.autoScroll) {
        setState(() {
          _isUserScrolling = false;
        });
      }
    });
  }

  // 加载更多标签
  void _loadMoreTags() {
    setState(() {
      _isLoading = true;
    });

    // 模拟加载更多数据
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;

      setState(() {
        // 加载数据
        PromptConfigItem targetData = widget.dataList[_currentIndex];
        _tags.addAll([
          TagItem(
              text: targetData.label ?? '',
              icon: targetData.icon ?? '',
              subAgentCode: targetData.subAgentCode ?? ''),
        ]);
        _isLoading = false;
        // 更新索引
        if (_currentIndex + 1 >= widget.dataList.length) {
          _currentIndex = 0;
        } else {
          _currentIndex++;
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // 检测用户手势，暂停/恢复自动滚动
      onPanDown: (_) => _pauseAutoScroll(),
      onPanEnd: (_) => _resumeAutoScroll(),
      onPanCancel: () => _resumeAutoScroll(),
      child: ListView.builder(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(), // 使用弹性滚动效果
        // 添加性能优化相关参数
        addAutomaticKeepAlives: true,
        addRepaintBoundaries: true,
        itemCount: _tags.length,
        itemBuilder: (context, index) {
          return RepaintBoundary(
            child: KeepAliveChip(
              tagItem: _tags[index],
              onTap: () {
                if (widget.onTagTap != null) {
                  widget.onTagTap(_tags[index]);
                }
              },
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _isDisposed = true;
    _autoScrollTimer?.cancel();
    _resumeAutoScrollTimer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }
}

class KeepAliveChip extends StatefulWidget {
  final TagItem tagItem;
  final Function() onTap; // 点击事件回调

  KeepAliveChip({Key key, this.tagItem, this.onTap}) : super(key: key);

  @override
  _KeepAliveChipState createState() => _KeepAliveChipState();
}

class _KeepAliveChipState extends State<KeepAliveChip>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      onTap: widget.onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildIcon(),
              Text(widget.tagItem.text),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    if (widget.tagItem.icon == null || widget.tagItem.icon.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 15,
          height: 15,
          child: Image(
            image: AdvancedNetworkImage(
              widget.tagItem.icon,
              useDiskCache: true,
              timeoutDuration: const Duration(seconds: 10),
              loadedCallback: () {},
              loadFailedCallback: () {},
            ),
            errorBuilder: (context, error, stackTrace) {
              return const SizedBox.shrink();
            },
          ),
        ),
        const SizedBox(width: 6),
      ],
    );
  }
}
