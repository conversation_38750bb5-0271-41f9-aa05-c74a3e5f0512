import 'package:flutter/widgets.dart';

abstract class BasePage extends StatefulWidget {
  BasePage({Key key, String pageName, @required this.params})
      : pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : ''),
        super(key: key);

  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  StatefulElement createElement() {
    report(pageName);
    return StatefulElement(this);
  }

  void report(String pageName) async {}
}
