class ImageModel {
  ImageModel.fromJson(Map<dynamic, dynamic> json) {
    if (json['photoInfos'] != null) {
      photoInfos = <ImageModelObj>[];
      json['photoInfos'].forEach((v) {
        photoInfos.add(ImageModelObj.fromJson(v));
      });
    }
  }

  List<ImageModelObj> photoInfos;
}

class ImageModelObj {
  ImageModelObj.fromJson(Map<dynamic, dynamic> json) {
    localId = json['localId'];
  }

  String localId;
}
