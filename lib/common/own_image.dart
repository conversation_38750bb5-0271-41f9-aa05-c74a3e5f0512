import 'package:flutter/cupertino.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_advanced_networkimage/transition.dart';

class ImageWidget extends StatelessWidget {
  const ImageWidget({
    @required this.url,
    this.w = 100,
    this.h = 100,
    this.borderRadius,
    this.defaultUrl = "images/common/empty_user.jpg",
    this.fit = BoxFit.cover,
    this.showDefaultImg,
    this.useDiskCache = false,
  });

  final String url;
  final double w;
  final BorderRadius borderRadius;
  final double h;
  final String defaultUrl;
  final BoxFit fit;
  final bool showDefaultImg;
  final bool useDiskCache;

  @override
  Widget build(BuildContext context) {
    final Image bgImg = Image(
      image: AssetImage(defaultUrl),
      width: w,
      height: h,
      fit: fit,
    );
    return url != null && url != ''
        ? TransitionToImage(
            image: AdvancedNetworkImage(
              url,
              useDiskCache: useDiskCache,
            ),
            borderRadius: borderRadius ?? BorderRadius.circular(0),
            fit: fit,
            placeholder: bgImg,
            width: w,
            height: h,
            loadingWidget: (url == null || url == '')
                ? bgImg
                : Center(
                    child: CupertinoActivityIndicator(radius: 14.0),
                  ),
            duration: Duration(milliseconds: 0),
          )
        : showDefaultImg == true
            ? bgImg
            : SizedBox.shrink();
  }
}