import 'dart:convert';

import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/aot/im_scene/voice_assistant_im_msg_queue.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/constant.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_push_data.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_scene_constant.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_api.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_general.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/order/order_tools.dart';
import 'package:waimai_e_native_order/waimai_e_native_order.dart';
import 'package:waimai_e_push/push_handler.dart';
import 'package:waimai_e_flutter_order_stone/native_plugins/order_business_plugin.dart';

class PushImSenceTagHandler extends PushHandler {
  @override
  void process(Map pushInfo) {
    super.process(pushInfo);
    OrderBusinessPlugin.isForeground().then((isForeground) {
      //增加push埋点
      Map<String, dynamic> params = {
        'isForeground': isForeground,
        'isVoiceAssistantProcessing':
            VoiceAssistantIMMsgManager.instance.isVoiceAssistantProcessing
      };
      VoiceAssistantReporter.reportPush(val: params);
      if (isForeground) {
        //应用在前台
        processPush(pushInfo);
      }
    });
  }

  void processPush(Map pushMap) async {
    final extraParams = jsonDecode(pushMap['extra']['extraParams']);
    String wmOrderViewId = extraParams['wmOrderViewId']?.toString();

    String moseSceneTagCode = extraParams['moseSceneTagCode'];
    if (StringUtil.isEmpty(wmOrderViewId) ||
        StringUtil.isEmpty(moseSceneTagCode)) {
      return;
    }
    var order = await OrderTools.getOrder(wmOrderViewId);
    if (order == null) {
      return;
    }
    SceneData sceneData = SceneData();
    sceneData.wmOrderId = wmOrderViewId?.toString();
    int daySeq = await OrderTools.getDayseq(wmOrderViewId?.toString());
    sceneData.orderSeq = daySeq;
    sceneData.userImContent = extraParams['content'];
    sceneData.imBtnInfos ??= [];
    //添加各个场景下的数据
    sceneData = await getSceneData(sceneData, wmOrderViewId, moseSceneTagCode);

    if (ArrayUtil.isEmpty(sceneData?.imBtnInfos)) {
      return;
    }
    sceneData.operationDesc = '顾客消息："${sceneData?.userImContent}"';
    sceneData.sceneTagCode = moseSceneTagCode;
    sceneData.pushExtra = pushMap['extra']['extraParams'];
    sceneData.entranceType = entranceFromPush;
    bool isSuggestWordGray =
        await LLMApi.getGrayAb(VoiceAssistantConstant.bcImGenerateTextGrayKey);
    sceneData.isSuggestWordGray = isSuggestWordGray;
    if (!(isSuggestWordGray ?? false)) {
      /// 插入预处理到队列
      VoiceAssistantIMMsgManager.enqueue(sceneData);
      return;
    }
    List<String> actions;
    if (sceneData?.imBtnInfos != null) {
      actions = sceneData?.imBtnInfos?.map((info) => info.name)?.toList();
    }
    Map<String, dynamic> params = {
      'orderViewId': wmOrderViewId,
      'intent': moseSceneTagCode,
      'orderNumber': daySeq,
      'content': extraParams['content'],
      'allowActions': actions
    };
    UnionAnswerData unionAnswer = await LLMApi.getGptTextResult(
        params, VoiceAssistantConstant.bcImComfortSuggest);
    if (unionAnswer == null) {
      return;
    }
    sceneData.comfortText = unionAnswer?.comfortText;
    sceneData.suggestText = unionAnswer?.suggestText;

    /// 插入预处理到队列
    VoiceAssistantIMMsgManager.enqueue(sceneData);
  }

  Future<SceneData> getSceneData(SceneData sceneData, String wmOrderViewId,
      String moseSceneTagCode) async {
    //自行配送
    bool isShowSelfLogisticsButtonVo =
        await OrderTools.isShowSelfLogisticsButtonVo(wmOrderViewId?.toString());
    //加小费
    bool isShowAddFeeButtonVo =
        await OrderTools.isShowAddFeeButtonVo(wmOrderViewId?.toString());
    //联系骑手
    bool isShowPhoneBtn =
        await OrderTools.isShowPhoneBtn(wmOrderViewId?.toString());
    //联系顾客
    bool isShowCallUser =
        await OrderTools.isShowCallUser(wmOrderViewId?.toString());
    //部分退款
    bool isShowPartRefund =
        await OrderTools.isShowPartRefund(wmOrderViewId?.toString());
    switch (moseSceneTagCode) {
      //催单场景和IM弹窗融合
      case sceneFoodLess:
        //餐品少送
        getSceneFoodLessData(sceneData, isShowCallUser, isShowPartRefund);
        break;
      case sceneRemarkAdd:
        //补充备注
        sceneData = await getSceneRemarkAddData(sceneData);
        break;
      case sceneOrderAlter:
        //修改订单
        getSceneOrderAlterData(sceneData, isShowCallUser, isShowPhoneBtn);
        break;
      case sceneFoodLoss:
        //餐损撒餐
        getSceneFoodLossData(
            sceneData, isShowPartRefund, isShowCallUser, isShowPhoneBtn);
        break;

      default:
        break;
    }
    return sceneData;
  }

//催单
  void getSceneOrderRemindData(
      SceneData sceneData,
      bool isShowSelfLogisticsButtonVo,
      bool isShowAddFeeButtonVo,
      bool isShowPhoneBtn) {
    if (isShowSelfLogisticsButtonVo) {
      IMBtnInfo imBtnInfo = IMBtnInfo('自行配送', '自己配送，自己送，转自配', selfBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
    if (isShowAddFeeButtonVo) {
      IMBtnInfo imBtnInfo = IMBtnInfo('加小费', '小费,给骑手加钱，加点钱', feeBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
    if (isShowPhoneBtn) {
      IMBtnInfo imBtnInfo = IMBtnInfo('联系骑手', '给骑手打电话，联系骑手', riderBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
    IMBtnInfo imBtnInfo =
        IMBtnInfo('安抚顾客', '补充备注，知道了，好的，已完成，安抚顾客，回复，自动回复', replyCustomerBtnID);
    sceneData.imBtnInfos.add(imBtnInfo);
    IMBtnInfo orderBtnInfo =
        IMBtnInfo('查看订单', '看下订单,查看订单详情,查看订单,查看订单状态', orderDetailID);
    sceneData.imBtnInfos.add(orderBtnInfo);
  }

//餐品少送
  void getSceneFoodLessData(
      SceneData sceneData, bool isShowCallUser, bool isShowPartRefund) {
    if (isShowCallUser) {
      IMBtnInfo imBtnInfo = IMBtnInfo('电话联系顾客', '给用户打电话，联系顾客，打电话', userBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
    if (isShowPartRefund) {
      IMBtnInfo imBtnInfo = IMBtnInfo('部分退款', '部分商品退款，退款，退钱', partRefundBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
    IMBtnInfo imBtnInfo =
        IMBtnInfo('安抚顾客', '补充备注，知道了，好的，已完成，安抚顾客，回复，自动回复', replyCustomerBtnID);
    sceneData.imBtnInfos.add(imBtnInfo);
    IMBtnInfo orderBtnInfo =
        IMBtnInfo('查看订单', '看下订单,查看订单详情,查看订单,查看订单状态', orderDetailID);
    sceneData.imBtnInfos.add(orderBtnInfo);
  }

//补充备注
  Future<SceneData> getSceneRemarkAddData(SceneData sceneData) async {
    IMBtnInfo imBtnInfo =
        IMBtnInfo('安抚顾客', '补充备注，知道了，好的，已完成，安抚顾客，回复，自动回复', replyCustomerBtnID);
    sceneData?.imBtnInfos?.add(imBtnInfo);
    IMBtnInfo orderBtnInfo =
        IMBtnInfo('查看订单', '看下订单,查看订单详情,查看订单,查看订单状态', orderDetailID);
    sceneData.imBtnInfos.add(orderBtnInfo);
    bool isConnect = await WaimaiENativeOrder.isBtDeviceConnected();
    if (isConnect) {
      IMBtnInfo orderBtnInfo = IMBtnInfo('打印备注', '打印备注，打印小票', printRemarkID);
      sceneData.imBtnInfos.add(orderBtnInfo);
    }
    return sceneData;
  }

  //修改订单
  void getSceneOrderAlterData(
      SceneData sceneData, bool isShowCallUser, bool isShowPhoneBtn) {
    IMBtnInfo imBtnInfo =
        IMBtnInfo('安抚顾客', '补充备注，知道了，好的，已完成，安抚顾客，回复，自动回复', replyCustomerBtnID);
    sceneData.imBtnInfos.add(imBtnInfo);
    if (isShowCallUser) {
      IMBtnInfo imBtnInfo = IMBtnInfo('电话联系顾客', '给用户打电话，联系顾客，打电话', userBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
    if (isShowPhoneBtn) {
      IMBtnInfo imBtnInfo = IMBtnInfo('联系骑手', '给骑手打电话，联系骑手', riderBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
  }

//餐损撒餐
  void getSceneFoodLossData(SceneData sceneData, bool isShowPartRefund,
      bool isShowCallUser, bool isShowPhoneBtn) {
    IMBtnInfo imBtnInfo =
        IMBtnInfo('安抚顾客', '补充备注，知道了，好的，已完成，安抚顾客，回复，自动回复', replyCustomerBtnID);
    sceneData.imBtnInfos.add(imBtnInfo);
    if (isShowPartRefund) {
      IMBtnInfo imBtnInfo = IMBtnInfo('部分退款', '部分商品退款，退款，退钱', partRefundBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
    if (isShowCallUser) {
      IMBtnInfo imBtnInfo = IMBtnInfo('电话联系顾客', '给用户打电话，联系顾客，打电话', userBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
    if (isShowPhoneBtn) {
      IMBtnInfo imBtnInfo = IMBtnInfo('联系骑手', '给骑手打电话，联系骑手', riderBtnID);
      sceneData.imBtnInfos.add(imBtnInfo);
    }
  }
}
