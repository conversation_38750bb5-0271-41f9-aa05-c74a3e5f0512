import 'dart:collection';

import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/common/wme_flutter_util.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_push_data.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_scene_constant.dart';

class VoiceAssistantIMMsgManager {
  // 私有化构造方法
  VoiceAssistantIMMsgManager._();

  // 单例
  static final VoiceAssistantIMMsgManager _instance =
      VoiceAssistantIMMsgManager._();
  static VoiceAssistantIMMsgManager get instance => _instance;

  /// 语音助手是否在处理中
  bool isVoiceAssistantProcessing = false;

  Queue<SceneData> queue = Queue();

  static void test() {
    SceneData sceneData = SceneData();
    sceneData.pushExtra = "我的餐怎么还没到";
    sceneData.operationDesc = '我不吃香菜';
    sceneData.imBtnInfos = [];
    // IMBtnInfo imBtnInfo1 = IMBtnInfo('自行配送', '自己配送，自己送，转自配', selfBtnID);
    // sceneData.imBtnInfos.add(imBtnInfo1);
    // IMBtnInfo imBtnInfo2 = IMBtnInfo('加小费', '小费,给骑手加钱', feeBtnID);
    // sceneData.imBtnInfos.add(imBtnInfo2);

    IMBtnInfo imBtnInfo3 = IMBtnInfo('联系骑手', '给骑手打电话，联系骑手', riderBtnID);
    sceneData.imBtnInfos.add(imBtnInfo3);
    IMBtnInfo imBtnInfo4 = IMBtnInfo('换骑手', '换个骑手，换骑手', changeRiderBtnID);
    sceneData.imBtnInfos.add(imBtnInfo4);
    IMBtnInfo imBtnInfo5 = IMBtnInfo('电话联系顾客', '给用户打电话，联系顾客', userBtnID);
    sceneData.imBtnInfos.add(imBtnInfo5);
    // IMBtnInfo imBtnInfo6 = IMBtnInfo('部分退款', '部分商品退款', partRefundBtnID);
    // sceneData.imBtnInfos.add(imBtnInfo6);

    sceneData.entranceType = entranceFromPush;

    sceneData.wmOrderId = '1100778191745139577';

    sceneData.orderSeq = 1;
    enqueue(sceneData);

    // IMBtnInfo imBtnInfo3 = IMBtnInfo('联系骑手', '给骑手打电话，联系骑手', riderBtnID);
    // sceneData.imBtnInfos.add(imBtnInfo3);
    // IMBtnInfo imBtnInfo4 = IMBtnInfo('换骑手', '换个骑手，换骑手', changeRiderBtnID);
    // sceneData.imBtnInfos.add(imBtnInfo4);
    // IMBtnInfo imBtnInfo5 = IMBtnInfo('电话联系顾客', '给用户打电话，联系顾客', userBtnID);
    // sceneData.imBtnInfos.add(imBtnInfo5);
    // IMBtnInfo imBtnInfo6 = IMBtnInfo('部分退款', '部分商品退款', partRefundBtnID);
    // sceneData.imBtnInfos.add(imBtnInfo6);
    IMBtnInfo imBtnInfo7 = IMBtnInfo('补充备注', '补充备注', replyCustomerBtnID);
    sceneData.imBtnInfos.add(imBtnInfo7);
    // pageVo.mainEntranceType = sceneData?.entranceType;
  }

  /// 入队
  static void enqueue(SceneData data) {
    instance.queue.addLast(data);

    if (instance.isVoiceAssistantProcessing == false) {
      handleNext();
    }
  }

  /// 出队
  static Map<String, dynamic> dequeue() {
    if (instance.queue.isEmpty) {
      return null;
    }
    return instance.queue.removeFirst().toJson();
  }

  /// 清空队列
  static void clear() {
    return instance.queue.clear();
  }

  /// 修改处理状态
  static void setVoiceAssistantProcessing(bool processing) {
    instance.isVoiceAssistantProcessing = processing;
  }

  /// 处理下一条消息
  static void handleNext() {
    if (instance.isVoiceAssistantProcessing != false) {
      return;
    }

    RouteUtils.open(
      WMESchemeUrls.flutterPageUrl('voice_assistant_gpt', params: {
        // 'flap_id': 'voice_assistant_gpt',
        // 'flap_entry': 'VoiceAssistantGPTPage',
        'moduleName': 'waimai_e_flutter',
        'mainEntranceType': entranceFromPush
      }),
      present: true,
      opaque: false,
    );
  }
}
