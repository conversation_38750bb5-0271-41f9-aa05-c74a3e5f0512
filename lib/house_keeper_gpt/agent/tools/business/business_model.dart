import 'dart:convert';

class ActivityInfoModel {
  /// 小活动id
  // int actId;

  /// 活动类型
  int actType;

  /// 门店id
  int poiId;

  /// 活动开始时间
  int startTime;

  /// 活动结束时间
  int endTime;

  /// 活动自动延期天数（0：不延期 30：延期），目前开启自动延期都是传30天
  int autoDelayDays;

  /// 活动时段
  String period;

  /// 活动周期
  String weeksTime;

  /// 门店用户类型（全部顾客：0）
  int poiUserType;

  /// 是否接受自营销协议
  int isAgree;

  /// pn号
  // String pn;

  ActivityInfoModel({
    // this.actId,
    this.actType,
    this.poiId,
    this.startTime,
    this.endTime,
    this.autoDelayDays,
    this.period,
    this.weeksTime,
    this.poiUserType,
    this.isAgree,
    // this.pn,
  });

  factory ActivityInfoModel.fromJson(Map<String, dynamic> json) {
    return ActivityInfoModel(
      // actId: json['actId'] as int ?? 0,
      actType: json['actType'] as int ?? 0,
      poiId: json['poiId'] as int ?? 0,
      startTime: json['startTime'] as int ?? 0,
      endTime: json['endTime'] as int ?? 0,
      autoDelayDays: json['autoDelayDays'] as int ?? 0,
      period: json['period'] as String ?? '',
      weeksTime: json['weeksTime'] as String ?? '',
      poiUserType: json['poiUserType'] as int ?? 0,
      isAgree: json['isAgree'] as int ?? 0,
      // pn: json['pn'] as String ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        // 'actId': actId,
        'actType': actType,
        'poiId': poiId,
        'startTime': startTime,
        'endTime': endTime,
        'autoDelayDays': autoDelayDays,
        'period': period,
        'weeksTime': weeksTime,
        'poiUserType': poiUserType,
        'isAgree': isAgree,
        // 'pn': pn,
      };
}

class ActivityPickUpPolicyModel {
  /// 顺序（档位顺序）
  int conditionSortIndex;

  /// 立减金额
  int amount;

  /// 门槛金额
  double price;

  /// 立减金额
  double discount;

  /// 美团承担成本
  double mtCharge;

  /// 商家承担成本
  double poiCharge;

  /// 代理商承担成本
  double agentCharge;

  /// 行为类型
  int behaviourType;

  /// 成本设置方式(0为按照金额设置,1为按照比例设置)
  int chargeMethod;
  ActivityPickUpPolicyModel({
    this.conditionSortIndex,
    this.amount,
    this.price,
    this.discount,
    this.mtCharge,
    this.poiCharge,
    this.agentCharge,
    this.chargeMethod,
    this.behaviourType,
  });

  factory ActivityPickUpPolicyModel.fromJson(Map<String, dynamic> json) {
    return ActivityPickUpPolicyModel(
      conditionSortIndex: json['conditionSortIndex'] as int ?? 0,
      amount: json['amount'] as int ?? 0,
      price: (json['price'] as num)?.toDouble() ?? 0.0,
      discount: (json['discount'] as num)?.toDouble() ?? 0.0,
      mtCharge: (json['mtCharge'] as num)?.toDouble() ?? 0.0,
      poiCharge: (json['poiCharge'] as num)?.toDouble() ?? 0.0,
      agentCharge: (json['agentCharge'] as num)?.toDouble() ?? 0.0,
      chargeMethod: json['chargeMethod'] as int ?? 0,
      behaviourType: json['behaviourType'] as int ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
        'conditionSortIndex': conditionSortIndex,
        'amount': amount,
        'price': price,
        'discount': discount,
        'mtCharge': mtCharge,
        'poiCharge': poiCharge,
        'agentCharge': agentCharge,
        'chargeMethod': chargeMethod,
      };
}

class ActivityPickUpInfoModel extends ActivityInfoModel {
  /// 优惠信息
  List<ActivityPickUpPolicyModel> policy;
  int behaviourType;

  ActivityPickUpInfoModel({
    int actId,
    int actType,
    int poiId,
    int startTime,
    int endTime,
    int autoDelayDays,
    String period,
    String weeksTime,
    int poiUserType,
    int isAgree,
    int behaviourType,
    String pn,
    this.policy,
  }) : super(
          // actId: actId,
          actType: actType,
          poiId: poiId,
          startTime: startTime,
          endTime: endTime,
          autoDelayDays: autoDelayDays,
          period: period,
          weeksTime: weeksTime,
          poiUserType: poiUserType,
          isAgree: isAgree,
          // pn: pn,
        );

  factory ActivityPickUpInfoModel.fromJson(Map<String, dynamic> json) {
    return ActivityPickUpInfoModel(
      actId: json['actId'] as int ?? 0,
      actType: json['actType'] as int ?? 0,
      poiId: json['poiId'] as int ?? 0,
      startTime: json['startTime'] as int ?? 0,
      endTime: json['endTime'] as int ?? 0,
      autoDelayDays: json['autoDelayDays'] as int ?? 0,
      period: json['period'] as String ?? '',
      weeksTime: json['weeksTime'] as String ?? '',
      poiUserType: json['poiUserType'] as int ?? 0,
      isAgree: json['isAgree'] as int ?? 0,
      behaviourType: json['behaviourType'] as int ?? 0,
      pn: json['pn'] as String ?? '',
      policy: (json['policy'] as List<dynamic>)
              ?.map((e) =>
                  ActivityPickUpPolicyModel.fromJson(e as Map<String, dynamic>))
              ?.toList() ??
          [],
    );
  }

  @override
  Map<String, dynamic> toJson() => super.toJson()
    ..addAll({
      'policy': jsonEncode(policy?.map((e) => e.toJson())?.toList() ?? []),
      'behaviourType': behaviourType,
    });
}

class ActivityNewCustomerPolicyModel {
  /// 立减金额
  int amount;

  /// 美团承担成本
  double mtCharge;

  /// 商家承担成本
  double poiCharge;

  /// 代理商承担成本
  double agentCharge;

  ActivityNewCustomerPolicyModel({
    this.amount,
    this.mtCharge,
    this.poiCharge,
    this.agentCharge,
  });

  factory ActivityNewCustomerPolicyModel.fromJson(Map<String, dynamic> json) {
    return ActivityNewCustomerPolicyModel(
      amount: json['amount'] as int ?? 0,
      mtCharge: (json['mtCharge'] as num)?.toDouble() ?? 0.0,
      poiCharge: (json['poiCharge'] as num)?.toDouble() ?? 0.0,
      agentCharge: (json['agentCharge'] as num)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() => {
        'amount': amount,
        'mtCharge': mtCharge,
        'poiCharge': poiCharge,
        'agentCharge': agentCharge,
      };
}

class ActivityNewCustomerInfoModel extends ActivityInfoModel {
  /// 优惠信息
  ActivityNewCustomerPolicyModel policy;

  ActivityNewCustomerInfoModel({
    int actType,
    int poiId,
    int startTime,
    int endTime,
    int autoDelayDays,
    String period,
    String weeksTime,
    int poiUserType,
    int isAgree,
    String pn,
    this.policy,
  }) : super(
          actType: actType,
          poiId: poiId,
          startTime: startTime,
          endTime: endTime,
          autoDelayDays: autoDelayDays,
          period: period,
          weeksTime: weeksTime,
          poiUserType: poiUserType,
          isAgree: isAgree,
        );

  factory ActivityNewCustomerInfoModel.fromJson(Map<String, dynamic> json) {
    return ActivityNewCustomerInfoModel(
      actType: json['actType'] as int ?? 0,
      poiId: json['poiId'] as int ?? 0,
      startTime: json['startTime'] as int ?? 0,
      endTime: json['endTime'] as int ?? 0,
      autoDelayDays: json['autoDelayDays'] as int ?? 0,
      period: json['period'] as String ?? '',
      weeksTime: json['weeksTime'] as String ?? '',
      poiUserType: json['poiUserType'] as int ?? 0,
      isAgree: json['isAgree'] as int ?? 0,
      pn: json['pn'] as String ?? '',
      policy: json['policy'] != null
          ? ActivityNewCustomerPolicyModel.fromJson(
              json['policy'] as Map<String, dynamic>)
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() => super.toJson()
    ..addAll({
      'policy': jsonEncode(policy?.toJson() ?? {}),
    });
}

class PoiPKInfo {
  int wmPoiId;
  String wmPoiName;

  PoiPKInfo({this.wmPoiId, this.wmPoiName});

  factory PoiPKInfo.fromJson(Map<String, dynamic> json) {
    return PoiPKInfo(
      wmPoiId: json['wmPoiId'] as int ?? 0,
      wmPoiName: json['wmPoiName'] as String ?? '',
    );
  }
}

class PKDiffInfo {
  int pkRes;
  String indicatorName;
  String indicatorValue;

  PKDiffInfo({this.pkRes, this.indicatorName, this.indicatorValue});

  factory PKDiffInfo.fromJson(Map<String, dynamic> json) {
    return PKDiffInfo(
      pkRes: int.parse(json['pkRes']?.toString() ?? '0') ?? 0,
      indicatorName: json['indicatorName']?.toString() ?? '',
      indicatorValue: json['indicatorValue']?.toString() ?? '',
    );
  }
}

class PKContentInfo {
  String diffName;
  List<PKDiffInfo> diff;

  PKContentInfo({this.diff, this.diffName});

  factory PKContentInfo.fromJson(Map<String, dynamic> json) {
    return PKContentInfo(
      diffName: json['diffName']?.toString() ?? '',
      diff: (json['diff'] as List<dynamic>)
              ?.map((e) => PKDiffInfo.fromJson(e as Map<String, dynamic>))
              ?.toList() ??
          [],
    );
  }
}
