import 'package:wef_network/wef_request.dart';

class HouseKeeperBusinessApi {
  static Future<bool> createActivityXK(
    int decrease,
  ) {
    Map<String, dynamic> params = {
      'decrease': decrease ?? 0,
    };

    return postEApi(
            path: '/gw/api/ai/assistant/activity/poi/act/create/xk',
            params: params,
            isControlShowToast: true)
        .then((response) {
      return response?.code == 0;
    }).catchError((error) {
      return false;
    });
  }

  static Future<bool> createActivityDD(
      String benefitType, double decrease, double price, double discount) {
    Map<String, dynamic> params = {
      'benefitType': benefitType ?? '',
      'decrease': decrease ?? 0,
      'price': price ?? 0,
      'discount': discount ?? 0,
    };

    return postEApi(
            path: '/gw/api/ai/assistant/activity/poi/act/create/dd',
            params: params,
            isControlShowToast: true)
        .then((response) {
      return response?.code == 0;
    }).catchError((error) {
      return false;
    });
  }
}
