import 'dart:collection';
import 'dart:math';

import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/collection_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_tag_info.dart';

class HouseKeeperWmProductTagHelper {
  /// 商品SPU数据是否含有标签数据
  static bool containTagValue(HouseKeeperProductTagInfo productTagInfo) {
    return (productTagInfo != null &&
        !CollectionUtils.isEmpty(productTagInfo.tagKeys) &&
        !CollectionUtils.isEmpty(productTagInfo.tagValues));
  }

  /// 获取标签概况
  static List<String> getTagsPromptValue(
      HouseKeeperProductTagInfo productTagInfo) {
    if (!containTagValue(productTagInfo)) {
      return [];
    }

    List<String> tagList = [];

    for (ProductTagKeyItem keyItem in productTagInfo.tagKeys) {
      List<ProductTagValueItem> tagValues =
          productTagInfo.tagValues[keyItem.tagId.toString()];
      if (!CollectionUtils.isEmpty(tagValues)) {
        String tagInfoStr = tagValues
            .map((ProductTagValueItem tag) {
              return tag.value;
            })
            .toList()
            .join('、');

        tagList.add('${keyItem.tagName}：$tagInfoStr');
      }
    }

    return tagList;
  }

  /// 获取完成百分比
  /// <p>百分比只筛选有标准标签key的数据</p>
  static String getTagsRatioValue(HouseKeeperProductTagInfo productTagInfo) {
    if (!containTagValue(productTagInfo)) {
      return "";
    }
    int totalCount = productTagInfo.tagKeys.length;
    if (totalCount <= 0) {
      return "";
    }
    int finishCount = 0;
    for (ProductTagKeyItem keyItem in productTagInfo.tagKeys) {
      List<ProductTagValueItem> valueItems =
          productTagInfo.tagValues[keyItem.tagId.toString()];
      if (valueItems != null) {
        bool forinContinue = true;
        for (ProductTagValueItem valueItem in valueItems) {
          if (forinContinue) {
            if (valueItem.value.isNotEmpty) {
              // 有一个标签值即可
              finishCount++;
              forinContinue = false;
            }
          }
        }
      }
    }
    return "已填写（$finishCount/$totalCount）";
  }

  /// 处理历史数据的兼容，存在两种结果，此函数将属性平铺的数据，转换为头尾节点的形式
  static void processHistoryData(HouseKeeperProductTagInfo productTagInfo) {
    if (!containTagValue(productTagInfo)) {
      return;
    }

    for (ProductTagKeyItem keyItem in productTagInfo.tagKeys) {
      List<ProductTagValueItem> valueItems =
          productTagInfo.tagValues[keyItem.tagId.toString()];
      // 通过属性项目列表匹配属性列表
      if (!CollectionUtils.isEmpty(valueItems)) {
        List<ProductTagValueItem> leafItems = [];
        // 遍历属性值列表，处理非叶子节点的数据
        for (ProductTagValueItem valueItem in valueItems) {
          if (valueItem.isLeaf) {
            leafItems.add(valueItem);
          } else {
            // 遍历可能存在多个叶子节点
            List<ProductTagValueItem> noneLeafItems = [];
            // 树的层次遍历，最终求出所有的叶子节点
            Queue<ProductTagValueItem> tempQueue = Queue();
            tempQueue.add(valueItem);
            int maxLevel = valueItem.level;
            int deepGuard = 5;

            bool breakFlag = false;
            while (tempQueue.isNotEmpty &&
                maxLevel <= deepGuard &&
                breakFlag == false) {
              ProductTagValueItem item = tempQueue.removeFirst();
              // ValueList构成一个层级关系
              List<ProductTagValueItem> subItems =
                  productTagInfo.tagValues[item.valueId.toString()];
              if (subItems == null) {
                // 这条线路上的相关节点全部删除
                breakFlag = true;
              }
              if (!breakFlag) {
                for (ProductTagValueItem subValueItem in subItems) {
                  if (!subValueItem.isLeaf) {
                    tempQueue.add(subValueItem);
                  } else {
                    noneLeafItems.add(subValueItem);
                  }
                  maxLevel = max(maxLevel, subValueItem.level);
                }
              }
            }
            // 妥了，节点和顶层树都找到了，我们来构建合规数据
            for (var ele in noneLeafItems) {
              ele.tagName = keyItem.tagName;
              ele.tagId = keyItem.tagId;
            }
            leafItems.addAll(noneLeafItems);
          }
        }
        productTagInfo.tagValues[keyItem.tagId.toString()] = leafItems;
      }
    }
  }
}
