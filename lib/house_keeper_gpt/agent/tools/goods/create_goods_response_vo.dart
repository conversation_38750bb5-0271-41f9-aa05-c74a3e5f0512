import 'dart:core';

/// 新建商品意图VO
class CreateGoodsResponseVo {
  factory CreateGoodsResponseVo.fromJson(Map<String, dynamic> json) {
    return CreateGoodsResponseVo(
      spuName: List<String>.from(json['spuName'] ?? <String>[]),
      price: List<num>.from(json['price'] ?? <num>[]),
      relevantSpuName: List<String>.from(json['relevantSpuName'] ?? <String>[]),
      relevantSpuId: List<int>.from(json['relevantSpuId'] ?? <int>[]),
    );
  }

  CreateGoodsResponseVo({
    this.spuName,
    this.price,
    this.relevantSpuName,
    this.relevantSpuId,
  });

  /// 商品名
  List<String> spuName;

  /// 商品价格
  List<num> price;

  /// 相关的商品名。length=1
  List<String> relevantSpuName;

  /// 相关的商品ID。length=1
  List<int> relevantSpuId;
}
