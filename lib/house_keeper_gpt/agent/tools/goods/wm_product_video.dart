class HouseKeeperWmProductVideo {
  HouseKeeperWmProductVideo(
      {this.wmProductVideoId = -1,
      this.wmPoiId = 0,
      this.title = '',
      this.length = 0,
      this.size = 0,
      this.videoUrlMp4 = '',
      this.videoPicUrl = '',
      this.status = 0,
      this.isRelate = 0,
      this.relateProductDesc = '',
      this.isExceedRelateLimit = 0});

  HouseKeeperWmProductVideo.fromJson(Map<String, dynamic> json) {
    wmProductVideoId = json['wmProductVideoId'] ?? -1;
    wmPoiId = json['wmPoiId'] ?? 0;
    title = json['title'] ?? '';
    length = json['length'] ?? 0;
    size = json['size'] != null ? double.parse(json['size'].toString()) : 0;
    videoUrlMp4 = json['videoUrlMp4'] ?? '';
    videoPicUrl = json['videoPicUrl'] ?? '';
    status = json['status'] ?? 0;
    isRelate = json['isRelate'] ?? 0;
    relateProductDesc = json['relateProductDesc'] ?? '';
    isExceedRelateLimit = json['isExceedRelatelimit'] ?? 0;
  }

  int wmProductVideoId;
  int wmPoiId;
  String title;
  int length;
  double size;
  String videoUrlMp4;
  String videoPicUrl;
  int status;
  int isRelate;
  String relateProductDesc;
  int isExceedRelateLimit;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['wmProductVideoId'] = wmProductVideoId ?? -1;
    data['wmPoiId'] = wmPoiId ?? 0;
    data['title'] = title ?? '';
    data['length'] = length ?? 0;
    data['size'] = size ?? 0;
    data['videoUrlMp4'] = videoUrlMp4 ?? '';
    data['videoPicUrl'] = videoPicUrl ?? '';
    data['status'] = status ?? 0;
    data['isRelate'] = isRelate ?? 0;
    data['relateProductDesc'] = relateProductDesc ?? '';
    data['isExceedRelatelimit'] = isExceedRelateLimit ?? '';
    return data;
  }
}
