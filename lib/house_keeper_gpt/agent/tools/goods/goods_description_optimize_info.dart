class GoodsDescriptionOptimizeInfo {
  GoodsDescriptionOptimizeInfo({
    this.recommendCount,
    this.updateTime,
  });

  factory GoodsDescriptionOptimizeInfo.fromJson(Map<String, dynamic> json) =>
      GoodsDescriptionOptimizeInfo(
        recommendCount: json["recommendCount"] ?? 0,
        updateTime: json["updateTime"] ?? '',
      );

  int recommendCount;
  String updateTime; // 更新时间

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      "recommendCount": recommendCount ?? 0,
      "updateTime": updateTime ?? '',
    };
  }
}
