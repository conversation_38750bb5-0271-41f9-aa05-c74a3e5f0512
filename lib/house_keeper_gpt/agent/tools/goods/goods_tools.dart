import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/common/house_keeper_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/goods_valid_args_data.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_spu_vo.dart';
import 'package:wef_network/wef_request.dart';
import 'goods_api.dart';

class HouseKeeperGoodsTools {
  static Map getGoodsArguments(String message) {
    Map arguments = {};
    Map map = jsonDecode(message);
    if (map == null || map is! Map) {
      return arguments;
    }

    if (!map.containsKey('arguments')) {
      return arguments;
    }

    Map args = map['arguments'];
    if (args == null || args is! Map) {
      return arguments;
    }

    return args;
  }

  static List<int> getSpuIds(String message) {
    List<int> spuIds = [];

    Map args = getGoodsArguments(message);

    if (!args.containsKey('spuIds')) {
      return spuIds;
    }

    if (args['spuIds'] != null && args['spuIds'] is List) {
      args['spuIds'].forEach((v) {
        spuIds.add(v);
      });
    }

    return spuIds;
  }

  static String getSpuNames(String message) {
    Map args = getGoodsArguments(message);
    if (!args.containsKey('spuName')) {
      return '';
    }

    List<String> spuName = [];
    if (args['spuName'] != null && args['spuName'] is List) {
      args['spuName'].forEach((v) {
        spuName.add(v);
      });
      String result = spuName.join(", ");
      return result;
    }

    return '';
  }

  /// 是否是全部商品
  static bool isAllGoods(List<int> spuIds) {
    if (ArrayUtil.isEmpty(spuIds)) {
      return false;
    }

    return spuIds.length == 1 && spuIds[0] == -1;
  }

  static String getChatContent(String message) {
    String content = '不好意思，没有找到该商品，请检查下商品哦';
    Map map = jsonDecode(message);
    if (map == null || map is! Map) {
      return content;
    }

    if (!map.containsKey('response')) {
      return content;
    }

    return map['response'];
  }

  static String getChatContentNew(String message) {
    String content = '';
    Map map = jsonDecode(message);
    if (map == null || map is! Map) {
      return content;
    }

    if (!map.containsKey('response')) {
      return content;
    }

    return map['response'];
  }

  static String getContentType(String message) {
    String type = '';
    Map args = getGoodsArguments(message);
    if (!args.containsKey('type')) {
      return type;
    }
    if (args['type'] != null && args['type'] is String) {
      type = args['type'];
    }
    return type;
  }

// 获取生成文本内容
  static List<String> getContents(String message) {
    List<String> content = [];
    Map args = getGoodsArguments(message);
    if (!args.containsKey('content')) {
      return content;
    }

    if (args['content'] != null && args['content'] is List) {
      Set<String> uniqueContentSet = Set.from(args['content']);
      content.addAll(uniqueContentSet);
    }
    return content;
  }

  //设置商品上下架
  static Future<bool> setFoodStatus(
    int sellStatus,
    List<int> spuIds,
  ) {
    return HouseKeeperGoodsApi.updateSellingStatus(sellStatus, spuIds)
        .then((value) {
      return true;
    }).catchError((e) {
      return false;
    });
  }

  static Future<bool> batchModifyMinumPurchase(
    List<int> spuIds,
    int tagId,
    int count,
  ) {
    return HouseKeeperGoodsApi.batchModifyMinumPurchase(spuIds, tagId, count)
        .then((value) {
      return true;
    }).catchError((e) {
      return false;
    });
  }

  static Future<bool> modifyGoodsStock(int skuId, int stock) {
    List list = [
      {
        'skuId': skuId,
        'stock': stock,
        'maxStock': -1,
        'autoRefresh': false,
      }
    ];

    return HouseKeeperGoodsApi.updateStock(jsonEncode(list)).then((value) {
      return value;
    });
  }

  static Future<bool> batchModifyGoodsStock(List<int> spuIds, num stock) {
    Map<String, int> customBatchInfo = {
      'stockType': 0,
      'autoRefresh': 1,
      'stock': stock,
      'maxStock': -1,
    };

    Map<String, dynamic> selectedInfo = {
      'allChecked': 0,
      'spuIds': jsonEncode(spuIds),
    };

    return HouseKeeperGoodsApi.batchUpdateStock(
        selectedSpuInfo: selectedInfo, stockInfo: customBatchInfo.toString());
  }

  static Future<ResponseData> modifyGoodsPrice(int skuId, num price) {
    return HouseKeeperGoodsApi.updateGoodsPrice(skuId: skuId, price: price);
  }

  static Future<HouseKeeperValidArgsData> getModifyGoodsPriceLimit() {
    return HouseKeeperGoodsApi.fetchValidArgs().then((validArgsMap) {
      HouseKeeperValidArgsData data =
          validArgsMap[HouseKeeperValidArgCode.productPrice];
      return data;
    }).catchError((e) {
      return null;
    });
  }

  static Future<bool> saveDescriptionOptimize(
      HouseKeeperWmProductSpuVo spuVo, String description) {
    String path = '/gw/bizproduct/v3/recommend/w/saveSpuDescriptions';
    Map<String, dynamic> params = {
      'descriptionList': jsonEncode([
        {
          "description": description,
          "spuId": spuVo.id,
          "spuName": spuVo.name,
          "recommendList": [description]
        }
      ]),
    };
    return postEApi(
      path: path,
      params: params,
      isControlShowToast: true,
    ).then((response) {
      return response?.code == 0;
    }).catchError((_) {
      return false;
    });
  }

  static Future<bool> replaceGoodsName(
      HouseKeeperWmProductSpuVo spuVo, String newName) {
    Map<String, dynamic> params = {
      'spuInfos': jsonEncode([
        {
          "replacedSpuName": newName,
          "spuId": spuVo.id,
        }
      ])
    };
    return postEApi(
      path: 'gw/bizproduct/v3/recommend/w/replaceSpuNames',
      params: params,
      isControlShowToast: true,
    ).then((response) {
      return response?.code == 0;
    }).catchError((_) {
      return false;
    });
  }

  static Future<bool> copy(String text) {
    Completer<bool> completer = Completer();
    if (StringUtil.isEmpty(text)) {
      completer.complete(false);
      return completer.future;
    }
    HouseKeeperReporter.reportGoodsGrassCopy(val: {'copy': text});
    Clipboard.setData(ClipboardData(text: text));
    MTFToast.showToast(msg: '已复制到剪贴板');
    completer.complete(true);
    return completer.future;
  }
}
