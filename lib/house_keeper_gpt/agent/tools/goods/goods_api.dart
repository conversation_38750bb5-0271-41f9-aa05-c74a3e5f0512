import 'dart:convert';

import 'package:flap/flap.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/goods_description_optimize_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/goods_valid_args_data.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/pic_optimize_info.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/picture_optimize_info.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/recommend_spu_name.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_goods_spu_attr_info.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_category_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_label_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_pic_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_sku_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_spu_attr_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_spu_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_status_info.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_tag_info.dart';
import 'package:wef_network/wef_request.dart';

import 'goods_category_count_info.dart';
import 'goods_description_optimize_info.dart';
import 'goods_shop_assistant_entity.dart';

class PicUrls {
  PicUrls({
    this.picUrl,
    this.picLargeUrl,
    this.originPicPos,
    this.mPicType,
    this.mScore,
    this.detailList,
  });

  PicUrls.fromJson(Map<String, dynamic> dic) {
    picLargeUrl = dic['picLargeUrl'] ?? '';
    picUrl = dic['picUrl'] ?? '';
    originPicPos = dic['originPicPos'] ?? -1;
    mPicType = dic['mPicType'] ?? -1;
    mScore = (dic['mScore'] ?? -8).toDouble();
    mIsLowQuality = dic['mIsLowQuality'] ?? 0;

    List list = dic['detailList'] ?? [];
    detailList = list.map<DetailList>((item) {
      return DetailList.fromJson(item);
    }).toList();
  }

  Map<String, dynamic> toJson() => {
        "picUrl": picUrl,
        "picLargeUrl": picLargeUrl,
        "originPicPos": originPicPos,
        "mPicType": mPicType,
        "mScore": mScore,
        "mIsLowQuality": mIsLowQuality,
        "detailList": detailList?.map((v) => v.toJson())?.toList(),
      };

  String picLargeUrl;
  String picUrl;
  int originPicPos = -1;
  int mPicType = -1;
  double mScore = -8;

  /// 低质量 (1-是；2-否)
  int mIsLowQuality;
  List<DetailList> detailList;

  /// 是否低质图片
  bool get isLowQualityPic => mIsLowQuality == 1;

  String get imageUrl => (picLargeUrl?.isEmpty ?? true) ? picUrl : picLargeUrl;
}

class DetailList {
  DetailList({
    this.type,
    this.typeName,
    this.score,
    this.extra,
    this.picPropaganda,
    this.result,
  });

  DetailList.fromJson(Map<String, dynamic> json) {
    type = json['type'] ?? '';
    typeName = json['typeName'] ?? '';
    score =
        json['score'] != null ? double.parse(json['score'].toString()) : -8.0;
    extra = json['extra'] ?? '';
    picPropaganda = json['picPropaganda'] != null
        ? PicPropaganda.fromJson(json['picPropaganda'])
        : null;
    result = json['result'] != null ? json['result'].toString() : '';
  }

  String type;
  String typeName;
  double score;
  String extra;
  PicPropaganda picPropaganda;
  String result;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    data['typeName'] = typeName;
    data['score'] = score;
    data['extra'] = extra;
    if (picPropaganda != null) {
      data['picPropaganda'] = picPropaganda.toJson();
    }
    data['result'] = result;
    return data;
  }
}

class PicPropaganda {
  PicPropaganda({
    this.type,
    this.typeName,
    this.badPicUrl,
    this.goodPicUrl,
    this.suggestion,
  });

  PicPropaganda.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    typeName = json['typeName'];
    badPicUrl = json['badPicUrl'];
    goodPicUrl = json['goodPicUrl'];
    suggestion = json['suggestion'];
  }

  String type;
  String typeName;
  String badPicUrl;
  String goodPicUrl;
  String suggestion;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    data['typeName'] = typeName;
    data['badPicUrl'] = badPicUrl;
    data['goodPicUrl'] = goodPicUrl;
    data['suggestion'] = suggestion;
    return data;
  }
}

const String kDefaultRecommendUrl =
    'http://p1.meituan.net/tuling/b6ce73ad02219043bafeb0d255d52233786.png';

class HouseKeeperGoodsApi {
  /// 单品/套餐上下架
  static Future<ResponseData> updateSellingStatus(
      int status, List<int> spuIds) {
    Map params = {
      'sellStatus': '$status',
      'spuIds': jsonEncode(spuIds),
    };
    return postEApi(
        path: '/api/product/setSpuSell',
        params: params,
        isControlShowToast: true);
  }

  static Future<bool> setBatchStock(String tagId, int stock) {
    Map params = {
      'allChecked': 1,
      'tagId': tagId,
      'customBatchInfo': {
        'stockType': 0,
        'stock': stock,
        'autoRefresh': 1,
        'maxStock': 10000,
      }.toString(),
    };

    return postEApi(
            path: '/gw/api/product/setStock',
            params: params,
            isControlShowToast: true)
        .then((response) {
      return response?.code == 0;
    }).catchError((error) {});
  }

  // 批量上下架
  static Future<bool> setBatchSellStatus(int tagId, int sellStatus) {
    Map params = {
      'spuIds': '',
      'sellStatus': sellStatus,
      'selectAll': 1,
      'tagId': tagId,
      'excludeSpuIds': jsonEncode([])
    };

    return postEApi(
      path: '/api/product/setSpuSell',
      params: params,
      isControlShowToast: true,
    ).then((response) {
      return response?.code == 0;
    }).catchError((error) {});
  }

  // 获取所有套餐
  static Future<List<int>> getAllTags() {
    Map<String, dynamic> params = {};
    params['sellStatus'] = -1;
    params['inRecycleBin'] = 0;
    params['spuVo'] = null;
    return getEApi(
            path: '/api/product/getAllTag',
            params: params,
            isControlShowToast: true)
        .then((response) {
      List<int> ids = [];
      if (response?.data == null) {
        return ids;
      }
      response.data.forEach((tag) {
        HouseKeeperWmProductCategoryVo tagVo =
            HouseKeeperWmProductCategoryVo.fromJson(tag);
        ids.add(tagVo?.id);
      });
      return ids;
    }).catchError((error) {});
  }

  static Future<List<HouseKeeperWmProductSpuVo>> getSpuVosBySpuId(
      List<int> spuIds) async {
    List<Future<HouseKeeperWmProductSpuVo>> requests =
        List.generate(spuIds.length, (index) => getSpuVoBySpuId(spuIds[index]));
    List<HouseKeeperWmProductSpuVo> responses = await Future.wait(requests);
    return responses;
  }

  /// 通过SpuId查询商品
  static Future<HouseKeeperWmProductSpuVo> getSpuVoBySpuId(int spuId) {
    return postEApi(
        path: '/gw/api/product/getSpu',
        isControlShowToast: true,
        params: {'spuId': spuId, 'newProductPage': 1}).then((response) {
      if (response?.data == null || response?.data is! Map) {
        return null;
      }

      return HouseKeeperWmProductSpuVo.fromJson(response.data);
    }).catchError((e) {
      return null;
    });
  }

  //模糊查询获取第一个商品
  static Future<HouseKeeperWmProductSpuVo> getSpuVO(String keyWord) async {
    return fetchGoodsListBySearch(keyWord).then((value) {
      if (value?.isNotEmpty ?? false) {
        for (HouseKeeperWmProductSpuVo spuVo in value) {
          if (spuVo.name == keyWord) {
            return spuVo;
          }
        }
        return value[0];
      }
    }).catchError((e) {
      return null;
    });
  }

  static Future<List<HouseKeeperWmProductSpuVo>> fetchGoodsListBySearch(
      String spuPrefix,
      {int scenario = 1}) {
    Map<String, dynamic> params = {
      'spuPrefix': spuPrefix,
      'scenario': scenario,
    };
    return getEApi(
      params: params,
      path: '/api/product/searchSpu',
    ).then((response) {
      List<HouseKeeperWmProductSpuVo> searchSpuList =
          <HouseKeeperWmProductSpuVo>[];
      if (response?.data == null ||
          response?.data == '' ||
          (response?.data as List).isEmpty) {
        return searchSpuList;
      }

      response?.data?.forEach((searchVo) {
        if (searchVo != null && searchVo['spuVos'] != null) {
          searchVo['spuVos'].forEach((spuVo) {
            HouseKeeperWmProductSpuVo productSpuVo =
                HouseKeeperWmProductSpuVo.fromJson(spuVo);
            searchSpuList.add(productSpuVo);
          });
        }
      });
      return searchSpuList;
    }).catchError((error) {
      String errString = "网络请求异常，请稍后重试";
      if (error is NetWorkError && error.message != null) {
        errString = error.message;
      }
      MTFToast.showToast(msg: errString);
      return <HouseKeeperWmProductSpuVo>[];
    });
  }

  /// 商品状态，接口文档：https://km.sankuai.com/collabpage/1827509374
  static Future<HouseKeeperProductStatusInfo> fetchProductState() {
    return getEApi(path: '/api/product/online/status').then((response) {
      return HouseKeeperProductStatusInfo.fromJson(response.data);
    }).catchError((error) {});
  }

  static Future batchModifyMinumPurchase(
      List<int> spuIds, int tagId, int count) {
    Map<String, dynamic> customBatchInfo = {
      'minOrderCount': 2,
    };
    Map<String, dynamic> params = {
      "spuIds": jsonEncode(spuIds),
      "tagId": tagId,
      "customBatchInfo": customBatchInfo.toString(),
    };

    return postEApi(path: '/gw/api/product/setMinOrderCount', params: params)
        .then((response) {
      return response?.code == 0;
    });
  }

  //更新库存
  static Future<bool> updateStock(String string) {
    return postEApi(
            params: {'stockInfos': string},
            path: '/api/product/update-stocks-v2',
            isControlShowToast: true)
        .then((response) {
      return response?.code == 0;
    }).catchError((e) {
      return false;
    });
  }

  static Future<bool> batchUpdateStock(
      {Map<String, dynamic> selectedSpuInfo, String stockInfo = ''}) {
    Map<String, dynamic> params = {};
    params.addAll(selectedSpuInfo);
    params["customBatchInfo"] = stockInfo;

    return postEApi(path: '/gw/api/product/setStock', params: params)
        .then((response) {
      return response?.code == 0;
    });
  }

  static Future<ResponseData> modifyGoodsPrice({
    int skuId,
    String price,
  }) async {
    String path = '/gw/bizproduct/v3/priceOptimize/w/savePriceOptimizedProduct';
    Map<String, dynamic> params = {
      'skuId': skuId,
      'priceType': 1, // 1: 修改原价，2:修改折扣价
      'price': price,
    };
    return postEApi(
      path: path,
      params: params,
      isControlShowToast: true,
    ).catchError((e) {
      throw e;
    });
  }

  static Future<ResponseData> updateGoodsPrice({int skuId, num price}) async {
    String path = '/reuse/product/food/w/updatePrice';
    Map<String, dynamic> params = {
      'skuId': skuId,
      'price': price,
    };
    return postEApi(
      path: path,
      params: params,
      isControlShowToast: true,
    ).then((value) {
      return value;
    }).catchError((e) {
      if (e is NetWorkError) {
        return ResponseData(e.code, e.message, e.details);
      }

      return ResponseData(-1, '系统溜号了，稍后再试一下吧～', null);
    });
  }

  // 获取规则原始数据
  static Future<Map<int, HouseKeeperValidArgsData>> fetchValidArgs() {
    return getEApi(path: '/api/product/getValidArgs', params: {})
        .then((response) {
      if (response.data == null || response.data is! List) {
        return {};
      }
      List<Map> validArgsData = List<Map>.from(response.data);

      List<HouseKeeperValidArgsData> validArgs = validArgsData
          .map((e) => HouseKeeperValidArgsData.fromJson(e))
          .toList();

      Map<int, HouseKeeperValidArgsData> validArgsMap = {};
      for (HouseKeeperValidArgsData element in validArgs) {
        validArgsMap[element.code] = element;
      }

      return validArgsMap;
    });
  }

  // 获取商品信息列表
  static Future<List<HouseKeeperWmProductSpuVo>> fetchSpus(List<int> spuIds) {
    Map<String, dynamic> params = {
      'spuIdList': spuIds.join(","),
    };
    return getEApi(path: '/gw/api/product/getSpuBySpuList', params: params)
        .then((response) {
      if (response?.code != 0) {
        return null;
      }
      List<HouseKeeperWmProductSpuVo> spuVoList = <HouseKeeperWmProductSpuVo>[];
      response.data?.forEach((e) {
        HouseKeeperWmProductSpuVo spuVo = HouseKeeperWmProductSpuVo.fromJson(e);
        spuVoList.add(spuVo);
      });
      if (spuVoList.isEmpty) {
        return null;
      }

      return spuVoList;
    }).catchError((error) {});
  }

  static Future<HouseKeeperWmProductSpuVo> fetchSpu(int spuId) {
    Map<String, dynamic> params = {
      'spuId': spuId,
      'newProductPage': 1,
      // 'sceneType': 1, // 编辑页请求时传1。当版本号大于某一版本时，sceneType=1则进行套餐校验，故不传
      // 'isShowNoPriceAttrSku': 1// 是否返回未加价sku的库存打包费。
    };
    return getEApi(path: '/gw/api/product/getSpu', params: params)
        .then((response) {
      if (response.data == null) {
        return null;
      }
      Map<String, dynamic> spuMap = Map<String, dynamic>.from(response.data);
      return HouseKeeperWmProductSpuVo.fromJson(spuMap);
    }).catchError((e) {
      return null;
    });
  }

  static Future<HouseKeeperProductTagInfo> getSpuTagInfo(int spuId) {
    Map<String, dynamic> params = {'spuId': spuId};
    String urlString = '/gw/reuse2/product/r/template';
    return getEApi(path: urlString, params: params).then((response) {
      if (response.data == null || response.data is! Map) {
        return null;
      }
      HouseKeeperProductTagInfo productTagInfo =
          HouseKeeperProductTagInfo.fromJson(response.data);
      return productTagInfo;
    });
  }

  // 获取推荐商品图片
  static Future<String> fetchGoodsRecommendPic(String recommendName) {
    if (StringUtil.isEmpty(recommendName)) {
      return Future.value(kDefaultRecommendUrl);
    }
    return postEApi(
            params: {'recommendName': recommendName},
            path: 'gw/bizproduct/v3/recommend/r/getRecommendFoodPic',
            isControlShowToast: true) // 高频接口，这个接口挂了业务影响也不大，但是Toast会频繁弹，非常恶心
        .then((response) {
      if (StringUtil.isEmpty(response?.data)) {
        return kDefaultRecommendUrl;
      }
      return response.data as String;
    }).catchError((e) {
      return Future.value(kDefaultRecommendUrl);
    });
  }

  // 保存商品
  static Future<ResponseData> saveGoodsPicture(
      HouseKeeperWmProductSpuVo goods, String picUrls) {
    HouseKeeperWmProductPicVo wmProductPicVo;
    if (goods.wmProductPicVos.isEmpty) {
      wmProductPicVo = HouseKeeperWmProductPicVo();
    } else {
      wmProductPicVo = goods.wmProductPicVos[0] ?? {};
    }
    wmProductPicVo.picUrl = picUrls ?? '';
    wmProductPicVo.picLargeUrl = picUrls ?? "";
    wmProductPicVo.picExtend = null;

    List<NewPicOptimizeVo> optimizePicList = [
      NewPicOptimizeVo(
          spuId: goods.id,
          newPicUrl: picUrls ?? '',
          originPicId: wmProductPicVo.id,
          originPicUrl: wmProductPicVo.picUrl,
          newPicSourceType: 2,
          newSource: 7)
    ];
    Map<String, dynamic> params = {
      'optimizePicList':
          jsonEncode(optimizePicList?.map((e) => e.toServerMap())?.toList())
    };
    return postEApi(
      path: '/gw/bizproduct/v3/productAssistant/w/batchSaveOptimizePics',
      params: params,
    );
  }

  // 保存商品
  static Future<ResponseData> saveGoods(
      HouseKeeperWmProductSpuVo goods, int requestType, int costTime) {
    Map<String, String> params = {
      'spuVo': _goodsString(goods),
      'type': requestType.toString(),
      'costTime': costTime.toString(),
      'picType': '-1',
      'picTime': '0',
    };

    if (goods.wmProductSkuVos != null && goods.wmProductSkuVos.isNotEmpty) {
      params['skuCount'] = goods.wmProductSkuVos.length.toString();
    }

    return postEApi(
        path: '/api/product/saveSpu', params: params, isControlShowToast: true);
  }

  // 用来生产保存商品时的传参
  static String _goodsString(HouseKeeperWmProductSpuVo goods) {
    List searchTerms = goods.searchTerms ?? [];

    Map risk = {};
    if (goods.riskManaVo != null) {
      risk = goods.riskManaVo.toJson();
    }

    List<Map> labelValues = [];
    if (goods.labelValues != null) {
      labelValues = goods.labelValues.map<Map>((item) {
        return item.toJson();
      }).toList();
    }

    List<HouseKeeperWmProductPicVo> wmProductPicVos =
        goods.wmProductPicVos ?? [];
    List<Map> picVos = wmProductPicVos.map<Map>((item) {
      return item.toJson();
    }).toList();

    List<HouseKeeperWmProductLabelVo> wmProductLabelVos =
        goods.wmProductLabelVos ?? [];
    List<Map> labelVos = wmProductLabelVos.map<Map>((item) {
      return item.toJson();
    }).toList();

    List<HouseKeeperWmProductSpuAttrVos> wmProductSpuAttrVos =
        goods.wmProductSpuAttrVos ?? [];
    List<Map> spuAttrVos = wmProductSpuAttrVos.map<Map>((item) {
      return item.toJson();
    }).toList();

    List<HouseKeeperWmProductSkuVo> wmProductSkuVos =
        goods.wmProductSkuVos ?? [];
    List<Map> skuVos = wmProductSkuVos.map<Map>((item) {
      Map itemJson = item.toJson();
      itemJson['limitStock'] = goods.stock != -1;
      if (item.unit == (item.weight.toString() + item.weightUint)) {
        itemJson['unit'] = '';
      }
      return itemJson;
    }).toList();

    List<GoodsSpuAttrInfo> wmProductSpuAttrVoV2List =
        goods.wmProductSpuAttrVoV2List ?? [];
    List<Map> spuAttrVoV2List = wmProductSpuAttrVoV2List.map((item) {
      return item.toJson();
    }).toList();

    Map stockAndBoxPriceInfoJson = goods.stockAndBoxPriceInfo.toJson();

    Map<String, dynamic> dic = {
      'id': goods.id ?? 0,
      'name': goods.name ?? '',
      'tagId': goods.tagId ?? 0,
      'tagName': goods.tagName ?? '',
      'secondTagId': goods.secondTagId ?? 0,
      'secondTagName': goods.secondTagName ?? '',
      'level': goods.level ?? 0,
      'price': goods.price ?? 0,
      'discountPrice': goods.discountPrice ?? 0,
      'unit': goods.unit ?? '',
      'minOrderCount': goods.minOrderCount ?? 0,
      'monthSale': goods.monthSale ?? 0,
      'stock': goods.stock ?? 0,
      'allSoldOut': goods.allSoldOut ?? false,
      'someSoldOut': goods.someSoldOut ?? false,
      'needBindTag': goods.needBindTag ?? false,
      'sellStatus': goods.sellStatus ?? 0,
      'offSellType': goods.offSellType ?? 0,
      'shippingTimeX': goods.shippingTimeX ?? '-',
      'wmProductVideoId': goods.wmProductVideoId ?? 0,
      'example': goods.example ?? 1000001,
      'description': goods.description ?? '',
      'wmProductSkuVos': skuVos ?? [],
      'wmProductSpuAttrVos': spuAttrVos ?? [],
      'wmProductLabelVos': labelVos ?? [],
      'searchTerms': searchTerms ?? [],
      'wmProductPicVos': picVos ?? [],
      'riskManaVo': risk ?? {},
      'wmProductSpuAttrVoV2List': spuAttrVoV2List ?? [],
      'stockAndBoxPriceInfo': stockAndBoxPriceInfoJson ?? {},
      'newProductPage': true,
      'onlySellInCombo': goods.onlySellInCombo ?? false,
    };

    // 2: 套餐
    if (goods.isLabelType(2)) {
      dic['labelValues'] = labelValues ?? [];
    }
    if (kFlapMode) {
      Map<String, dynamic> tagInfo = _tagInfoDic(goods.tagInfo);
      dic.addAll(tagInfo);
    } else {
      Map<String, dynamic> tagInfo =
          _tagInfoDic(goods.tagInfo).cast<String, dynamic>();
      dic.addAll(tagInfo);
    }

    return jsonEncode(dic);
  }

  // 用来生产保存商品时的相关传参
  static Map _tagInfoDic(HouseKeeperProductTagInfo tagInfo) {
    Map emptyDic = {};
    if (tagInfo?.categoryInfo == null) {
      return emptyDic;
    }

    Map dic = {};
    if (tagInfo.categoryInfo.id > 0) {
      dic['category_id'] = tagInfo.categoryInfo.id;
    }

    Map<String, List<ProductTagValueItem>> tagValues =
        tagInfo.tagValues ?? emptyDic;
    Map tagInfoValues = tagValues.map((key, values) {
      return MapEntry(
          key,
          values.map((value) {
            return value.toJson();
          }).toList());
    });
    dic['properties_values'] = tagInfoValues;

    List<ProductTagValueItem> customTagValues = tagInfo.customTagValues;
    List<Map> customTags = customTagValues.map<Map>((value) {
      return value.toJson();
    }).toList();
    dic['customPropertiesValues'] = customTags;

    return dic;
  }

  /// 更新商品图片
  static Future<bool> combineGoods(
      {int spuId = 0, String picUrl, int picType, String picExtend}) {
    if (spuId == 0 || spuId == null) {
      return Future.value(false);
    }
    if (picUrl?.isEmpty ?? true) {
      return Future.value(false);
    }
    return postEApi(path: 'api/product/update-spu-pics', params: {
      'spuId': spuId,
      'picType': picType,
      'pics': jsonEncode(['$picUrl']),
      'picExtend': picExtend,
    }).then((data) {
      if (data != null) {
        MTFToast.showToast(msg: '图片更新成功');
        return Future.value(true);
      }
      return Future.value(false);
    });
  }

  static Future<HouseKeeperGoodsShopAssistantEntity> requestOptimization({
    int sellStatus = 0,
    int pageNo = 1,
    int pageSize = 20,
  }) async {
    Map params = {
      'sellStatus': sellStatus,
      'pageNo': pageNo,
      'pageSize': pageSize
    };

    return postEApi(
      path: '/api/productAssistant/needOptimization',
      params: params,
    ).then((response) async {
      if (response?.data == null || response?.data == '') {
        return null;
      }
      return HouseKeeperGoodsShopAssistantEntity.fromJson(response.data);
    });
  }

  static Future<GoodsCategoryCountInfo> fetchGoodsCategory() async {
    return postEApi(path: '/gw/api/product/getCount').then((response) {
      if (response?.data == null || response?.data == '') {
        return null;
      }
      return GoodsCategoryCountInfo.fromJson(response.data);
    });
  }

  // 获取商品待美化主图列表
  static Future<PicOptimizeInfo> fetchPicOptimizeInfo({int pageNum = 1}) {
    Map<String, dynamic> params = {'pageNum': pageNum, 'pageSize': 20};
    return postEApi(
      path: '/gw/bizproduct/v3/productAssistant/r/getOptimizePics',
      params: params,
      isControlShowToast: true,
    ).then((resp) {
      return PicOptimizeInfo.fromMap(resp?.data ?? <String, dynamic>{});
    });
  }

  /// 获取推荐商品描述
  static Future<GoodsDescriptionOptimizeInfo> fetchOptimizeDescription() {
    return postEApi(
      path: '/gw/bizproduct/v3/recommend/r/checkHasRecommendDescription',
    ).then((response) {
      if (response?.data == null || response?.data == '') {
        return GoodsDescriptionOptimizeInfo();
      }
      return GoodsDescriptionOptimizeInfo.fromJson(response.data);
    });
  }

  static Future<List<GoodsDescriptionOptimizeVo>>
      fetchGoodsDescriptionOptimizeList({int queryCount = 20}) {
    String path = '/gw/bizproduct/v3/recommend/r/getSpuDescriptions';
    Map<String, dynamic> params = {
      'queryCount': queryCount,
    };
    return postEApi(
      path: path,
      params: params,
    ).then((response) {
      if (response?.data == null) {
        return <GoodsDescriptionOptimizeVo>[];
      }
      List<GoodsDescriptionOptimizeVo> spuList = [];
      response.data.forEach((v) {
        spuList.add(GoodsDescriptionOptimizeVo.fromJson(v));
      });
      return spuList;
    }).catchError((e) {
      return <GoodsDescriptionOptimizeVo>[];
    });
  }

  // 获取商品名称推荐信息
  static Future<HouseKeeperRecommendSpuNameListInfo> fetchRecommendSpuName() {
    return postEApi(
      path: '/gw/bizproduct/v3/recommend/r/getRecommendSpuNames',
    ).then((resp) {
      return HouseKeeperRecommendSpuNameListInfo.fromMap(
          resp?.data ?? <String, dynamic>{});
    });
  }
}
