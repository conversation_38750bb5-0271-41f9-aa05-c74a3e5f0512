import 'dart:convert';

class HouseKeeperWmProductShippingTime {
  HouseKeeperWmProductShippingTime.from(String timeStr) {
    dynamic timeObject = jsonDecode(timeStr);
    if (timeObject is! List) {
      return;
    }
    List<dynamic> timeSegments = timeObject;
    for (int i = 0; i < timeSegments.length; i++) {
      var dayModel = timeSegments[i];
      if (dayModel.length > 0) {
        saleDaysIndex.add(i);
        // 目前不支持按天设置可售时间段，所以直接取第一个不为空的展示
        if (saleSegments.length <= 0) {
          dayModel.forEach((segmentStr) {
            saleSegments.add(HouseKeeperProductTimeSegment.from(segmentStr));
          });
        }
      }
    }
  }
  HouseKeeperWmProductShippingTime({this.saleDaysIndex, this.saleSegments});

  /// 显示的选中的日期
  String displaySelectedDays() {
    if (saleDaysIndex.length == 7) {
      return '每天';
    } else {
      String dayString = '';
      for (var index in saleDaysIndex) {
        if (dayString == '') {
          dayString = allDays[index];
        } else {
          dayString = dayString + '，' + allDays[index];
        }
      }
      return dayString;
    }
  }

  /// 时间数组转String
  String displayTimeSegmentString() {
    List<String> times = [];
    for (var segment in saleSegments) {
      if (segment != null &&
          segment.originString != null &&
          segment.originString != '') {
        times.add(segment.originString);
      }
    }
    return times.join('，');
  }

  /// 星期数据
  final List<String> allDays = [
    '周一',
    '周二',
    '周三',
    '周四',
    '周五',
    '周六',
    '周日',
  ];

  /// 星期数组下标
  final List<int> allDaysIndex = [0, 1, 2, 3, 4, 5, 6];

  /// 选中的星期_allDays的下标
  List<int> saleDaysIndex = [];

  /// 添加的时间段列表
  List<HouseKeeperProductTimeSegment> saleSegments = [];
}

String timeDigits(int value, int length) {
  String valueStr = value.toString();
  return valueStr.padLeft(length, "0");
}

class HouseKeeperTimeModel {
  HouseKeeperTimeModel(this.hour, this.minute);

  HouseKeeperTimeModel.parse(String timeStr) {
    List<String> valueList = timeStr.split(':');
    if (valueList.length == 2) {
      hour = valueList.first;
      minute = valueList.last;
    } else {
      hour = '00';
      minute = '00';
    }
  }

  String hour;
  String minute;
  int hourValue() {
    if (hour?.isNotEmpty == true) {
      return int.parse(hour);
    }
    return null;
  }

  int minuteValue() {
    if (minute?.isNotEmpty == true) {
      return int.parse(minute);
    }
    return null;
  }
}

/// 商品【售卖或置顶】时间片段
class HouseKeeperProductTimeSegment {
  HouseKeeperProductTimeSegment.from(String segmentString) {
    List<String> startEndTimeList = segmentString.split('-');
    if (startEndTimeList.length == 2) {
      originString = segmentString;
      startTime = HouseKeeperTimeModel.parse(startEndTimeList[0]);
      String endStr = startEndTimeList[1];
      if (endStr == '24:00') {
        endStr = '23:59';
      }
      endTime = HouseKeeperTimeModel.parse(endStr);
    }
  }
  HouseKeeperProductTimeSegment.fromStartEndTimeValues(
      HouseKeeperTimeModel start, HouseKeeperTimeModel end) {
    int startHour = start.hourValue();
    int startMinute = start.minuteValue();
    int endHour = end.hourValue();
    int endMinute = end.minuteValue();

    startTime = HouseKeeperTimeModel(
        timeDigits(startHour, 2), timeDigits(startMinute, 2));
    endTime =
        HouseKeeperTimeModel(timeDigits(endHour, 2), timeDigits(endMinute, 2));

    String segment =
        '${startTime.hour}:${startTime.minute}-${endTime.hour}:${endTime.minute}';

    originString = segment;
  }

  HouseKeeperProductTimeSegment({this.startTime, this.endTime});

  /// 起始时间
  // String startTime;
  HouseKeeperTimeModel startTime;

  /// 结束时间
  HouseKeeperTimeModel endTime;

  /// 原始字符串: 00:00-00:00
  String originString;
}
