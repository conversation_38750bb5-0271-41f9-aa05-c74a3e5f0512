class HouseKeeperProductStatusInfo extends Object {
  const HouseKeeperProductStatusInfo(this.status, this.describe, this.memo);

  static HouseKeeperProductStatusInfo fromJson(
      Map<String, dynamic> parsedJson) {
    return HouseKeeperProductStatusInfo(
        parsedJson['status'], parsedJson['describe'], parsedJson['memo']);
  }

  final String describe;
  final String memo;
  final int status;
}
