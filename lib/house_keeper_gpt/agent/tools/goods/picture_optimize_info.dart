import 'dart:convert';

import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_product_pic_vo.dart';

import 'collection_utils.dart';

class PicOptimizeInfo {
  PicOptimizeInfo({
    this.total,
    this.pageNum,
    this.pageSize,
    this.newPicUpdateTime,
    this.optimizePicList,
  });

  factory PicOptimizeInfo.fromJson(String str) =>
      PicOptimizeInfo.fromMap(json.decode(str));

  factory PicOptimizeInfo.fromMap(Map<String, dynamic> json) {
    List list = json["optimizePicList"] ?? [];
    return PicOptimizeInfo(
      total: json['total'] ?? 0,
      pageNum: json['pageNum'] ?? 0,
      pageSize: json['pageSize'] ?? 0,
      newPicUpdateTime: json["newPicUpdateTime"] ?? 0,
      optimizePicList: list.map((e) => PicOptimizeVo.fromMap(e)).toList(),
    );
  }

  int total = 0;
  int pageNum;
  int pageSize;
  int newPicUpdateTime;
  List<PicOptimizeVo> optimizePicList;

  PicOptimizeVo get firstOptimizePic =>
      (optimizePicList?.isNotEmpty ?? false) ? optimizePicList.first : null;

  Map<String, dynamic> toJson() => {
        "total": total,
        "pageNum": pageNum,
        "pageSize": pageSize,
        "newPicUpdateTime": newPicUpdateTime,
        "optimizePicList": optimizePicList.map((e) => e.toJson()).toList(),
      };
}

class PicOptimizeVo {
  PicOptimizeVo({
    this.spuId,
    this.spuName,
    this.price,
    this.monthSaled,
    this.originPicId,
    this.originPicUrl,
    this.newPicUrls,
    this.newPicSourceType,
    this.specialEffectPic,
    this.originPicProblems,
    this.isNewCombo,
    this.toppingSpu,
  }) {
    if (!CollectionUtils.isEmpty(newPicUrls)) {
      newPicUrl = newPicUrls.first;
    } else {
      newPicUrl = '';
    }
  }

  factory PicOptimizeVo.fromJson(String str) =>
      PicOptimizeVo.fromMap(json.decode(str));

  factory PicOptimizeVo.fromMap(Map<String, dynamic> json) => PicOptimizeVo(
        spuId: json["spuId"],
        spuName: json["spuName"],
        price: json["price"],
        monthSaled: json["monthSaled"],
        originPicId: json["originPicId"],
        originPicUrl: json["originPicUrl"],
        newPicUrls: json["newPicUrls"] == null
            ? []
            : List<String>.from(json['newPicUrls']),
        newPicSourceType: json["newPicSourceType"],
        specialEffectPic: json['specialEffectPic'] == null
            ? {}
            : HouseKeeperWmProductPicVo.fromJson(json["specialEffectPic"]),
        originPicProblems: json["originPicProblems"] == null
            ? []
            : List<String>.from(json['originPicProblems']),
        isNewCombo: json['isNewCombo'] ?? false,
        toppingSpu: json['toppingSpu'] ?? false,
      );

  int spuId;
  String spuName;
  String price;
  int monthSaled;
  num originPicId;
  String originPicUrl;
  List<String> newPicUrls;
  int newPicSourceType;
  HouseKeeperWmProductPicVo specialEffectPic;
  List<String> originPicProblems;
  bool isNewCombo;
  bool toppingSpu;
  String newPicUrl;

  bool checked = false;

  Map<String, dynamic> toJson() => {
        "spuId": spuId,
        "spuName": spuName,
        "price": price,
        "monthSaled": monthSaled,
        "originPicId": originPicId,
        "originPicUrl": originPicUrl,
        "newPicUrls": List.from(newPicUrls.map((x) => x)),
        "newPicSourceType": newPicSourceType,
        "specialEffectPic": specialEffectPic.toJson(),
        "originPicProblems": List.from(originPicProblems.map((x) => x)),
        "isNewCombo": isNewCombo,
        "toppingSpu": toppingSpu,
      };

  Map<String, dynamic> toServerMap() => {
        "spuId": spuId,
        "newPicUrl": newPicUrl,
        "newPicSourceType": newPicSourceType,
        "originPicUrl": originPicUrl,
        "originPicId": originPicId,
        "specialEffectPic": specialEffectPic.toJson(),
      };
}
