class HouseKeeperValidArgsData {
  HouseKeeperValidArgsData(
      {this.regionMin,
      this.code,
      this.type,
      this.tips,
      this.maxLength,
      this.regionMax});

  HouseKeeperValidArgsData.fromJson(Map json) {
    regionMin = json['regionMin'];
    code = json['code'];
    type = json['type'];
    tips = json['tips'];
    maxLength = json['maxLength'];
    regionMax = json['regionMax'];
  }

  num regionMin;
  int code;
  int type;
  String tips;
  int maxLength;
  num regionMax;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['regionMin'] = regionMin;
    data['code'] = code;
    data['type'] = type;
    data['tips'] = tips;
    data['maxLength'] = maxLength;
    data['regionMax'] = regionMax;
    return data;
  }
}

class HouseKeeperValidArgCode {
  static const int unitCount = 9; // 套餐人数
  static const int productPrice = 11; // 商品价格
  static const int ruleCodeBoxPrice = 14; //商品包装费
  static const int productMaxStock = 15; //商品最大库存
  static const int numberLimitForSkusOfFixedCombo = 16; //固定搭配套餐SKU总数量
  static const int numberLimitForGroups = 17; //套餐下分组数量限制
  static const int numberLimitForSingleSkuOfFixedCombo = 18; //固定搭配套餐单一sku数量限制
  static const int numberLimitForSingleSkuOfFixedGroup =
      19; //可选套餐固定分组下单一sku的份量限制
  static const int numberLimitForSkusOfOptionalGroup = 20; //可选套餐可选分组下sku总数量限制
  static const int numberLimitForSkusOfFixedGroup = 21; //可选套餐固定分组下sku总数量限制
  static const int newComboCategoryId = 22; //新套餐分类id
  static const int packingFeePriceLimit = 23; //打包费设置页价格限制(口袋和订单模式使用)
}
