class HouseKeeperWmProductLabelVo {
  HouseKeeperWmProductLabelVo(
      {this.id, this.groupId, this.groupName, this.subAttr});

  HouseKeeperWmProductLabelVo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    groupId = json['groupId'];
    groupName = json['groupName'];
    subAttr = json['subAttr'];
  }
  int id;
  int groupId;
  String groupName;
  String subAttr;
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['groupId'] = groupId;
    data['groupName'] = groupName;
    data['subAttr'] = subAttr;
    return data;
  }
}

class HouseKeeperLabelValues {
  HouseKeeperLabelValues({this.value = '', this.sequence});

  HouseKeeperLabelValues.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    sequence = json['sequence'];
  }
  String value;
  int sequence;
  bool isValid = true; //不超过10个字为有效
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['value'] = value;
    data['sequence'] = sequence;
    return data;
  }
}

enum HouseKeeperWmProductLabel { noSingleDelivery }

class HouseKeeperWmProductLabelHelper {
  static Map<String, int> getLabelMap(HouseKeeperWmProductLabel label) {
    switch (label) {
      case HouseKeeperWmProductLabel.noSingleDelivery:
        return {'groupId': 18, 'labelId': 32};
      default:
        return {'groupId': -1, 'labelId': -1};
    }
  }
}

class HouseKeeperWmProductLabelStatus {
  static const int available = 1;
  static const int unavailable = 0;
}
