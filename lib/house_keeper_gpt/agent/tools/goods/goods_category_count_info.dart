class GoodsCategoryCountInfo {
  int total;
  int suspended;
  int soldOut;
  int noPic;
  int incomplete;
  int inSale;
  int needTag;
  bool showTopGuide;
  int qualityLow;
  int discount;
  int buyGift;
  int noSingleDelivery;

  GoodsCategoryCountInfo({
    this.total,
    this.suspended,
    this.soldOut,
    this.noPic,
    this.incomplete,
    this.inSale,
    this.needTag,
    this.showTopGuide,
    this.qualityLow,
    this.discount,
    this.buyGift,
    this.noSingleDelivery,
  });

  factory GoodsCategoryCountInfo.fromJson(Map<String, dynamic> json) {
    return GoodsCategoryCountInfo(
      total: json['total'],
      suspended: json['suspended'],
      soldOut: json['soldOut'],
      noPic: json['noPic'],
      incomplete: json['incomplete'],
      inSale: json['inSale'],
      needTag: json['needTag'],
      showTopGuide: json['showTopGuide'],
      qualityLow: json['qualityLow'],
      discount: json['discount'],
      buyGift: json['buyGift'],
      noSingleDelivery: json['noSingleDelivery'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'suspended': suspended,
      'soldOut': soldOut,
      'noPic': noPic,
      'incomplete': incomplete,
      'inSale': inSale,
      'needTag': needTag,
      'showTopGuide': showTopGuide,
      'qualityLow': qualityLow,
      'discount': discount,
      'buyGift': buyGift,
      'noSingleDelivery': noSingleDelivery,
    };
  }
}
