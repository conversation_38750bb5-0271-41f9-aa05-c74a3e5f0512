import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';

class HouseKeeperWmProductPicVo {
  HouseKeeperWmProductPicVo({
    this.id = 0,
    this.picLargeUrl = '',
    this.picMaterialId = 0,
    this.isMaster = 2,
    this.picUrl = '',
    this.specialEffectUrl = '',
    this.specialEffectLargeUrl = '',
    this.specialEffectEnable = 0,
    this.content = '',
    this.type = 1,
    this.score = -8.0,
    this.isLowQuality = 0,
    this.picExtend,
  });

  HouseKeeperWmProductPicVo.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    picLargeUrl = json['picLargeUrl'] ?? '';
    picMaterialId = json['picMaterialId'] ?? 0;
    //是主图（ 1-是；2-否）
    isMaster = json['isMaster'] ?? 2;
    picUrl = json['picUrl'] ?? '';
    specialEffectUrl = json['specialEffectUrl'] ?? '';
    specialEffectLargeUrl = json['specialEffectLargeUrl'] ?? '';
    specialEffectEnable = json['specialEffectEnable'] ?? 0;
    content = json['content'] ?? '';
    type = json['type'] ?? 1;
    //美团云图库默认值
    score =
        json['score'] != null ? double.parse(json['score'].toString()) : -8.0;
    isLowQuality = json['isLowQuality'] ?? 0;
    picExtend = json['picExtend'] ?? '';
  }

  int id;
  String picLargeUrl;
  int picMaterialId;
  int isMaster;
  String picUrl;
  String specialEffectUrl;
  String specialEffectLargeUrl;
  // 1-亮点打开 2-关闭 3-腰带打开，亮点功能已经下线
  int specialEffectEnable;
  // 亮点文案
  String content;
  int type;
  double score;
  int isLowQuality;
  String picExtend;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['picLargeUrl'] = picLargeUrl;
    data['picMaterialId'] = picMaterialId;
    data['isMaster'] = isMaster;
    data['picUrl'] = picUrl;
    data['specialEffectUrl'] = specialEffectUrl;
    data['specialEffectLargeUrl'] = specialEffectLargeUrl;
    data['specialEffectEnable'] = specialEffectEnable;
    data['content'] = content;
    data['type'] = type;
    data['score'] = score;
    data['isLowQuality'] = isLowQuality;
    data['picExtend'] = picExtend;

    return data;
  }

  void setIsMaster(bool isMasterPic) {
    isMaster = isMasterPic ? 1 : 2;
  }

  // 是否拥有特效图
  bool hasSpecialEffect() {
    return isHighLightEnable() || isBeltEnable();
  }

  // 是否有亮点
  bool isHighLightEnable() {
    return specialEffectEnable == 1;
  }

  // 是否有腰带
  bool isBeltEnable() {
    return specialEffectEnable == 3;
  }

  // 是首图 isMater == 1； 否则 == 2
  bool isMasterImage() {
    return isMaster == 1;
  }

  bool isLowPic() {
    return isLowQuality == 1;
  }

  // 优先展示小图，优先效果图
  String getDisplaySmallImageUrl() {
    String photoUrl = StringUtil.isNotEmpty(picUrl) ? picUrl : picLargeUrl;

    // 展示时，腰带图和亮点图共用url
    if (hasSpecialEffect()) {
      if (!StringUtil.isEmpty(specialEffectUrl)) {
        photoUrl = specialEffectUrl;
      } else if (!StringUtil.isEmpty(specialEffectLargeUrl)) {
        photoUrl = specialEffectLargeUrl;
      }
    }
    return photoUrl;
  }

  // 优先展示大图，优先效果图
  String getDisplayLargeImageUrl() {
    String photoUrl = StringUtil.isNotEmpty(picLargeUrl) ? picLargeUrl : picUrl;

    // 展示时，腰带图和亮点图共用url
    if (hasSpecialEffect()) {
      if (!StringUtil.isEmpty(specialEffectLargeUrl)) {
        photoUrl = specialEffectLargeUrl;
      } else if (!StringUtil.isEmpty(specialEffectUrl)) {
        photoUrl = specialEffectUrl;
      }
    }
    return photoUrl;
  }

  //判断原图和特效图和传进来的url是否相同，相同代表是原始url
  bool isSameToOriginalPic(String pic, String largePic) {
    if (!StringUtil.isEmpty(pic)) {
      return picUrl == pic || specialEffectUrl == pic;
    } else if (!StringUtil.isEmpty(largePic)) {
      return picLargeUrl == largePic || specialEffectLargeUrl == largePic;
    }

    return false;
  }

  // 移除原有特效信息，比如亮点、腰带
  void clearSpecialEffectInfoIfHas() {
    specialEffectUrl = '';
    specialEffectLargeUrl = '';
    specialEffectEnable = 2;
    content = '';
  }
}
