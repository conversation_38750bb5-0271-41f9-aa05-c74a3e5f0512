class HouseKeeperWmProductLadderBoxPriceVo {
  HouseKeeperWmProductLadderBoxPriceVo(
      {this.status, this.ladderNum, this.ladderPrice});

  HouseKeeperWmProductLadderBoxPriceVo.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    ladderNum = json['ladderNum'];
    ladderPrice = double.parse(json['ladderPrice'].toString());
  }

  int status;
  int ladderNum;
  double ladderPrice;
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['ladderNum'] = ladderNum;
    data['ladderPrice'] = ladderPrice;
    return data;
  }
}
