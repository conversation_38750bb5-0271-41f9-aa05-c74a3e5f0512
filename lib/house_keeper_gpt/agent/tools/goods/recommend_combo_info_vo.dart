import 'dart:convert';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';

class RecommendComboInfoNew {
  RecommendComboInfoNew({
    this.avgPrice,
    this.name,
    this.foodPicUrl,
    this.comboInfoV2,
  });

  RecommendComboInfoNew.fromJson(Map<dynamic, dynamic> json) {
    avgPrice = json['avgPrice']?.toDouble();
    name = json['name'];
    foodPicUrl = json['foodPicUrl'];

    // 解析嵌套的 comboInfoV2 对象
    if (json['comboInfoV2'] != null) {
      comboInfoV2 = RecommendComboInfoV2.fromJson(json['comboInfoV2']);
    }
  }

  double avgPrice;
  RecommendComboInfoV2 comboInfoV2;
  String name;
  String foodPicUrl;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['avgPrice'] = avgPrice;
    data['name'] = name;
    data['foodPicUrl'] = foodPicUrl;

    if (comboInfoV2 != null) {
      data['comboInfoV2'] = comboInfoV2.toJson();
    }

    return data;
  }

  int getSkuIdsNum() {
    // 创建一个 Set 用于存储 skuId，自动去重
    final Set<String> skuIdSet = {};

    // 空值检查
    if (comboInfoV2 == null) {
      return 0;
    }

    // 获取 comboInfoV2 中的 groupContents
    final groupContents = comboInfoV2.groupContents;
    if (groupContents == null || groupContents.isEmpty) {
      return 0;
    }

    // 遍历每个 groupContent
    for (final groupContent in groupContents) {
      // 获取 dishContents
      final dishContents = groupContent.dishContents;
      if (dishContents == null || dishContents.isEmpty) {
        continue;
      }

      // 遍历每个 dishContent，提取 skuId
      for (final dishContent in dishContents) {
        final skuId = dishContent.skuId;
        // 只添加非空的 skuId
        if (skuId != null && skuId.isNotEmpty) {
          skuIdSet.add(skuId);
        }
      }
    }

    // 将 Set 转换为 List 并返回
    return skuIdSet?.toList()?.length ?? 0;
  }
}

class RecommendComboInfoV2 {
  RecommendComboInfoV2({this.newDishNum, this.comboType, this.groupContents});
  RecommendComboInfoV2.fromJson(Map<dynamic, dynamic> json) {
    newDishNum = json['newDishNum'];
    comboType = json['comboType'];
    persons = json['persons'];
    if (json['groupContents'] != null) {
      groupContents = <GroupContents>[];
      json['groupContents'].forEach((v) {
        groupContents.add(GroupContents.fromJson(v));
      });
    }
  }
  double avgPrice;

  int newDishNum;
  int comboType;
  int persons;
  List<GroupContents> groupContents;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['newDishNum'] = newDishNum;
    data['comboType'] = comboType;
    data['persons'] = persons;
    if (groupContents != null) {
      data['groupContents'] = groupContents.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class GroupContents {
  GroupContents(
      {this.groupName,
      this.minChoice,
      this.maxChoice,
      this.isChoice,
      this.dishContents});

  GroupContents.fromJson(Map<dynamic, dynamic> json) {
    groupName = json['groupName'];
    minChoice = json['minChoice'];
    maxChoice = json['maxChoice'];
    isChoice = json['isChoice'];
    if (json['dishContents'] != null) {
      dishContents = <DishContents>[];
      json['dishContents'].forEach((v) {
        DishContents dishContent = DishContents.fromJson(v);
        dishContents.add(dishContent);

        // /// 如果为可选分组，单品份数固定为1
        // if (isOptionGroup) {
        //   dishContent.dishAmount = '1';
        // }
      });
    }
  }

  String groupName;
  int minChoice;
  int maxChoice;
  int isChoice;
  List<DishContents> dishContents;

  // /// 是否固搭分组
  // bool get isFixedGroup => isChoice == ComboGroupType.fixedGroup.index;

  // /// 是否可选分组
  // bool get isOptionGroup => isChoice == ComboGroupType.optionalGroup.index;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['groupName'] = groupName;
    data['minChoice'] = minChoice;
    data['maxChoice'] = maxChoice;
    data['isChoice'] = isChoice;
    if (dishContents != null) {
      data['dishContents'] = dishContents.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DishContents {
  DishContents(
      {this.dishName,
      this.dishPrice,
      this.dishAmount,
      this.skuId,
      this.picUrl,
      this.spuRecommendLabelVOList})
      : isRecommend = StringUtil.isEmpty(skuId);

  DishContents.fromJson(Map<dynamic, dynamic> json) {
    dishName = json['dishName'];
    dishPrice = json['dishPrice'];
    dishAmount = json['dishAmount'] != null
        ? json['dishAmount'].toString()
        : json['dishAmount'];
    skuId = json['skuId'];
    picUrl = json['picUrl'];
    spuRecommendLabelVOList = json['spuRecommendLabelVOList'] == null
        ? []
        : (json['spuRecommendLabelVOList'] as List<dynamic>)
            .map((e) => SpuRecommendLabelVo.fromMap(e))
            .toList();

    isRecommend = StringUtil.isEmpty(skuId);
  }

  String dishName;
  String dishPrice;
  String dishAmount;
  String skuId;
  String picUrl;
  List<SpuRecommendLabelVo> spuRecommendLabelVOList;

  // 附加信息
  // 是否是推荐商品
  bool isRecommend;
  // 信息是否已完善
  bool get hasComplite {
    if (isRecommend) {
      return StringUtil.isNotEmpty(skuId);
    }
    return true;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['dishName'] = dishName;
    data['dishPrice'] = dishPrice;
    data['dishAmount'] = dishAmount;
    data['skuId'] = skuId;
    data['picUrl'] = picUrl;
    data['spuRecommendLabelVOList'] = spuRecommendLabelVOList == null
        ? []
        : spuRecommendLabelVOList.map((x) => x.toMap()).toList();
    return data;
  }
}

class SpuRecommendLabelVo {
  factory SpuRecommendLabelVo.fromMap(Map<String, dynamic> json) =>
      SpuRecommendLabelVo(
        showText: json["showText"] ?? '',
      );

  factory SpuRecommendLabelVo.fromJson(String str) =>
      SpuRecommendLabelVo.fromMap(json.decode(str));

  SpuRecommendLabelVo({this.showText});

  String showText;

  String toJson() => json.encode(toMap());

  Map<String, dynamic> toMap() => {"showText": showText};
}
