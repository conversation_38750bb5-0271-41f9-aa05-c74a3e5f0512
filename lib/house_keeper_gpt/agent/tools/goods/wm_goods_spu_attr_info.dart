import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/food_sale_status.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/wm_stock_box_price_info.dart';

const int standerWeight = -2;

class GoodsSpuAttrInfo {
  GoodsSpuAttrInfo(
      {this.name = '份量', this.nameId = 0, this.spuAttrValueVoList});

  GoodsSpuAttrInfo.fromJson(Map<String, dynamic> json) {
    nameId = json['name_id'];
    name = json['name'];
    List<dynamic> list = json['spuAttrValueVoList'] ?? [];
    spuAttrValueVoList = list.map((item) {
      return GoodsSpuAttrValueVo.fromJson(item);
    }).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name_id'] = nameId ?? 0;
    data['name'] = name ?? '';
    if (spuAttrValueVoList != null) {
      List<Map<String, dynamic>> list = spuAttrValueVoList.map((item) {
        return item.toJson();
      }).toList();
      data['spuAttrValueVoList'] = list;
    }

    return data;
  }

  GoodsSpuAttrValueVo createProp() {
    return GoodsSpuAttrValueVo(name: name, nameId: nameId);
  }

  GoodsSpuAttrInfo copyWith({name, nameId, spuAttrValueVoList}) {
    return GoodsSpuAttrInfo(
      name: name ?? this.name,
      nameId: nameId ?? this.nameId,
      spuAttrValueVoList: spuAttrValueVoList ?? this.spuAttrValueVoList,
    );
  }

  bool hasSpecPropWithPrice() {
    if (ArrayUtil.isEmpty(spuAttrValueVoList)) {
      return false;
    }
    for (final specProp in spuAttrValueVoList) {
      if ((specProp?.price ?? 0) > 0) {
        return true;
      }
    }
    return false;
  }

  num nameId;
  String name;

  bool get isCustom => nameId == 0;
  List<GoodsSpuAttrValueVo> spuAttrValueVoList;
}

class GoodsSpuAttrValueVo {
  GoodsSpuAttrValueVo(
      {this.id = 0,
      this.value = '',
      this.name = '',
      this.valueId = 0,
      this.nameId = 0,
      this.price,
      this.mode = 1,
      this.sellStatus = 0,
      this.unit,
      this.weight,
      this.weightUnit = '克',
      this.extFields});

  GoodsSpuAttrValueVo.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    value = json['value'] ?? '';
    name = json['name'] ?? '';
    price = json['price'];
    mode = json['mode'] ?? 0;
    sellStatus = json['sell_status'] ?? 0;
    valueId = json['value_id'] ?? 0;
    nameId = json['name_id'] ?? 0;
    unit = json['unit'] ?? '';
    if (json['weight'] == 'null') {
      weight = null;
    } else {
      weight = json['weight'];
    }
    weightUnit = json['weightUnit'] ?? '克';
    // 处理历史数据（原weightUnit 为空 则认为是脏数据， 清空unit 和  weight字段）
    if (StringUtil.isEmpty(json['weightUnit'])) {
      unit = '';
      weight = null;
    }
    spuAttrId = json['spu_attr_id'] ?? 0;

    List<dynamic> list = json['extFields'] ?? [];
    extFields = list.map((element) {
      return ExcludeAttr.fromJson(element);
    }).toList();
  }

  num id;
  String value;
  num valueId;
  String name;
  int nameId;
  String unit;
  int weight;
  String weightUnit;
  bool standard;
  num mode;
  num price;
  num sellStatus;

  bool isDeleted = false;

  // 组成 sku 与 spu 的 attr 关联
  num spuAttrId;

  // 互斥属性列表
  List<ExcludeAttr> extFields;

  // 多规格：判断是否可选择
  bool canBeSelect = true;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id ?? 0;
    data['value'] = value ?? '';
    data['name'] = name ?? '';
    data['price'] = price;
    data['mode'] = mode ?? 0;
    data['sell_status'] = sellStatus ?? 0;
    // 自定义属性值id为零，使用时会用时间戳的负数作为id
    num valueIdTmp = valueId;
    if (valueId == null || valueId < 0) {
      valueIdTmp = 0;
    }
    data['value_id'] = valueIdTmp;
    data['name_id'] = nameId ?? 0;
    data['unit'] = '';
    data['weight'] = weight ?? 0;
    data['weightUnit'] = weightUnit ?? '';
    data['spu_attr_id'] = spuAttrId ?? 0;
    return data;
  }

  GoodsSpuAttrValueVo copyWith(
      {value,
      valueId,
      name,
      nameId,
      price,
      mode,
      sellStatus, // @王硕 需要给默认 status
      unit,
      weight,
      weightUnit}) {
    return GoodsSpuAttrValueVo(
      value: value ?? this.value,
      valueId: valueId ?? this.valueId,
      name: name ?? this.name,
      nameId: nameId ?? this.nameId,
      price: price ?? this.price,
      mode: mode ?? this.mode,
      sellStatus: sellStatus ?? this.sellStatus,
      unit: unit ?? this.unit,
      weight: weight ?? this.weight,
      weightUnit: weightUnit ?? this.weightUnit,
    );
  }

  String priceStr() {
    if (price == null || price == 0) {
      return '';
    } else {
      return '￥$price';
    }
  }

  num getPrice() {
    if (price == null) {
      return 0;
    }
    return price;
  }

  String specValue({isShowApproximated = false}) {
    if (name == '份量') {
      return nameStr(isShowApproximated: isShowApproximated);
    } else {
      return value;
    }
  }

  String nameStr({isShowApproximated = false}) {
    String valueStr = value ?? '';

    String approximatedStr = isShowApproximated ? '约' : '';

    String unitStr = '';
    if (weightUnit != null) {
      if (weight == null) {
        unitStr = '${approximatedStr}0$weightUnit';
      } else {
        unitStr = '$approximatedStr$weight$weightUnit';
      }
    }

    String originWeight = '';
    if (weight != null) {
      if (weight == standerWeight) {
        originWeight = weightUnit;
      } else {
        originWeight = unitStr;
      }
    }

    if ((valueStr) == '' || originWeight == '') {
      String result = '$valueStr$originWeight';
      return result;
    } else {
      String result = '$valueStr($originWeight)';
      return result;
    }
  }

  SkuDetail toSkuDetail() {
    String propValue = (name == '份量') ? nameStr() : value;

    SkuDetail skuDetail = SkuDetail(
        nameId: nameId, name: name, value: propValue, valueId: valueId);
    skuDetail.sellStatus = sellStatus;
    return skuDetail;
  }

  // 存在互斥项
  bool existExtFields() {
    return ArrayUtil.isNotEmpty(extFields);
  }

  // 互斥的属性名称集合
  List<String> getExcludeNames() {
    if (existExtFields()) {
      return extFields.map((e) => e.attrName).toList();
    }
    return [];
  }

  @override
  String toString() {
    return 'weight ${this?.value} weight ${this?.weight} price ${this?.price} '
        'delete ${this?.isDeleted} ';
  }

  bool get isSelling => sellStatus == HouseKeeperFoodSellStatus.selling;
}

class ExtFields {
  ExtFields(this.excludeAttr);

  ExtFields.fromJson(Map<String, dynamic> json) {
    List<dynamic> list = json['exclude_attr'] ?? [];

    excludeAttr = list.map((element) {
      return ExcludeAttr.fromJson(element);
    }).toList();
  }

  List<ExcludeAttr> excludeAttr;
}

class ExcludeAttr {
  ExcludeAttr({this.attrName, this.attrValues});

  ExcludeAttr.fromJson(Map<String, dynamic> json) {
    attrName = json['attr_name'] ?? '';
    List<dynamic> list = json['attr_values'] ?? [];
    attrValues = list.map((value) {
      return value.toString();
    }).toList();
  }

  String attrName;
  List<String> attrValues;
}
