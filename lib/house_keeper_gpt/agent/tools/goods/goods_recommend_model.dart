import 'dart:convert';

import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/recommend_combo_info_vo.dart';

class RecSourceReasons {
  RecSourceReasons({
    this.name,
    this.reason,
  });

  String name;
  String reason;

  factory RecSourceReasons.fromJson(Map<String, dynamic> json) =>
      RecSourceReasons(
        name: json["name"],
        reason: json["reason"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "reason": reason,
      };
}

class SingleItemRecommendVo {
  SingleItemRecommendVo({
    this.avgPrice,
    this.localQuery,
    this.name,
    this.ordNum,
    this.recSourceReasons,
    this.foodPicUrl,
  });

  factory SingleItemRecommendVo.fromRawJson(String str) =>
      SingleItemRecommendVo.fromJson(json.decode(str));

  factory SingleItemRecommendVo.fromJson(Map<String, dynamic> json) =>
      SingleItemRecommendVo(
        avgPrice: json["avgPrice"] != null
            ? double.tryParse(json["avgPrice"].toString())
            : null,
        localQuery: json["localQuery"] != null
            ? int.tryParse(json["localQuery"].toString())
            : null,
        name: json["name"],
        ordNum: json["ordNum"],
        recSourceReasons: json["recSourceReasons"] != null
            ? List<RecSourceReasons>.from(json["recSourceReasons"]
                .map((x) => RecSourceReasons.fromJson(x)))
            : [],
        foodPicUrl: json["foodPicUrl"],
      );

  /// 平均售价
  double avgPrice;

  /// 平台月搜索
  int localQuery;

  /// 商品名称
  String name;

  /// 城市月销量
  int ordNum;

  /// 标签
  List<RecSourceReasons> recSourceReasons;

  /// 图片URL
  String foodPicUrl;

  String toRawJson() => json.encode(toJson());

  Map<String, dynamic> toJson() => {
        "avgPrice": avgPrice,
        "localQuery": localQuery,
        "name": name,
        "ordNum": ordNum,
        "recSourceReasons": recSourceReasons != null
            ? List<dynamic>.from(recSourceReasons.map((x) => x.toJson()))
            : [],
        "foodPicUrl": foodPicUrl,
      };
}

class HotSellingVo {
  HotSellingVo({
    this.name,
    this.cnt,
    this.commodityPic,
  });

  String name;
  String cnt;
  String commodityPic;

  factory HotSellingVo.fromJson(Map<String, dynamic> json) => HotSellingVo(
        name: json["name"],
        cnt: json["cnt"],
        commodityPic: json["commodityPic"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "cnt": cnt,
        "commodityPic": commodityPic,
      };
}

class SpuComboPriceModelVo {
  SpuComboPriceModelVo({
    this.totalPrice,
  });

  double totalPrice;

  factory SpuComboPriceModelVo.fromJson(Map<String, dynamic> json) =>
      SpuComboPriceModelVo(
        totalPrice: json["totalPrice"],
      );

  Map<String, dynamic> toJson() => {
        "totalPrice": totalPrice,
      };
}

class RecLabelCodeType {
  static const int storeCombo = 15; // 店内搭配
  static const int groupCombo = 9; // 团购套餐
}

class PackageRecommendVo {
  PackageRecommendVo({
    this.recommendComboInfo,
    this.recommendComboInfoNew,
    this.type,
  });
  RecommendComboInfo recommendComboInfo;
  RecommendComboInfoNew recommendComboInfoNew;
  int type;

  factory PackageRecommendVo.fromJson(Map<String, dynamic> json) {
    if (json["wm_ai_of_recLabelCode"] == 15) {
      return PackageRecommendVo(
        type: RecLabelCodeType.storeCombo,
        recommendComboInfo: RecommendComboInfo.fromJson(json),
      );
    } else if (json["wm_ai_of_recLabelCode"] == 9) {
      return PackageRecommendVo(
        type: RecLabelCodeType.groupCombo,
        recommendComboInfoNew: RecommendComboInfoNew.fromJson(json),
      );
    }
    return null;
  }

  Map<String, dynamic> toJson() => {
        "recommendComboInfo": recommendComboInfo.toJson(),
        "recommendComboInfoNew": recommendComboInfoNew.toJson(),
      };
}

class RecommendComboInfo {
  RecommendComboInfo({
    this.packageFoodName,
    this.spuComboPriceModelVo,
    this.packageFoodPicUrl,
  });

  String packageFoodName;
  SpuComboPriceModelVo spuComboPriceModelVo;
  String packageFoodPicUrl;

  factory RecommendComboInfo.fromJson(Map<String, dynamic> json) =>
      RecommendComboInfo(
        packageFoodName: json["packageFoodName"],
        spuComboPriceModelVo:
            SpuComboPriceModelVo.fromJson(json["spuComboPriceModelVo"]),
        packageFoodPicUrl: json["packageFoodPicUrl"],
      );

  Map<String, dynamic> toJson() => {
        "packageFoodName": packageFoodName,
        "spuComboPriceModelVo": spuComboPriceModelVo.toJson(),
        "packageFoodPicUrl": packageFoodPicUrl,
      };
}
