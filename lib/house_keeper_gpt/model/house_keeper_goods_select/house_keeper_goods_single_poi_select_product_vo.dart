/// 单店已选商品ProductVo
class SinglePoiSelectProductVo {
  SinglePoiSelectProductVo({
    this.spuId,
    this.spuName,
    this.tagId,
    this.tagName,
    this.secondTagId,
    this.minPrice,
    this.maxPrice,
    this.monthSale,
    this.stock,
    this.picture,
    this.refImgUrl,
    this.select,
  });

  SinglePoiSelectProductVo.fromJson(Map<String, dynamic> json) {
    spuId = json['spuId'] ?? 0;
    spuName = json['spuName'] ?? '';
    tagId = json['tagId'] ?? 0;
    tagName = json['tagName'] ?? '';
    secondTagId = json['secondTagId'] ?? 0;
    minPrice = json['minPrice'] ?? '0';
    maxPrice = json['maxPrice'] ?? '0';
    monthSale = json['monthSale'] ?? '0';
    stock = json['minStock'] ?? '0';
    picture = json['picture'] ?? '';
    refImgUrl = json['refImgUrl'] ?? '';
    select = json['selecte'] ?? true;
  }

  SinglePoiSelectProductVo.fromParams(Map<String, dynamic> json) {
    spuId = json['spuId'] ?? 0;
    spuName = json['spuName'] ?? '';
    tagId = json['tagId'] ?? 0;
    tagName = json['tagName'] ?? '';
    secondTagId = json['secondTagId'] ?? 0;
    minPrice = json['minPrice'] ?? '0';
    maxPrice = json['maxPrice'] ?? '0';
    monthSale = json['monthSale'] ?? '0';
    stock = json['stock'] ?? '0';
    picture = json['picture'] ?? '';
    refImgUrl = json['refImgUrl'] ?? '';
    select = json['select'] ?? true;
  }

  int spuId; // 商品id
  String spuName; // 商品名称
  int tagId; // 分组id
  String tagName; // 分组名称
  int secondTagId; // 二级分组名称
  String minPrice; // 商品最低价
  String maxPrice; // 商品最高价
  String monthSale; // 商品月售
  String stock; // 商品库存
  String picture; // 商品图片

  String refImgUrl; // 背景图生成的
  bool select;

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = Map<String, dynamic>();
    data['spuId'] = this.spuId;
    data['spuName'] = this.spuName;
    data['tagId'] = this.tagId;
    data['tagName'] = this.tagName;
    data['secondTagId'] = this.secondTagId;
    data['minPrice'] = this.minPrice;
    data['maxPrice'] = this.maxPrice;
    data['monthSale'] = this.monthSale;
    data['stock'] = this.stock;
    data['picture'] = this.picture;
    data['refImgUrl'] = this.refImgUrl;
    data['select'] = this.select;
    return data;
  }
}
