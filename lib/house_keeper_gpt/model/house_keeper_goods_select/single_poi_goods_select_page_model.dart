import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/common/common_goods_select_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_goods_select/common_goods_select_converter.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/house_keeper_goods_single_poi_select_product_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/single_poi_goods_select_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/goods_group_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/goods_spu_vo.dart';

/// 通用选品页PageModel
class SinglePoiGoodsSelectPageModel {
  SinglePoiGoodsSelectPageModel(this.pageVo);

  // 选品页pageVo
  SinglePoiGoodsSelectPageVo pageVo;

  /// 初始化数据
  void initializeData(Map params) {
    _initParams(params);
    _initPageVo();
    _getGroupListInfo();
    _getGoodsList();
  }

  /// 初始化参数
  void _initParams(Map params) {
    debugPrint(
        'SinglePoiGoodsSelectPageModel [_initParams] params:${jsonEncode(params)}');
    pageVo.sceneType = _getSceneType(params);
    pageVo.subSceneType = _getSubSceneType(params);
    pageVo.minNum = _getMinNum(params);
    pageVo.maxNum = _getMaxNum(params, pageVo.minNum);
    // 初始化已选择的商品信息
    List<SinglePoiSelectProductVo> selectedProductList =
        _getAppSelectedProductList(params);
    pageVo.selectedSpuList = CommonGoodsSelectConverter.convertGoodsSpuList(
      selectedProductList,
      fromPageParams: true,
    );
  }

  int _getSceneType(Map params) {
    if (params == null) {
      return 0;
    }
    return int.tryParse(params['sceneType'] ?? '0') ?? 0;
  }

  int _getSubSceneType(Map params) {
    if (params == null) {
      return null;
    }
    return int.tryParse(params['subSceneType'] ?? '0') ?? 0;
  }

  int _getMinNum(Map params) {
    if (params == null) {
      return 1;
    }
    return int.tryParse(params['minNum'] ?? '1') ?? 1;
  }

  int _getMaxNum(Map params, int minNum) {
    if (params == null) {
      return minNum;
    }
    return int.tryParse(params['maxNum'] ?? '$minNum') ?? minNum;
  }

  /// App端已选商品数据
  List<SinglePoiSelectProductVo> _getAppSelectedProductList(Map params) {
    List<SinglePoiSelectProductVo> selectedProductList = [];
    if (params == null || params['data'] == null) {
      return selectedProductList;
    }
    List productList = jsonDecode(params['data']);
    productList.forEach((product) {
      SinglePoiSelectProductVo info =
          SinglePoiSelectProductVo.fromParams(product);
      selectedProductList.add(info);
    });
    return selectedProductList;
  }

  /// 初始化PageVo数据
  void _initPageVo() {
    pageVo.pageNum = 1;
    pageVo.pageSize = SinglePoiGoodsSelectPageVo.pageSizeDefault;
    pageVo.groupVoList = [];
    pageVo.spuVoList = [];
    pageVo.searchSpuVoList = [];
  }

  /// 初始化已选商品信息
  void initializeSelectedGoods(List<SinglePoiSelectProductVo> productList) {
    if (productList?.isEmpty ?? true) {
      return;
    }
    pageVo.selectedSpuList = CommonGoodsSelectConverter.convertGoodsSpuList(
      productList,
      fromPageParams: true,
    );
    // 初始化商品选中状态
    _initSpuVoSelectStatus(pageVo.selectedSpuList);
    // 初始化分组下商品选中数量
    _initSpuVoSelectCountByGroup();
    pageVo.safeNotifyListeners();
  }

  /// 获取选品规则
  Future<String> getGoodsSelectRules() {
    EasyLoading.show(
      status: '',
      maskType: EasyLoadingMaskType.clear,
    );
    return CommonGoodsSelectApi.getGoodsSelectRules(
      sceneType: pageVo.sceneType,
      subSceneType: pageVo.subSceneType,
    ).then((value) {
      pageVo.selectRules = value ?? '';
      return pageVo.selectRules;
    }).whenComplete(() {
      EasyLoading.dismiss(animation: false);
    });
  }

  /// 获取分组列表信息
  Future _getGroupListInfo() {
    return CommonGoodsSelectApi.getGroupList(
      sceneType: pageVo.sceneType,
      subSceneType: pageVo.subSceneType,
    ).then((value) {
      if (value == null) return;
      pageVo.groupVoList = value;
      // 默认选中第一个分组
      GoodsGroupVo firstGroupVo = value.isEmpty ? null : value.first;
      pageVo.currentSelectedGroupId = firstGroupVo.groupId;
      // 初始化分组下已选中商品数量
      _initSpuVoSelectCountByGroup();
      // 刷新页面
      pageVo.safeNotifyListeners();
    });
  }

  /// 初始化分组下商品选中数量
  void _initSpuVoSelectCountByGroup() {
    groupVoList.forEach((groupVo) {
      int count = selectedSpuList
          .where((spuVo) => spuVo.groupId == groupVo.groupId)
          .length;
      groupVo.setSelectedCount(count);
    });
  }

  /// 获取商品列表
  Future _getGoodsList({int groupId}) {
    EasyLoading.show(
      status: '',
      maskType: EasyLoadingMaskType.clear,
    );
    pageVo.isSpuListRefreshing = true;
    pageVo.pageNum = 1;
    pageVo.hasNextPage = false;
    pageVo.spuVoList = [];
    pageVo.safeNotifyListeners();
    return CommonGoodsSelectApi.getGoodsList(
      sceneType: pageVo.sceneType,
      subSceneType: pageVo.subSceneType,
      groupId: groupId,
      pageNum: pageVo.pageNum,
      pageSize: pageVo.pageSize,
    ).then((value) {
      if (value == null) return;
      pageVo.spuVoList = value.spuList ?? [];
      pageVo.hasNextPage = value.hasNextPage;
      // 初始化已选商品信息
      _initSpuVoSelectStatus(value.spuList);
    }).whenComplete(() {
      EasyLoading.dismiss(animation: false);
      pageVo.isSpuListRefreshing = false;
      pageVo.safeNotifyListeners();
    });
  }

  /// 初始化商品选择状态
  void _initSpuVoSelectStatus(List<GoodsSpuVo> spuList) {
    if (spuList?.isEmpty ?? true) {
      return;
    }
    // 遍历从服务器获取到的商品列表
    spuList.forEach((spuVo) {
      // 检查当前商品是否在已选商品列表中
      GoodsSpuVo selectedSpuVo = selectedSpuList
          .firstWhere((e) => e.spuId == spuVo.spuId, orElse: () => null);
      if (selectedSpuVo == null) {
        return;
      }
      // 当前商品在已选商品列表中，设置为选中状态
      spuVo.checked = true;
      // 如果已选商品列表中的当前商品信息是来自页面参数，则使用服务器的商品数据进行替换，因为后续的操作对象都是服务器返回的商品数据
      if (selectedSpuVo.fromPageParams == true) {
        // 对象替换
        int index = selectedSpuList.indexOf(selectedSpuVo);
        if (index >= 0 && index < selectedSpuList.length) {
          selectedSpuList.removeAt(index);
          selectedSpuList.insert(index, spuVo);
        }
      }
    });
  }

  /// 加载更多商品列表信息
  Future loadMoreGoods() {
    int prePageNum = pageVo.pageNum ?? 0;
    pageVo.pageNum = prePageNum + 1;
    return CommonGoodsSelectApi.getGoodsList(
      sceneType: pageVo.sceneType,
      subSceneType: pageVo.subSceneType,
      groupId: pageVo.currentSelectedGroupId,
      pageNum: pageVo.pageNum,
      pageSize: pageVo.pageSize,
    ).then((value) {
      if (value == null) {
        pageVo.hasNextPage = false;
        return;
      }
      if (pageVo.spuVoList == null) {
        pageVo.spuVoList = [];
      }
      _initSpuVoSelectStatus(value.spuList);
      List<GoodsSpuVo> nextGoodsSpuVoList = value.spuList ?? [];
      pageVo.spuVoList = [...pageVo.spuVoList, ...nextGoodsSpuVoList];
      pageVo.hasNextPage = value.hasNextPage;
    }).whenComplete(() {
      pageVo.safeNotifyListeners();
    });
  }

  /// 设置当前选中的分组
  void setCurrentGroup(GoodsGroupVo groupVo) {
    if (pageVo.currentSelectedGroupId == groupVo.groupId) {
      return;
    }
    pageVo.currentSelectedGroupId = groupVo.groupId;
    _getGoodsList(groupId: groupVo.groupId);
    pageVo.safeNotifyListeners();
  }

  /// 设置商品选中状态
  void setGoodsCheckStatus(GoodsSpuVo spuVo, bool checked) {
    // 检查商品数量是否已超最大值
    if (checked == true && selectedSpuList.length >= pageVo.maxNum) {
      MTFToast.showToast(msg: '最多只能选择${pageVo.maxNum}个商品');
      return;
    }
    spuVo.checked = checked == true;
    if (checked == true) {
      selectedSpuList.add(spuVo);
    } else {
      selectedSpuList.removeWhere((e) => e?.spuId == spuVo.spuId);
    }
    // 更新该分组下选中商品数量
    GoodsGroupVo groupVo = pageVo.groupVoList?.firstWhere(
        (element) => element.groupId == spuVo.groupId,
        orElse: () => null);
    if (groupVo != null) {
      int count = checked == true
          ? (groupVo.selectedCount + 1)
          : (groupVo.selectedCount - 1);
      groupVo.setSelectedCount(count);
    }
    pageVo.safeNotifyListeners();
  }

  /// 搜索商品
  Future<List<GoodsSpuVo>> searchGoods() {
    EasyLoading.show(
      status: '',
      maskType: EasyLoadingMaskType.clear,
    );
    pageVo.isSpuListRefreshing = true;
    pageVo.searchPageNum = 1;
    pageVo.hasNextPage = false;
    pageVo.spuVoList = [];
    pageVo.safeNotifyListeners();
    return CommonGoodsSelectApi.getGoodsList(
      sceneType: pageVo.sceneType,
      subSceneType: pageVo.subSceneType,
      keyWord: pageVo.searchKey,
      pageNum: pageVo.pageNum,
      pageSize: pageVo.pageSize,
    ).then((value) {
      if (value == null) return;
      pageVo.searchSpuVoList = value.spuList ?? [];
      pageVo.searchHasNextPage = value.hasNextPage;
      // 初始化已选商品信息
      _initSpuVoSelectStatus(value.spuList);
    }).whenComplete(() {
      EasyLoading.dismiss(animation: false);
      pageVo.isSpuListRefreshing = false;
      pageVo.safeNotifyListeners();
    });
  }

  /// 搜索更多商品
  Future searchMoreGoods() {
    int prePageNum = pageVo.searchPageNum ?? 0;
    pageVo.searchPageNum = prePageNum + 1;
    return CommonGoodsSelectApi.getGoodsList(
      sceneType: pageVo.sceneType,
      subSceneType: pageVo.subSceneType,
      keyWord: pageVo.searchKey,
      pageNum: pageVo.searchPageNum,
      pageSize: pageVo.pageSize,
    ).then((value) {
      if (value == null) {
        pageVo.searchHasNextPage = false;
        return;
      }
      if (pageVo.searchSpuVoList == null) {
        pageVo.searchSpuVoList = [];
      }
      _initSpuVoSelectStatus(value.spuList);
      List<GoodsSpuVo> nextGoodsSpuVoList = value.spuList ?? [];
      pageVo.searchSpuVoList = [
        ...pageVo.searchSpuVoList,
        ...nextGoodsSpuVoList
      ];
      pageVo.searchHasNextPage = value.hasNextPage;
    }).whenComplete(() {
      pageVo.safeNotifyListeners();
    });
  }

  /// 是否本组商品全被选中
  bool isCurGroupTotalSelected() {
    GoodsGroupVo currentGroup = pageVo.groupVoList.firstWhere(
      (element) => element.groupId == pageVo.currentSelectedGroupId,
      orElse: () => null,
    );
    return currentGroup?.selectedCount == pageVo.spuVoList.length;
  }

  /// 清空选择的商品
  void clearSelectGoods() {
    selectedSpuList?.clear();
    groupVoList?.forEach((element) {
      element.setSelectedCount(0);
    });
    spuVoList?.forEach((element) {
      element.checked = false;
    });
    safeNotifyListeners();
  }

  /// 删除选择的商品
  void deleteSelectGoods(GoodsSpuVo spuVo) {
    selectedSpuList?.removeWhere((e) => e.spuId == spuVo.spuId);
    GoodsGroupVo targetGroupVo = groupVoList
        ?.firstWhere((e) => e.groupId == spuVo.groupId, orElse: () => null);
    if (targetGroupVo != null) {
      int count = targetGroupVo.selectedCount;
      targetGroupVo.setSelectedCount(count - 1);
    }
    GoodsSpuVo targetSpuVo = spuVoList
        ?.firstWhere((e) => e.spuId == spuVo.spuId, orElse: () => null);
    if (targetSpuVo != null) {
      targetSpuVo.checked = false;
    }
    safeNotifyListeners();
  }

  /// 将商品移到首位
  void moveTopSelectGoods(GoodsSpuVo spuVo) {
    selectedSpuList?.removeWhere((e) => e.spuId == spuVo.spuId);
    selectedSpuList?.insert(0, spuVo);
    safeNotifyListeners();
  }

  /// 拖拽重排序
  void dragReorder(int oldIndex, int newIndex) {
    GoodsSpuVo spuVo = selectedSpuList[oldIndex];
    selectedSpuList?.removeWhere((e) => e.spuId == spuVo.spuId);
    selectedSpuList?.insert(newIndex, spuVo);
    safeNotifyListeners();
  }

  /// 校验已选商品数据
  bool checkSelectedProductData() {
    // 检查商品数量是否小于最小值
    if (selectedSpuList.length < pageVo.minNum) {
      MTFToast.showToast(msg: '请至少选择${pageVo.minNum}个商品');
      return false;
    }
    // 检查商品数量是否已超最大值
    if (selectedSpuList.length > pageVo.maxNum) {
      MTFToast.showToast(msg: '最多只能选择${pageVo.maxNum}个商品');
      return false;
    }
    return true;
  }

  /// 通知更新
  void safeNotifyListeners() {
    pageVo?.safeNotifyListeners();
  }

  /// 获取场景类型
  int get sceneType => pageVo.sceneType;

  /// 获取子场景类型
  int get subSceneType => pageVo.subSceneType;

  /// 获取选品数量范围
  String get goodsSelectNumRange {
    if (pageVo.minNum == pageVo.maxNum) {
      return '${pageVo.minNum}';
    } else {
      return '${pageVo.minNum}-${pageVo.maxNum}';
    }
  }

  /// 获取最大可选数量
  int get maxNum => pageVo.maxNum ?? 0;

  /// 获取选品规则
  String get selectRules => pageVo.selectRules;
  bool get showSelectRules => {
        101, // 门头视频
        100, // 图片墙
        102, // 商品橱窗
        106, // 店铺招牌
      }.contains(sceneType);

  /// 是否搜索模式
  bool get isSearchMode => pageVo.searchKey?.isNotEmpty ?? false;

  /// 获取搜搜关键词
  String get searchKey => pageVo.searchKey ?? '';
  set searchKey(String key) {
    pageVo.searchKey = key;
  }

  /// 获取分组列表
  List<GoodsGroupVo> get groupVoList => pageVo.groupVoList ?? [];

  /// 获取商品列表
  List<GoodsSpuVo> get spuVoList => pageVo.spuVoList ?? [];

  /// 获取是否还有下一页
  bool get hasNextPage => pageVo.hasNextPage ?? false;

  /// 获取搜索模式是否还有下一页
  bool get searchHasNextPage => pageVo.searchHasNextPage ?? false;

  /// 获取已选择的商品列表
  List<GoodsSpuVo> get selectedSpuList => pageVo.selectedSpuList ?? [];
}
