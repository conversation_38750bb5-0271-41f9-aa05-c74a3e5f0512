class HouseKeeperRoleVo {
  HouseKeeperRoleVo(
      {this.name,
      this.roleName,
      this.shortName,
      this.work,
      this.desc,
      this.photo,
      this.lastMessage,
      this.date,
      this.isGroup,
      this.isSpeaking,
      this.guideText,
      this.voiceName,
      this.speed,
      this.sendMessage,
      this.guideOperation,
      this.shortcuts});

  factory HouseKeeperRoleVo.fromJson(Map<String, dynamic> json) {
    return HouseKeeperRoleVo(
      name: json['name'],
      roleName: json['roleName'],
      shortName: json['shortName'],
      work: json['work'],
      desc: json['desc'],
      photo: json['photo'],
      lastMessage: json['lastMessage'],
      date: json['date'],
      isGroup: json['isGroup'],
      isSpeaking: json['isSpeaking'],
      guideText: json['guideText'],
      voiceName: json['voiceName'],
      speed: json['speed'],
      sendMessage: json['sendMessage'],
      guideOperation: json['guideOperation'] == null
          ? []
          : List<String>.from(json['guideOperation']),
      shortcuts:
          json['shortcuts'] == null ? [] : List<String>.from(json['shortcuts']),
    );
  }

  String name;
  String roleName;
  String shortName;
  String work;
  String desc;
  String photo;
  String lastMessage;
  String date;
  bool isGroup;
  bool isSpeaking;
  String guideText;
  String voiceName;
  String sendMessage;
  int speed;
  List<String> shortcuts;
  List<String> guideOperation;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['roleName'] = roleName;
    data['work'] = work;
    data['desc'] = desc;
    data['photo'] = photo;
    data['lastMessage'] = lastMessage;
    data['date'] = date;
    data['isGroup'] = isGroup;
    data['isSpeaking'] = isSpeaking;
    data['guideText'] = guideText;
    data['voiceName'] = voiceName;
    data['speed'] = speed;
    data['sendMessage'] = sendMessage;
    data['guideOperation'] = guideOperation;
    data['shortcuts'] = shortcuts;
    return data;
  }
}

enum OperationType {
  link,
  message,
}

class HouseKeeperOperation {
  HouseKeeperOperation(
      {this.operationTitle, this.operationDes, this.operationUrl, this.type});

  factory HouseKeeperOperation.fromJson(Map<String, dynamic> json) {
    return HouseKeeperOperation(
      operationTitle: json['operationTitle'],
      operationUrl: json['operationUrl'],
      type: json['type'],
      operationDes: json['operationDes'],
    );
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['operationTitle'] = operationTitle;
    data['operationUrl'] = operationUrl;
    data['operationDes'] = operationDes;
    data['type'] = type;
  }

  String operationTitle;
  String operationUrl;
  OperationType type;
  String operationDes;
}
