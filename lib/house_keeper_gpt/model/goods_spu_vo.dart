import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/goods_sku_vo.dart';

/// 商品SpuVo
class GoodsSpuVo {
  GoodsSpuVo({
    this.spuId,
    this.spuName,
    this.wmPoiId,
    this.groupId,
    this.groupName,
    this.secondGroupId,
    this.picture,
    this.minPrice,
    this.maxPrice,
    this.minStock,
    this.sellStatus,
    this.monthSale,
    this.skuList,
    this.selectable,
    this.unSelectableReason,
    this.fromPageParams,
    this.checked,
    this.toStock,
    this.toPrice,
    this.toSellStatus,
    this.firstSkuId,
  });

  GoodsSpuVo.fromJson(Map<String, dynamic> json) {
    spuId = json['spuId'] ?? 0;
    spuName = json['spuName'] ?? json['name'] ?? '';
    wmPoiId = json['wmPoiId'] ?? 0;
    groupId = json['tagId'] ?? 0;
    groupName = json['tagName'] ?? '';
    secondGroupId = json['secondTagId'] ?? 0;
    picture = json['picture'] ?? '';
    minPrice = json['minPrice'] ?? '0';
    maxPrice = json['maxPrice'] ?? minPrice;
    minStock = json['minStock'] ?? '0';
    sellStatus = json['sellStatus'] ?? 0;
    monthSale = json['monthSale'] ?? '0';
    skuList = [];
    if (json['skuList'] != null) {
      json['skuList'].forEach((v) {
        skuList.add(GoodsSkuVo.fromJson(v));
      });
    }
    selectable = json['selectable'] ?? true;
    unSelectableReason = json['unselectableReason'] ?? '';

    toStock = json['toStock'] ?? 0;
    toPrice = json['toPrice'] ?? 0.0;
    toSellStatus = json['toSellStatus'] ?? 0;
    firstSkuId = json['firstSkuId'] ?? 0;
  }

  int wmPoiId; // 门店id
  int spuId; // 商品id
  String spuName; // 商品名称
  int groupId; // 分组id
  String groupName; // 分组名称
  int secondGroupId; // 第二分组id
  String picture; // 商品图片
  String minPrice; // 商品最小价格
  String maxPrice; // 商品最大价格
  String minStock; // 库存
  int sellStatus; // 售卖状态
  String monthSale; // 月售
  List<GoodsSkuVo> skuList; // sku列表
  bool selectable; // 是否可选
  String unSelectableReason; // 不可选原因

  // 页面交互需要，记录商品是否来自页面参数，是否被选中
  bool fromPageParams;
  bool checked;

  // 目标改动，非商品信息
  int toStock; // 目标库存
  double toPrice; // 目标价格
  int toSellStatus; // 目标上架状态
  int firstSkuId; //第一个sku的id

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = Map<String, dynamic>();
    data['spuId'] = this.spuId;
    data['spuName'] = this.spuName;
    data['wmPoiId'] = this.wmPoiId;
    data['tagId'] = this.groupId;
    data['tagName'] = this.groupName;
    data['secondTagId'] = this.secondGroupId;
    data['picture'] = this.picture;
    data['minPrice'] = this.minPrice;
    data['maxPrice'] = this.maxPrice;
    data['minStock'] = this.minStock;
    data['sellStatus'] = this.sellStatus;
    data['monthSale'] = this.monthSale;
    if (this.skuList != null) {
      data['skuList'] = this.skuList.map((v) => v.toJson()).toList();
    }
    data['selectable'] = this.selectable;
    data['unselectableReason'] = this.unSelectableReason;
    return data;
  }

  /// 是否多规格商品
  bool get isMultiSku {
    return (skuList?.length ?? 0) > 1;
  }

  /// 获取商品价格
  String get priceString {
    return (minPrice?.isEmpty ?? true) ? '0' : minPrice;
  }

  /// 获取图片
  String get displayImage {
    return (picture?.isEmpty ?? true)
        ? 'http://p1.meituan.net/tuling/b6ce73ad02219043bafeb0d255d52233786.png'
        : picture;
  }

  /// 获取商品库存(-1代表无限)
  String get stockString {
    int stock = int.tryParse(minStock ?? '0') ?? 0;
    return stock > -1 ? '${stock}' : '无限';
  }
}
