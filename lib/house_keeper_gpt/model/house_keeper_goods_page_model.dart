import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/common/image_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/goods/goods_api.dart';
import 'package:waimai_e_horn/waimai_e_horn.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import 'package:waimai_e_perf_opt/waimai_e_perf_opt.dart';
import 'package:wef_network/wef_request.dart';

import 'house_keeper_page_vo.dart';

class HouseKeeperGoodsPageModel {
  static const int aiType = 1 << 4;
  static const String banMoreType = '菜品图参考图';
  WmProductSpuVo spuVo;
  HouseKeeperPageVo pageVo;
  HouseKeeperMessagePageModel model;

  HouseKeeperGoodsPageModel(this.pageVo, this.model);

  /// 解析传入的商品数据
  String genGoodsInfo() {
    if (spuVo == null) {
      return '';
    }
    String name = spuVo?.name;
    String description = spuVo?.description;
    String recommendName = spuVo?.recommendName;
    String goodsInfo =
        '商品信息是：商品名称：$name ,商品描述：$description ,推荐商品名：$recommendName';
    return goodsInfo;
  }

  void fetchCachedGoodsList() {
    WaimaiEHorn.getHornValue('wmb_goods_cache_switch').then((value) {
      if (StringUtil.isEmpty(value)) {
        return;
      }
      try {
        Map<String, dynamic> results = jsonDecode(value);
        if (results['wmb_goods_cache_switch'] == null) {
          return;
        }
        bool _enable = results['wmb_goods_cache_switch'] == true;
        String cacheKey = results['wmb_goods_cache_key'] + 'v2';
        // 缓存可用 且 缓存Key不能为空
        if (_enable && StringUtil.isNotEmpty(cacheKey)) {
          // 初始化缓存对象
          Map map = SingleInstance.getInstance(cacheKey) ?? {};
          List<String> spuNames = map.values
              .expand((element) => element)
              .map((e) => WmProductSpuVo.fromJson(e).name)
              .toList();

          pageVo.associates.addAll(spuNames);
          pageVo.safeNotifyListeners();
        }
      } catch (error) {
        KNB.sendLog(text: "fetchCachedGoodsList, $error");
      }
    });
    // 查询缓存是否可用
  }

  /// 图片已保存到手机
  void savePictureToGallery({String spuName, String url, Function callBack}) {
    String date = DateFormat('yyyy-MM-dd-HH:mm:ss').format(DateTime.now());
    String pictureName = '$date-${jsonEncode(spuName)}.png';
    NetworkAssetBundle(Uri.parse(url)).load(url).then((ByteData data) {
      Uint8List pngBytes = data.buffer.asUint8List();
      WaimaiENativeBusiness.saveImageToGallery(pictureName, pngBytes)
          .then((value) {
        MTFToast.showToast(msg: '图片已保存到手机');
        if (callBack != null) {
          callBack();
        }
      });
    });
  }

  //选择本地图片
  Future<String> addLocalImage() {
    return KNB
        .chooseImage(
      sceneToken: 'dj-5ea52754bff69d34',
      returnType: 'localId',
      count: 1,
      source: '',
    )
        .then((result) {
      if (result['errorCode'] == 543) {
        MTFToast.showToast(msg: '请授予相册和相机权限以便拍照和读取相册');
      }
      var photos = ImageModel.fromJson(result);
      if (photos == null || ArrayUtil.isEmpty(photos.photoInfos)) {
        return Future.value(null);
      }
      if (photos.photoInfos[0].localId != null) {
        return _uploadImages(photos.photoInfos[0].localId).then((picUrl) {
          return picUrl;
        });
      }
      return Future.value(null);
    }).catchError((onError) {
      return Future.value(null);
    });
  }

  //上传图片
  Future<String> _uploadImages(String localId) {
    model.insertMessage(
        HouseKeeperUserMessageVo(type: HouseKeeperMessageType.userPlaceholder));
    return uploadImagesEApi(
      path: '/api/system/picture/upload',
      filePaths: [localId],
      maxHeight: 1024,
      maxWidth: 1024,
    ).then((ResponseData res) {
      model.refreshPlaceholderWidget(
          type: HouseKeeperMessageType.userPlaceholder);
      Map picItem = res.data;
      String picUrl = picItem['url'];
      return picUrl;
    }).catchError((onError) {
      return Future.value(null);
    });
  }
}
