class HouseKeeperCharacterModel {
  final String name;
  final String avatarId;
  final String avatarImage;
  final String styleImage;

  const HouseKeeperCharacterModel({
    this.name,
    this.avatarId,
    this.avatarImage,
    this.styleImage,
  });
}

class CharacterConfig {
  static const String xiaoDaiId = 'house_keeper_xiaodai';
  static const String xiaoMeiId = 'house_keeper_xiaomei';

  static const String defaultAvatarId = xiaoMeiId;
  static const String defaultAvatarName = '小美';

  static const List<HouseKeeperCharacterModel> characters = [
    HouseKeeperCharacterModel(
      name: '小美',
      avatarId: xiaoMeiId,
      avatarImage:
          'https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/undefined/%E5%A5%B3-%E5%B0%8F%E5%9B%BE1743670552512.gif',
      styleImage:
          'http://p0.meituan.net/tuling/07d7fcc5c78796939ee50c8b532329af3247.png',
    ),
    HouseKeeperCharacterModel(
      name: '小袋',
      avatarId: xiaoDaiId,
      avatarImage:
          'https://s3plus.meituan.net/v1/mss_1ada830d56584ddeae1b0899c231c552/goku/undefined/%E7%94%B7-%E5%B0%8F%E5%9B%BE1743670577322.gif',
      styleImage:
          'http://p0.meituan.net/tuling/fb87c1a6beb8bfdbb455e5d1a7a8cc2b4184.png',
    )
  ];
}
