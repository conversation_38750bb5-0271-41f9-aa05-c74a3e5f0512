import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/aot/im_scene/voice_assistant_im_msg_queue.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/common/house_keeper_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/common/log_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/service/house_keeper_sse_client_dio.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_scene_constant.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/voice_asr_callback.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/plugin/voice_assistant_plugin.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/service/house_keeper_llm_api.dart';
import 'package:waimai_e_sound/waimai_e_sound.dart';
import 'package:flutter/services.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';
import 'house_keeper_goods_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/test/custom_block_test.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';

class HouseKeeperMessagePageModel with VoiceASRCallback {
  HouseKeeperMessagePageModel();

  HouseKeeperPageVo pageVo;
  Map params;

  VoiceAssistantASRPlugin asrPlugin = VoiceAssistantASRPlugin();

  WMAgentExecutor agentExecutor = WMAgentExecutor();

  WMLLMContext llMContext = WMLLMContext(); //// 当前问答上下文

  ScrollController scrollController = ScrollController();

  HouseKeeperGoodsPageModel houseKeeperGoodsPageModel;

  bool _isWorking = false; // 是否正在处理消息

  HouseKeeperLLMApi _llmApi;

  bool get isWorking => _isWorking;

  String mode = ''; // 主应用还是财务对账

  // 设置是否为新会话
  void setNewSession(bool value) {
    _llmApi.isNewSession = value;
  }

  String serviceStatus; //已购买，未购买，已过期

  Timer _scrollDebounceTimer;

  void init(HouseKeeperPageVo pageVo, Map params) {
    VoiceAssistantIMMsgManager.setVoiceAssistantProcessing(true);
    this.pageVo = pageVo;
    this.params = params;
    houseKeeperGoodsPageModel = HouseKeeperGoodsPageModel(pageVo, this);
    asrPlugin.asrCallback = this;

    /// 区分不同的来源
    HouseKeeperReporter.reportInitMC();

    _llmApi = HouseKeeperLLMApi(mode: mode);
    setNewSession(true);
  }

  /// 发送纯文本消息
  /// 默认添加到用户消息列表
  void sendPlainMessage(String text) {
    sendStreamMessage(text);
  }

  /// 发送纯文本消息
  /// 主要适用于快捷输入
  /// extra: 额外参数，要体现在Body中
  void sendPlainMessageWithAgent(String text, String subAgentCode) {
    sendStreamMessage(text, subAgentCode: subAgentCode);
  }

  /// 发送卡片消息
  /// cardKey: 卡片key，要体现在Header中
  /// extra: 额外参数，要体现在Body中
  void sendCardMessage(String text, String contextMessage, String cardKey) {
    sendStreamMessage(text,
        contextMessage: contextMessage,
        cardKey: cardKey,
        addUserMessage: false);
  }

  /// 发送流式消息
  void sendStreamMessage(String textMessage,
      {bool addUserMessage = true, // 是否添加用户消息
      String contextMessage, // 实际发送的上下文消息
      String cardKey, // 卡片key，要体现在Header中
      String subAgentCode, // 子代理code，要体现在Header中
      Map<String, dynamic> extra}) {
    if (_isWorking) {
      MTFToast.showToast(msg: "正在处理上一个问题，请稍等！");
      return;
    }

    _isWorking = true;
    pageVo.voiceInputState = VoiceAssistantState.normal;
    pageVo.voiceInput = textMessage;

    // 添加用户消息列表
    if (addUserMessage == true) {
      insertMessage(HouseKeeperUserMessageVo(
          content: textMessage, type: HouseKeeperMessageType.text));
    }

    // 创建广播流控制器，确保多个监听器可以接收相同的事件
    StreamController<SSEDioModel> streamController =
        StreamController<SSEDioModel>.broadcast();

    LogUtils.logWithTimestamp(
        'sendStreamMessage: [insertMessage(HouseKeeperStreamMessageBo)]');

    // 添加助理消息列表
    insertMessage(HouseKeeperStreamMessageVo(
      userMessage: textMessage,
      contextMessage: contextMessage,
      streamController: streamController,
    ));

    // 发送流式请求,本地-测试模拟数据
    if (!kReleaseMode && CustomBlockDebugTestSwitch.testMode) {
      CustomBlockDebugTestSwitch().handleTestMode(
        streamController,
        onComplete: () {
          _replaceStreamMessageWithNormal();
          _isWorking = false;
        },
        onProgress: () => scrollToBottom(),
      );
    } else {
      // 拼装额外的数据,正常流式请求
      _streamMessageAndUpdateUI(contextMessage ?? textMessage, streamController,
          cardKey: cardKey, subAgentCode: subAgentCode, extra: extra);
    }
  }

  /// 根据流式消息更新UI，通过StreamController控制流式消息的更新，
  /// 在onData中将数据添加到缓冲区，在onDone中将缓冲区的内容发送给StreamController，
  /// 在onError中处理错误，在onDone中关闭StreamController。
  Future<void> _streamMessageAndUpdateUI(
    String content,
    StreamController<SSEDioModel> streamController, {
    String cardKey,
    String subAgentCode,
    Map<String, dynamic> extra,
  }) async {
    try {
      _llmApi.streamDataApi(
        input: content,
        cardKey: cardKey,
        subAgentCode: subAgentCode,
        extra: extra,
        onData: (data) {
          if (data is SSEDioModel) {
            if (!streamController.isClosed) {
              streamController.add(data);
              LogUtils.logWithTimestamp('streamDataApi: [onData]${data.data}');
            }
            scrollToBottom();
          }
        },
        onDone: () {
          LogUtils.logWithTimestamp('streamDataApi: [onDone]');
          if (!streamController.isClosed) {
            streamController.close();
          }
          scrollToBottom();
          _isWorking = false;
        },
        onError: (error) {
          LogUtils.logWithTimestamp('streamDataApi: [onError]');
          if (!streamController.isClosed) {
            streamController.addError(error);
            streamController.close();
          }
          _isWorking = false;
        },
      );
    } catch (e) {
      // 捕获异常，关闭 StreamController
      if (!streamController.isClosed) {
        streamController.addError(e);
        streamController.close();
      }
      // // 显示错误消息
      // _showErrorMessage(e.toString());
    }
  }

  // 替换流式消息为普通消息
  void _replaceStreamMessageWithNormal() {
    if (pageVo.messages.isNotEmpty &&
        pageVo.messages.last is HouseKeeperStreamMessageVo) {
      final streamMessage = pageVo.messages.last as HouseKeeperStreamMessageVo;

      // 只需要关闭流控制器，不需要替换消息对象
      if (!streamMessage.streamController.isClosed) {
        streamMessage.streamController.close();
      }
      // 通知UI更新
      pageVo.safeNotifyListeners();
    }
  }

  /// 取消所有正在进行的消息流
  void cancelAllMessages() {
    // 取消最后一条消息的流式传输
    if (ArrayUtil.isNotEmpty(pageVo?.messages)) {
      final lastMessage = pageVo.messages.last;
      if (lastMessage is HouseKeeperStreamMessageVo) {
        // 关闭流控制器
        if (!lastMessage.streamController.isClosed) {
          lastMessage.streamController.close();
        }
        // 取消 LLM API 的流式请求
        _llmApi.cancelStream();
      }
    }

    // 重置上下文
    llMContext.resetContext();

    // 停止TTS播放
    stopTTS();

    // 重置语音助手状态
    VoiceAssistantIMMsgManager.setVoiceAssistantProcessing(false);
    VoiceAssistantIMMsgManager.clear();
  }

  void dispose() {
    // 取消所有正在进行的消息流
    cancelAllMessages();

    // 释放语音识别插件资源
    asrPlugin.stopListening();

    // 释放滚动控制器
    scrollController.dispose();

    // 重置 API 状态并释放资源
    _llmApi.dispose();
    _isWorking = false;

    // 清空消息列表
    if (pageVo?.messages != null) {
      pageVo.messages.clear();
    }

    // 重置语音助手状态
    VoiceAssistantIMMsgManager.setVoiceAssistantProcessing(false);
    VoiceAssistantIMMsgManager.clear();

    // 停止所有TTS播放
    stopTTS();

    // 清空上下文
    if (llMContext != null) {
      llMContext.resetContext();
    }

    // 清空页面数据
    pageVo = null;
    params = null;
    houseKeeperGoodsPageModel = null;
  }

  /// 新增消息，控制刷新
  void insertMessage(HouseKeeperMessage message) {
    // Flap 的保护逻辑，真实蛋疼
    if (message is HouseKeeperStreamMessageVo) {
      message.role = 'ASSISTANT';
    }
    pageVo.messages.add(message);
    pageVo.safeNotifyListeners();

    LogUtils.logWithTimestamp(
        'Message: [insertMessage]${message.role}-${message.content}');

    // 滚动到底部
    scrollToBottom();
  }

  void handleNewSession() {
    // 先取消当前正在进行的消息流
    if (ArrayUtil.isNotEmpty(pageVo?.messages)) {
      final lastMessage = pageVo.messages.last;
      if (lastMessage is HouseKeeperStreamMessageVo) {
        // 关闭流控制器
        if (!lastMessage.streamController.isClosed) {
          lastMessage.streamController.close();
        }
        // 取消 LLM API 的流式请求
        _llmApi.cancelStream();
      }
    }

    // 清空消息列表
    pageVo.messages.clear();

    // 重置状态
    _llmApi.dispose();
    _isWorking = false;

    // 通知UI更新
    pageVo.safeNotifyListeners();
  }

  void scrollToBottom() {
    if (scrollController == null) return;
    if (!scrollController.hasClients) return;
    // 防抖：16ms 内多次调用只执行一次
    if (_scrollDebounceTimer != null) {
      _scrollDebounceTimer.cancel();
    }
    _scrollDebounceTimer = Timer(const Duration(milliseconds: 16), () {
      try {
        // 用户本来就在底部，插入新内容后再滚动到底部
        WidgetsBinding.instance.addPostFrameCallback((_) {
          scrollController.jumpTo(scrollController.position.maxScrollExtent);
        });
      } catch (e) {
        LogUtils.logWithTimestamp('滚动到底部失败: $e');
      }
    });
  }

  /// 修改输入类型
  void changeInputType(HouseKeeperInputType type) {
    HouseKeeperReporter.reportChangeInputTypeClick(type: type);
    pageVo.inputType = type;
    pageVo.safeNotifyListeners();
  }

  void startListening() {
    KNB
        .requestPermission(
            forceJump: true,
            type: 'microphone',
            sceneToken: "dj-ad492866f1fc5a10")
        .then((value) {
      if (value['status'] == 'fail') {
        MTFToast.showToast(msg: '请先开启麦克风权限');
      } else {
        stopTTS();
        Map<String, dynamic> params = {};
        if (pageVo.sourceType == entranceFromPush) {
          params = pageVo?.sceneData?.toJson();
        }
        HouseKeeperReporter.reportClickVoice(val: params);
        pageVo.voiceInputState = VoiceAssistantState.listening;
        pageVo.safeNotifyListeners();
        asrPlugin.startListening();
      }
    });
  }

  void stopListening() {
    asrPlugin.stopListening();
  }

  void handleMuteToggle(bool isMuted) {
    if (isMuted) {
      stopTTS();
    } else {
      playTTS('小袋正在为您服务');
    }
  }

  void handleClickGoods(String data) {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_g5dou9wv_mc',
        val: {"readableContent": data});
    MTFToast.showToast(msg: '感谢您的点赞，您的反馈对我们很重要');
  }

  void handleClickBad(String data) {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_xggi2zx0_mc',
        val: {"readableContent": data});

    MTFToast.showToast(msg: '感谢您的反馈，我们会继续改进');
  }

  void handleCopy(String data) {
    if (data != null && data.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: data));
      MTFToast.showToast(msg: '已复制到剪贴板，$data');
    }
  }

  /// 处理重新生成消息，只会重发有用户自己的消息
  void handleReSend(HouseKeeperStreamMessageVo message) {
    if (message == null || message.userMessage == null) {
      return;
    }

    // 如果消息是卡片消息，则需要重发卡片消息
    sendStreamMessage(message.userMessage);
  }

  /// ------------ ASR ------------
  @override
  void onASRFailed() {
    asrPlugin.stopListening();
    pageVo.voiceInputState = VoiceAssistantState.normal;
    pageVo.safeNotifyListeners();
  }

  @override
  void onASRSuccess(String audioId, String result) {
    VoiceAssistantReporter.reportASRResult(asr: result);
    HouseKeeperReporter.reportASRResult(asr: result);
    if (pageVo.disposed == true) {
      return;
    }
    if (StringUtil.isEmpty(result)) {
      return;
    }
    sendPlainMessage(result);
  }

  @override
  void onASRTempResult(String audioId, String result) {
    pageVo.voiceInput = result;
    pageVo.safeNotifyListeners();
  }

  @override
  void onOvertimeClose() {
    asrPlugin.stopListening();
  }

  @override
  void onVoiceDBSize(double voiceDB) {
    pageVo.dbSize = voiceDB.toInt();
    pageVo.safeNotifyListeners();
  }

  /// ------------ ASR ------------

  void refreshPlaceholderWidget({HouseKeeperMessageType type}) {
    if (ArrayUtil.isNotEmpty(pageVo.messages) &&
        pageVo.messages.last is HouseKeeperStreamMessageVo) {
      List<HouseKeeperMessage> _messages = List.from(pageVo.messages);
      _messages.removeLast();
      pageVo.messages = _messages;
      pageVo.safeNotifyListeners();
    }
  }

  void playTTS(String contentMeg,
      {String soundName, bool changeTTS = true, VoidCallback onComplete}) {
    WaimaiESoundPlugin.startPlayTtsSound(
        soundName ?? SoundName.voiceAssistantTTSSoundWithCallback, contentMeg,
        playCount: 1, useEmbed: true, flag: true);

    if (onComplete != null) {
      WaimaiESoundPlugin().setCompleteHandlerListener(
          soundName ?? SoundName.voiceAssistantTTSSoundWithCallback,
          onComplete);
    }
  }

  void stopTTS() {
    WaimaiESoundPlugin.stopSoundPlay(
        soundName: SoundName.voiceAssistantTTSSoundWithCallback);
  }
}
