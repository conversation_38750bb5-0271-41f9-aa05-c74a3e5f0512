import 'package:flap/flap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/utils/avatar_url_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_character_model.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

// 常量定义
class _Constants {
  static const double containerWidth = 360.0;
  static const double buttonWidth = 180.0;
  static const double buttonHeight = 50.0;
  static const double buttonBorderRadius = 25.0;

  static const List<Color> gradientColors = [
    Color(0xFF63EDFD),
    Color(0xFF2A61C1),
    Color(0xFF8542ED),
  ];

  static const List<double> gradientStops = [0.0, 0.5, 1.0];
}

@Flap('house_keeper_gpt')
@MTFRoute('house_keeper_avatar_selection')
class HouseKeeperAvatarSelectionPage extends StatefulWidget {
  const HouseKeeperAvatarSelectionPage({Key key, this.params, this.pageName})
      : super(key: key);

  final Map<dynamic, dynamic> params;

  final String pageName;

  @override
  State<HouseKeeperAvatarSelectionPage> createState() =>
      _HouseKeeperAvatarSelectionPageState();
}

class _HouseKeeperAvatarSelectionPageState
    extends State<HouseKeeperAvatarSelectionPage> {
  final PageController _pageController = PageController(
    initialPage: 0,
    viewportFraction: 0.7,
  );

  int _currentPage = 0;
  double _pageValue = 0.0;
  final TextEditingController _nicknameController = TextEditingController();
  String _avatarId = '';
  bool _isInited = false;

  // 图片资源常量
  static const String _backgroundImg =
      "http://p0.meituan.net/tuling/c23ca4f8ab0626d7c350f0f129407b16293792.png";
  static const String _titleImg =
      "http://p0.meituan.net/tuling/d68cce109d8d9e5b661247866a2e69bd9842.png";

  @override
  void initState() {
    super.initState();
    FlutterLx.moudleView(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_3x81rtym_mv');
    _pageController.addListener(() {
      setState(() {
        _pageValue = _pageController.page ?? 0;
      });
    });
    _initializeWithParams();
  }

  Future<void> _initializeWithParams() async {
    try {
      final results = await Future.wait([
        HouseKeeperSPUtils.getAvatarName(),
        HouseKeeperSPUtils.getAvatarId(),
      ]);

      setState(() {
        _avatarId = results[1];
        _isInited = true;

        // 如果有已保存的角色ID，找到对应的索引并滚动到该位置
        if (_avatarId.isNotEmpty) {
          final index = CharacterConfig.characters
              .indexWhere((character) => character.avatarId == _avatarId);
          if (index != -1) {
            _currentPage = index;
            // 获取该角色对应的名字
            HouseKeeperSPUtils.getCharacterName(_avatarId).then((name) {
              if (name.isNotEmpty) {
                _nicknameController.text = name;
              } else {
                _nicknameController.text =
                    CharacterConfig.characters[index].name;
              }
            });
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _pageController.jumpToPage(index);
            });
          }
        }
      });
    } catch (error) {
      debugPrint('初始化失败: $error');
      setState(() => _isInited = true);
    }
  }

  @override
  void dispose() {
    _pageController.removeListener(() {
      setState(() {
        _pageValue = _pageController.page ?? 0;
      });
    });
    _pageController.dispose();
    _nicknameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black.withOpacity(0.85),
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AdvancedNetworkImage(
              _backgroundImg,
              useDiskCache: true,
            ),
            fit: BoxFit.contain,
            alignment: Alignment.topCenter,
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          body: SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: _buildPageView(),
                ),
                _buildPageIndicator(),
                _buildNicknameInput(),
                _buildCreateButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            alignment: Alignment.center,
            child: Image(
              image: AdvancedNetworkImage(
                _titleImg,
                useDiskCache: true,
              ),
              height: 64,
              fit: BoxFit.contain,
            ),
          ),
          Positioned(
            right: 0,
            child: IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: _handleClose,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageView() {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: _handlePageChanged,
      itemCount: CharacterConfig.characters.length,
      itemBuilder: (context, index) {
        final character = CharacterConfig.characters[index];

        // 计算每个卡片的缩放值
        double diff = (index.toDouble() - _pageValue).abs();
        double scale = 1.0 - (0.15 * diff); // 0.15 是缩放因子，可以调整
        scale = scale.clamp(0.85, 1.0); // 限制缩放范围

        return Transform(
          transform: Matrix4.identity()..scale(scale),
          alignment: Alignment.center,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 10.0),
            child: GestureDetector(
              onTap: () => _handleCharacterSelect(character),
              child: _buildCharacterBackground(character),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        CharacterConfig.characters.length,
        (index) {
          // 计算当前滑动进度与索引的差值
          double diff = (index.toDouble() - _pageValue).abs();
          // 根据差值计算宽度，让过渡更平滑
          double width = 6.0;
          if (diff < 1.0) {
            width = 6.0 + (1.0 - diff) * 12.0; // 从6逐渐变化到18
          }

          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 4.0),
            width: width,
            height: 6.0,
            decoration: BoxDecoration(
              color: diff < 0.5 ? Colors.white : Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(4.0),
            ),
          );
        },
      ),
    );
  }

  Widget _buildNicknameInput() {
    // 计算当前角色名称
    String currentName = '';
    if (_pageValue.floor() >= 0 &&
        _pageValue.floor() < CharacterConfig.characters.length) {
      final nextName = CharacterConfig.characters[_pageValue.floor()].name;
      if (_pageValue.ceil() < CharacterConfig.characters.length) {
        final nextCharacter = CharacterConfig.characters[_pageValue.ceil()];
        // 根据滑动进度计算名称过渡
        double progress = _pageValue - _pageValue.floor().toDouble();
        if (progress > 0.0 && nextCharacter != null) {
          currentName = nextName;
        } else {
          currentName = nextName;
        }
      } else {
        currentName = nextName;
      }
    }

    return Container(
      padding: const EdgeInsets.all(24.0),
      width: _Constants.containerWidth,
      child: TextField(
        controller: _nicknameController,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.right,
        cursorColor: Colors.white,
        decoration: InputDecoration(
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 8, vertical: 14),
          border: InputBorder.none,
          enabledBorder: const UnderlineInputBorder(
            borderSide: BorderSide(color: Color(0xFFD8D8D8)),
          ),
          focusedBorder: const UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.white),
          ),
          hintText: currentName,
          hintStyle: TextStyle(
            color: Colors.white.withOpacity(0.3),
            fontSize: 20,
          ),
          prefixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 16),
              const Icon(Icons.edit, color: Colors.white54, size: 20),
              const SizedBox(width: 8),
              Text(
                '昵称',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.5),
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              const SizedBox(width: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCreateButton() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 24,
        right: 24,
        bottom: 32,
      ),
      child: GestureDetector(
        onTap: _handleCreate,
        child: Container(
          width: _Constants.buttonWidth,
          height: _Constants.buttonHeight,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(_Constants.buttonBorderRadius),
            gradient: const LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: _Constants.gradientColors,
              stops: _Constants.gradientStops,
            ),
          ),
          alignment: Alignment.center,
          child: const Text(
            '创建',
            style: TextStyle(
              fontSize: 18,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCharacterBackground(HouseKeeperCharacterModel character) {
    return Container(
      width: double.infinity,
      child: Stack(
        children: [
          Center(
            child: Container(
              margin: const EdgeInsets.only(top: 50),
              width: 240,
              height: 320 * 0.85,
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: const LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: _Constants.gradientColors,
                  stops: _Constants.gradientStops,
                ),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: Colors.black,
                ),
                child: Column(
                  children: [
                    const Spacer(flex: 2), // 增加上部分空间
                    Container(
                      padding: const EdgeInsets.only(bottom: 20), // 添加底部内边距
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            '对话风格',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Image(
                            image: AdvancedNetworkImage(
                              character.styleImage,
                              useDiskCache: true,
                            ),
                            height: 26,
                            fit: BoxFit.contain,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 220,
            left: 0,
            right: 0,
            child: Container(
              width: double.infinity,
              alignment: Alignment.center,
              child: Image(
                image: AdvancedNetworkImage(
                  character.avatarImage,
                  useDiskCache: true,
                ),
                height: 200,
                width: 200,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handlePageChanged(int index) {
    if (!_isInited) return;

    final selectedCharacter = CharacterConfig.characters[index];
    setState(() {
      _currentPage = index;
      _avatarId = selectedCharacter.avatarId;

      // 获取该角色已保存的名字
      HouseKeeperSPUtils.getCharacterName(selectedCharacter.avatarId)
          .then((name) {
        if (name.isNotEmpty) {
          _nicknameController.text = name;
        } else {
          _nicknameController.text = selectedCharacter.name;
        }
      });
    });
  }

  void _handleCreate() {
    if (!_isInited) return;
    final selectedCharacter = CharacterConfig.characters[_currentPage];
    final nickname = _nicknameController.text.trim();
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_fc6doqil_mc',
        val: {"nickname": nickname, "role": selectedCharacter.name});
    // 验证数据
    if (selectedCharacter == null) {
      debugPrint('错误：未选择角色');
      return;
    }
    if (nickname.isEmpty) {
      debugPrint('错误：昵称不能为空');
      return;
    }

    // 保存数据：角色ID和对应的名字
    HouseKeeperSPUtils.setAvatarId(selectedCharacter.avatarId);
    HouseKeeperSPUtils.setCharacterName(selectedCharacter.avatarId, nickname);
    RouteUtils.close(context);
  }

  void _handleClose() {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_mvo29dx2_mc');
    RouteUtils.close(context);
  }

  void _handleCharacterSelect(HouseKeeperCharacterModel character) {
    final index = CharacterConfig.characters.indexOf(character);
    if (index != -1 && index != _currentPage) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
      );
    }
  }
}
