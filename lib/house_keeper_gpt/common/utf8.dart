// 将 UTF-8 验证逻辑抽取为单独的类
import 'dart:typed_data';

class Utf8Validator {
  static bool isValidUtf8(Uint8List bytes, int start) {
    int byte = bytes[start];
    if ((byte & 0x80) == 0) {
      // 单字节字符 (0xxxxxxx)
      return true;
    } else if ((byte & 0xE0) == 0xC0) {
      // 双字节字符 (110xxxxx 10xxxxxx)
      return start + 1 < bytes.length && (bytes[start + 1] & 0xC0) == 0x80;
    } else if ((byte & 0xF0) == 0xE0) {
      // 三字节字符 (1110xxxx 10xxxxxx 10xxxxxx)
      return start + 2 < bytes.length &&
          (bytes[start + 1] & 0xC0) == 0x80 &&
          (bytes[start + 2] & 0xC0) == 0x80;
    } else if ((byte & 0xF8) == 0xF0) {
      // 四字节字符 (11110xxx 10xxxxxx 10xxxxxx 10xxxxxx)
      return start + 3 < bytes.length &&
          (bytes[start + 1] & 0xC0) == 0x80 &&
          (bytes[start + 2] & 0xC0) == 0x80 &&
          (bytes[start + 3] & 0xC0) == 0x80;
    }
    return false;
  }

  static int extractValidUtf8Bytes(Uint8List bytes) {
    int i = 0;
    while (i < bytes.length) {
      if (!isValidUtf8(bytes, i)) {
        return i;
      }
      int byte = bytes[i];
      if ((byte & 0x80) == 0) {
        i += 1;
      } else if ((byte & 0xE0) == 0xC0) {
        i += 2;
      } else if ((byte & 0xF0) == 0xE0) {
        i += 3;
      } else if ((byte & 0xF8) == 0xF0) {
        i += 4;
      }
    }
    return bytes.length;
  }
}
