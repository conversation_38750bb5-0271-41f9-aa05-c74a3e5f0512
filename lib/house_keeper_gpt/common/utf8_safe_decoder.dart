import 'dart:convert';
import 'dart:typed_data';

class Utf8SafeDecoder {
  final List<int> _buffer = [];
  static const _utf8Decoder = Utf8Decoder(allowMalformed: false);
  String lastResult = '';

  String addChunk(Uint8List chunk) {
    _buffer.addAll(chunk);
    return _tryDecode();
  }

  String finalize() {
    final result = _forceDecode();
    _buffer.clear();
    return result;
  }

  String _tryDecode() {
    if (_buffer.isEmpty) return null;
    try {
      final bufferCopy = Uint8List.fromList(_buffer);
      final result = _utf8Decoder.convert(bufferCopy);
      _buffer.clear();
      lastResult = result;
      return result;
    } on FormatException catch (e) {
      // final invalidSequenceLength = _handleDecodeError(e.offset ?? 0);
      // _buffer.removeRange(0, invalidSequenceLength);
      return lastResult;
    }
  }

  String _forceDecode() {
    final bufferCopy = Uint8List.fromList(_buffer);
    try {
      return _utf8Decoder.convert(bufferCopy);
    } on FormatException {
      return _replaceInvalidBytes(bufferCopy);
    }
  }

  int _handleDecodeError(int errorOffset) {
    // 鎵惧埌鏈€鍚庝竴涓湁鏁堝瓧绗︾殑杈圭晫
    int lastValid = 0;
    for (int i = errorOffset - 1; i >= 0; i--) {
      if (_isStartByte(_buffer[i])) {
        lastValid = i;
        break;
      }
    }
    return lastValid;
  }

  bool _isStartByte(int byte) => (byte & 0xC0) != 0x80;

  String _replaceInvalidBytes(Uint8List bytes) {
    final result = StringBuffer();
    int i = 0;

    while (i < bytes.length) {
      final byte = bytes[i];

      if (byte <= 0x7F) {
        result.writeCharCode(byte);
        i++;
      } else if ((byte & 0xE0) == 0xC0) {
        if (i + 1 < bytes.length) {
          result.writeCharCode(((byte & 0x1F) << 6) | (bytes[i + 1] & 0x3F));
          i += 2;
        } else {
          result.write('\uFFFD');
          i++;
        }
      } else if ((byte & 0xF0) == 0xE0) {
        if (i + 2 < bytes.length) {
          result.writeCharCode(((byte & 0x0F) << 12) |
              ((bytes[i + 1] & 0x3F) << 6) |
              (bytes[i + 2] & 0x3F));
          i += 3;
        } else {
          result.write('\uFFFD');
          i++;
        }
      } else if ((byte & 0xF8) == 0xF0) {
        if (i + 3 < bytes.length) {
          result.writeCharCode(((byte & 0x07) << 18) |
              ((bytes[i + 1] & 0x3F) << 12) |
              ((bytes[i + 2] & 0x3F) << 6) |
              (bytes[i + 3] & 0x3F));
          i += 4;
        } else {
          result.write('\uFFFD');
          i++;
        }
      } else {
        result.write('\uFFFD');
        i++;
      }
    }

    return result.toString();
  }
}
