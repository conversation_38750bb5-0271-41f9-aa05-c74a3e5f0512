import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/goods_group_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/goods_info_to.dart';
import 'package:wef_network/wef_request.dart';

/// 通用选品页api
class CommonGoodsSelectApi {
  /// 获取选品规则
  static Future<String> getGoodsSelectRules({
    int sceneType,
    int subSceneType,
  }) {
    Map<String, dynamic> params = {
      'sceneType': sceneType,
      'subSceneType': subSceneType,
    };
    return getEApi(
      path: '/gw/product/selection/v1/r/getSelectionRules',
      params: params,
      isControlShowToast: false,
    ).then((response) {
      if (response?.data == null || response.data is! List) {
        return '';
      }
      if (ArrayUtil.isEmpty(response.data)) {
        return '';
      }
      List contents = List.from(response.data);
      if (contents.first is! Map) {
        return '';
      }
      Map<String, dynamic> dataMap = Map.from(contents.first);
      return dataMap['content']?.toString() ?? '';
    }).catchError((e) {
      KNB.sendLog(
          text: 'CommonGoodsSelectApi [getGoodsSelectRules]: ${e.toString()}');
    });
  }

  /// 获取商品分组列表
  static Future<List<GoodsGroupVo>> getGroupList({
    int sceneType,
    int subSceneType,
  }) {
    Map<String, dynamic> params = {
      'sceneType': sceneType,
      'subSceneType': subSceneType,
    };
    return getEApi(
      path: '/gw/product/selection/v1/r/getGroupItems',
      params: params,
    ).then((response) {
      List<GoodsGroupVo> groupList = <GoodsGroupVo>[];
      if (response?.data == null || response.data is! List) {
        return groupList;
      }
      if (ArrayUtil.isEmpty(response.data)) {
        return groupList;
      }
      List contents = List.from(response.data);
      if (contents.first is! Map) {
        return groupList;
      }
      Map<String, dynamic> dataMap = Map.from(contents.first);
      dataMap['groupList']?.forEach((value) {
        GoodsGroupVo groupVo = GoodsGroupVo.fromJson(value);
        groupList.add(groupVo);
      });
      return groupList;
    }).catchError((e) {
      KNB.sendLog(text: 'CommonGoodsSelectApi [getGroupList]: ${e.toString()}');
    });
  }

  /// 获取商品列表
  static Future<GoodsInfoTo> getGoodsList({
    int sceneType,
    int subSceneType,
    int groupId,
    int pageNum,
    int pageSize,
    String keyWord,
    String groupOptions,
  }) {
    Map<String, dynamic> params = {
      'sceneType': sceneType,
      'subSceneType': subSceneType,
      'tagId': groupId ?? -99,
      'pageNum': pageNum ?? 1,
      'pageSize': pageSize ?? 20,
      'keyWord': keyWord,
      'groupOptions': groupOptions,
    };
    return getEApi(
      path: '/gw/product/selection/v1/r/queryProductWithPage',
      params: params,
    ).then((response) {
      GoodsInfoTo goodsInfo = GoodsInfoTo();
      if (response?.data is! Map) {
        return goodsInfo;
      }
      return GoodsInfoTo.fromJson(response.data);
    }).catchError((e) {
      KNB.sendLog(text: 'CommonGoodsSelectApi [getGoodsList]: ${e.toString()}');
    });
  }
}
