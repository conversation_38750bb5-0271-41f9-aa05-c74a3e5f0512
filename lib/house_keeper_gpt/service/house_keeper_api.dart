import 'package:flutter/foundation.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/config/prompt_config.dart';
import 'package:wef_network/wef_request.dart';

/// 提示词配置服务类
class HouseKeeperAPI {
  /// 获取常见问题配置
  static Future<PromptConfigData> fetchFrequentQuestions() async {
    return getEApi(
      path: '/proxy-gw/api/ai/assistant/master/frequent/questions',
      isControlShowToast: false,
    ).then((response) {
      if (response?.data == null || response.data is! List) {
        debugPrint('API响应数据格式错误: ${response?.data}');
        return null;
      }
      return parseConfig(response.data as List);
    }).catchError((e) {
      debugPrint('获取配置数据失败: $e');
      MTFToast.showToast(msg: '网络请求异常，请稍后重试');
      return null;
    });
  }

  /// 解析欢迎页配置
  static Future<PageConfig> parseWelcomeConfig(
      Map<String, dynamic> data) async {
    try {
      if (data == null || !data.containsKey('welcome')) {
        return null;
      }

      final welcomeData = data['welcome'];

      // 创建欢迎页配置
      final Map<String, List<PromptConfigItem>> sections = {};

      // 解析各行配置
      if (welcomeData.containsKey('line1')) {
        sections['line1'] = _parsePromptItems(welcomeData['line1']);
      }

      if (welcomeData.containsKey('line2')) {
        sections['line2'] = _parsePromptItems(welcomeData['line2']);
      }

      if (welcomeData.containsKey('line3')) {
        sections['line3'] = _parsePromptItems(welcomeData['line3']);
      }

      return PageConfig(
        page: 'welcome',
        sections: sections,
      );
    } catch (e) {
      debugPrint('解析欢迎页配置异常: $e');
      return null;
    }
  }

  /// 解析消息页配置
  static Future<PageConfig> parseMessageConfig(
      Map<String, dynamic> data) async {
    try {
      if (data == null || !data.containsKey('message')) {
        return null;
      }

      final messageData = data['message'];

      // 创建消息页配置
      final Map<String, List<PromptConfigItem>> sections = {};

      // 解析默认提示词
      if (messageData.containsKey('default')) {
        sections['default'] = _parsePromptItems(messageData['default']);
      }

      // 解析展开提示词
      if (messageData.containsKey('expand')) {
        sections['expand'] = _parsePromptItems(messageData['expand']);
      }

      return PageConfig(
        page: 'message',
        sections: sections,
      );
    } catch (e) {
      debugPrint('解析消息页配置异常: $e');
      return null;
    }
  }

  /// 解析提示词项列表
  static List<PromptConfigItem> _parsePromptItems(List<dynamic> items) {
    if (items == null) return [];

    return items
        .map((item) => PromptConfigItem.fromJson(item))
        .where((item) => item != null)
        .toList();
  }

  /// 解析配置数据，包含 welcome 和 message 配置
  static Future<PromptConfigData> parseConfig(List<dynamic> dataList) async {
    if (dataList == null || dataList.isEmpty) {
      return null;
    }

    try {
      PageConfig welcomeConfig;
      PageConfig messageConfig;

      // 遍历配置列表
      for (var pageData in dataList) {
        if (pageData is! Map<String, dynamic>) continue;

        String page = pageData['page'];

        switch (page) {
          case 'welcome':
            // 解析欢迎页配置
            final Map<String, List<PromptConfigItem>> welcomeSections = {};

            // 解析三行配置
            for (int i = 1; i <= 3; i++) {
              final String key = 'line$i';
              if (pageData.containsKey(key) && pageData[key] is List) {
                welcomeSections[key] = _parsePromptItems(pageData[key]);
              }
            }

            welcomeConfig = PageConfig(
              page: 'welcome',
              sections: welcomeSections,
            );
            break;

          case 'message':
            // 解析消息页配置
            final Map<String, List<PromptConfigItem>> messageSections = {};

            // 解析默认提示词
            if (pageData.containsKey('defaults')) {
              messageSections['defaults'] =
                  _parsePromptItems(pageData['defaults']);
            }

            // 解析展开提示词
            if (pageData.containsKey('expands')) {
              messageSections['expands'] =
                  _parsePromptItems(pageData['expands']);
            }

            messageConfig = PageConfig(
              page: 'message',
              sections: messageSections,
            );
            break;
        }
      }

      // 创建并返回配置数据
      return PromptConfigData(
        welcomeConfig: welcomeConfig,
        messageConfig: messageConfig,
      );
    } catch (e) {
      debugPrint('解析配置数据失败: $e');
      return null;
    }
  }
}
