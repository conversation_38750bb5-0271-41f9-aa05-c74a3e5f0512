import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/base_block.dart';

class GuessQuestion {
  final String text;
  final String link;
  GuessQuestion({this.text, this.link});
}

/// MarkdownParser 解析逻辑说明：
///
/// 1. 解析顺序：
///    - 先全局查找所有完整的 <think>...</think> 区块，原样作为 ThinkBlock 保留。
///    - <think>...</think> 之外的内容，按顺序依次解析 step、custom、guess、markdown。
///
/// 2. 区块类型处理：
///    - <step ...></step>：解析为 StepBlock，isComplete=true。
///    - :::type ... :::：解析为 CustomBlock，isComplete=true。
///    - <Guess>...</Guess>：解析为 GuessBlock，isComplete=true。
///    - 其它内容解析为 MarkdownBlock。
///
/// 3. 不完整区块处理：
///    - 不完整 <think>：如遇 <think> 未闭合，剩余内容整体作为 ThinkBlock，isComplete=false。
///    - 不完整 <step ...>：如遇 <step ...> 未闭合，剩余内容整体作为 StepBlock，isComplete=false。
///    - 不完整 :::type ...：如遇 ::: 未闭合，剩余内容整体作为 CustomBlock，isComplete=false。
///    - 不完整 <Guess>：如遇 <Guess> 未闭合，遇到换行才算一项，最后一项丢弃，isComplete=false。
///
/// 4. 任何区块内的内容不会被再次分块（如 <think> 内部的 step/custom/guess 标签会被原样保留）。
///
/// 5. 解析结果顺序与原始文本顺序一致，保证流式和完整场景下的健壮性。
class MarkdownParser {
  static const String thinkTag = '<think>';
  static const String thinkTagEnd = '</think>';
  static const String stepsTag = '<steps>';
  static const String stepsTagEnd = '</steps>';
  static const String guessTag = '<Guess>';
  static const String guessTagEnd = '</Guess>';
  static const String customTag = ':::';

  /// 预处理 Markdown 数据，优先解析思考（<think>）和步骤（<steps>）区块，再对正文内容分块解析。
  static List<ContentBlock> preprocessData(String data) {
    if (data.isEmpty) return [];
    final List<ContentBlock> blocks = [];
    int pos = 0;
    // 1. 先全局查找所有完整 <think> 区块，保证 <think> 内部内容不会被误分割
    final thinkPattern = RegExp(r'(<think>[\s\S]*?<\/think>)',
        multiLine: true, caseSensitive: false);
    final thinkMatches = thinkPattern.allMatches(data).toList();
    for (final thinkMatch in thinkMatches) {
      // 1.1 处理 <think> 之前的内容，递归分块
      if (thinkMatch.start > pos) {
        final before = data.substring(pos, thinkMatch.start);
        blocks.addAll(_preprocessNonThink(before));
      }
      // 1.2 处理完整 <think> 区块，原样保留
      final thinkStr = thinkMatch.group(0) ?? '';
      final thinkContent = thinkStr.substring(7, thinkStr.length - 8);
      blocks.add(ThinkBlock(content: thinkContent, isComplete: true));
      pos = thinkMatch.end;
    }
    // 2. 处理最后剩余内容（可能有不完整 <think> 或其它区块）
    if (pos < data.length) {
      final rest = data.substring(pos);
      final thinkStart = rest.indexOf('<think>');
      if (thinkStart != -1) {
        // 2.1 剩余内容有未闭合 <think>，先处理前面的内容，再整体作为不完整 ThinkBlock
        if (thinkStart > 0) {
          final before = rest.substring(0, thinkStart);
          blocks.addAll(_preprocessNonThink(before));
        }
        final thinkContent =
            rest.substring(thinkStart + 7); // 7 = '<think>'.length
        blocks.add(ThinkBlock(content: thinkContent, isComplete: false));
      } else {
        // 2.2 没有 <think>，正常处理剩余内容
        blocks.addAll(_preprocessNonThink(rest));
      }
    }
    return blocks;
  }

  // 只处理非think区块，按顺序分割 step/custom/guess/markdown
  static List<ContentBlock> _preprocessNonThink(String data) {
    final List<ContentBlock> blocks = [];
    int lastEnd = 0;
    // 主正则：全局匹配 step、custom、guess 三类区块，剩余内容为 markdown
    final pattern = RegExp(
      r'(<step\s+[^>]*>[\s\S]*?<\/step>)|(:::\S*\s*\n?[\s\S]*?:::)|(<Guess>[\s\S]*?<\/Guess>)',
      multiLine: true,
      caseSensitive: false,
    );
    final matches = pattern.allMatches(data);
    for (final match in matches) {
      // 匹配区块前的内容，作为 MarkdownBlock
      if (match.start > lastEnd) {
        final before = data.substring(lastEnd, match.start);
        if (before.trim().isNotEmpty) {
          blocks.add(MarkdownBlock(content: before.trim()));
        }
      }
      final matchedStr = match.group(0) ?? '';
      // step 匹配
      if (matchedStr.startsWith('<step')) {
        final stepBlock = parseStepBlock(matchedStr);
        if (stepBlock != null) blocks.add(stepBlock);
        // custom block 匹配
      } else if (matchedStr.startsWith(':::')) {
        final customBlockReg =
            RegExp(r'^:::(\S*)\s*\n?([\s\S]*?):::', multiLine: true);
        final m = customBlockReg.firstMatch(matchedStr);
        if (m != null) {
          final blockType = m.group(1)?.trim() ?? '';
          final customContent = m.group(2)?.trim() ?? '';
          if (blockType.isNotEmpty || customContent.isNotEmpty) {
            blocks.add(CustomBlock(
              blockType: blockType,
              content: customContent,
              isComplete: true,
            ));
          }
        }
        // guess 匹配
      } else if (matchedStr.startsWith('<Guess>')) {
        final guessContent = matchedStr.substring(7, matchedStr.length - 8);
        final questions = _parseGuessQuestions(guessContent);
        blocks.add(GuessBlock(questions: questions, isComplete: true));
      }
      lastEnd = match.end;
    }
    if (lastEnd < data.length) {
      final after = data.substring(lastEnd);
      // 优先检查未闭合的 <Guess>，保证流式场景下 guess 能被识别
      final guessStart = after.indexOf('<Guess>');
      if (guessStart != -1) {
        if (guessStart > 0) {
          final before = after.substring(0, guessStart);
          if (before.trim().isNotEmpty) {
            blocks.add(MarkdownBlock(content: before.trim()));
          }
        }
        final guessContent =
            after.substring(guessStart + 7); // 7 = '<Guess>'.length
        final questions = _parseGuessQuestions(guessContent);
        blocks.add(GuessBlock(questions: questions, isComplete: false));
      } else {
        // 检查未闭合的 :::，保证流式场景下 custom block 能被识别
        final customStart = after.indexOf(':::');
        if (customStart != -1) {
          if (customStart > 0) {
            final before = after.substring(0, customStart);
            if (before.trim().isNotEmpty) {
              blocks.add(MarkdownBlock(content: before.trim()));
            }
          }
          // 解析未闭合 custom block，isComplete=false
          final typeMatch = RegExp(r'^:::(\S*)\s*\n?')
              .firstMatch(after.substring(customStart));
          final blockType = typeMatch?.group(1)?.trim() ?? '';
          final contentStart = customStart + (typeMatch?.end ?? 3);
          final customContent = after.substring(contentStart).trim();
          if (blockType.isNotEmpty || customContent.isNotEmpty) {
            blocks.add(CustomBlock(
              blockType: blockType,
              content: customContent,
              isComplete: false,
            ));
          }
        } else {
          // 再检查未闭合的 <step，保证流式场景下 step 能被识别
          final stepStart = after.indexOf('<step');
          if (stepStart != -1) {
            if (stepStart > 0) {
              final before = after.substring(0, stepStart);
              if (before.trim().isNotEmpty) {
                blocks.add(MarkdownBlock(content: before.trim()));
              }
            }
            // 解析未闭合 step，生成 StepBlock，isComplete=false
            final stepContent = after.substring(stepStart);
            final stepBlock = parseIncompleteStepBlock(stepContent);
            if (stepBlock != null) {
              blocks.add(stepBlock);
            }
          } else if (after.trim().isNotEmpty) {
            blocks.add(MarkdownBlock(content: after.trim()));
          }
        }
      }
    }
    return blocks;
  }

  /// 解析 Guess 问题列表
  static List<GuessQuestion> _parseGuessQuestions(String content) {
    final lines = content.split('\n');
    if (lines.isEmpty) return [];

    // 判断是否为未闭合 <Guess>，即没有 </Guess> 结尾
    final isIncomplete = !content.trim().endsWith('</Guess>');

    // 未闭合 <Guess> 时，最后一个片段（未以\n结尾的内容）丢弃
    // 例如 <Guess>\nQ1\nQ2\nAfter 只保留 Q1、Q2，丢弃 After
    final items = isIncomplete ? lines.sublist(0, lines.length - 1) : lines;

    return items.where((q) => q.trim().isNotEmpty).map((line) {
      final cleaned = line.replaceFirst(RegExp(r'^(\d+\.|[-*])\s*'), '');
      final regex = RegExp(r'\[(.*?)\]\((.*?)\)');
      final match = regex.firstMatch(cleaned);
      if (match != null) {
        return GuessQuestion(
          text: match.group(1) ?? '',
          link: match.group(2) ?? '',
        );
      }
      return GuessQuestion(text: cleaned, link: '');
    }).toList();
  }

  /// 解析单个<step>为StepBlock
  static StepBlock parseStepBlock(String data) {
    final stepReg =
        RegExp(r'<step\s+([^>]*)>([\s\S]*?)<\/step>', multiLine: true);
    final match = stepReg.firstMatch(data);
    if (match == null) return null;
    final attrs = match.group(1) ?? '';
    final content = match.group(2) ?? '';
    final nameReg = RegExp(r'name\s*=\s*"([^"]*)"');
    final statusReg = RegExp(r'status\s*=\s*"([^"]*)"');
    final keyReg = RegExp(r'key\s*=\s*"([^"]*)"');
    String name = '';
    String status = '';
    String key = '';
    final nameMatch = nameReg.firstMatch(attrs);
    if (nameMatch != null) {
      name = nameMatch.group(1) ?? '';
    }
    final statusMatch = statusReg.firstMatch(attrs);
    if (statusMatch != null) {
      status = statusMatch.group(1) ?? '';
    }
    final keyMatch = keyReg.firstMatch(attrs);
    if (keyMatch != null) {
      key = keyMatch.group(1) ?? '';
    }
    return StepBlock(
      key: key,
      name: name,
      status: status,
      content: content,
      isComplete: true,
    );
  }

  /// 解析未闭合 <step ... 的 StepBlock，isComplete=false
  static StepBlock parseIncompleteStepBlock(String data) {
    final stepReg = RegExp(r'<step\s+([^>]*)>([\s\S]*)$', multiLine: true);
    final match = stepReg.firstMatch(data);
    if (match == null) return null;
    final attrs = match.group(1) ?? '';
    final content = match.group(2) ?? '';
    final nameReg = RegExp(r'name\s*=\s*"([^"]*)"');
    final statusReg = RegExp(r'status\s*=\s*"([^"]*)"');
    final keyReg = RegExp(r'key\s*=\s*"([^"]*)"');
    String name = '';
    String status = '';
    String key = '';
    final nameMatch = nameReg.firstMatch(attrs);
    if (nameMatch != null) {
      name = nameMatch.group(1) ?? '';
    }
    final statusMatch = statusReg.firstMatch(attrs);
    if (statusMatch != null) {
      status = statusMatch.group(1) ?? '';
    }
    final keyMatch = keyReg.firstMatch(attrs);
    if (keyMatch != null) {
      key = keyMatch.group(1) ?? '';
    }
    return StepBlock(
      name: name,
      status: status,
      content: content,
      isComplete: false,
      key: key,
    );
  }
}
