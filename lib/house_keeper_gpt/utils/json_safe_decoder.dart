import 'dart:convert';

class SafeJsonDecoder {
  static dynamic decode(String jsonString) {
    try {
      return jsonDecode(jsonString);
    } catch (e) {
      return _attemptRecovery(jsonString);
    }
  }

  static dynamic _attemptRecovery(String brokenJson) {
    try {
      // 尝试补全可能的缺失括号
      final trimmed = brokenJson.trim();
      if (trimmed.endsWith(',')) {
        return jsonDecode(trimmed + 'null]');
      }
      if (trimmed.endsWith(':')) {
        return jsonDecode(trimmed + 'null}');
      }
      if (!trimmed.endsWith('}') && !trimmed.endsWith(']')) {
        if (trimmed.startsWith('{')) {
          return jsonDecode(trimmed + '}');
        }
        if (trimmed.startsWith('[')) {
          return jsonDecode(trimmed + ']');
        }
      }
    } catch (_) {}
    return null;
  }
}
