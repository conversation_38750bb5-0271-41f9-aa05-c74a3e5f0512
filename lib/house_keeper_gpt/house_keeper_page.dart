import 'package:flap/flap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:provider/provider.dart';
import 'package:waimai_e_base_ui/waimai_e_base_ui.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/common/house_keeper_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/config/prompt_config.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/service/house_keeper_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/utils/avatar_url_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_infinity_tag_scroll_row.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_voice_input_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_text_input_widget.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/voice_asr_callback.dart';
import 'package:waimai_e_flutter_order/common_class/index.dart';
import 'dart:async';
import 'package:flutter_lx/channel/statistics_lx.dart';

@Flap('house_keeper_gpt')
@MTFRoute('house_keeper_gpt')
class HouseKeeperPage extends StatefulWidget {
  const HouseKeeperPage({Key key, this.params, this.pageName})
      : super(key: key);

  final Map<dynamic, dynamic> params;

  final String pageName;

  @override
  State<HouseKeeperPage> createState() => _HouseKeeperPageState();
}

class _HouseKeeperPageState extends State<HouseKeeperPage>
    with SingleTickerProviderStateMixin, RouteLifecycleStateMixin
    implements VoiceASRCallback {
  double _opacity1 = 0.0;
  double _opacity2 = 0.0;

  HouseKeeperPageVo pageVo = HouseKeeperPageVo();

  HouseKeeperMessagePageModel pageModel;

  int _enterMilliseconds = 0;

  String avatarUrl = '';
  String avatarName = '';

  PromptConfigData _configManager;

  @override
  void initState() {
    super.initState();
    FlutterLx.moudleView(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_lovrgr0h_mv');
    pageModel = HouseKeeperMessagePageModel();
    pageModel.init(pageVo, widget.params);

    // 设置语音识别回调
    pageModel.asrPlugin.asrCallback = this;

    // 记录进入页面时间
    _enterMilliseconds = DateTime.now().millisecondsSinceEpoch;

    // 第一个组件在 0~1 秒渐显
    Future.delayed(const Duration(seconds: 0), () {
      setState(() {
        _opacity1 = 1.0;
      });
    });

    // 第二个组件在 1~2 秒渐显
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _opacity2 = 1.0;
      });
    });

    // 初始化语音识别插件
    pageModel.asrPlugin.init();

    HouseKeeperSPUtils.getAvatarUrl().then((value) {
      setState(() {
        avatarUrl = value;
      });
    });
    HouseKeeperSPUtils.getAvatarName().then((value) {
      setState(() {
        avatarName = value;
      });
    });

    // 加载配置
    _loadConfig();
  }

  // 加载配置
  void _loadConfig() {
    HouseKeeperAPI.fetchFrequentQuestions().then((config) {
      setState(() {
        _configManager = config ?? {};
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    bool isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F8FF),
      resizeToAvoidBottomInset: true,
      body: ChangeNotifierProvider<HouseKeeperPageVo>.value(
        value: pageVo,
        child: Container(
          // 背景图
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: NetworkImage(
                  "https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/24677844958da0f5/homepage_bg.png"),
              fit: BoxFit.cover,
            ),
          ),
          child: SafeArea(
            bottom: !isKeyboardVisible,
            child: Column(
              children: [
                // 顶部区域
                _buildHeader(),
                // 中间区域 - 使用Expanded确保它占据所有可用空间
                Expanded(
                  child: Column(
                    children: [
                      _buildAvatar(),
                      // 问候语
                      Container(
                        color: Colors.transparent,
                        child: _buildGreeting(),
                      ),
                      const SizedBox(height: 20),
                      // 快捷方式
                      Visibility(
                        visible: _configManager != null,
                        child: _buildShortcuts(),
                      ),
                    ],
                  ),
                ),
                // 底部输入区域
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeOut,
                  decoration: BoxDecoration(
                    color: isKeyboardVisible
                        ? Colors.white.withOpacity(0.9)
                        : Colors.transparent,
                    borderRadius: isKeyboardVisible
                        ? const BorderRadius.only(
                            topLeft: Radius.circular(16),
                            topRight: Radius.circular(16),
                          )
                        : null,
                    boxShadow: isKeyboardVisible
                        ? [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, -2),
                            )
                          ]
                        : [],
                  ),
                  padding: const EdgeInsets.only(bottom: 10),
                  child: _buildInputWidget(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String getGreeting() {
    DateTime now = DateTime.now();
    int hour = now.hour;

    if (hour >= 5 && hour < 12) {
      return 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/5c76cf66085b39ff/shangwuhao.png';
    } else if (hour >= 12 && hour < 14) {
      return 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/377a8b2c60929af5/shangwuhao.png';
    } else if (hour >= 14 && hour < 18) {
      return 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/e1d6fb9dfd82d5a0/xiawuhao.png';
    } else if (hour >= 18 && hour < 22) {
      return 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/e177ee49c8d7d807/wanshanghao.png';
    } else {
      return 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/e177ee49c8d7d807/wanshanghao.png'; // 深夜也可以认为是晚上
    }
  }

  Widget _buildHeader() {
    return Container(
      color: Colors.transparent,
      child: Align(
        alignment: Alignment.centerLeft,
        child: SAKButton(
          child: Image.asset(
            'images/icon_black_back.png',
            width: 22,
            height: 22,
          ),
          onPressed: () {
            RouteUtils.close(context);
          },
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      height: 250,
      color: Colors.transparent,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 背景图
          Image.network(
            'https://p0.meituan.net/ingee/17c20dba5b74474685d8edf79444c19492330.png',
            height: 208,
            fit: BoxFit.fill,
          ),
          // 头像容器
          Visibility(
              visible: StringUtil.isNotEmpty(avatarUrl),
              child: Container(
                margin: const EdgeInsets.only(top: 10),
                child: Stack(
                  children: [
                    ClipOval(
                      child: Image(
                        fit: BoxFit.contain,
                        height: 220,
                        image: AdvancedNetworkImage(
                          avatarUrl,
                          useDiskCache: true,
                          timeoutDuration: const Duration(seconds: 10),
                        ),
                      ),
                    ),
                    // 右下角图标
                    Positioned(
                      right: 20,
                      bottom: 0,
                      child: GestureDetector(
                        onTap: () {
                          _handleAvatarTap();
                        },
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Image(
                              image: AdvancedNetworkImage(
                                'https://p0.meituan.net/ingee/ad53e8d16080e9b020a645becb7977863370.png',
                                useDiskCache: true,
                              ),
                              width: 32,
                              height: 32,
                              fit: BoxFit.fill,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildGreeting() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        children: [
          // 问候语
          AnimatedOpacity(
            opacity: _opacity1,
            duration: const Duration(seconds: 1),
            child: Column(
              children: [
                Image(
                  fit: BoxFit.fill,
                  height: 22,
                  image: AdvancedNetworkImage(
                    getGreeting(),
                    useDiskCache: true,
                  ),
                ),
                const SizedBox(height: 16)
              ],
            ),
          ),
          AnimatedOpacity(
            opacity: _opacity2,
            duration: const Duration(seconds: 1),
            child: Padding(
              padding: const EdgeInsets.only(left: 22),
              child: Image(
                fit: BoxFit.fill,
                height: 22,
                image: AdvancedNetworkImage(
                  'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/6e00f46804d47ddb/somehelp.png',
                  useDiskCache: true,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShortcuts() {
    return Column(
      children: [
        // 第一行提示词
        Visibility(
          visible: _configManager != null &&
              _configManager.welcomeLine1 != null &&
              _configManager.welcomeLine1.isNotEmpty,
          child: SizedBox(
            height: 56,
            child: ScrollTagsWidget(
              dataList: _configManager?.welcomeLine1 ?? [],
              autoScroll: true,
              scrollSpeed: 15.0,
              onTagTap: (tag) {
                _handleShortcut(tag.text, tag.subAgentCode);
              },
            ),
          ),
        ),
        // 第二行提示词
        Visibility(
          visible: _configManager != null &&
              _configManager.welcomeLine2 != null &&
              _configManager.welcomeLine2.isNotEmpty,
          child: SizedBox(
            height: 56,
            child: ScrollTagsWidget(
              dataList: _configManager?.welcomeLine2 ?? [],
              autoScroll: true,
              scrollSpeed: 10.0,
              onTagTap: (tag) {
                _handleShortcut(tag.text, tag.subAgentCode);
              },
            ),
          ),
        ),
        // 第三行提示词（如果有）
        Visibility(
          visible: _configManager != null &&
              _configManager.welcomeLine3 != null &&
              _configManager.welcomeLine3.isNotEmpty,
          child: SizedBox(
            height: 56,
            child: ScrollTagsWidget(
              dataList: _configManager?.welcomeLine3 ?? [],
              autoScroll: true,
              scrollSpeed: 15.0,
              onTagTap: (tag) {
                _handleShortcut(tag.text, tag.subAgentCode);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInputWidget() {
    return Selector<HouseKeeperPageVo, HouseKeeperInputType>(
      selector: (_, pageVo) => pageVo.inputType,
      builder: (context, inputType, child) {
        if (inputType == HouseKeeperInputType.voice) {
          return _buildVoiceInput();
        } else {
          return _buildTextInput();
        }
      },
    );
  }

  Widget _buildVoiceInput() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        _showMessagePage();
      },
      child: HouseKeeperVoiceInputWidget(
        pageModel: pageModel,
        bottomText: '按住说话',
      ),
    );
  }

  Widget _buildTextInput() {
    return HouseKeeperTextInputWidget(
      pageModel: pageModel,
      onTextSubmit: (String text) {
        if (StringUtil.isNotEmpty(text)) {
          _showMessagePage(initialMessage: text);
        }
      },
    );
  }

  // 点击快捷方式
  void _handleShortcut(String text, String subAgentCode) {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_x75mhl5i_mc',
        val: {"text": text});
    _showMessagePage(initialMessage: text, subAgentCode: subAgentCode);
  }

  // 点击头像
  void _handleAvatarTap() {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_a5uzfszy_mc');
    Map<String, dynamic> params = {
      'flap_id': 'house_keeper_gpt',
      'flap_entry': 'HouseKeeperAvatarSelectionPage',
      'moduleName': 'waimai_e_flutter_house_keeper',
    };
    final url = SchemeUrls.flutterPageUrl('flap',
        params: params, channel: 'waimai_e_flutter_house_keeper');

    RouteUtils.open(url).then((_) {
      // 强制刷新头像和名称
      _refreshAvatarInfo();
    });
  }

  // 刷新头像信息
  void _refreshAvatarInfo() async {
    // 获取最新的头像URL和名称
    final newAvatarUrl = await HouseKeeperSPUtils.getAvatarUrl();
    final newAvatarName = await HouseKeeperSPUtils.getAvatarName();

    // 只有当值发生变化时才更新状态
    if (newAvatarUrl != avatarUrl || newAvatarName != avatarName) {
      setState(() {
        avatarUrl = newAvatarUrl;
        avatarName = newAvatarName;
      });
    }
  }

  // 显示消息页面
  void _showMessagePage({String initialMessage, String subAgentCode}) {
    Map<String, dynamic> params = {
      'flap_id': 'house_keeper_gpt',
      'flap_entry': 'HouseKeeperMessagePage',
      'moduleName': 'waimai_e_flutter_house_keeper',
    };
    if (subAgentCode != null && subAgentCode.isNotEmpty) {
      params['sub_agent_code'] = subAgentCode;
    }
    if (initialMessage != null && initialMessage.isNotEmpty) {
      params['initial_message'] = initialMessage;
    }
    final url = SchemeUrls.flutterPageUrl('flap',
        params: params, channel: 'waimai_e_flutter_house_keeper');

    RouteUtils.open(url).then((_) {
      if (context != null) {
        RouteUtils.close(context);
      }
    });
  }

  @override
  void dispose() {
    pageModel.dispose();
    HouseKeeperReporter.reportClosePage(_enterMilliseconds);
    super.dispose();
  }

  @override
  void didAppear() {
    HouseKeeperReporter.reportPV();
  }

  @override
  void didDisappear() {}

  @override
  void onASRFailed() {
    pageModel.asrPlugin.stopListening();
  }

  @override
  void onASRSuccess(String audioId, String result) {
    if (StringUtil.isNotEmpty(result)) {
      _showMessagePage(initialMessage: result);
    }
  }

  @override
  void onASRTempResult(String audioId, String result) {
    // 不需要处理
  }

  @override
  void onOvertimeClose() {
    // 不需要处理
  }

  @override
  void onVoiceDBSize(double voiceDB) {
    // voiceDB 会自动设置到 pageVo.dbSize 中
  }
}
