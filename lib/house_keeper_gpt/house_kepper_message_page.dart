import 'package:flap/flap.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:provider/provider.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/service/house_keeper_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/utils/avatar_url_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_message_list.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_text_input_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_voice_input_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/test/custom_block_test.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/config/prompt_config.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_character_model.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

@Flap('house_keeper_gpt')
@MTFRoute('house_keeper_message')
class HouseKeeperMessagePage extends StatefulWidget {
  const HouseKeeperMessagePage({Key key, this.params, this.pageName})
      : super(key: key);

  final Map<dynamic, dynamic> params;

  final String pageName;

  @override
  _HouseKeeperMessagePageState createState() => _HouseKeeperMessagePageState();
}

class _HouseKeeperMessagePageState extends State<HouseKeeperMessagePage> {
  // 添加键盘监听器
  final FocusNode _focusNode = FocusNode();
  HouseKeeperMessagePageModel pageModel;
  HouseKeeperPageVo pageVo = HouseKeeperPageVo();
  String avatarUrl = '';
  String characterName = '';
  int _tapCount = 0;
  int _lastTapTime = 0;
  String _mode = '';

  bool isMuted = false;

  String muteImg =
      'http://p0.meituan.net/tuling/114dcca7a0537b6b77df4fe5b679af6d1243.png';
  String unmuteImg =
      'http://p0.meituan.net/tuling/8871a9538a1090f5fea39d5d08b0b929972.png';

  final ScrollController _scrollController = ScrollController();
  String avatarId;

  // 添加配置管理器
  PromptConfigData _configManager;
  final String financeMode = "finance";

  @override
  void initState() {
    super.initState();
    FlutterLx.moudleView(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_7b8pxhcw_mv');
    pageModel = HouseKeeperMessagePageModel();
    if (widget != null && widget.params != null) {
      _mode = widget.params["mode"];
      pageModel.mode = _mode;
    }
    pageModel.init(pageVo, {});

    // 将滚动控制器传递给 pageModel
    pageModel.scrollController = _scrollController;
    // 初始化语音识别插件
    pageModel.asrPlugin.init();

    // 设置上下文
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 设置状态栏为透明
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ));
    });

    if (widget != null && widget.params != null) {
      String query = widget.params["initial_message"];
      String subAgentCode = widget.params["sub_agent_code"];
      if (_mode == financeMode) {
        pageModel.insertMessage(HouseKeeperMessage(
            type: HouseKeeperMessageType.guide,
            content:
                '老板您好！可以告诉我您的账单定制化需求。我会帮您智能定制专属数据分析。例如您可以说：“展示近7天的退款订单情况”'));
      }
      if (query != null && query.isNotEmpty) {
        if (subAgentCode != null && subAgentCode.isNotEmpty) {
          pageModel.sendPlainMessageWithAgent(query, subAgentCode);
        } else {
          pageModel.sendPlainMessage(query);
        }
      }
    }
    // 获取当前头像url和名字
    _loadCurrentCharacter();
    // 加载配置
    _loadConfig();

    HouseKeeperSPUtils.getMutedState().then((muted) {
      setState(() {
        isMuted = muted;
      });
    });

    // 添加键盘监听
    _focusNode.addListener(_onFocusChange);
  }

  // 加载当前角色信息
  Future<void> _loadCurrentCharacter() async {
    try {
      final storageAvatarId = await HouseKeeperSPUtils.getAvatarId();
      avatarId = storageAvatarId;
      final url = await HouseKeeperSPUtils.getMessagePageUrl();
      final name = await HouseKeeperSPUtils.getCharacterName(avatarId);
      final character = CharacterConfig.characters.firstWhere(
        (c) => c.avatarId == avatarId,
        orElse: () => CharacterConfig.characters.first,
      );
      setState(() {
        avatarUrl = url;
        characterName = name.isNotEmpty ? name : character.name;
      });
    } catch (e) {
      // 设置默认值
      avatarId = CharacterConfig.defaultAvatarId;
      setState(() {
        avatarUrl = '';
        characterName = '';
      });
    }
  }

  // 加载配置
  void _loadConfig() {
    HouseKeeperAPI.fetchFrequentQuestions().then((config) {
      setState(() {
        _configManager = config ?? {};
      });
    });
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();

    // 取消所有正在进行的消息流
    pageModel.cancelAllMessages();

    // 释放语音识别插件资源
    pageModel.asrPlugin.stopListening();

    // 释放滚动控制器
    _scrollController.dispose();

    super.dispose();
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      // 当输入框获取焦点时，滚动到底部
      _scrollToBottom();
    }
  }

  void _scrollToBottom() {
    // 确保滚动控制器已初始化
    if (_scrollController.hasClients) {
      // 添加一点延迟，确保键盘完全展开后再滚动
      Future.delayed(const Duration(milliseconds: 500), () {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取AppBar的高度
    final double appBarHeight = AppBar().preferredSize.height;
    final double statusBarHeight = MediaQuery.of(context).padding.top;
    final double topPadding = appBarHeight + statusBarHeight;

    return ChangeNotifierProvider<HouseKeeperPageVo>.value(
      value: pageVo,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
        ),
        child: Scaffold(
            // 添加 resizeToAvoidBottomInset 确保键盘弹出时内容正确调整
            resizeToAvoidBottomInset: true,
            backgroundColor: Colors.transparent,
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: TextButton(
                onPressed: () {
                  RouteUtils.close(context);
                },
                child: Image.asset(
                  'images/icon_black_back.png',
                  width: 22,
                  height: 22,
                ),
              ),
              title: GestureDetector(
                onTap: () {
                  final now = DateTime.now().millisecondsSinceEpoch;
                  // 如果两次点击间隔超过 1.5 秒，重置计数器
                  if (now - _lastTapTime > 1500) {
                    _tapCount = 0;
                  }
                  _lastTapTime = now;
                  _tapCount++;

                  // 达到 8 次点击时执行操作
                  if (_tapCount >= 8) {
                    _tapCount = 0; // 重置计数器
                    _showCustomSettingsDialog(context);
                  }
                },
                child: Container(
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.transparent,
                        ),
                        child: Image(
                            fit: BoxFit.fill,
                            width: 32,
                            height: 32,
                            image: AdvancedNetworkImage(
                              avatarUrl,
                              useDiskCache: true,
                              timeoutDuration: const Duration(seconds: 10),
                              loadedCallback: () {},
                              loadFailedCallback: () {},
                            )),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        characterName,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
              titleSpacing: -4, // 增加负值使标题更靠左
              actions: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  textDirection: TextDirection.ltr,
                  children: [
                    Visibility(
                        visible: _mode != financeMode, // 财务管家模式下不展示
                        child: GestureDetector(
                            child: Image(
                              image: AdvancedNetworkImage(
                                "https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/44634900335c0425/new_dialog.png",
                                useDiskCache: true,
                              ),
                              width: 22,
                              height: 22,
                            ),
                            onTap: () {
                              _handleNewSession();
                            })),
                    const SizedBox(width: 15),
                    // 只有小美角色才显示小喇叭
                    Visibility(
                      visible: avatarId == CharacterConfig.xiaoMeiId,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          GestureDetector(
                              child: Image(
                                image: isMuted
                                    ? AdvancedNetworkImage(
                                        muteImg,
                                        useDiskCache: true,
                                      )
                                    : AdvancedNetworkImage(
                                        unmuteImg,
                                        useDiskCache: true,
                                      ),
                                width: 22,
                                height: 22,
                              ),
                              onTap: () {
                                setState(() {
                                  isMuted = !isMuted;
                                });
                                pageModel.handleMuteToggle(isMuted);
                                HouseKeeperSPUtils.setMutedState(isMuted);
                                MTFToast.showToast(msg: '切换声音');
                              }),
                          const SizedBox(width: 10),
                        ],
                      ),
                    ),
                    // 调试按钮
                    // GestureDetector(
                    //     child: Image(
                    //       image: AdvancedNetworkImage(
                    //         "http://p0.meituan.net/tuling/ec1e57fc5f31eaabe3c20058d3486cbc498.png",
                    //         useDiskCache: true,
                    //       ),
                    //       width: 22,
                    //       height: 22,
                    //     ),
                    //     onTap: () {
                    //       _showCustomSettingsDialog(context);
                    //     }),
                    const SizedBox(width: 20),
                  ],
                ),
              ],
            ),
            // 在body外层添加Container并设置背景图
            body: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: const NetworkImage(
                        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/7c88497db3cdf5a7/background.png'),
                    colorFilter: ColorFilter.mode(
                      Colors.white.withOpacity(
                          0.25), // Set the transparency of the background image
                      BlendMode.srcOver,
                    ),
                    fit: BoxFit.cover),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // 消息列表
                    Expanded(
                      child: Consumer<HouseKeeperPageVo>(
                        builder: (BuildContext context, HouseKeeperPageVo vo,
                            Widget child) {
                          return Builder(
                            builder: (BuildContext builderContext) {
                              return HouseKeeperMessageListWidget(
                                pageModel: pageModel,
                                scrollController: _scrollController,
                              );
                            },
                          );
                        },
                      ),
                    ),

                    // 底部导航栏
                    _buildBottomNavBar(),

                    // 底部语音输入
                    _buildInputWidget(),
                  ],
                ),
              ),
            )),
      ),
    );
  }

  Widget _buildBottomNavBar() {
    // 获取默认提示词，如果为空则使用默认列表
    final defaultPrompts = _configManager?.messageDefaultPrompts ?? [];

    if (defaultPrompts.isEmpty) {
      return const SizedBox.shrink();
    }
    // 财务管家模式下不展示
    if (_mode == financeMode) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Row(
        children: [
          // 导航按钮
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: defaultPrompts.map((button) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 6),
                    child: _buildNavButton(
                      icon: button.icon,
                      label: button.label,
                      onTap: () {
                        FlutterLx.moudleClick('43392360', 'c_waimai_e_jxnzlx1r',
                            'b_waimai_e_tztbymfs_mc',
                            val: {"text": button.prompt ?? button.label ?? ""});
                        // 使用 prompt 而不是 label 发送消息
                        pageModel.sendPlainMessageWithAgent(
                            button.prompt ?? button.label, button.subAgentCode);
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),

          // 添加空隙
          const SizedBox(width: 12),

          // 更多按钮 - 使用"更多 ^"文本样式
          GestureDetector(
            onTap: () => _showPromptSuggestions(context),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                // 使用半透明白色背景
                color: Colors.white.withOpacity(0.8),
                borderRadius: BorderRadius.circular(20), // 圆角
              ),
              child: Row(
                children: const [
                  Text(
                    '更多',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 2),
                  Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Colors.black87,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIcon(String url) {
    if (url == null || url.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 15,
          height: 15,
          child: Image(
            image: AdvancedNetworkImage(
              url,
              useDiskCache: true,
              timeoutDuration: const Duration(seconds: 10),
              loadedCallback: () {},
              loadFailedCallback: () {},
            ),
            errorBuilder: (context, error, stackTrace) {
              return const SizedBox.shrink();
            },
          ),
        ),
        const SizedBox(width: 2),
      ],
    );
  }

  Widget _buildNavButton({String icon, String label, VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildIcon(icon),
            Text(
              label ?? '',
              style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputWidget() {
    return Selector<HouseKeeperPageVo, HouseKeeperInputType>(
      selector: (_, pageVo) => pageVo.inputType,
      builder: (context, inputType, child) {
        if (inputType == HouseKeeperInputType.voice) {
          return HouseKeeperVoiceInputWidget(
            pageModel: pageModel,
            bottomText: '按住说话',
          );
        } else {
          return HouseKeeperTextInputWidget(
            mode: _mode,
            pageModel: pageModel,
            focusNode: _focusNode,
          );
        }
      },
    );
  }

  void _handleNewSession() {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_6oy26ha7_mc');
    pageModel.handleNewSession();
    pageModel.setNewSession(true);
  }

  void _showCustomSettingsDialog(BuildContext context) {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_8pponb9i_mc');
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      isDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            final bool isDebugMode = CustomBlockDebugTestSwitch.testMode;
            return Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 顶部把手示意
                    Center(
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    // 标题
                    const Padding(
                      padding: EdgeInsets.only(bottom: 16),
                      child: Text(
                        '设置',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF333333),
                        ),
                      ),
                    ),
                    // 调试开关
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Expanded(
                            child: Text(
                              '卡片调试模式',
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                          Switch(
                            value: isDebugMode,
                            onChanged: (bool value) {
                              setState(() {
                                CustomBlockDebugTestSwitch.testMode = value;
                              });
                              // 刷新整个页面
                              this.setState(() {});
                            },
                            activeColor: const Color(0xFF333333),
                          ),
                        ],
                      ),
                    ),
                    // 底部说明文本
                    Visibility(
                        visible: isDebugMode,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Text(
                            '调试模式已开启，将使用测试数据',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        )),
                    // 底部安全区域
                    SizedBox(height: MediaQuery.of(context).padding.bottom),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // 在弹窗中使用配置的展开提示词
  void _showPromptSuggestions(BuildContext context) {
    FlutterLx.moudleClick(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_wf1vz1y0_mc');
    // 获取展开提示词，如果为空则使用默认列表
    final expandPrompts = _configManager?.messageExpandPrompts ?? [];

    if (expandPrompts.isEmpty) {
      return;
    }

    // 按 subAgentCode 对数据进行分组
    final Map<String, List<dynamic>> groupedPrompts = {};
    for (var prompt in expandPrompts) {
      final context = prompt.subAgentCode;
      String groupKey = context;

      // 将 comment_agent 和 spu_agent 归为一组
      if (context == 'comment_agent' || context == 'spu_agent') {
        groupKey = 'spu_agent';
      }

      if (!groupedPrompts.containsKey(groupKey)) {
        groupedPrompts[groupKey] = [];
      }
      groupedPrompts[groupKey].add(prompt);
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.75,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // 顶部把手示意
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // 使用 Expanded + SingleChildScrollView 实现滚动
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: groupedPrompts.entries.map((entry) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 分类标题
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: Text(
                              _getReadableKey(entry.key),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                          // 分类下的提示词列表
                          Wrap(
                            spacing: 12,
                            runSpacing: 12,
                            children: entry.value.map((item) {
                              return GestureDetector(
                                onTap: () {
                                  FlutterLx.moudleClick(
                                      '43392360',
                                      'c_waimai_e_jxnzlx1r',
                                      'b_waimai_e_oiaqq5qy_mc',
                                      val: {"text": item?.label ?? ''});

                                  pageModel.sendPlainMessageWithAgent(
                                      item.prompt ?? item.label,
                                      item.subAgentCode);
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 10,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[50],
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(color: Colors.grey[200]),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (item.icon != null &&
                                          item.icon.isNotEmpty)
                                        Image.network(
                                          item.icon,
                                          width: 20,
                                          height: 20,
                                          fit: BoxFit.cover,
                                        ),
                                      if (item.icon != null &&
                                          item.icon.isNotEmpty)
                                        const SizedBox(width: 8),
                                      Text(
                                        item.label,
                                        style: const TextStyle(
                                            fontSize: 12,
                                            color: Color(0xFF222222),
                                            fontWeight: FontWeight.w500),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                          const SizedBox(height: 20),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ),
              // 底部安全区域
              SizedBox(height: MediaQuery.of(context).padding.bottom),
            ],
          ),
        );
      },
    );
  }

  String _getReadableKey(String key) {
    if (key == 'spu_agent') {
      return '门店运营';
    } else if (key == 'operation_agent') {
      return '经营分析';
    } else if (key == 'governance_agent') {
      return '规则中心';
    } else {
      return '其他';
    }
  }
}
