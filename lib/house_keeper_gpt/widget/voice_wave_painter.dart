import 'package:flutter/material.dart';
import 'dart:math';

class VoiceWavePainter extends CustomPainter {
  final double amplitude;
  final List<double> amplitudeHistory;
  final Color startColor;
  final Color endColor;

  static const int barCount = 27;
  static const double barSpacing = 2.0;
  static const double minHeightFactor = 0.2;
  static const double maxHeightFactor = 1.0;
  static const double cornerRadius = 2.0;
  static const double minAmplitudeScale = 0.2;
  static const double maxAmplitudeScale = 1.2;

  const VoiceWavePainter({
    @required this.amplitude,
    @required this.amplitudeHistory,
    this.startColor = const Color(0xFF63EDFD),
    this.endColor = const Color(0xFF8542ED),
  });

  Color _getColorForPosition(double position) {
    return Color.lerp(startColor, endColor, position);
  }

  List<double> _getDisplayAmplitudes() {
    final List<double> result =
        List<double>.filled(barCount, minAmplitudeScale);
    final int historyLength = amplitudeHistory.length;

    if (historyLength > 0) {
      final int copyLength = min(historyLength, barCount);
      for (int i = 0; i < copyLength; i++) {
        result[i] = amplitudeHistory[i];
      }
    }

    return result;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint();
    paint.style = PaintingStyle.fill;

    final double totalSpacing = barSpacing * (barCount - 1);
    final double barWidth = (size.width - totalSpacing) / barCount;
    final List<double> displayAmplitudes = _getDisplayAmplitudes();

    for (int i = 0; i < barCount; i++) {
      paint.color = _getColorForPosition(i / barCount);

      final double currentAmplitude = minAmplitudeScale +
          (displayAmplitudes[i] * (maxAmplitudeScale - minAmplitudeScale));

      final double barHeight = size.height * currentAmplitude;
      final double xPosition = i * (barWidth + barSpacing);
      final double yPosition = (size.height - barHeight) / 2;

      final RRect barRect = RRect.fromRectAndRadius(
        Rect.fromLTWH(
          xPosition,
          yPosition,
          barWidth,
          barHeight,
        ),
        const Radius.circular(cornerRadius),
      );

      canvas.drawRRect(barRect, paint);
    }
  }

  @override
  bool shouldRepaint(VoiceWavePainter oldDelegate) {
    return oldDelegate.amplitude != amplitude ||
        oldDelegate.startColor != startColor ||
        oldDelegate.endColor != endColor ||
        oldDelegate.amplitudeHistory != amplitudeHistory;
  }
}

class VoiceWaveWidget extends StatefulWidget {
  final double amplitude;
  final Color startColor;
  final Color endColor;
  final double width;
  final double height;

  const VoiceWaveWidget({
    Key key,
    @required this.amplitude,
    this.startColor = const Color(0xFF63EDFD),
    this.endColor = const Color(0xFF8542ED),
    this.width = 227.5,
    this.height = 15,
  }) : super(key: key);

  @override
  State<VoiceWaveWidget> createState() => _VoiceWaveWidgetState();
}

class _VoiceWaveWidgetState extends State<VoiceWaveWidget>
    with SingleTickerProviderStateMixin {
  List<double> _amplitudeHistory = List<double>.empty(growable: true);
  AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 50),
    )..addListener(_updateAmplitudeHistory);
    _controller.repeat();
  }

  void _updateAmplitudeHistory() {
    setState(() {
      _amplitudeHistory.insert(0, widget.amplitude);
      if (_amplitudeHistory.length > VoiceWavePainter.barCount) {
        _amplitudeHistory.removeLast();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: CustomPaint(
        painter: VoiceWavePainter(
          amplitude: widget.amplitude,
          amplitudeHistory: _amplitudeHistory,
          startColor: widget.startColor,
          endColor: widget.endColor,
        ),
      ),
    );
  }
}
