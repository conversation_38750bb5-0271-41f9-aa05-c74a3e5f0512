import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/common/markdown_style_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/utils/url_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/markdown_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/base_block.dart';

/// Markdown区块组件
class MarkdownBlockWidget extends BaseMessageBlockWidget {
  const MarkdownBlockWidget({
    Key key,
    @required ContentBlock block,
    bool isVisible = true,
    void Function(String text, String href, String title) onTapLink,
    HouseKeeperMessagePageModel model,
  }) : super(
          key: key,
          block: block,
          isVisible: isVisible,
          model: model,
        );

  @override
  Widget buildBlock(BuildContext context) {
    if (block is! MarkdownBlock) return const SizedBox.shrink();
    final markdownBlock = block as MarkdownBlock;

    return SafeMarkdownBody(
      data: markdownBlock.content,
      styleSheet: MarkdownStyleUtils.getStandardStyle(context),
      onTapLink: (text, href, title) =>
          _defaultOnTapLink(context, text, href, title),
      builders: {},
      blockSyntaxes: [],
    );
  }

  void _defaultOnTapLink(
      BuildContext context, String text, String href, String title) {
    MTFToast.showToast(msg: '点击了链接: $href');
    try {
      if (href != null && href.isNotEmpty) {
        if (isUrlCanOpen(href)) {
          RouteUtils.open(href);
        } else if (href.startsWith('waimaieapi.jump')) {
          // widget.model?.sendPlainMessage(text);
        }
      }
    } catch (e) {
      debugPrint('处理链接点击时出错: $e');
    }
  }
}
