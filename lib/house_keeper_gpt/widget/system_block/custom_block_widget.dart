import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/custom_block_factory.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/loading_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/base_block.dart';

/// 自定义区块组件
class CustomBlockWidget extends StatefulWidget {
  final ContentBlock block;
  final bool isLoading;
  final Map<String, bool> blockLoadingStates;
  final bool isVisible;
  final void Function(String text, String href, String title) onTapLink;
  final HouseKeeperMessagePageModel model;

  const CustomBlockWidget({
    Key key,
    @required this.block,
    @required this.isLoading,
    @required this.blockLoadingStates,
    this.isVisible = true,
    this.onTapLink,
    this.model,
  }) : super(key: key);

  @override
  _CustomBlockWidgetState createState() => _CustomBlockWidgetState();
}

class _CustomBlockWidgetState extends State<CustomBlockWidget> {
  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) return const SizedBox.shrink();

    if (widget.block is! CustomBlock) return const SizedBox.shrink();
    final customBlock = widget.block as CustomBlock;

    if (!customBlock.isComplete) {
      return LoadingBlockWidget(
        blockType: customBlock.blockType,
        isVisible: widget.blockLoadingStates[customBlock.blockType] ?? true,
      );
    }

    final blockWidget = CustomBlockFactory.createCustomBlockWidget(
      customBlock.blockType,
      customBlock.content,
      widget.model,
    );

    return blockWidget ?? const SizedBox.shrink();
  }

  @override
  void initState() {
    super.initState();
    if (widget.block is CustomBlock &&
        (widget.block as CustomBlock).isComplete) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          widget.blockLoadingStates[(widget.block as CustomBlock).blockType] =
              false;
        }
      });
    }
  }
}
