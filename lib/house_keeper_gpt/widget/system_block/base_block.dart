import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/utils/markdown_parser.dart';

/// 内容区块基类
abstract class ContentBlock {
  final ContentBlockType type;
  final String content;
  final bool isComplete;

  ContentBlock({
    @required this.type,
    @required this.content,
    @required this.isComplete,
  });
}

/// 内容区块类型
enum ContentBlockType {
  think, // 思考块
  markdown, // 普通 Markdown 内容
  custom, // 自定义块
  guess, // 猜你想问块
  steps, // 步骤块
}

/// 自定义区块
class CustomBlock extends ContentBlock {
  final String blockType;

  CustomBlock({
    @required this.blockType,
    @required String content,
    bool isComplete = true,
  }) : super(
          type: ContentBlockType.custom,
          content: content,
          isComplete: isComplete,
        );
}

/// Markdown 区块
class MarkdownBlock extends ContentBlock {
  MarkdownBlock({
    @required String content,
    bool isComplete = true,
  }) : super(
          type: ContentBlockType.markdown,
          content: content,
          isComplete: isComplete,
        );
}

/// 步骤区块
class StepBlock extends ContentBlock {
  final String key;
  final String name;
  final String status;
  StepBlock({
    @required this.key,
    @required this.name,
    @required this.status,
    @required String content,
    bool isComplete = true,
  }) : super(
          type: ContentBlockType.steps,
          content: content,
          isComplete: isComplete,
        );
}

/// 思考区块
class ThinkBlock extends ContentBlock {
  ThinkBlock({
    @required String content,
    bool isComplete = true,
  }) : super(
          type: ContentBlockType.think,
          content: content,
          isComplete: isComplete,
        );
}

/// 猜你想问区块
class GuessBlock extends ContentBlock {
  final List<GuessQuestion> questions;

  GuessBlock({
    @required this.questions,
    bool isComplete = true,
  }) : super(
          type: ContentBlockType.guess,
          content: '',
          isComplete: isComplete,
        );
}

/// 基础区块组件
abstract class BaseMessageBlockWidget extends StatelessWidget {
  final ContentBlock block;
  final bool isVisible;
  final HouseKeeperMessagePageModel model;

  const BaseMessageBlockWidget({
    Key key,
    @required this.block,
    this.isVisible = true,
    this.model,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();
    return buildBlock(context);
  }

  Widget buildBlock(BuildContext context);
}
