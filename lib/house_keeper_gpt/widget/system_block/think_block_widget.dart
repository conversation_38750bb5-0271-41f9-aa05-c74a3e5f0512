import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/common/markdown_style_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/base_block.dart';

/// 思考区块组件
class ThinkBlockWidget extends BaseMessageBlockWidget {
  final bool showThinking;
  final VoidCallback onToggleThinking;

  const ThinkBlockWidget({
    Key key,
    @required ContentBlock block,
    @required this.showThinking,
    @required this.onToggleThinking,
    bool isVisible = true,
    void Function(String text, String href, String title) onTapLink,
    HouseKeeperMessagePageModel model,
  }) : super(
          key: key,
          block: block,
          isVisible: isVisible,
          model: model,
        );

  @override
  Widget buildBlock(BuildContext context) {
    if (block is! ThinkBlock) return const SizedBox.shrink();
    final thinkBlock = block as ThinkBlock;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: onToggleThinking,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  thinkBlock.isComplete ? '深度思考过程' : '深度思考中',
                  style: const TextStyle(
                    color: Color(0xFF222222),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 4),
                Image(
                  image: AdvancedNetworkImage(
                    showThinking
                        ? 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/7874d425f2c5485e/arrow_up.png'
                        : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/edeb84061e9ca377/arrow_down.png',
                    useDiskCache: true,
                  ),
                  width: 12,
                  height: 12,
                ),
              ],
            ),
          ),
        ),
        Visibility(
          visible: showThinking,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.only(left: 8, right: 4),
                decoration: const BoxDecoration(
                  color: Color(0xFFFFFFFF),
                  border: Border(
                    left: BorderSide(
                      color: Color(0xFFE2E2E2),
                      width: 2,
                    ),
                  ),
                ),
                child: MarkdownBody(
                  data: thinkBlock.content,
                  selectable: false,
                  styleSheet:
                      MarkdownStyleUtils.getStandardStyle(context).copyWith(
                    p: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF222222),
                      height: 1.6,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
            ],
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
