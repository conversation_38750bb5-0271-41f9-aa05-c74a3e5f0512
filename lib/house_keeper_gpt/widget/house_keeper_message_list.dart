import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/utils/markdown_parser.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/messages/house_keeper_markdown_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/messages/house_keeper_stream_message_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/messages/house_keeper_user_message.dart';

/// 消息列表组件
class HouseKeeperMessageListWidget extends StatelessWidget {
  final HouseKeeperMessagePageModel pageModel;
  final ScrollController scrollController;

  const HouseKeeperMessageListWidget({
    Key key,
    @required this.pageModel,
    @required this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Selector<HouseKeeperPageVo, List<HouseKeeperMessage>>(
      selector: (_, vo) => vo.messages ?? [],
      builder: (context, messages, child) {
        if (messages.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          controller: scrollController,
          padding: const EdgeInsets.all(16),
          physics: const BouncingScrollPhysics(),
          itemCount: messages.length,
          itemBuilder: (context, index) {
            final message = messages[index];
            if (message == null) {
              return const SizedBox.shrink();
            }

            return Padding(
              key: ValueKey('${message.hashCode}'),
              padding: const EdgeInsets.only(bottom: 1),
              child: MessageItemWidget(
                message: message,
                pageModel: pageModel,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Text(
        ' 让我们开始对话吧～',
        style: TextStyle(
          fontSize: 14,
          color: Colors.black54,
        ),
      ),
    );
  }
}

/// 消息项组件
class MessageItemWidget extends StatelessWidget {
  final HouseKeeperMessage message;
  final HouseKeeperMessagePageModel pageModel;

  const MessageItemWidget({
    Key key,
    @required this.message,
    @required this.pageModel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// 用户消息
    if (message.role == "USER") {
      return Container(
        alignment: Alignment.centerRight,
        child: UserMessageWidget(message: message),
      );
    }

    /// 助手消息
    if (message.role == "ASSISTANT") {
      /// 引导卡片
      if (message.type == HouseKeeperMessageType.guide) {
        return _buildGuideCard(context);
      }

      /// 流式消息
      if (message is HouseKeeperStreamMessageVo) {
        return HouseKeeperStreamMessageWidget(
          key: ValueKey(message.hashCode),
          message: message,
          model: pageModel,
        );
      }

      /// 普通历史记录
      return HouseKeeperMessageBlockWidget(
        vo: message,
        model: pageModel,
        contentBlocks: MarkdownParser.preprocessData(message.content ?? ''),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildGuideCard(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.65,
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      child: Text(
        message.content ?? '',
        style: const TextStyle(
          fontSize: 14.0,
          color: Colors.black,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
