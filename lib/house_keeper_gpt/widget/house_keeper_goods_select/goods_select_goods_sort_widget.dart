import 'package:flutter/material.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/goods_spu_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/goods_select_goods_sort_item.dart';

/// 商品选品页排序widget
class GoodsSelectGoodsSortWidget extends StatelessWidget {
  const GoodsSelectGoodsSortWidget({
    this.spuVoList,
    this.onTapDeleteGoods,
    this.onTapMoveTop,
    this.onDragReorder,
    Key key,
  }) : super(key: key);

  final List<GoodsSpuVo> spuVoList;
  final Function(GoodsSpuVo) onTapDeleteGoods;
  final Function(GoodsSpuVo) onTapMoveTop;
  final Function(int, int) onDragReorder;

  /// 点击删除
  void _onTapDelete(int index) {
    GoodsSpuVo spuVo = spuVoList[index];
    if (onTapDeleteGoods != null) {
      onTapDeleteGoods(spuVo);
    }
  }

  /// 点击置顶
  void _onTapMoveTop(int index) {
    GoodsSpuVo spuVo = spuVoList[index];
    if (onTapMoveTop != null) {
      onTapMoveTop(spuVo);
    }
  }

  /// 拖拽商品排序
  void _onReorder(int oldIndex, int newIndex) {
    int length = spuVoList?.length ?? 0;
    if (length <= oldIndex) {
      MTFToast.showToast(msg: '拖拽排序商品失败');
      return;
    }
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    if (newIndex >= length) {
      newIndex = length - 1;
    }
    if (newIndex < 0) {
      newIndex = 0;
    }
    if (onDragReorder != null) {
      onDragReorder(oldIndex, newIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> widgets = <Widget>[];
    for (int i = 0; i < spuVoList.length; i++) {
      GoodsSpuVo spuVo = spuVoList[i];
      GoodsSelectGoodsSortItem item = GoodsSelectGoodsSortItem(
        key: ValueKey("GoodsSelectGoodsSortItem_${spuVo.spuId}"),
        spuVo: spuVo,
        index: i,
        onTapDelete: _onTapDelete,
        onTagMoveTop: _onTapMoveTop,
      );
      widgets.add(item);
    }
    return ReorderableListView(
      children: widgets,
      padding: EdgeInsets.zero,
      onReorder: _onReorder,
    );
  }
}
