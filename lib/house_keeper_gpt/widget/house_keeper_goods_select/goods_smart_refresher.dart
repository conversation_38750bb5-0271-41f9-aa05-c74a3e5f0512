import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class GoodsSmartRefresher extends StatelessWidget {
  GoodsSmartRefresher(
      {this.refreshController,
      this.enablePullDown = true,
      this.enablePullUp = true,
      this.onRefresh,
      this.onLoadMore,
      this.child,
      this.header,
      this.footer});

  final RefreshController refreshController;
  final bool enablePullDown;
  final bool enablePullUp;
  final VoidCallback onRefresh;
  final VoidCallback onLoadMore;
  final Widget child;
  final ClassicHeader header;
  final ClassicFooter footer;

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
      enablePullDown: enablePullDown,
      enablePullUp: enablePullUp,
      child: child,
      header: header ??
          const ClassicHeader(
            releaseText: '释放刷新',
            idleText: '下拉刷新',
            refreshingText: '正在刷新',
            completeText: "完成刷新",
            textStyle: TextStyle(color: Color(0xFF222222)),
            refreshingIcon: SizedBox(
              width: 20.0,
              height: 20.0,
              child: CircularProgressIndicator(
                strokeWidth: 2.0,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF222222)),
              ),
            ),
          ),
      footer: footer ??
          const ClassicFooter(
            loadStyle: LoadStyle.ShowAlways,
            idleText: '上拉加载更多',
            canLoadingText: '释放加载更多',
            loadingText: '正在加载下一页...',
            noDataText: "",
            textStyle: TextStyle(color: Color(0xFF222222)),
            loadingIcon: SizedBox(
              width: 20.0,
              height: 20.0,
              child: CircularProgressIndicator(
                strokeWidth: 2.0,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF222222)),
              ),
            ),
          ),
      controller: refreshController,
      onRefresh: onRefresh,
      onLoading: onLoadMore,
    );
  }
}
