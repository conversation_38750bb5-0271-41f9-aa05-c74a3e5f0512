import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:provider/provider.dart';
import 'package:roo_flutter/basic_components/checkbox/roo_single_checkbox.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/single_poi_goods_select_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/goods_spu_vo.dart';

/// 通用选品页商品列表Item
class GoodsSelectGoodsListItem extends StatelessWidget {
  GoodsSelectGoodsListItem({
    this.spuVo,
    this.onGoodsCheckChanged,
  });

  final GoodsSpuVo spuVo;
  final Function(GoodsSpuVo, bool) onGoodsCheckChanged;

  /// 点击商品
  void _onTapProduct() {
    // 商品不可选
    if (spuVo.selectable != true) {
      return;
    }
    // 反置选择状态
    bool checked = !(spuVo.checked ?? false);
    if (onGoodsCheckChanged != null) {
      onGoodsCheckChanged(spuVo, checked);
    }
  }

  /// 选择状态发生变化
  void _onCheckChanged(bool checked) {
    if (onGoodsCheckChanged != null) {
      onGoodsCheckChanged(spuVo, checked);
    }
  }

  /// 构建图片Widget
  Widget _buildImageWidget() {
    return Container(
      height: 60,
      width: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        image: DecorationImage(
          image: AdvancedNetworkImage(
            spuVo.displayImage,
            useDiskCache: true,
          ),
          fit: BoxFit.fill,
        ),
      ),
    );
  }

  /// 构建商品信息widget
  Widget _buildProductWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 商品名
        Padding(
          padding: const EdgeInsets.only(right: 10),
          child: Text(
            spuVo.spuName,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Color(0xFF222222),
              fontSize: 14,
              fontWeight: FontWeight.w500,
              decoration: TextDecoration.none,
            ),
          ),
        ),
        SizedBox(height: 4),
        Text(
          '月售${spuVo.monthSale}  库存${spuVo.stockString}',
          style: TextStyle(
            color: Color(0xFF666666),
            fontSize: 12,
            fontWeight: FontWeight.w400,
            decoration: TextDecoration.none,
          ),
        ),
        SizedBox(height: 4),
        // 价格
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: '￥',
                style: TextStyle(
                  color: Color(0xFFFF5F59),
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.none,
                ),
              ),
              TextSpan(
                text: spuVo.priceString,
                style: TextStyle(
                  color: Color(0xFFFF5F59),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.none,
                ),
              ),
              TextSpan(
                text: '${spuVo.minPrice != spuVo.maxPrice ? '起' : ''}',
                style: TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 12,
                  fontWeight: FontWeight.normal,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建CheckBoxWidget
  Widget _buildCheckBoxWidget() {
    return Container(
      width: 62,
      height: 62,
      color: Colors.white10,
      alignment: Alignment.centerRight,
      child: Visibility(
        visible: spuVo.selectable == true,
        child: RooSingleCheckBox(
          checked: spuVo.checked == true,
          disabled: false,
          iconRadius: Radius.circular(4.0),
          onChange: _onCheckChanged,
        ),
        replacement: Text(
          '${spuVo.unSelectableReason}',
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 12,
            color: Color(0xFF999999),
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Selector<SinglePoiGoodsSelectPageVo, Tuple2<int, bool>>(
      selector: (BuildContext context, SinglePoiGoodsSelectPageVo pageVo) {
        return Tuple2(spuVo.spuId, spuVo.checked);
      },
      builder: (context, tuple, _) {
        return GestureDetector(
          onTap: _onTapProduct,
          behavior: HitTestBehavior.opaque,
          child: Container(
            padding: EdgeInsets.all(12),
            child: Row(
              children: [
                _buildImageWidget(),
                SizedBox(width: 12),
                Expanded(
                  child: _buildProductWidget(),
                ),
                SizedBox(width: 12),
                _buildCheckBoxWidget(),
              ],
            ),
          ),
        );
      },
    );
  }
}
