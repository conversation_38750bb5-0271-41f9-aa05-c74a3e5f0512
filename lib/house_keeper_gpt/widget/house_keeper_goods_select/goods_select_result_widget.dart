import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/single_poi_goods_select_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/goods_spu_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_goods_select/goods_select_goods_sort_widget.dart';

/// 通用选品页商品选择结果widget
class GoodsSelectResultWidget extends StatefulWidget {
  GoodsSelectResultWidget({
    this.onTapClear,
    this.onTapDeleteGoods,
    this.onTapGoodsMoveTop,
    this.onDragReorder,
    Key key,
  }) : super(key: key);

  final Function onTapClear;
  final Function(GoodsSpuVo) onTapDeleteGoods;
  final Function(GoodsSpuVo) onTapGoodsMoveTop;
  final Function(int, int) onDragReorder;

  @override
  State<StatefulWidget> createState() => _GoodsSelectResultWidgetState();
}

class _GoodsSelectResultWidgetState extends State<GoodsSelectResultWidget> {
  @override
  void initState() {
    super.initState();
  }

  /// 点击清空
  void _onTapClear() {
    if (widget.onTapClear != null) {
      widget.onTapClear();
    }
  }

  /// 删除商品
  void _onTapDeleteGoods(GoodsSpuVo spuVo) {
    if (widget.onTapDeleteGoods != null) {
      widget.onTapDeleteGoods(spuVo);
    }
  }

  /// 置顶商品
  void _onTapGoodsMoveTop(GoodsSpuVo spuVo) {
    if (widget.onTapGoodsMoveTop != null) {
      widget.onTapGoodsMoveTop(spuVo);
    }
  }

  /// 拖拽商品排序
  void _onDragReorder(int oldIndex, int newIndex) {
    if (widget.onDragReorder != null) {
      widget.onDragReorder(oldIndex, newIndex);
    }
  }

  /// 构建顶部tipWidget
  Widget _buildTopTipWidget() {
    return Container(
      height: 34,
      padding: EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Color(0xFFFFF8E1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(17),
          topRight: Radius.circular(17),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            child: Text(
              "系统将按以下顺序展示商品",
              style: TextStyle(
                color: Color(0xFF222222),
                fontSize: 14,
                fontWeight: FontWeight.w400,
                decoration: TextDecoration.none,
              ),
            ),
          ),
          GestureDetector(
            onTap: _onTapClear,
            child: Container(
              child: Text(
                "全部清空",
                style: TextStyle(
                  color: Color(0xFFFF6A00),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.none,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空widget
  Widget _buildEmptyWidget() {
    return Container(
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Image(
            width: 100,
            height: 70,
            image: AdvancedNetworkImage(
              'http://p0.meituan.net/tuling/630dc2cf8cf359fd7fb3fb6dfeb2a0b44886.png',
              useDiskCache: true,
            ),
          ),
          SizedBox(height: 25),
          Text(
            '未选择商品',
            style: TextStyle(
              fontSize: 16.0,
              color: Color(0xFF666666),
              fontWeight: FontWeight.w400,
              decoration: TextDecoration.none,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Selector<SinglePoiGoodsSelectPageVo,
        Tuple2<String, List<GoodsSpuVo>>>(
      selector: (BuildContext context, SinglePoiGoodsSelectPageVo pageVo) {
        List<int> idList =
            pageVo.selectedSpuList?.map<int>((e) => e.spuId)?.toList();
        String idListStr = idList?.join(',');
        return Tuple2(idListStr, pageVo.selectedSpuList);
      },
      builder: (context, tuple, _) {
        List<GoodsSpuVo> goodsList = tuple.item2 ?? [];
        return Container(
          height: 400,
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).padding.bottom + 64,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(10.5),
            ),
            color: Colors.white,
          ),
          child: Column(
            children: <Widget>[
              _buildTopTipWidget(),
              Expanded(
                child: Visibility(
                  visible: goodsList.isEmpty,
                  child: _buildEmptyWidget(),
                  replacement: GoodsSelectGoodsSortWidget(
                    spuVoList: goodsList,
                    onTapDeleteGoods: _onTapDeleteGoods,
                    onTapMoveTop: _onTapGoodsMoveTop,
                    onDragReorder: _onDragReorder,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
