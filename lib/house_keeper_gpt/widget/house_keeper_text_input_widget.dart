import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/common/house_keeper_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_page_vo.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

const String commonInputPlaceholder = '请告诉我你的需求......';
const String financeInputPlaceholder = '请告诉我你的账单数据定制需求';

class HouseKeeperTextInputWidget extends StatefulWidget {
  const HouseKeeperTextInputWidget(
      {Key key,
      @required this.pageModel,
      this.mode,
      this.onTextSubmit,
      this.focusNode})
      : super(key: key);

  final HouseKeeperMessagePageModel pageModel;
  final String mode;
  final Function(String) onTextSubmit;
  final FocusNode focusNode;

  @override
  State<HouseKeeperTextInputWidget> createState() =>
      _HouseKeeperTextInputWidgetState();
}

class _HouseKeeperTextInputWidgetState
    extends State<HouseKeeperTextInputWidget> {
  final TextEditingController _textEditingController = TextEditingController();

  @override
  void dispose() {
    super.dispose();
    _textEditingController?.dispose();
  }

  /// 创建带有渐变描边的容器
  Widget _buildGradientBorderContainer({
    @required Widget child,
    @required BuildContext context,
    BoxDecoration innerDecoration,
    List<BoxShadow> boxShadow,
  }) {
    // 将 MediaQuery 的获取移到 build 方法中
    return Container(
      width: double.infinity,
      height: 52,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(26),
        gradient: const LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Color(0xFF63EDFD),
            Color(0xFF2A61C1),
            Color(0xFF8542ED),
          ],
        ),
        boxShadow: boxShadow,
      ),
      child: Container(
        margin: const EdgeInsets.all(2), // 2dp 边框宽度
        decoration: innerDecoration ??
            BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
            ),
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
        alignment: Alignment.center,
        child: Stack(
          children: [
            child,
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onTap: () {
                  widget.pageModel.changeInputType(HouseKeeperInputType.voice);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  alignment: Alignment.center,
                  child: Image(
                    width: 24,
                    height: 24,
                    image: AdvancedNetworkImage(
                        'https://p0.meituan.net/ingee/4641ee515f7ee904049b581fa2ac8c361372.png'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double viewPaddingBottom = MediaQuery.of(context).viewPadding.bottom;
    double viewInsetBottom = MediaQuery.of(context).viewInsets.bottom;
    bool isKeyboardVisible = viewInsetBottom > 0;

    // Set bottom padding to 0 when keyboard is visible
    double bottom = isKeyboardVisible ? 0.0 : 12.0 + viewPaddingBottom;

    // 计算 boxShadow
    List<BoxShadow> shadows = [];
    if (isKeyboardVisible) {
      shadows = [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    }

    return Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.fromLTRB(16, 12, 16, bottom),
      color: Colors.transparent,
      child: _buildGradientBorderContainer(
        context: context,
        boxShadow: shadows,
        child: Row(
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.only(left: 16),
                child: TextField(
                  controller: _textEditingController,
                  focusNode: widget.focusNode,
                  maxLines: 5,
                  minLines: 1,
                  inputFormatters: [LengthLimitingTextInputFormatter(200)],
                  style: const TextStyle(
                    textBaseline: TextBaseline.alphabetic,
                    color: Colors.black,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'PingFang SC',
                    height: 1.4,
                  ),
                  decoration: InputDecoration(
                    isDense: true,
                    hintText: widget.mode == 'finance'
                        ? financeInputPlaceholder
                        : commonInputPlaceholder,
                    hintStyle: const TextStyle(
                      color: Color(0xFF999999),
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                      fontFamily: 'PingFang SC',
                      height: 1.4,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.fromLTRB(-18, 0, 0, 0),
                  ),
                  textInputAction: TextInputAction.send,
                  keyboardType: TextInputType.text,
                  onSubmitted: (text) {
                    FlutterLx.moudleClick('43392360', 'c_waimai_e_jxnzlx1r',
                        'b_waimai_e_yczaku3k_mc',
                        val: {"text": text});
                    HouseKeeperReporter.reportSendTextClick(content: text);
                    if (StringUtil.isEmpty(text)) {
                      return;
                    }
                    if (widget.onTextSubmit != null) {
                      widget.onTextSubmit(text);
                    } else {
                      widget.pageModel.sendPlainMessage(text);
                    }
                    _textEditingController.clear();
                  },
                ),
              ),
            ),
            // 发送按钮
            GestureDetector(
              onTap: () {
                String text = _textEditingController.text;
                HouseKeeperReporter.reportSendTextClick(content: text);
                if (StringUtil.isEmpty(text)) {
                  return;
                }
                if (widget.onTextSubmit != null) {
                  widget.onTextSubmit(text);
                } else {
                  widget.pageModel.sendPlainMessage(text);
                }
                _textEditingController.clear();
              },
              child: Container(
                margin: const EdgeInsets.only(left: 8),
                padding: const EdgeInsets.all(6),
                child: Image(
                  width: 24,
                  height: 24,
                  image: AdvancedNetworkImage(
                      'https://p0.meituan.net/ingee/0e93f3d7431b5eee8c9e5fabcc205421900.png'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
