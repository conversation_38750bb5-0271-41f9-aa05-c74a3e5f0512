import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/comment/comment_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/comment/agent/select_comment.dart';
import 'dart:convert';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';

/// 评价选择组件
class CommentChoseBlockWidget extends BaseBlockWidget {
  final HouseKeeperMessagePageModel model;

  const CommentChoseBlockWidget({
    Key key,
    @required String content,
    this.model,
  }) : super(key: key, content: content);

  @override
  _CommentChoseBlockWidgetState createState() =>
      _CommentChoseBlockWidgetState();
}

class _CommentChoseBlockWidgetState
    extends BaseBlockWidgetState<CommentChoseBlockWidget> {
  bool hasChoseComment = false;
  String _selectedTone = '真诚'; // 默认选中"真诚"语气
  bool _needTone = true; // 默认需要选择语气

  @override
  void initState() {
    super.initState();
    _initData();
  }

  void _initData() {
    try {
      final contentJson = jsonDecode(widget.content);
      if (contentJson is Map<String, dynamic>) {
        final params = contentJson['needTone'];
        if (params is bool) {
          _needTone = params;
        } else if (params is String) {
          _needTone = params.trim().toLowerCase() == 'true';
        }
      }

      if (!_needTone) {
        // 如果不需要选择语气，直接调用评论选择
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _handleCommentSelection();
        });
      }
    } catch (e) {
      _needTone = true;
    }
  }

  void _handleToneSelect(String tone) {
    setState(() {
      _selectedTone = tone;
    });
  }

  Future<void> _handleCommentSelection() async {
    final CommentCardItem selectedComment = await showSelectedComment(context);
    if (selectedComment == null) return;

    String commentContent = selectedComment.comment;
    String commentTone = _selectedTone;
    // 移除可能存在的特殊字符
    commentContent = commentContent.trim().replaceAll(RegExp(r'\s+'), ' ');
    String displayUserMessage =
        '请帮我回复: $commentContent' + (_needTone ? ' 语气: $commentTone' : '');

    setState(() {
      hasChoseComment = true;
    });

    // 使用 Map.from() 创建新的 Map 并添加语气字段
    final commentData = Map<String, dynamic>.from(selectedComment.toJson());
    if (_needTone) {
      commentData['tone'] = Uri.encodeComponent(_selectedTone);
    }

    // 确保 JSON 字符串中的内容不包含控制字符
    final jsonStr = jsonEncode(commentData)
        .replaceAll(RegExp(r'\\n'), ' ')
        .replaceAll(RegExp(r'\\r'), ' ');

    widget.model?.sendStreamMessage(
      displayUserMessage,
      contextMessage: jsonStr,
      addUserMessage: true,
    );
  }

  @override
  Widget buildContentView() {
    // 如果不需要选择语气，返回空容器
    if (!_needTone) {
      return const Text(
        '已为您打开评价选择页面。',
        style: TextStyle(
          fontSize: 14,
          color: Color(0xFF222222),
          fontWeight: FontWeight.w400,
        ),
      );
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const Padding(
            padding: EdgeInsets.only(top: 16, left: 16),
            child: Text(
              '请选择您想要回复的评价',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF222222),
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          _buildToneSelectionSection(),
          _buildSelectButton(),
        ],
      ),
    );
  }

  Widget _buildToneOption(String text, bool isSelected) {
    return GestureDetector(
      onTap: () => _handleToneSelect(text),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF222222) : Colors.transparent,
          border: Border.all(
            color: const Color(0xFF222222),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 13,
            color: isSelected ? Colors.white : const Color(0xFF222222),
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildToneSelectionSection() {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 12),
      child: Row(
        children: <Widget>[
          const Text(
            '回复语气',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 12),
          _buildToneOption('真诚', _selectedTone == '真诚'),
          const SizedBox(width: 8),
          _buildToneOption('专业', _selectedTone == '专业'),
          const SizedBox(width: 8),
          _buildToneOption('幽默', _selectedTone == '幽默'),
        ],
      ),
    );
  }

  Widget _buildSelectButton() {
    return GestureDetector(
      onTap: _handleCommentSelection,
      child: Container(
        height: 36,
        margin: const EdgeInsets.only(
          top: 16,
          left: 16,
          right: 16,
          bottom: 16,
        ),
        width: double.infinity,
        decoration: BoxDecoration(
          color: const Color(0xFF222222),
          borderRadius: BorderRadius.circular(18),
        ),
        child: const Center(
          child: Text(
            '选择评价',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
