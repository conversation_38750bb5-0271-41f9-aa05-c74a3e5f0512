import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/comment/comment_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

/// 修改自动回复状态卡片
class CommentChangeAutoReplyWidget extends BaseBlockWidget {
  const CommentChangeAutoReplyWidget({
    Key key,
    @required String content,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;

  @override
  _CommentChangeAutoReplyCardState createState() =>
      _CommentChangeAutoReplyCardState();
}

class _CommentChangeAutoReplyCardState
    extends BaseBlockWidgetState<CommentChangeAutoReplyWidget> {
  String _title = '';
  int _valid = 0;
  bool _hasConfirmed = false;
  bool _hasCancelled = false;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;

      setState(() {
        // 安全地获取基本属性
        _title = data['title']?.toString() ?? '';
        final validStr = data['valid']?.toString() ?? '0';
        _valid = int.tryParse(validStr) ?? 0;
      });
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': '切换自动回复状态卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': '切换自动回复状态卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: 切换自动回复状态卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析切换自动回复状态卡片数据时出错: $e');
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            _title,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF000000),
              fontWeight: FontWeight.w400,
              height: 1.3,
            ),
          ),
          const SizedBox(height: 20),
          Visibility(
              visible: !_hasConfirmed && !_hasCancelled,
              child: Row(
                children: [
                  Expanded(
                    child: _buildCancelButton(),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConfirmButton(),
                  ),
                ],
              ),
              replacement: _buildValidButton()),
        ],
      ),
    );
  }

  Widget _buildValidButton() {
    return Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 7,
        ),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: const Color(0xFFe2e2e2)),
          borderRadius: const BorderRadius.all(Radius.circular(17)),
        ),
        child: Text(
          _hasCancelled
              ? '已取消'
              : _hasConfirmed
                  ? '已修改'
                  : '',
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            color: Color(0xffACACAC),
          ),
        ));
  }

  // 取消按钮
  Widget _buildCancelButton() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _hasCancelled = true;
        });
      },
      child: Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 7,
          ),
          decoration: BoxDecoration(
            border: Border.all(width: 0.5, color: const Color(0xFF999999)),
            borderRadius: const BorderRadius.all(Radius.circular(17)),
            color: Colors.white, // 白色背景
          ),
          child: const Text(
            '取消',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              color: Color(0xFF333333), // 黑色文字
            ),
          )),
    );
  }

  Widget _buildConfirmButton() {
    return GestureDetector(
      onTap: () async {
        await CommentApi.changeAutoReply(_valid).then((value) {
          if (value) {
            setState(() {
              _hasConfirmed = true;
            });
          }
        });
      },
      child: Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 7,
          ),
          decoration: BoxDecoration(
            border: Border.all(width: 0.5, color: const Color(0xFF999999)),
            borderRadius: const BorderRadius.all(Radius.circular(17)),
            color: const Color(0xff222222),
          ),
          child: const Text(
            '确认 ',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              color: Color(0xffffffff),
            ),
          )),
    );
  }
}
