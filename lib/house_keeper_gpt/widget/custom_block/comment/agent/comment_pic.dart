class UserCommentCardwmCommentVideo {
  UserCommentCardwmCommentVideo.fromJson(Map<String, dynamic> json) {
    cover = json['cover'];
    url = json['url'];
    height = json['height'];
    width = json['width'];
    duration = json['duration'];
    status = json['status'];
  }
  String cover;
  String url;
  num height;
  num width;

  /// 视频时长 单位是毫秒
  num duration;
  num status;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data["cover"] = this.cover;
    data["url"] = this.url;
    data["height"] = this.height;
    data["width"] = this.width;
    data["duration"] = this.duration;
    data["status"] = this.status;
    return data;
  }
}
