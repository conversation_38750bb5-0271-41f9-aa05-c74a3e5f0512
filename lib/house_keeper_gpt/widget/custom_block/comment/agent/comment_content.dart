import 'package:flutter/material.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';

class CommentTextWidget extends StatelessWidget {
  const CommentTextWidget({
    Key key,
    @required this.isSimple,
    @required this.cleanComment,
    this.black = false,
  }) : super(key: key);

  final String cleanComment;
  final bool isSimple;
  final bool black;
  @override
  Widget build(BuildContext context) {
    if (cleanComment == null || cleanComment.length == 0) {
      return SizedBox.shrink();
    }
    return Container(
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.only(top: 12),
      child: Text(
        '${cleanComment}',
        maxLines: isSimple ? 2 : 50,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
            color: black == false
                ? ColorUtil.scBlackColor222222
                : Color(0xffffffff),
            fontSize: 14,
            fontWeight: FontWeight.w400,
            height: 16 / 14),
      ),
    );
  }
}
