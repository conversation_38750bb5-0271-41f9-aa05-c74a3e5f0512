import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_advanced_networkimage/transition.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/common/own_image.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/comment/agent/comment_pic.dart';

const double _kPreviewImageRadius = 6.5;

class PreviewImagesWidget extends StatelessWidget {
  const PreviewImagesWidget({
    Key key,
    this.imgUrls,
    this.screenWidth,
    this.maxCount,
    this.commentId,
    this.context,
    this.paddingWidth = 12,
  }) : super(key: key);

  final List<String> imgUrls;
  final num screenWidth;
  final int maxCount;
  final num commentId;
  final BuildContext context;

  /// 单侧padding值
  final int paddingWidth;

  @override
  Widget build(BuildContext context) {
    if ((imgUrls == null || imgUrls.length == 0)) {
      return SizedBox.shrink();
    }
    List<String> imgs = [];
    imgs.addAll(imgUrls);
    String showVideoNumString = '';

    int count = maxCount ?? 3;

    /// 图片高度需要统一，简单计算一下即可
    double imageHeight = PreviewImage.getBoxWidth(
      screenWidth,
      count: count,
      paddingWidth: paddingWidth,
    );
    List<Widget> arr = [];

    int len = imgs.length > count ? count : imgs.length;
    bool showNum = imgs.length > count;
    String showNumString;
    showNumString = '${showVideoNumString}${imgUrls.length}图';
    for (int i = 0; i < count; i++) {
      Widget imageWidget;
      if (i > imgs.length - 1) {
        imageWidget = SizedBox.shrink();
      } else {
        final String url = imgs[i];
        Radius leftRadius = Radius.circular(i == 0 ? _kPreviewImageRadius : 0);
        Radius rightRadius =
            Radius.circular(i == len - 1 ? _kPreviewImageRadius : 0);
        imageWidget = Stack(
          alignment: AlignmentDirectional.center,
          children: <Widget>[
            Container(
              padding: EdgeInsets.all(2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: leftRadius,
                  bottomLeft: leftRadius,
                  topRight: rightRadius,
                  bottomRight: rightRadius,
                ),
              ),
              child: Container(
                constraints:
                    const BoxConstraints(maxWidth: 106, maxHeight: 106),
                color: Colors.transparent,
                child: CommentCardImageWidget(
                  borderRadius: BorderRadius.circular(_kPreviewImageRadius),
                  url: url,
                  width: imageHeight,
                  height: imageHeight,
                  cdnResize: true,
                  showDecoration: true,
                  fit: BoxFit.cover,
                  quality: 50,
                ),
              ),
            ),
            Positioned(
              right: 4.5,
              bottom: 2,
              child: Visibility(
                visible: (showNum == true && i == len - 1),
                child: Container(
                  height: 32,
                  constraints: const BoxConstraints(minWidth: 32),
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(
                    showNumString,
                    style: const TextStyle(
                      fontSize: 11,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  decoration: const BoxDecoration(
                    color: Color(0x59000000),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(_kPreviewImageRadius),
                      bottomRight: Radius.circular(_kPreviewImageRadius),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }
      arr.add(Expanded(child: imageWidget));
    }
    return Row(
      children: arr,
    );
  }
}

class PreviewImage {
  static void previewImages(String currentUrl, List<String> imgUrls,
      UserCommentCardwmCommentVideo videoMsg,
      {num commentId, BuildContext context}) {
    if (videoMsg == null) {
      KNB.previewImage(
        current: currentUrl,
        urls: imgUrls,
      );
    } else {
      ResponsiveSystem.bothAppPc(
          runApp: () {
            RouteUtils.open(
              SchemeUrls.flutterPageUrl('user_comment/previewPage',
                  channel: 'waimai_e_fe_flutter_user_comment',
                  params: {
                    'current': jsonEncode(currentUrl),
                    'urls': jsonEncode(imgUrls),
                    'video': jsonEncode(videoMsg.toJson()),
                    'commentId': commentId
                  }),
            );
          },
          runPc: () {});
    }
  }

  static double boxWidth = 0;

  static double getBoxWidth(double screenWidth,
      {int count = 3, int paddingWidth = 0}) {
    // if (PreviewImage.boxWidth == 0) {
    //   PreviewImage.boxWidth = (screenWidth - paddingWidth * 2) / count;
    //   // 缓存下来  顾客评价图片布局使用
    // }
    // PreviewImage.boxWidth = (screenWidth - paddingWidth * 2) / count;
    return (screenWidth - paddingWidth * 2) / count;
  }

  static Widget renderImagePC(
    List<String> imgUrls, {
    UserCommentCardwmCommentVideo videoMsg,
    num commentId,
  }) {
    List<RooMediaData> photoList = [];
    imgUrls.forEach((element) {
      photoList.add(
        RooMediaData(
          picURL: element,
          fileType: FileType.photo,
        ),
      );
    });
    if (videoMsg != null) {
      photoList.add(
        RooMediaData(
          videoURL: videoMsg.url,
          picURL: videoMsg.cover,
          fileType: FileType.video,
        ),
      );
    }
    return RooPhotoVideoPreview(
      width: 80,
      mediaDataList: photoList,
    );
  }

  static Widget renderImages(
    List<String> imgUrls,
    num screenWidth, {
    int maxCount,
    BoxFit fit,
    Color imgbgColor,
    UserCommentCardwmCommentVideo videoMsg,
    num commentId,
    BuildContext context,

    /// 单侧padding值
    int paddingWidth = 25,
  }) {
    if ((imgUrls == null || imgUrls.length == 0) && videoMsg == null) {
      return SizedBox.shrink();
    }
    List<String> imgs = [];
    imgs.addAll(imgUrls);
    String showVideoNumString = '';
    if (videoMsg != null) {
      imgs.insert(0, videoMsg.cover);
      showVideoNumString = '1视频+';
    }
    int count = maxCount ?? 3;

    /// 图片高度需要统一，简单计算一下即可
    double imageHeight = PreviewImage.getBoxWidth(
      screenWidth,
      count: count,
      paddingWidth: paddingWidth,
    );
    List<Widget> arr = [];

    int len = imgs.length > count ? count : imgs.length;
    bool showNum = imgs.length > count;
    String showNumString;
    showNumString = '${showVideoNumString}${imgUrls.length}图';
    for (var i = 0; i < count; i++) {
      Widget imageWidget;
      if (i > imgs.length - 1) {
        imageWidget = SizedBox.shrink();
      } else {
        final String url = imgs[i];
        imageWidget = Stack(
          alignment: AlignmentDirectional.center,
          children: <Widget>[
            GestureDetector(
              onTap: () {
                PreviewImage.previewImages(
                  url,
                  imgUrls,
                  videoMsg,
                  commentId: commentId,
                  context: context,
                );
              },
              child: Container(
                padding: EdgeInsets.all(2),
                child: ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(i == 0 ? 6.5 : 0),
                    bottomLeft: Radius.circular(i == 0 ? 6.5 : 0),
                    topRight: Radius.circular(i == len - 1 ? 6.5 : 0),
                    bottomRight: Radius.circular(i == len - 1 ? 6.5 : 0),
                  ),
                  child: Container(
                    constraints: BoxConstraints(maxWidth: 82, maxHeight: 82),
                    color: imgbgColor ?? Colors.transparent,
                    child: ImageWidget(
                      url: url,
                      w: double.infinity,
                      h: imageHeight,
                      fit: fit ?? BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              right: 2,
              bottom: 2,
              child: (showNum == true && i == len - 1)
                  ? Container(
                      height: 32,
                      constraints: BoxConstraints(minWidth: 32),
                      alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(horizontal: 6),
                      child: Text(
                        showNumString,
                        style: TextStyle(
                            fontSize: 11,
                            color: Color(0xFFFFFFFF),
                            fontWeight: FontWeight.w500),
                      ),
                      decoration: BoxDecoration(
                        color: Color(0x59000000),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(6.5),
                          bottomRight: Radius.circular(6.5),
                        ),
                      ),
                    )
                  : SizedBox.shrink(),
            ),
            Positioned(
              child: i == 0 && videoMsg != null
                  ? GestureDetector(
                      onTap: () {
                        PreviewImage.previewImages(
                          url,
                          imgUrls,
                          videoMsg,
                          commentId: commentId,
                        );
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: Color.fromRGBO(0, 0, 0, 0.3),
                        ),
                        child: Center(
                          child: ImageWidget(
                            url:
                                'http://p0.meituan.net/scarlett/285c4096b5fcad86b8246f52ddaba025655.png',
                            w: 16,
                            h: 16,
                          ),
                        ),
                      ),
                    )
                  : SizedBox.shrink(),
            )
          ],
        );
      }
      arr.add(Expanded(child: imageWidget));
    }
    return Row(
      children: arr,
    );
  }
}

/// 封装图片加载控件，增加图片加载失败时加载默认图片
class CommentCardImageWidget extends StatelessWidget {
  const CommentCardImageWidget({
    @required this.url,
    this.width = 100,
    this.height,
    this.fit = BoxFit.cover,
    this.disabled = false,
    this.borderRadius,
    this.placeholder,
    this.cdnResize = false,
    this.showDecoration = false,
    this.quality = 100,
  });

  final String url;
  final double width;
  final double height;
  final BoxFit fit;
  final bool disabled;
  final bool cdnResize;
  final BorderRadius borderRadius;
  final Widget placeholder;
  final bool showDecoration;
  final int quality;

  @override
  Widget build(BuildContext context) {
    if (url != null && url != '') {
      return Container(
        height: height,
        width: width,
        decoration: showDecoration
            ? BoxDecoration(
                color: Color(0xFFF5F6FA),
                borderRadius: borderRadius,
              )
            : BoxDecoration(),
        child: TransitionToImage(
          color: disabled ? Color(0x99CCCCCC) : null,
          image: cdnResize
              ? AdvancedNetworkImage(
                  url,
                  useDiskCache: true,
                  width: width.toInt(),
                  height: height.toInt(),
                )
              : AdvancedNetworkImage(url, useDiskCache: true),
          borderRadius: borderRadius ?? BorderRadius.circular(0),
          fit: fit,
          width: width,
          height: height,
          loadingWidget: Image.asset(
            'images/default_food_pic.png',
            cacheHeight: height.toInt(),
            cacheWidth: width.toInt(),
          ),
          duration: Duration(milliseconds: 0),
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }
}
