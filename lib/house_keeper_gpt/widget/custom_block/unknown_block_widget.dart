import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';

/// 未知类型块组件
class UnknownBlockWidget extends BaseBlockWidget {
  final String type;

  const UnknownBlockWidget({
    Key key,
    @required this.type,
    @required String content,
  }) : super(key: key, content: content);

  @override
  _UnknownBlockWidgetState createState() => _UnknownBlockWidgetState();
}

class _UnknownBlockWidgetState
    extends BaseBlockWidgetState<UnknownBlockWidget> {
  @override
  Widget buildContentView() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.05),
        border: Border.all(color: Colors.red.shade300),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '未知标签: ${widget.type}',
            style: const TextStyle(
              fontSize: 12.0,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            widget.content,
            style: const TextStyle(
              fontSize: 12.0,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }
}
