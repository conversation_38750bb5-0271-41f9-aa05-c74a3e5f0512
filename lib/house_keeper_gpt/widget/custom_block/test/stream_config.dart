import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/service/house_keeper_sse_client_dio.dart';

/// 流式数据模拟配置
///
/// 提供流式数据模拟的基础功能，供测试组件使用
class StreamConfig {
  /// 默认模拟速度（毫秒/字符）
  static const int defaultSpeed = 16;

  /// 默认流结束延迟（毫秒）
  static const int defaultCloseDelay = 10;

  /// 模拟流式数据
  ///
  /// [content] - 完整内容
  /// [controller] - 流控制器
  /// [speed] - 流速（毫秒/字符）
  /// [onComplete] - 完成时的回调
  /// [onProgress] - 进度更新时的回调
  static void mockStream(
    String content,
    StreamController<SSEDioModel> controller, {
    int speed = defaultSpeed,
    Function onComplete,
    Function onProgress,
  }) {
    debugPrint('正在模拟流式响应 (速度: ${speed}ms/字符)');

    int currentLength = 0;

    // 分批次发送数据，模拟流式输出
    Timer.periodic(Duration(milliseconds: speed), (timer) {
      if (currentLength >= content.length) {
        timer.cancel();

        // 延迟关闭流
        Future.delayed(const Duration(milliseconds: defaultCloseDelay), () {
          if (!controller.isClosed) {
            controller.close();
          }

          // 调用完成回调
          if (onComplete != null) {
            onComplete();
          }
        });
        return;
      }

      // 每次增加1-3个字符，模拟不均匀的流速
      int chunkSize = Random().nextInt(3) + 1;
      int end = currentLength + min(chunkSize, content.length - currentLength);

      String partialData = content.substring(currentLength, end);

      currentLength = end;

      // 发送部分数据
      if (!controller.isClosed) {
        controller.add(SSEDioModel(data: partialData));
      }

      // 调用进度回调
      if (onProgress != null) {
        onProgress();
      }
    });
  }
}
