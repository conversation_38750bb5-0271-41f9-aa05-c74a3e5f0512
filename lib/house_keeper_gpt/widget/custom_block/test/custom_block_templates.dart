/// 自定义区块模板配置
class BlockTemplates {
  static const String comboPuzzle = """
:::comoPuzzle
{
}
:::
""";

  // 下架商品
  static const String changeSellStatusContent =
      """:::wm_activity_for_new_customer_reduction
{"agent_sys_data":{"uuid":"2#27952369#1750340568595#93#wm_activity_for_new_customer_reduction","card_expire_timestamp":1750348799},"biz_data":{"title":"门店新客立减活动","subtitle":"可与其他活动与红包同时享受，配置时请注意控制活动成本。","policyInfo":"门店新客立减3元","poiUserTypeInfo":"门店新客","periodInfo":"20250619-20250718","actions":[{"label":"自行设置","url":"https://waimaieapp.meituan.com/igate/wmact/h5/createEditAct.html?actType=22&subSource=1","type":1},{"label":"立即采纳","url":"https://waimaieapp.meituan.com/proxy-gw/promotion/wmapi/activity/poi/createOrUpdate","type":2}],"submit_default":{"actType":22,"poiId":27952369,"startTime":1750262400,"endTime":1752854399,"period":"00:00-23:59","weeksTime":"1,2,3,4,5,6,7","poiUserType":1,"autoDelayDays":0,"isAgree":1,"policy":{"amount":3,"poiCharge":3,"mtCharge":0,"agentCharge":0}}}}
:::""";

  /// 新版成长计划问卷
  static const String newGrowthPlanList = """
:::wm_new_poi_waimaiGrowthPlan 
[
    {
        "featureSelectList": [
            {
                "featureTextValue": "1-4单",
                "featureValue": 1
            },
            {
                "featureTextValue": "4-13单(超越60%同行)",
                "featureValue": 2
            },
            {
                "featureTextValue": "13单以上(超越80%同行)",
                "featureValue": 3
            }
        ],
        "featureTitle": "01 开业30天内，店铺每天单量的预期是?",
        "featureType": 1
    },
    {
        "featureSelectList": [
            {
                "featureTextValue": "我愿意尝试",
                "featureValue": 3
            },
            {
                "featureTextValue": "暂不考虑",
                "featureValue": 1
            }
        ],
        "featureTitle": "02 开业30天内，是否会参照同行的活动设置，或使用付费推广?",
        "featureType": 2
    },
    {
        "featureSelectList": [
            {
                "featureTextValue": "我愿意尝试",
                "featureValue": 3
            },
            {
                "featureTextValue": "暂不考虑",
                "featureValue": 1
            }
        ],
        "featureTitle": "02 开业30天内，是否会参照同行的活动设置，或使用付费推广?",
        "featureType": 3
    }
]
:::""";

  /// 商品上下架内容模板
  static const String productSellStatusContent = """
:::changeSellUp
{
"operation":"up",
"spus":[{"id": "2207851398", "name": "烤羊肉201685", "price": 23.0, "stock": -1, "description": "这是描述模版", "sellStatus": 0, "defaultPicUrl": ""}]
}
:::
""";

  /// 商家行动建议模板
  static const String actionContent = """:::commonAction 
  {
    "title":"进阶提升27个商品信息",
    "subTitle":"提高信息质量，顾客更有兴趣点击下单",
    "action":
    {
      "url":"itakeawaybiz:\/\/waimaieapi.meituan.com\/web?url=https:\/\/waimaieapp.meituan.com\/web_pro\/product_h5\/assistant\/advancedProduct",
      "label":"去优化"}
    } 
  :::
  :::commonAction 
  {
    "title":"优化1个商品定价",
    "subTitle":"根据建议调整商品价格，提升商品竞争力",
    "action":
    {
      "url":"itakeawaybiz:\/\/waimaieapi.meituan.com\/web?url=https:\/\/waimaieapp.meituan.com\/web_pro\/product_h5\/assistant\/advancedProduct",
      "label":"去优化"
    }
  } 
  ::: 
  :::commonAction 
  {
    "title":"优化23个低质商品信息",
    "subTitle":"商品优质率达到100%，可提升店铺分",
    "action":
    {
      "url":"itakeawaybiz:\/\/waimaieapi.meituan.com\/web?url=https:\/\/waimaieapp.meituan.com\/web_pro\/product_h5\/assistant\/advancedProduct",
      "label":"去优化"
    }
  }
  :::
""";

  /// 财务管家
  static const String financeOrder = """:::wm_biz_finance_order
{
"content": "若符合您的需求，请点击“确认定制”完成定制。若您对生成的内容不满意，请重新告诉我您的需求。",
"acctId":"16010180",
"businessLine":"1",
"sceneType":"4",
"requestId":"f1ca2d4c-5fdb-4a81-be07-f18f29b44c18"
}
:::""";

  /// 竞品对比模板
  static const String compareContent = """:::competitorComparison
{
  "myShop": {
    "name": "小刺猬咖啡",
    "branch": "望京融新店"
  },
  "competitorShop": {
    "name": "Manner Coffee",
    "branch": "望京店"
  },
  "operation": {
    "validOrders": {
      "title": "有效订单",
      "myValue": "1,375",
      "competitorValue": "365",
      "comparison": "胜"
    },
    "actualPayment": {
      "title": "实付客单",
      "myValue": "365",
      "competitorValue": "365",
      "comparison": "平"
    }
  },
  "traffic": {
    "exposure": {
      "title": "曝光人数",
      "myValue": "3,375",
      "competitorValue": "3,375",
      "comparison": "负"
    },
    "storeConversion": {
      "title": "入店转化率",
      "myValue": "28%",
      "competitorValue": "28%",
      "comparison": "负"
    },
    "orderConversion": {
      "title": "下单转化率",
      "myValue": "26%",
      "competitorValue": "26%",
      "comparison": "平"
    }
  },
  "customer": {
    "rating": {
      "title": "评分",
      "myValue": "4.5",
      "competitorValue": "4.5",
      "comparison": "胜"
    },
    "repurchaseRate": {
      "title": "复购率",
      "myValue": "17%",
      "competitorValue": "17%",
      "comparison": "胜"
    },
    "regularCustomerRate": {
      "title": "老客占比",
      "myValue": "35%",
      "competitorValue": "35%",
      "comparison": "负"
    }
  },
  "transaction": {
    "storeScore": {
      "title": "店铺分",
      "myValue": "4.5",
      "competitorValue": "4.5",
      "comparison": "胜"
    },
    "activityCount": {
      "title": "活动数量",
      "myValue": "17%",
      "competitorValue": "17%",
      "comparison": "胜"
    },
    "activityStrength": {
      "title": "活动力度",
      "myValue": "35%",
      "competitorValue": "35%",
      "comparison": "负"
    }
  }
}
:::""";

  /// 冲单内容模板
  static const String rushOrderContent = """:::wm_biz_new_poi_order_task
{
    "orderTask": {
        "target": "25单",
        "targetDesc": "目标",
        "nowValue": "13单",
        "nowValueDesc": "当前完成"
    }
}
:::""";

  /// 商家概览内容模板
  static const String businessOverviewContent =
      """:::wm_biz_new_poi_summary_diagnosis
{
    "biz": [
        {
            "field": "settleAmount",
            "title": "营业收入",
            "type": "double",
            "base": 102.83,
            "baseDelta":+62.32,
            "desc": "比前日"
        },
        {
            "field": "orderCnt",
            "title": "有效订单",
            "type": "int",
            "base": 6,
            "baseDelta": -1,
            "desc": "比前日"
        },
        {
            "field": "avgPrice",
            "title": "实付单均价",
            "type": "double",
            "base": 33.1,
            "baseDelta": -6.87,
            "desc": "比前日"
        }
    ]
}
:::""";

  /// 成长计划问卷模板
  static const String growthPlanContent = """:::waimaiGrowthPlan
[
    {
        "id": 1,
        "type": 1,
        "title": "您的店铺类型是？",
        "value": [
            "纯外卖店",
            "堂食店铺"
        ],
        "selectedValue":[]
    },
    {
        "id": 2,
        "type": 1,
        "title": "您的外卖经验有多久？",
        "value": [
            "没有经验",
            "1-2年",
            "2年以上",
            "没有经验",
            "没有经验",
            "没有经验",
            "没有经验"
        ],
        "selectedValue":[]
    },
    {
        "id": 3,
        "type": 2,
        "title": "您预期每天有多少订单？",
        "value": [
            "15",
            "50",
            "100",
            "500"
        ],
        "selectedValue":[]
    },
    {
        "id": 4,
        "type": 1,
        "title": "您是否愿意尝试付费推广？",
        "value": [
            "可以尝试",
            "不太愿意"
        ],
        "selectedValue":[]
    }
]
:::""";

  static const String requisitionWeightContent = """:::wmAiRequisitionWeight
{
  "title": "店铺权重",
  "subTitle": "店铺权重店铺权重店铺权重店铺权重店铺权重店铺权重",
  "buttonName": "去申请"
}
:::""";

  // 到店自取折扣营销活动
  static const String setPickUpdDiscountActivityContent =
      """:::wm_activity_for_pickup_for_discounts
{
            "agent_sys_data":{ 
                "uuid": "uuid",
                "card_expire_timestamp":"1766297342"
            },
            "biz_data":{
                "title": "到店自取优惠活动",  
                "subtitle": "可与其他活动同享，配置时请注意控制活动成本。",
                "policyInfo": "折扣比例 {simu_on_site_discounts}折",
                "poiUserTypeInfo":"全部顾客",
                "periodInfo": "periodInfo",
                "actions": [
                     {
                        "label": "自行设置",
                        "url": "https://waimaieapp.meituan.com/igate/wmact/h5/createEditAct.html?actType=501&subSource=1",
                        "type":1
                    },
                     {
                        "label": "立即采纳",
                        "url": "https://waimaieapp.meituan.com/proxy-gw/promotion/wmapi/activity/poi/createOrUpdate",
                        "type":2
                    }
                ],
                "submit_default": {
                    "actType":501,
                    "poiId": "poiId",
                    "startTime": "startTime",
                    "endTime": "endTime",
                    "period": "00:00-23:59",
                    "weeksTime":"1,2,3,4,5,6,7",
                    "poiUserType": 0,
                    "autoDelayDays": 0,
                    "isAgree":1,
                    "behaviourType": 1,
                    "remommend_simu_on_site_discounts":"simu_on_site_discounts"
                }
            }
}
:::""";

// 到店自取满减营销活动
  static const String setPickUpdFullCutActivityContent =
      """:::wm_activity_for_pickup_for_fullcut
{
            "agent_sys_data":{ 
                "uuid": "uuid",
                "card_expire_timestamp":"1766297342"
            },
            "biz_data":{
                "title": "到店自取优惠活动",  
                "subtitle": "可与其他活动同享，配置时请注意控制活动成本。",
                "policyInfo":"满{fullcut_act_discount}减{fullcut_act_price}元",
                "poiUserTypeInfo":"全部顾客",
                "periodInfo": "periodInfo",
                "actions": [
                    {
                        "label": "立即采纳",
                        "url": "https://waimaieapp.meituan.com/proxy-gw/promotion/wmapi/activity/poi/createOrUpdate",
                        "type":2
                    },
                    {
                        "label": "自行设置",
                        "url": "https://waimaieapp.meituan.com/igate/wmact/h5/createEditAct.html?actType=501&subSource=1",
                        "type":1
                    }
                ],
                "submit_default": {
                    "actType":501,
                    "poiId": "poiId",
                    "startTime": "startTime",
                    "endTime": "endTime",
                    "period": "00:00-23:59",
                    "weeksTime":"1,2,3,4,5,6,7",
                    "poiUserType": 0,
                    "autoDelayDays": 0,
                    "isAgree":1,
                    "remommend_fullcut_act_price":"fullcut_act_price",
                    "remommend_fullcut_act_discount":"fullcut_act_discount"
                }
            }
}
:::""";

// 新客立减活动创建
  static const String newCustomerReductionContent =
      """根据您的咨询，您目前已设置了减配送费和神券活动。结合店铺数据，发现您的曝光人数为1616，远低于商圈同行均值的6406；入店转化率为0.0538，下单转化率为0.1034，也明显低于同行均值（分别为0.0768和0.2866）。这说明当前店铺在提升曝光、进店转化和下单转化方面还有较大提升空间。

设置建议
为进一步提升订单量和转化效果，建议补充以下活动：

1. 折扣商品活动
通过设置部分商品为折扣价，吸引顾客下单，有效提升下单转化率。
:::commonAction
{
"title": "折扣商品",
"subtitle": "设置热销或新品为折扣商品，可快速提升下单转化及订单量。",
"action": {
"label": "去设置",
"url": "https://waimaieapp.meituan.com/igate/wmact/h5/createEditAct.html?subSource=1&actType=17"
}
}
:::

2. 门店新客立减活动
针对新顾客提供专属立减优惠，有助于提升新客首单转化。
:::wm_activity_for_new_customer_reduction
{"agent_sys_data":{"uuid":"2#27721557#1750156717346#61#wm_activity_for_new_customer_reduction","card_expire_timestamp":1750175999},"biz_data":{"title":"门店新客立减活动","subtitle":"可与其他活动与红包同时享受，配置时请注意控制活动成本。","policyInfo":"门店新客立减4元","poiUserTypeInfo":"门店新客","periodInfo":"20250617-20250716","actions":[{"label":"自行设置","url":"https://waimaieapp.meituan.com/igate/wmact/h5/createEditAct.html?actType=22&subSource=1","type":1},{"label":"立即采纳","url":"https://waimaieapp.meituan.com/proxy-gw/promotion/wmapi/activity/poi/createOrUpdate","type":2}],"submit_default":{"actType":22,"poiId":27721557,"startTime":1750089600,"endTime":1752681599,"period":"00:00-23:59","weeksTime":"1,2,3,4,5,6,7","poiUserType":1,"autoDelayDays":0,"isAgree":1,"policy":{"amount":4,"poiCharge":4,"mtCharge":0,"agentCharge":0}}}}
:::

3. 到店自取专享活动
如支持到店自取，建议为自取顾客设置专享满减优惠，带动更多订单。
:::wm_activity_for_pickup_for_fullcut
{"agent_sys_data":{"uuid":"1#27721557#1750156716710#77#wm_activity_for_pickup_for_fullcut","card_expire_timestamp":1750175999},"biz_data":{"title":"到店自取优惠活动","subtitle":"可与其他活动同享，配置时请注意控制活动成本。","policyInfo":"满21.0减7.0元","poiUserTypeInfo":"全部顾客","periodInfo":"20250617-20250716","actions":[{"label":"立即采纳","url":"https://waimaieapp.meituan.com/proxy-gw/promotion/wmapi/activity/poi/createOrUpdate","type":2},{"label":"自行设置","url":"https://waimaieapp.meituan.com/igate/wmact/h5/createEditAct.html?actType=501&subSource=1","type":1}],"submit_default":{"actType":501,"poiId":27721557,"startTime":1750089600,"endTime":1752681599,"period":"00:00-23:59","weeksTime":"1,2,3,4,5,6,7","poiUserType":0,"autoDelayDays":0,"isAgree":1,"behaviourType":2,"remommend_fullcut_act_price":21.0,"remommend_fullcut_act_discount":7.0}}}
:::

4. 收藏有礼、集点返券等老客复购类活动
鼓励老顾客重复下单，提高复购率和粘性。
:::commonAction
{
"title": "收藏有礼",
"subtitle": "顾客收藏门店后可获得优惠券，可有效促进老顾客复购。",
"action": {
"label": "去设置",
"url": "https://waimaieapp.meituan.com/igate/wmact/h5/createEditAct.html?subSource=1&actType=92"
}
}
:::
:::commonAction
{
"title": "集点返券",
"subtitle": "用户多次下单后可获得返券奖励，提升客户粘性。",
"action": {
"label": "去设置",
"url": "https://waimaieapp.meituan.com/igate/wmact/h5/createEditAct.html?subSource=1&actType=98"
}
}
:::

如有其他经营目标或具体诉求，也欢迎补充说明，我会为您定制更精准的营销方案！
""";

// 优秀同行
  static const String bizDataPKListContent = """根据商圈附近收入排名，为您推荐以下优秀同行
:::wm_poi_biz_data_pk_list
{
    "agent_sys_data": {},
    "biz_data": {
        "title": "",
        "subtitle": "",
        "list": [
            {
                "wmPoiId": 27009018,
                "wmPoiName": "寻宝记绍兴菜（利佰家店）"
            },
            {
                "wmPoiId": 27285879,
                "wmPoiName": "寻宝记绍兴菜（八佰伴店）"
            },
            {
                "wmPoiId": 27293234,
                "wmPoiName": "WELUCKYCOFFEE·现磨咖啡"
            },
            {
                "wmPoiId": 27009018,
                "wmPoiName": "111寻宝记绍兴菜（利佰家店）"
            },
            {
                "wmPoiId": 27285879,
                "wmPoiName": "222寻宝记绍兴菜（八佰伴店）"
            },
            {
                "wmPoiId": 27293234,
                "wmPoiName": "333WELUCKYCOFFEE·现磨咖啡"
            },
            {
                "wmPoiId": 27009018,
                "wmPoiName": "4444寻宝记绍兴菜（利佰家店）"
            },
            {
                "wmPoiId": 27285879,
                "wmPoiName": "5555寻宝记绍兴菜（八佰伴店）"
            },
            {
                "wmPoiId": 27293234,
                "wmPoiName": "6666WELUCKYCOFFEE·现磨咖啡"
            },
            {
                "wmPoiId": 27009018,
                "wmPoiName": "7777寻宝记绍兴菜（利佰家店）"
            },
            {
                "wmPoiId": 27285879,
                "wmPoiName": "888寻宝记绍兴菜（八佰伴店）"
            }
        ]
    }
}
:::""";
// 同行pk
  static const String bizDataPKInfoContent = """:::wm_poi_biz_data_pk_info
{
    "agent_sys_data": {},
    "biz_data": {
        "title": "",
        "subtitle": "",
        "poiName": "本店的名称",
        "pkPoiName": "对比门店的名称",
        "diffList": [
          {
           "diffName": "比经营",
           "diff": [
            {
                "indicatorName": "有效订单",
                "indicatorValue": "1375",
                "pkRes": 1
            },
            {
                "indicatorName": "实付客单",
                "indicatorValue": "365",
                "pkRes": 2
            }
           ]
          },
          {
           "diffName": "比流量",
           "diff": [
              {
                "indicatorName": "曝光人数",
                "indicatorValue": "3375",
                "pkRes": 2
            },
            {
                "indicatorName": "入店转化率",
                "indicatorValue": "35%",
                "pkRes": 1
            },
            {
                "indicatorName": "下单转化率",
                "indicatorValue": "65%",
                "pkRes": 2
            }
           ]
          },
          {
           "diffName": "比顾客",
           "diff": [
            {
                "indicatorName": "评分",
                "indicatorValue": "4.5",
                "pkRes": 1
            },
            {
                "indicatorName": "复购率",
                "indicatorValue": "18%",
                "pkRes": 2
            },
            {
                "indicatorName": "老客占比",
                "indicatorValue": "6%",
                "pkRes": 2
            }
           ]
          },
          {
           "diffName": "比交易",
           "diff": [
             {
                "indicatorName": "店铺分",
                "indicatorValue": "98",
                "pkRes": 1
            },
            {
                "indicatorName": "活动数量",
                "indicatorValue": "4",
                "pkRes": 2
            }
           ]
          }
        ]
    }
}
:::""";

// 上新推荐-热卖单品
  static const String singleItemRecommendContent =
      """:::wm_product_of_single_item_recommend
[
    {
        "avgPrice": 5.32,
        "localQuery": 3432,
        "name": "鸡米花111",
        "ordNum": 120,
        "recSourceReasons": [
            {
                "name": "热销",
                "reason": "销量超过同城91%的菜品"
            },
            {
                "name": "热搜",
                "reason": "用户热门搜索菜品"
            }
        ],
        "foodPicUrl": "https://img.meituan.net/waimaidpoipicmining/bcc0c8b984d39bd70a48e7c3a584fc76751207.png"
    },
    {
        "avgPrice": 5.32,
        "localQuery": 4444,
        "name": "鸡米花222",
        "ordNum": 23,
        "recSourceReasons": [
            {
                "name": "热销",
                "reason": "销量超过同城91%的菜品"
            },
            {
                "name": "热搜",
                "reason": "用户热门搜索菜品"
            }
        ],
        "foodPicUrl": "https://img.meituan.net/waimaidpoipicmining/bcc0c8b984d39bd70a48e7c3a584fc76751207.png"
    },
    {
        "avgPrice": 5.32,
        "localQuery": 22222,
        "name": "鸡米花333",
        "ordNum": 3011,
        "recSourceReasons": [
            {
                "name": "热销",
                "reason": "销量超过同城91%的菜品"
            },
            {
                "name": "热搜",
                "reason": "用户热门搜索菜品"
            }
        ],
        "foodPicUrl": "https://img.meituan.net/waimaidpoipicmining/bcc0c8b984d39bd70a48e7c3a584fc76751207.png"
    },{
        "avgPrice": 5.32,
        "localQuery": 567,
        "name": "鸡米花444",
        "ordNum": 76,
        "recSourceReasons": [
            {
                "name": "热销",
                "reason": "销量超过同城91%的菜品"
            },
            {
                "name": "热搜",
                "reason": "用户热门搜索菜品"
            }
        ],
        "foodPicUrl": "https://img.meituan.net/waimaidpoipicmining/bcc0c8b984d39bd70a48e7c3a584fc76751207.png"
    },
    {
        "avgPrice": 5.32,
        "localQuery": 856,
        "name": "鸡米花555",
        "ordNum": 8,
        "recSourceReasons": [
            {
                "name": "热销",
                "reason": "销量超过同城91%的菜品"
            },
            {
                "name": "热搜",
                "reason": "用户热门搜索菜品"
            }
        ],
        "foodPicUrl": "https://img.meituan.net/waimaidpoipicmining/bcc0c8b984d39bd70a48e7c3a584fc76751207.png"
    },
    {
        "avgPrice": 5.32,
        "localQuery": 4567,
        "name": "鸡米花666",
        "ordNum": 45,
        "recSourceReasons": [
            {
                "name": "热销",
                "reason": "销量超过同城91%的菜品"
            },
            {
                "name": "热搜",
                "reason": "用户热门搜索菜品"
            }
        ],
        "foodPicUrl": "https://img.meituan.net/waimaidpoipicmining/bcc0c8b984d39bd70a48e7c3a584fc76751207.png"
    },
    {
        "avgPrice": 5.32,
        "localQuery": 748,
        "name": "鸡米花777",
        "ordNum": 74,
        "recSourceReasons": [
            {
                "name": "热销",
                "reason": "销量超过同城91%的菜品"
            },
            {
                "name": "热搜",
                "reason": "用户热门搜索菜品"
            }
        ],
        "foodPicUrl": "https://img.meituan.net/waimaidpoipicmining/bcc0c8b984d39bd70a48e7c3a584fc76751207.png"
    },
    {
        "avgPrice": 5.32,
        "localQuery": 874,
        "name": "鸡米花888",
        "ordNum": 37,
        "recSourceReasons": [
            {
                "name": "热销",
                "reason": "销量超过同城91%的菜品"
            },
            {
                "name": "热搜",
                "reason": "用户热门搜索菜品"
            }
        ],
        "foodPicUrl": "https://img.meituan.net/waimaidpoipicmining/bcc0c8b984d39bd70a48e7c3a584fc76751207.png"
    }
]
  :::""";

  // 上新推荐-单品推荐
  static const String hotSellingContent = """:::wm_product_of_hot_selling
[
    {
        "name": "777（收藏⭐️福利/收藏就送荤菜4选1（多点不送））仅限一份！",
        "cnt": "123",
        "commodityPic": "http://p1.meituan.net/xianfu/7cd01d1c5fd63e193ceb1ea96dddb93d2414009.png"
    },
    {
        "name": "666点右上角⭐️加购送热狗(1根）",
        "cnt": "1000+",
        "commodityPic": "http://p0.meituan.net/xianfu/75fa0b2fbc63136ed8e9f54cf2369794151886.jpg"
    },
    {
        "name": "555（收藏⭐️福利/收藏就送荤菜4选1（多点不送））仅限一份！",
        "cnt": "4445",
        "commodityPic": "http://p1.meituan.net/xianfu/7cd01d1c5fd63e193ceb1ea96dddb93d2414009.png"
    },
    {
        "name": "444点右上角⭐️加购送热狗(1根）",
        "cnt": "534+",
        "commodityPic": "http://p0.meituan.net/xianfu/75fa0b2fbc63136ed8e9f54cf2369794151886.jpg"
    },
    {
        "name": "333（收藏⭐️福利/收藏就送荤菜4选1（多点不送））仅限一份！",
        "cnt": "634",
        "commodityPic": "http://p1.meituan.net/xianfu/7cd01d1c5fd63e193ceb1ea96dddb93d2414009.png"
    },
    {
        "name": "222点右上角⭐️加购送热狗(1根）",
        "cnt": "663+",
        "commodityPic": "http://p0.meituan.net/xianfu/75fa0b2fbc63136ed8e9f54cf2369794151886.jpg"
    },{
        "name": "（收藏⭐️福利/收藏就送荤菜4选1（多点不送））仅限一份！",
        "cnt": "154632",
        "commodityPic": "http://p1.meituan.net/xianfu/7cd01d1c5fd63e193ceb1ea96dddb93d2414009.png"
    },
    {
        "name": "111点右上角⭐️加购送热狗(1根）",
        "cnt": "6346+",
        "commodityPic": "http://p0.meituan.net/xianfu/75fa0b2fbc63136ed8e9f54cf2369794151886.jpg"
    }
]
  :::""";

  // 套餐推荐
  static const String packageRecommendContent =
      """:::wm_product_of_package_recommend
[
  {
    "packageFoodName": "原味螺蛳粉[热]+豆腐串套餐",
    "spuComboPriceModelVo": {
      "totalMaxPrice": 0.0,
      "totalPrice": 14.3
    },
    "packageFoodPicUrl": "http://p0.meituan.net/xianfu/383547799d0dc38264b5634a4a2d0343413094.jpg",
    "wm_ai_of_recLabelCode": 15
  },
  {
    "avgPrice": 17.5,
    "comboInfoV2": {
      "comboType": 1,
      "groupContents": [
        {
          "dishContents": [
            {
              "dishAmount": 1,
              "dishName": "大碗原味螺蛳粉+豆腐串（套餐B）",
              "dishPrice": "20.0",
              "picUrl": "http://p0.meituan.net/wmproduct/d04a351e984178894dbdc03fb94ce2dc719259.jpg",
              "skuId": "2732907279"
            },
            {
              "dishAmount": 1,
              "dishName": "自制辣椒酱（自取哦）",
              "dishPrice": "3.0",
              "picUrl": "",
              "skuId": ""
            }
          ],
          "groupName": "原味螺蛳+豆腐串+柳州带",
          "groupType": "TG-DP",
          "isChoice": 0,
          "maxChoice": 2,
          "minChoice": 0
        }
      ],
      "newDishNum": 1,
      "persons": 1
    },
    "name": "【春日特惠】原味螺蛳+豆腐串+柳州带皮鸭脚",
    "foodPicUrl": "http://p1.meituan.net/xianfu/d1ae81303081dce3a488799044f76d54356726.jpg",
    "wm_ai_of_recLabelCode": 9
  }
]
  :::""";

  static const String stepContent =
      """<step key="ir" name="意图识别中" status="完成"></step>\n<step key="ir" name="意图识别中" status="完成"></step>\n<step key="dg" name="数据获取中" status="进行中"></step>\n<step key="dg" name="数据获取中" status="完成">\n<think>首先，用户的问题是“如何获取更多流量”，这涉及到曝光/流量的优化。我需要基于系统附加信息来分析商家的经营数据，并按照分析流程来输出。
分析流程回顾：
数据现状：获取近7日曝光人数现状、环比表现、同行对比差异。总结曝光现状，表现优秀给予肯定；存在问题给出鼓励或安抚。注意：不用总结进店转化和下单转化。
问题诊断：如果曝光落后同行或波动异常，分析TOP3问题原因、数据表现及影响。如果曝光稳定且高于商圈同行前10%，跳过此步。
优化建议：
如果商家是新店期，且[加权赠送资格]为true，则建议申请新店加权资源扶持。
否则，如果新店期且有未完成的新店任务，建议[新店成长计划]。
否则，基于系统信息给出TOP3优化建议。
建议要具体、可行、成本合理，基于问题原因。
注意：新店加权只适用于新商家；神券节固定每月18日，无需报名；动作链接缺失时，不输出按钮。
系统附加信息关键点：
曝光数据：从bizData中提取。
近7日曝光人数：20136人（单位：人数）。
商圈同行均值：11134人。
商圈同行前10%均值：20739人。
商家信息：
店铺名称：TAKE A BREAK COFFEE LAB（咖啡）。
1. [点金推广在哪里设置？](itakeawaybiz://waimaieapi.meituan.com/mrn?mrn_biz=waimaibiz&mrn_entry=ad-homepage&mrn_component=ad-homepage&mrn_from=wmrw) 2. [怎么用神券活动效果更好？](https://waimaieapp.meituan.com/invite/h5/mutiple/activity/daycounpon)
检查：
字数：约150字。
链接：所有URL来自sys_url_info。
commonAction：所有有action字段。
猜你想问：基于建议，链接正确。
数据现状：
近7日曝光人数20136人，高于商圈平均11134人，表现不错！但还有提升空间，低于头部商家的20739人。别担心，优化后流量会更好！
</think>
问题诊断：
付费推广使用不足：推广曝光占比波动大（如25.3%升高30.6%后回落），影响曝光稳定性。
高峰营业时长略短：当前7.7小时/日，差0.1小时达满分标准，可能减少曝光机会。
流量活动参与少：未充分利用神会员等活动，自然曝光潜力未释放。
优化建议：
咱们店铺基础扎实，试试这几个动作快速提升曝光：
:::commonAction
{
"title": "增加点金推广预算",
"subtitle": "高峰期集中投放，稳定付费曝光，预计提升流量10%",
"action": {
"label": "去设置",
"url": "itakeawaybiz://waimaieapi.meituan.com/mrn?mrn_biz=waimaibiz&mrn_entry=ad-homepage&mrn_component=ad-homepage&mrn_from=wmrw"
}
}
:::
:::commonAction
{
"title": "延长高峰营业至7.8小时",
"subtitle": "对齐商圈满分标准，增加自然曝光权重",
"action": {
"label": "去优化",
"url": "itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=flap&flap_id=business_new_time&flap_entry=BusinessNewTimePage&moduleName=waimai_e_flutter_operation"
}
}
:::
:::commonAction
{
"title": "报名神券活动",
"subtitle": "免费流量扶持，提升自然曝光",
"action": {
"label": "去报名",
"url": "https://waimaieapp.meituan.com/invite/h5/mutiple/activity/daycounpon"
}
}
:::\n<Guess>\nQ1\nQ2\n</Guess>""";

  static const String autoReplyContent = """
:::wm_comment_auto_change
{
	"title": "开启”自动回复评价“功能后，系统将智能识别顾客好评，生成个性化回复。请确认是否开启？",
	"valid": 1
}
:::
""";
}
