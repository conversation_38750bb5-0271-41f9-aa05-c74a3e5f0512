import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

/// 新店排名特权申请卡片
class BusinessRequisitionWeightCard extends BaseBlockWidget {
  const BusinessRequisitionWeightCard({
    Key key,
    @required String content,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;

  @override
  _BusinessRequisitionWeightCardState createState() =>
      _BusinessRequisitionWeightCardState();
}

class _BusinessRequisitionWeightCardState
    extends BaseBlockWidgetState<BusinessRequisitionWeightCard> {
  // 标题
  String _title = '';
  // 副标题
  String _subTitle = '';
  // 按钮文本
  String _buttonName = '';
  // 是否已提交
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessRequisitionWeightCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;

      setState(() {
        _title = data['title']?.toString() ?? '';
        _subTitle = data['subTitle']?.toString() ?? '';
        _buttonName = data['buttonName']?.toString() ?? '去申请';
      });
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': '解析新店排名特权申请卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': '解析新店排名特权申请卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: 解析新店排名特权申请卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析新店排名特权申请卡片数据时出错: $e');
    }
  }

  @override
  Widget buildContentView() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 6,
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                    height: 1.3,
                  ),
                ),
                Visibility(
                  visible: _subTitle.isNotEmpty,
                  child: Container(
                    margin: const EdgeInsets.only(top: 4),
                    child: Text(
                      _subTitle,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                        height: 1.2,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildActionButton() {
    return Container(
      height: 32,
      constraints: const BoxConstraints(
        minWidth: 72,
      ),
      child: TextButton(
        onPressed: _isSubmitting
            ? null
            : () {
                _handleActionButtonClick();
              },
        style: TextButton.styleFrom(
          backgroundColor:
              _isSubmitting ? Colors.grey[300] : const Color(0xFF333333),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Text(
          _buttonName,
          style: TextStyle(
            fontSize: 13,
            color: _isSubmitting ? Colors.grey[600] : Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  void _handleActionButtonClick() {
    if (widget.model != null) {
      setState(() {
        _isSubmitting = true;
      });

      final submitData = {
        "cardKey": "wmAiRequisitionWeight",
        "data": "申请1天排名特权"
      };

      widget.model.sendCardMessage(
          '提交特权申请', json.encode(submitData), 'wmAiRequisitionWeight');

      Future.delayed(const Duration(milliseconds: 500), () {
        setState(() {
          _isSubmitting = false;
        });
        MTFToast.showToast(msg: '提交成功');
      });
    }
  }
}
