import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

/// 冲单进度卡片
class BusinessOrderTaskCard extends BaseBlockWidget {
  const BusinessOrderTaskCard({
    Key key,
    @required String content,
  }) : super(key: key, content: content);

  @override
  _BusinessRushOrderCardState createState() => _BusinessRushOrderCardState();
}

class _BusinessRushOrderCardState
    extends BaseBlockWidgetState<BusinessOrderTaskCard> {
  // 目标单数描述
  String _targetDesc = '';
  // 目标单数
  String _target = '';
  // 当前完成描述
  String _nowValueDesc = '';
  // 当前完成单数
  String _nowValue = '';

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessOrderTaskCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final data = json.decode(widget.content);
      final orderTask = data['orderTask'];

      if (orderTask != null) {
        setState(() {
          _target = orderTask['target'] ?? '';
          _targetDesc = orderTask['targetDesc'] ?? '';
          _nowValue = orderTask['nowValue'] ?? '';
          _nowValueDesc = orderTask['nowValueDesc'] ?? '';
        });
      }
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': '冲单进度卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': '冲单进度卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: 冲单进度卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析冲单进度数据时出错: $e');
    }
  }

  @override
  Widget buildContentView() {
    FlutterLx.moudleView(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_k83kqoy2_mv',
        val: {
          'usertext': '',
          'text': widget.content ?? '',
          'type': '新店冲单数据',
        });
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(
            height: 2,
            color: Color(0xFFEEEEEE),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '冲单进度',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF333333),
                  fontWeight: FontWeight.w500,
                ),
              ),
              GestureDetector(
                onTap: () {
                  FlutterLx.moudleClick('43392360', 'c_waimai_e_jxnzlx1r',
                      'b_waimai_e_k83kqoy2_mc',
                      val: {
                        'usertext': '',
                        'text': widget.content ?? '',
                        'type': '新店冲单数据',
                      });
                  RouteUtils.open(
                      'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=flap&flap_id=opening_helper&flap_entry=OpeningHelperPage&moduleName=waimai_e_flutter_operation&from=agents');
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: const [
                    Text(
                      '全部',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      size: 16,
                      color: Color(0xFF999999),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildDataGrid(),
          const SizedBox(height: 4)
        ],
      ),
    );
  }

  Widget _buildDataGrid() {
    List<Widget> columnWidgets = [];

    // 第一列
    columnWidgets.add(
      Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDataItem(
              title: _targetDesc,
              value: _target,
            ),
          ],
        ),
      ),
    );

    // 第二列
    columnWidgets.add(
      Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDataItem(
              title: _nowValueDesc,
              value: _nowValue,
            ),
          ],
        ),
      ),
    );

    // 第三列（空列，保持布局）
    columnWidgets.add(
      Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: const [],
        ),
      ),
    );

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: columnWidgets,
    );
  }

  Widget _buildDataItem({
    @required String title,
    @required String value,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF999999),
            ),
          ),
          const SizedBox(height: 8),
          // 判断是否为"数字+单"的格式
          if (value != null && RegExp(r'^\d+单$').hasMatch(value))
            Row(
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  value.substring(0, value.length - 1),
                  style: const TextStyle(
                    fontSize: 24,
                    height: 1,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const Text(
                  '单',
                  style: TextStyle(
                    fontSize: 11,
                    height: 1,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
              ],
            )
          else
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                height: 1.1,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
        ],
      ),
    );
  }
}
