import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/finance/finance_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

/// 财务管家
class BusinessFinanceOrderCard extends BaseBlockWidget {
  const BusinessFinanceOrderCard({
    Key key,
    @required String content,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;

  @override
  _BusinessFinanceOrderCardState createState() =>
      _BusinessFinanceOrderCardState();
}

class _BusinessFinanceOrderCardState
    extends BaseBlockWidgetState<BusinessFinanceOrderCard> {
  String _content;
  String _businessLine;
  String _sceneType;
  String _requestId;
  bool _confirmed = false;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessFinanceOrderCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;
      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;

      setState(() {
        _content = data['content']?.toString() ?? '';
        _businessLine = data['businessLine']?.toString() ?? '';
        _sceneType = data['sceneType']?.toString() ?? '';
        _requestId = data['requestId']?.toString() ?? '';
      });
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': '财务管家卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': '财务管家卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: 财务管家卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析财务管家卡片数据时出错: $e');
    }
  }

  @override
  Widget buildContentView() {
    return Column(
      children: [
        Text(
          _content ?? '',
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
        ),
        const SizedBox(
          height: 12,
        ),
        GestureDetector(
          onTap: () {
            _handleActionButtonClick();
          },
          child: Container(
            alignment: Alignment.center,
            margin: const EdgeInsets.only(
              bottom: 8,
              left: 10,
              right: 10,
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: 10,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(17)),
              color: _confirmed
                  ? const Color(0xffF8F8F8)
                  : const Color(0xff222222),
            ),
            child: Text(
              '确认定制',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: _confirmed
                    ? const Color(0xffACACAC)
                    : const Color(0xffffffff),
              ),
            ),
          ),
        )
      ],
    );
  }

  void _handleActionButtonClick() {
    HouseKeeperFinanceApi.confirmCustomization(
            _businessLine, _sceneType, _requestId)
        .then((value) {
      if (value) {
        setState(() {
          _confirmed = true;
          MTFToast.showToast(msg: '定制成功');
          Navigator.of(context).pop();
        });
      }
    });
  }
}
