import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/business/business_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

const pkIcon =
    'https://p0.meituan.net/waimaiegoods/4f1459541a0453b5392546cd2f8484621993.png';
const compareIcon =
    'https://p0.meituan.net/waimaiegoods/1198de14818479777b2d14851e2da9c91823.png';

/// 同行pk列表
class BusinessPKListCard extends BaseBlockWidget {
  const BusinessPKListCard({
    Key key,
    @required String content,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;

  @override
  _BusinessPKListCardState createState() => _BusinessPKListCardState();
}

class _BusinessPKListCardState
    extends BaseBlockWidgetState<BusinessPKListCard> {
  Map<String, dynamic> agentSysData;
  Map<String, dynamic> bizData;
  List<PoiPKInfo> pkList;
  List<PoiPKInfo> pkUseList = [];
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessPKListCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;
      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;
      agentSysData = data['agent_sys_data'];
      bizData = data['biz_data'];
      pkList = (bizData['list'] as List<dynamic>)
              ?.map((e) => PoiPKInfo.fromJson(e as Map<String, dynamic>))
              ?.toList() ??
          [];
      pkUseList = [];
      for (int i = 0; i < 3; i++) {
        int index = (currentIndex + i) % pkList.length;
        pkUseList.add(pkList[index]);
      }
      setState(() {});
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': 'pk列表卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': 'pk列表卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: pk列表卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析pk列表卡片数据时出错: $e');
    }
  }

  void _updatePkUseList() {
    if (pkList == null || pkList.isEmpty) {
      pkUseList = [];
      return;
    }

    pkUseList = [];
    for (int i = 0; i < 3; i++) {
      int index = (currentIndex + i) % pkList.length;
      pkUseList.add(pkList[index]);
    }
  }

  void _handleChangeClick() {
    currentIndex = (currentIndex + 3) % pkList.length;
    _updatePkUseList();
    setState(() {});
  }

  @override
  Widget buildContentView() {
    List<Widget> pkWidgetList = [];
    for (int i = 0; i < pkUseList.length; i++) {
      pkWidgetList.add(_getPKLineWidget(
          pkUseList[i].wmPoiName, pkUseList[i].wmPoiId.toString()));
      if (i < pkUseList.length - 1) {
        pkWidgetList.add(const SizedBox(
          height: 10,
        ));
      }
    }

    if (pkList.length > 3) {
      pkWidgetList.add(Container(
        margin: const EdgeInsets.symmetric(vertical: 12),
        color: const Color(0xFFEEEEEE),
        height: 1,
      ));

      pkWidgetList.add(GestureDetector(
        onTap: () {
          _handleChangeClick();
        },
        child: Container(
          alignment: Alignment.center,
          margin: const EdgeInsets.only(
            bottom: 8,
            left: 10,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 7,
          ),
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(17)),
            color: Color(0xff222222),
          ),
          child: const Text(
            '换一换',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              color: Color(0xffffffff),
            ),
          ),
        ),
      ));
    }
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, children: pkWidgetList),
    );
  }

  Widget _getPKLineWidget(String poiName, String poiId) {
    if (StringUtil.isEmpty(poiName) || StringUtil.isEmpty(poiId)) {
      return const SizedBox.shrink();
    }
    return Container(
      height: 62,
      padding: const EdgeInsets.symmetric(horizontal: 10),
      decoration: const BoxDecoration(
        color: Color(0xFFF8F8F8),
        borderRadius: BorderRadius.all(Radius.circular(9)),
      ),
      child: Row(children: [
        SizedBox(
          width: 200,
          child: Text(
            poiName ?? '',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Expanded(child: Container()),
        GestureDetector(
          onTap: () {
            widget.model?.sendStreamMessage('与门店${poiName}pk',
                addUserMessage: true, extra: {"pkWmPoiId": poiId});
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 7),
            decoration: const BoxDecoration(
                color: Color(0xFF000000),
                borderRadius: BorderRadius.all(Radius.circular(16))),
            child: Row(children: [
              Image(width: 20, height: 18, image: AdvancedNetworkImage(pkIcon)),
              Image(
                  width: 26,
                  height: 18,
                  image: AdvancedNetworkImage(compareIcon)),
            ]),
          ),
        )
      ]),
    );
  }

  // void _handleActionButtonClick() {
  //   HouseKeeperFinanceApi.confirmCustomization(
  //           _businessLine, _sceneType, _requestId)
  //       .then((value) {
  //     if (value) {
  //       setState(() {
  //         _confirmed = true;
  //         MTFToast.showToast(msg: '定制成功');
  //         Navigator.of(context).pop();
  //       });
  //     }
  //   });
  // }
}
