import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/business/business_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

const centerPkIcon =
    'https://p0.meituan.net/waimaiegoods/953a54cea6307713c6ac14d58c2889ef2376.png';
const blueBackground =
    'https://p0.meituan.net/waimaiegoods/1a7f77d17a17da8b4a7fd0c6190644463435.png';
const pinkBackground =
    'https://p0.meituan.net/waimaiegoods/e6d732ef51fa926ed6e54f9038fa3aff3452.png';
const winPic =
    'https://p0.meituan.net/waimaiegoods/c3accd442dc65a0809f6a01390107f7f708.png';
const losePic =
    'https://p0.meituan.net/waimaiegoods/52cc35f05ac2a477894327f589115d34585.png';
const drawPic =
    'https://p0.meituan.net/waimaiegoods/839265c8977e1ad9a0e726d5a0717b8d454.png';

class CompareRes {
  static const int win = 1;
  static const int lose = 2;
  static const int draw = 3;
}

/// 到店自取优惠活动创建
/// 门店新客立减活动创建
class BusinessPKInfoCard extends BaseBlockWidget {
  const BusinessPKInfoCard({
    Key key,
    @required String content,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;

  @override
  _BusinessPKInfoCardState createState() => _BusinessPKInfoCardState();
}

class _BusinessPKInfoCardState
    extends BaseBlockWidgetState<BusinessPKInfoCard> {
  Map<String, dynamic> agentSysData;
  Map<String, dynamic> bizData;
  String poiName;
  String pkPoiName;
  List<PKContentInfo> diffList;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessPKInfoCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;
      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;
      agentSysData = data['agent_sys_data'];
      bizData = data['biz_data'];
      poiName = bizData['poiName'];
      pkPoiName = bizData['pkPoiName'];
      diffList = (bizData['diffList'] as List<dynamic>)
              ?.map((e) => PKContentInfo.fromJson(e as Map<String, dynamic>))
              ?.toList() ??
          [];

      setState(() {});
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': 'pk详情卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': 'pk详情卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: pk详情卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析pk详情卡片数据时出错: $e');
    }
  }

  @override
  Widget buildContentView() {
    if (ArrayUtil.isEmpty(diffList)) {
      return const SizedBox.shrink();
    }
    List<Widget> widgetList =
        diffList.map((e) => _getCompareContentWidget(e)).toList();
    widgetList.insert(0, _getPKPoiWidget(poiName ?? '', pkPoiName ?? ''));
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgetList,
    );
  }

  Widget _getPKPoiWidget(String poiName, String plPoiName) {
    return SizedBox(
      height: 98,
      child: Stack(
        fit: StackFit.expand,
        children: [
          Positioned(
            left: 0,
            child: Image(
              height: 98,
              width: 163,
              image: AdvancedNetworkImage(blueBackground),
            ),
          ),
          Positioned(
            right: 0,
            child: Image(
              height: 98,
              width: 163,
              image: AdvancedNetworkImage(pinkBackground),
            ),
          ),
          Center(
            child: Container(
              height: 30,
              width: 30,
              decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(15))),
            ),
          ),
          Center(
            child: Image(
              height: 27,
              width: 24,
              image: AdvancedNetworkImage(centerPkIcon),
            ),
          ),
          Positioned(
            left: 0,
            child: Container(
              width: 112,
              margin: const EdgeInsets.fromLTRB(16, 16, 0, 20),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '您的门店',
                      style:
                          TextStyle(fontWeight: FontWeight.w900, fontSize: 12),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      poiName ?? '',
                      style: const TextStyle(
                          fontWeight: FontWeight.w600, fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    )
                  ]),
            ),
          ),
          Positioned(
            right: 0,
            child: Container(
              width: 112,
              margin: const EdgeInsets.fromLTRB(0, 16, 16, 20),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '对比门店',
                      style:
                          TextStyle(fontWeight: FontWeight.w900, fontSize: 12),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      plPoiName ?? '',
                      style: const TextStyle(
                          fontWeight: FontWeight.w600, fontSize: 14),
                    )
                  ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _getWinWidget() {
    return Container(
        height: 16,
        width: 16,
        padding: const EdgeInsets.all(2.5),
        decoration: const BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.all(Radius.circular(4))),
        child:
            Image(height: 16, width: 11, image: AdvancedNetworkImage(winPic)));
  }

  Widget _getLoseWidget() {
    return Container(
        height: 16,
        width: 16,
        padding: const EdgeInsets.all(2.5),
        decoration: BoxDecoration(
            border: Border.all(color: Colors.black, width: 1),
            borderRadius: const BorderRadius.all(Radius.circular(4))),
        child:
            Image(height: 16, width: 11, image: AdvancedNetworkImage(losePic)));
  }

  Widget _getDrawWidget() {
    return Container(
        height: 16,
        width: 16,
        padding: const EdgeInsets.all(2.5),
        decoration: const BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.all(Radius.circular(4))),
        child:
            Image(height: 16, width: 11, image: AdvancedNetworkImage(drawPic)));
  }

  Widget _getLeftCompareLine(String dataStr, int res) {
    Widget resWidget;
    if (res == CompareRes.win) {
      resWidget = _getWinWidget();
    } else if (res == CompareRes.lose) {
      resWidget = _getLoseWidget();
    } else {
      resWidget = _getDrawWidget();
    }
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisSize: MainAxisSize.min, // Row 使用最小宽度
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            dataStr.toString() ?? '',
            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
          ),
          const SizedBox(
            width: 2,
          ),
          resWidget
        ],
      ),
    );
  }

  Widget _getCenterCompareLine(String dataStr) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
      child: Text(
        dataStr ?? '',
        style: const TextStyle(fontWeight: FontWeight.w400, fontSize: 14),
      ),
    );
  }

  Widget _getRightCompareLine(int res) {
    Widget resWidget;
    if (res == CompareRes.win) {
      resWidget = _getLoseWidget();
    } else if (res == CompareRes.lose) {
      resWidget = _getWinWidget();
    } else {
      resWidget = _getDrawWidget();
    }
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: resWidget,
    );
  }

  Widget _getCompareContentWidget(PKContentInfo contentInfo) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 21, bottom: 7),
          child: Text(
            contentInfo.diffName ?? '',
            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
          ),
        ),
        Row(
          children: [
            Expanded(
                child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: 20),
              decoration: const BoxDecoration(
                  color: Color(0xFFF5FBFF),
                  borderRadius: BorderRadius.all(Radius.circular(9))),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: contentInfo.diff
                    .map((e) => _getLeftCompareLine(e.indicatorValue, e.pkRes))
                    .toList(),
              ),
            )),
            const SizedBox(width: 6),
            Container(
              decoration: const BoxDecoration(
                  color: Color(0xFFF8F8F8),
                  borderRadius: BorderRadius.all(Radius.circular(9))),
              child: Column(
                children: contentInfo.diff
                    .map((e) => _getCenterCompareLine(e.indicatorName))
                    .toList(),
              ),
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                    color: Color(0xFFFFF8F9),
                    borderRadius: BorderRadius.all(Radius.circular(9))),
                child: Column(
                  children: contentInfo.diff
                      .map((e) => _getRightCompareLine(e.pkRes))
                      .toList(),
                ),
              ),
            )
          ],
        )
      ],
    );
  }
  // void _handleActionButtonClick() {
  //   HouseKeeperFinanceApi.confirmCustomization(
  //           _businessLine, _sceneType, _requestId)
  //       .then((value) {
  //     if (value) {
  //       setState(() {
  //         _confirmed = true;
  //         MTFToast.showToast(msg: '定制成功');
  //         Navigator.of(context).pop();
  //       });
  //     }
  //   });
  // }
}
