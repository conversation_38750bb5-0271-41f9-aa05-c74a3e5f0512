import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/business/business_api.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/business/business_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';

class ActivityType {
  static const int pickUpDiscounts = 0;
  static const int pickUpFullCut = 1;
  static const int newCustomerReduction = 2;
}

class ActionType {
  static const int request = 2;
  static const int scheme = 1;
}

const pickUpActivityIcon =
    'https://p0.meituan.net/waimaiegoods/692933353f4eeb5a97b8b089c5a244b16331.png';
const newCustomerReductionIcon =
    'https://p0.meituan.net/waimaiegoods/c8546c3495bd04b94d7802f92341b3fb4592.png';

/// 到店自取优惠活动创建
/// 门店新客立减活动创建
class BusinessActivityCard extends BaseBlockWidget {
  const BusinessActivityCard({
    Key key,
    @required String content,
    this.type,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;
  final int type;

  @override
  _BusinessActivityCardState createState() => _BusinessActivityCardState();
}

class _BusinessActivityCardState
    extends BaseBlockWidgetState<BusinessActivityCard> {
  Map<String, dynamic> agentSysData;
  Map<String, dynamic> bizData;
  int cardExpireTimestamp = 0;
  bool _confirmed = false;
  bool _clickConfirm = false;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessActivityCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;
      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;
      agentSysData = data['agent_sys_data'];
      cardExpireTimestamp =
          int.tryParse(agentSysData['card_expire_timestamp'].toString()) ?? 0;
      bizData = data['biz_data'];
      Map<String, dynamic> submitDefault = bizData['submit_default'];
      setState(() {});
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': '到店自取专享卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': '到店自取专享卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: 到店自取专享卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析到店自取专享卡片数据时出错: $e');
    }
  }

  bool _checkValid() {
    int currentTimestamp =
        DateTime.now().millisecondsSinceEpoch ~/ 1000; // 获取当前时间戳（秒）
    return cardExpireTimestamp > currentTimestamp;
  }

  String _getIconString() {
    if (widget.type == ActivityType.pickUpDiscounts ||
        widget.type == ActivityType.pickUpFullCut) {
      return pickUpActivityIcon;
    } else if (widget.type == ActivityType.newCustomerReduction) {
      return newCustomerReductionIcon;
    }
    return pickUpActivityIcon;
  }

  Widget _getActionsWidget() {
    List<dynamic> actions = bizData['actions'];
    return Row(
      children:
          actions.map((e) => _getActionButton(e, (e != actions.last))).toList(),
    );
  }

  Color _getButtonBackgroundColor(bool notLast, bool confirmed, bool isValid) {
    if (notLast) {
      return const Color(0xffffffff);
    } else {
      if (confirmed || !isValid) {
        return const Color(0xffffffff);
      }
      return const Color(0xff222222);
    }
  }

  Color _getButtonTextColor(bool notLast, bool confirmed, bool isValid) {
    if (notLast) {
      return const Color(0xff222222);
    } else {
      if (confirmed || !isValid) {
        return const Color(0xffACACAC);
      }
      return const Color(0xffffffff);
    }
  }

  void _createActivity() {
    if (widget.type == ActivityType.pickUpDiscounts) {
      Map<String, dynamic> submitDefault = bizData['submit_default'];
      double discount = double.tryParse(
          submitDefault['remommend_simu_on_site_discounts'].toString());
      HouseKeeperBusinessApi.createActivityDD('discount', 0, 0, discount)
          .then((value) {
        if (value) {
          _confirmed = true;
          _clickConfirm = false;
          setState(() {});
        }
      });
    } else if (widget.type == ActivityType.pickUpFullCut) {
      Map<String, dynamic> submitDefault = bizData['submit_default'];
      double price = double.parse(submitDefault['remommend_fullcut_act_price']
          .toString()); // 按照金额设置时，该值代表门槛
      double decrease = double.parse(
          submitDefault['remommend_fullcut_act_discount'].toString());
      HouseKeeperBusinessApi.createActivityDD('decrease', price, decrease, 0)
          .then((value) {
        if (value) {
          _confirmed = true;
          _clickConfirm = false;
          setState(() {});
        }
      });
    } else if (widget.type == ActivityType.newCustomerReduction) {
      Map<String, dynamic> submitDefault = bizData['submit_default'];
      ActivityNewCustomerInfoModel model =
          ActivityNewCustomerInfoModel.fromJson(submitDefault);
      HouseKeeperBusinessApi.createActivityXK(model.policy.amount)
          .then((value) {
        if (value) {
          _confirmed = true;
          _clickConfirm = false;
          setState(() {});
        }
      });
    }
  }

  Widget _getActionButton(Map<String, dynamic> action, bool notLast) {
    if (notLast && _confirmed) {
      return const SizedBox.shrink();
    }
    return Expanded(
        child: GestureDetector(
      onTap: () {
        if (action['type'] == ActionType.scheme &&
            StringUtil.isNotEmpty(action['url'] ?? '')) {
          RouteUtils.open(action['url']);
        }
        if (action['type'] == ActionType.request) {
          if (!_checkValid() || _confirmed) {
            return;
          }
          _clickConfirm = true;
          setState(() {});
          return;
        }
      },
      child: Container(
        alignment: Alignment.center,
        margin: const EdgeInsets.only(
          bottom: 8,
          left: 10,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 7,
        ),
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: const Color(0xFF999999)),
          borderRadius: const BorderRadius.all(Radius.circular(17)),
          color: _getButtonBackgroundColor(notLast, _confirmed, _checkValid()),
        ),
        child: Text(
          (!notLast && _confirmed) ? '已采纳' : action['label'] ?? '',
          style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              color: _getButtonTextColor(notLast, _confirmed, _checkValid())),
        ),
      ),
    ));
  }

  Widget _buildButtonWidget(String buttonName, Color textColor,
      Color backgroundColor, bool showBorder) {
    return Container(
      alignment: Alignment.center,
      margin: const EdgeInsets.only(
        bottom: 8,
        left: 10,
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 7,
      ),
      decoration: BoxDecoration(
        border: Border.all(width: 0.5, color: const Color(0xFF999999)),
        borderRadius: const BorderRadius.all(Radius.circular(17)),
        color: backgroundColor ?? const Color(0xffffffff),
      ),
      child: Text(
        buttonName ?? '',
        style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            color: textColor ?? const Color(0xff222222)),
      ),
    );
  }

  Widget _getConfirmWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('采纳后活动配置将立即生效，确认要采纳吗？',
            style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
                child: GestureDetector(
              onTap: () {
                _clickConfirm = false;
                setState(() {});
              },
              child: _buildButtonWidget(
                  '取消', const Color(0xff222222), const Color(0xffffffff), true),
            )),
            Expanded(
                child: GestureDetector(
              onTap: () {
                _createActivity();
              },
              child: _buildButtonWidget('确认', const Color(0xffffffff),
                  const Color(0xff222222), false),
            ))
          ],
        )
      ],
    );
  }

  @override
  Widget buildContentView() {
    if (bizData == null || bizData.isEmpty) {
      return const SizedBox.shrink();
    }
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFFF8F8F8),
        borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image(
                width: 24,
                height: 24,
                image: AdvancedNetworkImage(_getIconString()),
              ),
              const SizedBox(
                width: 4,
              ),
              Text(
                bizData['title'] ?? '',
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              )
            ],
          ),
          const SizedBox(
            height: 6,
          ),
          _getTextWidget('力度', bizData['policyInfo'] ?? ''),
          const SizedBox(
            height: 4,
          ),
          _getTextWidget('顾客', bizData['poiUserTypeInfo'] ?? ''),
          const SizedBox(
            height: 4,
          ),
          _getTextWidget('周期', bizData['periodInfo'] ?? ''),
          const SizedBox(
            height: 10,
          ),
          Visibility(visible: _clickConfirm, child: _getConfirmWidget()),
          Visibility(
              visible: !_clickConfirm || _confirmed,
              child: _getActionsWidget()),
        ],
      ),
    );
  }

  Widget _getTextWidget(String title, String content) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: title + '：',
            style: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          TextSpan(
            text: content,
            style: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w400,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}
