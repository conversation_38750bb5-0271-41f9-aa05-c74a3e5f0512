import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/utils/url_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

/// 通用操作卡片
class BusinessActionCard extends BaseBlockWidget {
  const BusinessActionCard({
    Key key,
    @required String content,
  }) : super(key: key, content: content);

  @override
  _BusinessActionCardState createState() => _BusinessActionCardState();
}

class _BusinessActionCardState
    extends BaseBlockWidgetState<BusinessActionCard> {
  // 标题
  String _title = '';
  // 副标题
  String _subtitle = '';
  // 按钮文本
  String _buttonLabel = '';
  // 按钮链接
  String _buttonUrl = '';

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(BusinessActionCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;

      final data = json.decode(widget.content);
      if (data == null || data is! Map) return;

      setState(() {
        // 安全地获取基本属性
        _title = data['title']?.toString() ?? '';
        _subtitle = data['subtitle']?.toString() ?? '';

        // 安全地获取嵌套的action属性
        final action = data['action'];
        if (action != null && action is Map) {
          _buttonLabel = action['label']?.toString() ?? '';
          _buttonUrl = action['url']?.toString() ?? '';
        } else {
          _buttonLabel = '';
          _buttonUrl = '';
        }
      });
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': '操作卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': '操作卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: 操作卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析操作卡片数据时出错: $e');
    }
  }

  @override
  Widget buildContentView() {
    FlutterLx.moudleView(
        '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_zwuykor7_mv',
        val: {'usertext': '', 'type': '通用卡片' ?? '', 'task_name': _title ?? ''});

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F9FC),
        borderRadius: BorderRadius.circular(8),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 6,
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  _title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                    height: 1.3,
                  ),
                ),
                Visibility(
                  visible: _subtitle.isNotEmpty,
                  child: Container(
                    margin: const EdgeInsets.only(top: 8),
                    child: Text(
                      _subtitle,
                      style: const TextStyle(
                        fontSize: 11,
                        color: Color(0xFF666666),
                        height: 1.2,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Visibility(
            visible: _buttonLabel.isNotEmpty && _buttonUrl.isNotEmpty,
            child: Row(
              children: [
                const SizedBox(width: 12),
                _buildActionButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton() {
    return Container(
      height: 32,
      constraints: const BoxConstraints(
        minWidth: 72,
      ),
      child: TextButton(
        onPressed: () {
          if (_buttonUrl.isNotEmpty) {
            FlutterLx.moudleClick(
                '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_zwuykor7_mc',
                val: {
                  'usertext': '',
                  'type': '通用卡片' ?? '',
                  'task_name': _title ?? ''
                });

            try {
              if (isUrlCanOpen(_buttonUrl)) {
                RouteUtils.open(_buttonUrl);
              } else {
                debugPrint('不支持的链接: $_buttonUrl');
              }
            } catch (e) {
              debugPrint('处理链接点击时出错: $e');
            }
          }
        },
        style: TextButton.styleFrom(
          backgroundColor: const Color(0xFF333333),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Text(
          _buttonLabel,
          style: const TextStyle(
            fontSize: 13,
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
