import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_action_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_competitor_comparison_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_finance_order_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_growth_plan_old.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_growth_plan_new.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_activity_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_pk_info_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_pk_list_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_summary_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_order_task_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/business/business_requisition_weight_card.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/comment/change_auto_reply_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/goods/change_goods_status_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/comment/chose_comment_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/comment/comment_reply.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/goods/goods_recommend_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/goods/goods_select_background_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/goods/combo_puzzle_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/unknown_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';

/// 自定义块工厂类
/// 集中管理各种自定义块的创建逻辑
class CustomBlockFactory {
  /// 创建自定义块小部件
  static Widget createCustomBlockWidget(
      String type, String content, HouseKeeperMessagePageModel model) {
    // 确保类型不为空
    type = (type ?? '').isEmpty ? 'info' : type;
    content = content ?? ''; // 允许内容为空，由组件内部处理流式加载状态

    // 根据标签类型选择不同的处理方式
    switch (type) {
      /////////////////////////////////////////////
      /// 商品
      /////////////////////////////////////////////
      case 'searchGoods':
        return ChangeGoodsStatusWidget(GoodsStatusType.search,
            content: content);
      case 'changePrice':
        return ChangeGoodsStatusWidget(GoodsStatusType.price, content: content);
      case 'changeStock':
        return ChangeGoodsStatusWidget(GoodsStatusType.stock, content: content);
      case 'changeSellDown':
        return ChangeGoodsStatusWidget(GoodsStatusType.down, content: content);
      case 'changeSellUp':
        return ChangeGoodsStatusWidget(GoodsStatusType.up, content: content);
      /////////////////////////////////////////////
      /// 背景图
      /////////////////////////////////////////////
      case 'batchBackground':
        return GoodsBackgroundManagerWidget(content: content, model: model);
      case 'comoPuzzle':
        return ComboPuzzleWidget(content: content, model: model);
      /////////////////////////////////////////////
      /// 评论
      /////////////////////////////////////////////
      case "commentChose":
        return CommentChoseBlockWidget(model: model, content: content);
      case "commentReply":
        return CommentCardReplyBlockWidget(content: content);
      /////////////////////////////////////////////
      /// 经分
      /////////////////////////////////////////////
      case "waimaiGrowthPlan":
        return BusinessGrowthPlanBlockWidget(content: content, model: model);
      case "wm_new_poi_waimaiGrowthPlan":
        return BusinessGrowthPlanList(content: content, model: model);
      case "wm_biz_new_poi_order_task":
        return BusinessOrderTaskCard(content: content);
      case "wm_biz_new_poi_summary_diagnosis":
        return BusinessSummaryCard(content: content);
      case "competitorComparison":
        return BusinessCompetitorComparisonCard(content: content);
      case "wmAiRequisitionWeight":
        return BusinessRequisitionWeightCard(content: content, model: model);
      case "wm_biz_finance_order":
        return BusinessFinanceOrderCard(content: content, model: model);
      case "wm_activity_for_pickup_for_discounts":
        return BusinessActivityCard(
          content: content,
          model: model,
          type: ActivityType.pickUpDiscounts,
        );
      case "wm_activity_for_pickup_for_fullcut":
        return BusinessActivityCard(
            content: content, model: model, type: ActivityType.pickUpFullCut);
      case "wm_activity_for_new_customer_reduction":
        return BusinessActivityCard(
            content: content,
            model: model,
            type: ActivityType.newCustomerReduction);
      case "wm_poi_biz_data_pk_list":
        return BusinessPKListCard(content: content, model: model);
      case "wm_poi_biz_data_pk_info":
        return BusinessPKInfoCard(content: content, model: model);
      case "wm_product_of_single_item_recommend":
        return RecommendGoodsCard(
          content: content,
          model: model,
          type: RecommendGoodsType.singleItem,
        );
      case "wm_product_of_hot_selling":
        return RecommendGoodsCard(
          content: content,
          model: model,
          type: RecommendGoodsType.hotSelling,
        );
      case "wm_product_of_package_recommend":
        return RecommendGoodsCard(
          content: content,
          model: model,
          type: RecommendGoodsType.packageRecommend,
        );

      case "wm_comment_auto_change":
        return CommentChangeAutoReplyWidget(
          content: content,
          model: model,
        );

      /////////////////////////////////////////////
      /// 通用
      /////////////////////////////////////////////
      case "commonAction":
        return BusinessActionCard(content: content);
      default:
        // 线上不展示类型错误弹窗
        if (!kReleaseMode) {
          return UnknownBlockWidget(type: type, content: content);
        } else {
          return const SizedBox.shrink();
        }
    }
  }
}
