import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/goods_api.dart';
import 'dart:convert';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/goods_tools.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

/// 商品状态类型
enum GoodsStatusType { up, down, price, stock, search }

/// 商品数据模型
class SpuData {
  final String id;
  final String name;
  final double price;
  final int stock;
  final String description;
  final int sellStatus;
  final String picUrl;

  SpuData({
    this.id,
    this.name,
    this.price,
    this.stock,
    this.description,
    this.sellStatus,
    this.picUrl,
  });

  factory SpuData.fromJson(Map<String, dynamic> json) {
    return SpuData(
      id: json['id']?.toString(),
      name: json['name']?.toString() ?? '',
      price: (json['price'] as num)?.toDouble() ?? 0.0,
      stock: json['stock'] as int ?? 0,
      description: json['description']?.toString() ?? '',
      sellStatus: json['sellStatus'] as int ?? 0,
      picUrl: json['picUrl']?.toString() ?? '',
    );
  }
}

/// 请求数据模型
class RequestData {
  final String to;
  final List<SpuData> spus;

  RequestData({
    this.to,
    this.spus,
  });

  factory RequestData.fromJson(Map<String, dynamic> json) {
    return RequestData(
      to: json['to']?.toString() ?? '',
      spus: (json['spus'] as List<dynamic>)
              ?.map((e) => SpuData.fromJson(e as Map<String, dynamic>))
              ?.toList() ??
          [],
    );
  }
}

class ChangeGoodsStatusWidget extends BaseBlockWidget {
  final GoodsStatusType changeType;

  const ChangeGoodsStatusWidget(
    this.changeType, {
    Key key,
    @required String content,
  }) : super(key: key, content: content);

  @override
  _ChangeGoodsStatusWidgetState createState() =>
      _ChangeGoodsStatusWidgetState();
}

class _ChangeGoodsStatusWidgetState
    extends BaseBlockWidgetState<ChangeGoodsStatusWidget> {
  List<SpuData> _spuList = [];
  final Set<int> _selectedSpus = {};
  bool _canConfirm = true;
  bool _isLoading = false;
  RequestData _requestData;
  Map<int, String> _spuStatusMessages = {};
  bool _isExpanded = false;

  // 预计算的状态
  int _selectedCount = 0;
  String _confirmButtonText = '确定';

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(ChangeGoodsStatusWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      Map<String, dynamic> jsonData = json.decode(widget.content);
      _requestData = RequestData.fromJson(jsonData);
      _spuList = _requestData.spus;
      _updateState();
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': '商品状态修改卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': '商品状态修改卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: 商品状态修改卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析商品JSON数据失败: $e');
      _spuList = [];
    }
  }

  void _updateState() {
    _selectedCount = _selectedSpus.length;
    _confirmButtonText = _isLoading ? "处理中..." : (_canConfirm ? "确定" : "已完成");
    setState(() {});
  }

  void _toggleSpuSelection(int spuId) {
    if (!_canConfirm || _isLoading) return;
    setState(() {
      if (_selectedSpus.contains(spuId)) {
        _selectedSpus.remove(spuId);
      } else {
        _selectedSpus.add(spuId);
      }
      _updateState();
    });
  }

  void _clearSelection() {
    if (!_canConfirm || _isLoading) return;
    setState(() {
      _selectedSpus.clear();
      _updateState();
    });
  }

  Widget _buildSpuCard(SpuData spu, int index) {
    final spuId = int.tryParse(spu.id) ?? 0;
    final bool isSelected = _selectedSpus.contains(spuId);
    final String statusMessage = _spuStatusMessages[spuId] ?? '';
    final bool isSuccess = statusMessage.contains('成功');

    // 根据不同的操作类型获取状态变化文本
    String fromStatus = '';
    String toStatus = '';
    switch (widget.changeType) {
      case GoodsStatusType.up:
        fromStatus = '已下架';
        toStatus = '上架';
        break;
      case GoodsStatusType.down:
        fromStatus = '已上架';
        toStatus = '下架';
        break;
      case GoodsStatusType.price:
        fromStatus = '¥${spu.price}';
        toStatus = '¥${getTo()}';
        break;
      case GoodsStatusType.stock:
        fromStatus = '${spu.stock == -1 ? "无限" : spu.stock}';
        toStatus = ' ${getTo() == "-1" ? "无限" : getTo()}';
        break;
      default:
        break;
    }

    return GestureDetector(
      onTap: _isLoading ? null : () => _toggleSpuSelection(spuId),
      child: Opacity(
        opacity: _isLoading ? 0.6 : 1.0,
        child: Container(
            padding: const EdgeInsets.all(4.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 22),
                  child: Image(
                    height: 16,
                    width: 16,
                    image: AdvancedNetworkImage(isSelected
                        ? 'https://p0.meituan.net/waimaieassistant/cd204c3bb611c814af99c0e777b86851798.png'
                        : 'https://p0.meituan.net/waimaieassistant/603cfa8a58aae3b2f26762dfd02e0d93986.png'),
                  ),
                ),
                const SizedBox(
                  width: 12,
                ),
                Container(
                  height: 60,
                  width: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    image: DecorationImage(
                      image: AdvancedNetworkImage(
                        spu.picUrl?.isNotEmpty == true
                            ? spu.picUrl
                            : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/b6ce73ad02219043/img_default-20250324.png',
                        useDiskCache: true,
                      ),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(
                  width: 12,
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      spu.name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        color: Color(0xFF222222),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Visibility(
                      visible: fromStatus.isNotEmpty && toStatus.isNotEmpty,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            fromStatus,
                            style: const TextStyle(
                              color: Color(0xFF999999),
                              fontSize: 15,
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 2),
                            child: Icon(
                              Icons.arrow_forward,
                              size: 10,
                              color: Color(0xFFFF0000),
                            ),
                          ),
                          Text(
                            toStatus,
                            style: const TextStyle(
                              color: Color(0xFFFF0000),
                              fontSize: 15,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Visibility(
                        visible: statusMessage.isNotEmpty,
                        child: Container(
                          constraints: const BoxConstraints(maxWidth: 200),
                          margin: const EdgeInsets.only(top: 2),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 2),
                          decoration: BoxDecoration(
                            color: isSuccess
                                ? const Color(0xFFE8F5E9)
                                : const Color(0xFFFFEBEE),
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          child: Text(
                            statusMessage,
                            textAlign: TextAlign.left,
                            style: TextStyle(
                              color: isSuccess
                                  ? const Color(0xFF11AF50)
                                  : const Color(0xFFF11F59),
                              fontSize: 9,
                              fontWeight: FontWeight.w400,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )),
                  ],
                ),
              ],
            )),
      ),
    );
  }

  // 获取选中的商品列表
  List<SpuData> getSelectedGoods() {
    return _spuList.where((spu) {
      final spuId = int.tryParse(spu.id) ?? 0;
      return _selectedSpus.contains(spuId);
    }).toList();
  }

  String getTo() {
    return _requestData.to;
  }

  Widget _buildButton(
      String text, VoidCallback onTap, bool isEnabled, bool isSelected) {
    final bool finalEnabled = isEnabled && !_isLoading;
    return GestureDetector(
      onTap: finalEnabled ? onTap : null,
      child: Container(
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Visibility(
                visible: (_isLoading && text == "处理中..."),
                child: Container(
                  width: 14,
                  height: 14,
                  margin: const EdgeInsets.only(right: 8),
                  child: const CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )),
            Text(
              text,
              style: TextStyle(
                color: !finalEnabled
                    ? const Color(0xFF999999)
                    : (isSelected ? Colors.white : Colors.black),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        height: 40,
        width: 156,
        decoration: BoxDecoration(
          color: !finalEnabled
              ? const Color(0xFFF5F5F5)
              : (isSelected
                  ? const Color(0xFF050505)
                  : const Color(0xFFF5F5F5)),
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    );
  }

  @override
  Widget buildContentView() {
    // 计算要显示的商品列表
    final displayedSpuList = _isExpanded
        ? _spuList
        : (_spuList.length > 3 ? _spuList.sublist(0, 3) : _spuList);

    return Container(
        margin: const EdgeInsets.only(top: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            // 纵向列表展示商品
            Column(
              mainAxisSize: MainAxisSize.min,
              children: displayedSpuList
                  .map((spu) => Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: _buildSpuCard(spu, _spuList.indexOf(spu)),
                      ))
                  .toList(),
            ),

            // 展开/收起按钮
            Visibility(
              visible: _spuList.length > 3,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.only(top: 6, bottom: 16),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Text(
                        _isExpanded ? '收起' : '展开更多',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.black.withOpacity(0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        _isExpanded
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        size: 16,
                        color: Colors.black.withOpacity(0.8),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                _buildButton("取消", _clearSelection, _canConfirm, false),
                const SizedBox(width: 12),
                _buildButton(
                  _confirmButtonText,
                  _onConfirm,
                  _canConfirm && _selectedSpus.isNotEmpty,
                  _canConfirm && _selectedSpus.isNotEmpty,
                ),
              ],
            ),
          ],
        ));
  }

  void _onConfirm() async {
    if (!_canConfirm || _selectedSpus.isEmpty || _isLoading) return;

    setState(() {
      _isLoading = true;
      _updateState();
    });

    List<SpuData> selectedGoods = getSelectedGoods();

    try {
      switch (widget.changeType) {
        case GoodsStatusType.up:
          await _handleSellStatusChange(selectedGoods, GoodsStatusType.up);
          break;
        case GoodsStatusType.down:
          await _handleSellStatusChange(selectedGoods, GoodsStatusType.down);
          break;
        case GoodsStatusType.price:
          await _handlePriceChange();
          break;
        case GoodsStatusType.stock:
          await _handleStockChange(selectedGoods);
          break;
        case GoodsStatusType.search:
          // 处理搜索逻辑
          break;
      }
    } finally {
      setState(() {
        _isLoading = false;
        _updateState();
      });
    }
  }

  Future<void> _handleSellStatusChange(
      List<SpuData> selectedGoods, GoodsStatusType type) async {
    // 1. 获取并验证选中的商品
    final validGoods = selectedGoods
        .map((spu) => MapEntry(int.parse(spu.id), spu.name))
        .where((entry) => entry.key > 0)
        .toList();

    if (validGoods.isEmpty) {
      _updateStateAndShowToast(false, '', '商品ID格式错误');
      return;
    }

    // 2. 设置上下架状态
    final sellStatus = (type == GoodsStatusType.down) ? 1 : 0;
    final actionName = (type == GoodsStatusType.down) ? '下架' : '上架';

    // 3. 初始化状态信息
    setState(() {
      _spuStatusMessages = Map.fromIterables(
        selectedGoods.map((e) => int.parse(e.id)),
        List.filled(selectedGoods.length, ''),
      );
    });

    // 4. 批量处理商品状态修改
    final results = await Future.wait(
      validGoods.map((entry) =>
          _modifySpuStatus(entry.key, entry.value, sellStatus, actionName)),
    );

    // 5. 汇总结果
    final successGoods = <String>[];
    final failedGoods = <String>[];

    for (var result in results) {
      if (result.success) {
        successGoods.add(result.spuName);
      } else {
        failedGoods.add('${result.spuName}: ${result.message}');
      }
    }

    // 6. 更新最终状态
    final message = _buildResultMessage(successGoods, failedGoods);
    _updateStateAndShowToast(
      failedGoods.isEmpty,
      message,
      failedGoods.isEmpty ? '$actionName成功' : '部分商品$actionName失败',
    );
  }

  Future<_PriceModificationResult> _modifySpuStatus(
    int spuId,
    String spuName,
    int sellStatus,
    String actionName,
  ) async {
    final response =
        await HouseKeeperGoodsApi.updateSellingStatus(sellStatus, [spuId]);

    final success = response?.code == 0;
    final statusMessage = success ? '$actionName成功' : response?.msg ?? '系统错误';

    _updateSpuStatus(spuId, statusMessage);

    return _PriceModificationResult(
      spuName: spuName,
      success: success,
      message: statusMessage,
    );
  }

  Future<void> _handleStockChange(List<SpuData> selectedGoods) async {
    // 1. 获取并验证选中的商品
    final validGoods = selectedGoods
        .map((spu) => MapEntry(int.parse(spu.id), spu.name))
        .where((entry) => entry.key > 0)
        .toList();

    if (validGoods.isEmpty) {
      _updateStateAndShowToast(false, '', '商品ID格式错误');
      return;
    }

    // 2. 解析库存值
    num stock;
    if (getTo() == '无限') {
      stock = -1;
    } else {
      stock = num.tryParse(getTo()) ?? 0;
    }

    // 3. 初始化状态信息
    setState(() {
      _spuStatusMessages = Map.fromIterables(
        _spuList.map((e) => int.tryParse(e.id) ?? 0),
        List.filled(_spuList.length, ''),
      );
    });

    // 4. 批量处理商品库存修改
    final results = await Future.wait(
      validGoods.map((entry) => _modifySpuStock(entry.key, entry.value, stock)),
    );

    // 5. 汇总结果
    final successGoods = <String>[];
    final failedGoods = <String>[];

    for (var result in results) {
      if (result.success) {
        successGoods.add(result.spuName);
      } else {
        failedGoods.add('${result.spuName}: ${result.message}');
      }
    }

    // 6. 更新最终状态
    final message = _buildResultMessage(successGoods, failedGoods);
    _updateStateAndShowToast(
      failedGoods.isEmpty,
      message,
      failedGoods.isEmpty ? '库存修改成功' : '部分商品库存修改失败',
    );
  }

  Future<_PriceModificationResult> _modifySpuStock(
    int spuId,
    String spuName,
    num stock,
  ) async {
    final success =
        await HouseKeeperGoodsTools.batchModifyGoodsStock([spuId], stock);

    final statusMessage = success ? '库存修改成功' : '库存修改失败';
    _updateSpuStatus(spuId, statusMessage);

    return _PriceModificationResult(
      spuName: spuName,
      success: success,
      message: statusMessage,
    );
  }

  Future<void> _handlePriceChange() async {
    // 1. 获取并验证选中的商品
    final validGoods = _spuList
        .where((spu) => _selectedSpus.contains(int.tryParse(spu.id)))
        .map((spu) => MapEntry(int.parse(spu.id), spu.name))
        .toList();

    if (validGoods.isEmpty) {
      _updateStateAndShowToast(false, '', '请选择需要修改价格的商品');
      return;
    }

    // 2. 验证目标价格
    final toPrice = num.tryParse(getTo()) ?? 0;
    if (toPrice <= 0) {
      _updateStateAndShowToast(false, '', '价格必须大于0');
      return;
    }

    // 3. 初始化状态信息
    setState(() {
      _spuStatusMessages = Map.fromIterables(
        _spuList.map((e) => int.tryParse(e.id) ?? 0),
        List.filled(_spuList.length, ''),
      );
    });

    // 4. 批量处理商品价格修改
    final results = await Future.wait(
      validGoods
          .map((entry) => _modifySpuPrice(entry.key, entry.value, toPrice)),
    );

    // 5. 汇总结果
    final successGoods = <String>[];
    final failedGoods = <String>[];

    for (var result in results) {
      if (result.success) {
        successGoods.add(result.spuName);
      } else {
        failedGoods.add('${result.spuName}: ${result.message}');
      }
    }

    // 6. 更新最终状态
    final message = _buildResultMessage(successGoods, failedGoods);
    _updateStateAndShowToast(
      failedGoods.isEmpty,
      message,
      failedGoods.isEmpty ? '修改成功' : '部分商品修改失败',
    );
  }

  Future<_PriceModificationResult> _modifySpuPrice(
    int spuId,
    String spuName,
    num targetPrice,
  ) async {
    // 1. 获取SPU信息
    final spuVo = await HouseKeeperGoodsApi.fetchSpu(spuId);
    if (spuVo == null ||
        spuVo.wmProductSkuVos == null ||
        spuVo.wmProductSkuVos.isEmpty) {
      _updateSpuStatus(spuId, '获取商品SKU信息失败');
      return _PriceModificationResult(
        spuName: spuName,
        success: false,
        message: '获取商品SKU信息失败',
      );
    }

    // 2. 修改所有SKU价格
    final skuResults = await Future.wait(
      spuVo.wmProductSkuVos.map(
        (sku) => HouseKeeperGoodsTools.modifyGoodsPrice(sku.id, targetPrice),
      ),
    );

    // 3. 处理结果
    final allSuccess = skuResults.every((result) => result.code == 0);
    final errorMsgs = skuResults
        .where((result) =>
            result.code != 0 && result.msg != null && result.msg.isNotEmpty)
        .map((result) => result.msg)
        .toList();

    final statusMessage = allSuccess
        ? '价格修改成功'
        : errorMsgs.isNotEmpty
            ? errorMsgs.join('\n')
            : '部分SKU修改失败';

    _updateSpuStatus(spuId, statusMessage);

    return _PriceModificationResult(
      spuName: spuName,
      success: allSuccess,
      message: allSuccess ? '修改成功' : statusMessage,
    );
  }

  void _updateSpuStatus(int spuId, String message) {
    setState(() {
      _spuStatusMessages[spuId] = message;
    });
  }

  String _buildResultMessage(
      List<String> successGoods, List<String> failedGoods) {
    final buffer = StringBuffer();

    if (successGoods.isNotEmpty) {
      buffer.writeln('成功修改商品：${successGoods.join('、')}');
    }

    if (failedGoods.isNotEmpty) {
      if (buffer.isNotEmpty) buffer.writeln();
      buffer.writeln('修改失败商品：');
      buffer.writeAll(failedGoods, '\n');
    }

    return buffer.toString().trim();
  }

  void _updateStateAndShowToast(
      bool success, String successMsg, String failureMsg) {
    if (success) {
      _canConfirm = false;
      setState(() {});
      MTFToast.showToast(msg: successMsg);
    } else {
      MTFToast.showToast(msg: failureMsg);
    }
  }
}

class _PriceModificationResult {
  String spuName;
  bool success;
  String message;

  _PriceModificationResult({
    this.spuName = '',
    this.success = false,
    this.message = '',
  });
}
