import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_cat/flutter_cat.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/agent/tools/goods/goods_recommend_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/custom_block/base_block_widget.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

class RecommendGoodsType {
  static const int singleItem = 0;
  static const int hotSelling = 1;
  static const int packageRecommend = 2;
}

/// 上新推荐
/// 套餐搭配
class RecommendGoodsCard extends BaseBlockWidget {
  const RecommendGoodsCard({
    Key key,
    @required String content,
    this.type,
    this.model,
  }) : super(key: key, content: content);

  final HouseKeeperMessagePageModel model;
  final int type;

  @override
  _RecommendGoodsCardState createState() => _RecommendGoodsCardState();
}

class _RecommendGoodsCardState
    extends BaseBlockWidgetState<RecommendGoodsCard> {
  List<SingleItemRecommendVo> _singleItemRecommendVoList;
  List<HotSellingVo> _hotSellingVoList;
  List<dynamic> _packageRecommendVoList;
  int currentIndex = 0;
  bool canChange = false;

  @override
  void initState() {
    super.initState();
    _parseContent();
  }

  @override
  void didUpdateWidget(RecommendGoodsCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != oldWidget.content) {
      _parseContent();
    }
  }

  void _parseContent() {
    try {
      if (widget.content == null || widget.content.isEmpty) return;
      final data = json.decode(widget.content);
      if (data == null || data is! List) return;
      if (widget.type == RecommendGoodsType.singleItem) {
        _singleItemRecommendVoList = (data as List)
            .map((item) => SingleItemRecommendVo.fromJson(item))
            .toList()
            .cast<SingleItemRecommendVo>();
        canChange = _singleItemRecommendVoList.length > 3;
      } else if (widget.type == RecommendGoodsType.hotSelling) {
        _hotSellingVoList = (data as List)
            .map((item) => HotSellingVo.fromJson(item))
            .toList()
            .cast<HotSellingVo>();
        canChange = _hotSellingVoList.length > 3;
      } else if (widget.type == RecommendGoodsType.packageRecommend) {
        _packageRecommendVoList = (data as List)
            .map((item) => PackageRecommendVo.fromJson(item))
            .toList()
            .cast<PackageRecommendVo>();
        canChange = _packageRecommendVoList.length > 3;
      }
      setState(() {});
    } catch (e) {
      Map<String, List<double>> values = {
        'shop_assistant_card_parse': [0]
      };
      Map<String, String> tags = {
        'cardName': '商品机会洞察卡片',
      };
      FlutterLx.moudleView(
          '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_ussj3nnf_mv',
          val: {'cardName': '商品机会洞察卡片', 'content': widget?.content ?? ''});
      KNB.sendLog(
          text:
              'ShopAssistantCardParse cardName: 商品机会洞察卡片  error: ${e.toString()} content: ${widget?.content ?? ''}');
      FlutterCat.reportCustomField(values: values, tags: tags);
      debugPrint('解析商品机会洞察卡片数据时出错: $e');
    }
  }

  Widget _buildGoodCardWidget() {
    if (ArrayUtil.isEmpty(_singleItemRecommendVoList) &&
        ArrayUtil.isEmpty(_hotSellingVoList) &&
        ArrayUtil.isEmpty(_packageRecommendVoList)) {
      return const SizedBox.shrink();
    }
    if (widget.type == RecommendGoodsType.singleItem) {
      List<SingleItemRecommendVo> useList = [];
      for (int i = 0; i < 3; i++) {
        int index = (currentIndex + i) % _singleItemRecommendVoList.length;
        useList.add(_singleItemRecommendVoList[index]);
      }
      return Column(
          children: useList.map((e) => _buildSingleItemRecommend(e)).toList());
    } else if (widget.type == RecommendGoodsType.hotSelling) {
      List<HotSellingVo> useList = [];
      for (int i = 0; i < 3; i++) {
        int index = (currentIndex + i) % _hotSellingVoList.length;
        useList.add(_hotSellingVoList[index]);
      }
      return Column(children: useList.map((e) => _buildHotSelling(e)).toList());
    } else if (widget.type == RecommendGoodsType.packageRecommend) {
      List<PackageRecommendVo> useList = [];
      for (int i = 0; i < 3; i++) {
        int index = (currentIndex + i) % _packageRecommendVoList.length;
        useList.add(_packageRecommendVoList[index]);
      }
      return Column(
          children: useList.map((e) => _buildPackageRecommend(e)).toList());
    }
    return Container();
  }

  Widget _buildSingleItemRecommend(
      SingleItemRecommendVo singleItemRecommendVo) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image(
                width: 60,
                height: 60,
                image: AdvancedNetworkImage(singleItemRecommendVo.foodPicUrl),
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  singleItemRecommendVo.name ?? '',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(
                  height: 6,
                ),
                _buildTag(singleItemRecommendVo.recSourceReasons.first),
                const SizedBox(
                  height: 6,
                ),
                Text(
                  "城市月销${singleItemRecommendVo.ordNum}  平台月搜 ${singleItemRecommendVo.localQuery > 99999 ? '99999+' : singleItemRecommendVo.localQuery}\n平均售价${singleItemRecommendVo.avgPrice}",
                  style: const TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: Color(0xFF666666)),
                ),
              ],
            )
          ],
        ),
        Row(
          children: [Expanded(child: Container()), _getActionButton()],
        ),
      ],
    );
  }

  Widget _buildHotSelling(HotSellingVo hotSellingVo) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image(
                width: 60,
                height: 60,
                image: AdvancedNetworkImage(hotSellingVo.commodityPic),
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 230,
                  child: Text(
                    hotSellingVo.name ?? '',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(
                  height: 6,
                ),
                Text(
                  "销量${hotSellingVo.cnt}",
                  style: const TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: Color(0xFF666666)),
                ),
              ],
            )
          ],
        ),
        Row(
          children: [Expanded(child: Container()), _getActionButton()],
        )
      ],
    );
  }

  Widget _buildPackageRecommend(PackageRecommendVo packageRecommendVo) {
    if (packageRecommendVo == null) {
      return const SizedBox.shrink();
    }
    String picUrl = packageRecommendVo.type == RecLabelCodeType.storeCombo
        ? packageRecommendVo?.recommendComboInfo?.packageFoodPicUrl ?? ''
        : packageRecommendVo?.recommendComboInfoNew?.foodPicUrl ?? '';
    String name = packageRecommendVo.type == RecLabelCodeType.storeCombo
        ? packageRecommendVo?.recommendComboInfo?.packageFoodName ?? ''
        : packageRecommendVo?.recommendComboInfoNew?.name ?? '';
    String priceText = packageRecommendVo.type == RecLabelCodeType.storeCombo
        ? '套餐内单品原价总和:' +
                packageRecommendVo
                    ?.recommendComboInfo?.spuComboPriceModelVo?.totalPrice
                    .toString() ??
            ''
        : '平均售价¥' +
                packageRecommendVo?.recommendComboInfoNew?.avgPrice
                    .toString() ??
            '';

    String tagText = packageRecommendVo.type == RecLabelCodeType.storeCombo
        ? ''
        : '包含${packageRecommendVo.recommendComboInfoNew.getSkuIdsNum()}个商品';
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image(
                width: 60,
                height: 60,
                image: AdvancedNetworkImage(picUrl),
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 230,
                  child: Text(
                    name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(
                  height: 6,
                ),
                _buildPackageRecommendTag(tagText),
                Text(
                  priceText,
                  style: const TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: Color(0xFF666666)),
                ),
              ],
            )
          ],
        ),
        Row(
          children: [Expanded(child: Container()), _getActionButton()],
        )
      ],
    );
  }

  Widget _buildPackageRecommendTag(String tagText) {
    if (StringUtil.isEmpty(tagText)) {
      return const SizedBox.shrink();
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFFFC399), width: 1),
        color: const Color(0xFFFFF5F6),
        borderRadius: const BorderRadius.all(Radius.circular(3)),
      ),
      child: Text(
        tagText,
        style: const TextStyle(
          color: Color(0xFFFF6A00),
          fontSize: 11,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildTag(RecSourceReasons recSourceReasons) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFFFA3AB), width: 1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: const BoxDecoration(
              color: Color(0xFFFFF5F6),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(3),
                bottomLeft: Radius.circular(3),
              ),
            ),
            child: Text(
              recSourceReasons.name,
              style: const TextStyle(
                color: Color(0xFFFF192D),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(
            width: 1,
            height: 22,
            child: Container(
              color: const Color(0xFFFFA3AB),
            ),
          ),
          Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(3),
                bottomRight: Radius.circular(3),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            child: Text(
              recSourceReasons.reason,
              style: const TextStyle(
                color: Color(0xFFFF3B30),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _getActionButton() {
    return GestureDetector(
      onTap: () {
        if (widget.type == RecommendGoodsType.singleItem ||
            widget.type == RecommendGoodsType.hotSelling) {
          // 跳转快速新建商品页
          Map<String, dynamic> params = {
            'flap_id': 'edit_goods',
            'flap_entry': 'EditGoodsPage',
            'moduleName': 'waimai_e_flutter_goods',
            'actionType': 0
          };
          final url = SchemeUrls.flutterPageUrl('flap',
              params: params, channel: 'waimai_e_flutter_goods');
          RouteUtils.open(url);
        } else if (widget.type == RecommendGoodsType.packageRecommend) {
          // 跳转新建套餐页
          Map<String, dynamic> params = {
            'flap_id': 'goods_combo',
            'flap_entry': 'ComboMainPage',
            'moduleName': 'waimai_e_flutter_goods',
            'optionIndex': 0
          };
          final url = SchemeUrls.flutterPageUrl('flap',
              params: params, channel: 'waimai_e_flutter_goods');
          RouteUtils.open(url);
        }
      },
      child: Container(
        alignment: Alignment.center,
        margin: const EdgeInsets.only(bottom: 10),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 7,
        ),
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(17)),
            color: Color(0xFF222222)),
        child: const Text(
          '立即新建',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            color: Color(0xFFFFFFFF),
          ),
        ),
      ),
    );
  }

  void _handleChangeClick() {
    if (widget.type == RecommendGoodsType.singleItem) {
      currentIndex = (currentIndex + 3) % _singleItemRecommendVoList.length;
    } else if (widget.type == RecommendGoodsType.hotSelling) {
      currentIndex = (currentIndex + 3) % _hotSellingVoList.length;
    } else if (widget.type == RecommendGoodsType.packageRecommend) {
      currentIndex = (currentIndex + 3) % _packageRecommendVoList.length;
    }
    setState(() {});
  }

  Widget _getChangeButton() {
    if (!canChange) {
      return const SizedBox.shrink();
    }
    return Column(
      children: [
        Container(
          height: 1,
          color: const Color(0xFFEEEEEE),
        ),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: () {
            _handleChangeClick();
          },
          child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
            Image(
              width: 18,
              height: 18,
              image: AdvancedNetworkImage(
                  'https://p0.meituan.net/waimaieassistant/c87e72eb48c066bb1c5386d297b0883e1830.png'),
            ),
            const SizedBox(width: 3),
            const Text('换一换',
                style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF222222),
                    fontWeight: FontWeight.w400))
          ]),
        )
      ],
    );
  }

  @override
  Widget buildContentView() {
    return Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(10)),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [_buildGoodCardWidget(), _getChangeButton()],
        ));
  }
}
