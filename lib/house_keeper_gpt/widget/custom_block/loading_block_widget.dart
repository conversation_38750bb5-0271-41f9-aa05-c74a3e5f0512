import 'package:flutter/material.dart';

/// 自定义块加载占位组件
class LoadingBlockWidget extends StatelessWidget {
  final String blockType;
  final bool isVisible;

  const LoadingBlockWidget({
    Key key,
    @required this.blockType,
    this.isVisible = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 1.5,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[200]),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '正在渲染卡片...',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[400],
              fontWeight: FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
