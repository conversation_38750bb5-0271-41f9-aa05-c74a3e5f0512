import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/goods_spu_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/goods_group_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/goods_select_group_list_item.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/house_keeper_goods_single_poi_select_product_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/single_poi_goods_select_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_goods_select/single_poi_goods_select_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_goods_select/common_goods_select_converter.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_goods_select/goods_select_app_bar_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_goods_select/goods_select_bottom_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_goods_select/goods_select_goods_list_item.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_goods_select/goods_select_result_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/house_keeper_goods_select/goods_smart_refresher.dart';
import 'dart:convert';

/// App单店通用选品widget

class CommonGoodsSelectPage extends StatefulWidget {
  CommonGoodsSelectPage({
    this.goodSelectedCallBack,
    Key key,
    this.maxSelectGoodsCount,
    this.selectedGoods,
  }) : super(key: key);

  final Function goodSelectedCallBack;
  final int maxSelectGoodsCount;
  final List<SinglePoiSelectProductVo> selectedGoods;

  @override
  State<StatefulWidget> createState() => CommonGoodsSelectPageState();
}

class CommonGoodsSelectPageState extends State<CommonGoodsSelectPage>
    with SingleTickerProviderStateMixin {
  SinglePoiGoodsSelectPageVo pageVo;
  SinglePoiGoodsSelectPageModel pageModel;
  RefreshController _refreshController;
  AnimationController _animationController;
  Animation<Offset> _offsetAnimation;

  @override
  void initState() {
    super.initState();
    pageVo = SinglePoiGoodsSelectPageVo();
    pageModel = SinglePoiGoodsSelectPageModel(pageVo);

    // 传默认sceneType和已选商品数据
    pageModel.initializeData({
      'sceneType': '1',
      'subSceneType': '0',
      'maxNum': '${widget?.maxSelectGoodsCount ?? 1}',
      'data': widget.selectedGoods != null && widget.selectedGoods.isNotEmpty
          ? jsonEncode(widget.selectedGoods.map((e) => e.toJson()).toList())
          : null,
    });

    _refreshController = RefreshController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _offsetAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: const Offset(0.0, 0.0),
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInQuart,
      ),
    );
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// 关闭页面
  void _onClosePage() {
    Navigator.of(context).pop();
  }

  /// 关闭选择结果蒙层
  void _closeSelectResultMask() {
    pageModel.pageVo.showMask = false;
    pageModel.safeNotifyListeners();
    _animationController.reverse();
  }

  /// 点击已选商品信息
  void _onTapGoodsInfo() {
    if (pageModel.pageVo.showMask != true) {
      pageModel.pageVo.showMask = true;
      pageModel.safeNotifyListeners();
      _animationController?.forward();
    } else {
      pageModel.pageVo.showMask = false;
      pageModel.safeNotifyListeners();
      _animationController.reverse();
    }
  }

  /// 确认选择
  void _onTapConfirm() {
    // 校验已选的商品数据
    if (!pageModel.checkSelectedProductData()) {
      return;
    }
    List<SinglePoiSelectProductVo> result =
        CommonGoodsSelectConverter.convertSelectedProductList(
            pageModel.selectedSpuList);

    if (widget.goodSelectedCallBack != null) {
      widget.goodSelectedCallBack(result);
    }
    Navigator.of(context).pop();
  }

  /// 点击分组
  void _onTapGroup(GoodsGroupVo groupVo) {
    pageModel.setCurrentGroup(groupVo);
  }

  /// 点击商品
  void _onGoodsCheckChanged(GoodsSpuVo spuVo, bool checked) {
    pageModel.setGoodsCheckStatus(spuVo, checked);
  }

  /// 重置状态
  void _onResetState() {
    if (pageModel.spuVoList?.isEmpty ?? true) {
      _refreshController.loadNoData();
    } else {
      if (pageModel.hasNextPage == true) {
        _refreshController.resetNoData();
      } else {
        _refreshController.loadNoData();
      }
    }
  }

  /// 上拉加载更多
  void _onLoadMore() {
    pageModel.loadMoreGoods().whenComplete(() {
      _refreshController.loadComplete();
      if (pageModel.hasNextPage != true) {
        _refreshController.loadNoData();
      }
    });
  }

  /// 点击清空
  void _onTapClear() {
    pageModel.clearSelectGoods();
  }

  /// appBar
  Widget _buildAppBarWidget() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(44),
      child: GoodsSelectAppBarWidget(
        title: '选择商品',
        onBack: _onClosePage,
        hasAction: false,
      ),
    );
  }

  /// 构建分组列表widget
  Widget _buildGroupListWidget() {
    return Selector<SinglePoiGoodsSelectPageVo,
        Tuple2<List<GoodsGroupVo>, int>>(
      selector: (context, pageVo) {
        return Tuple2(
          pageVo.groupVoList,
          pageVo.currentSelectedGroupId,
        );
      },
      builder: (context, tuple, _) {
        List<GoodsGroupVo> groupVoList = tuple.item1 ?? [];
        return Container(
          width: 80,
          color: const Color(0xFFF5F6FA),
          child: ListView.builder(
            itemBuilder: (context, index) {
              GoodsGroupVo groupVo = groupVoList[index];
              return GoodsSelectGroupListItem(
                groupVo: groupVo,
                onTapGroup: _onTapGroup,
              );
            },
            itemCount: groupVoList?.length ?? 0,
          ),
        );
      },
    );
  }

  /// 构建空商品列表widget
  Widget _buildEmptySpuListWidget() {
    return Selector<SinglePoiGoodsSelectPageVo, bool>(
      selector: (context, pageVo) {
        return pageVo.isSpuListRefreshing;
      },
      builder: (context, isSpuListRefreshing, _) {
        return Visibility(
          visible: isSpuListRefreshing != true,
          child: Container(
            margin: const EdgeInsets.only(top: 200),
            alignment: Alignment.center,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Image(
                  width: 100,
                  height: 70,
                  image: AdvancedNetworkImage(
                    'http://p0.meituan.net/tuling/630dc2cf8cf359fd7fb3fb6dfeb2a0b44886.png',
                    useDiskCache: true,
                  ),
                ),
                const SizedBox(height: 25),
                const Text(
                  '暂无商品',
                  style: TextStyle(
                    fontSize: 16.0,
                    color: Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
          replacement: const SizedBox.shrink(),
        );
      },
    );
  }

  /// 构建商品列表widget
  Widget _buildGoodsListWidget() {
    return Selector<SinglePoiGoodsSelectPageVo, List<GoodsSpuVo>>(
      selector: (context, pageVo) {
        return pageVo.spuVoList;
      },
      builder: (context, spuVoList, _) {
        _onResetState();
        bool isEmptyList = spuVoList?.isEmpty ?? true;
        return GoodsSmartRefresher(
          child: CustomScrollView(
            physics: const ClampingScrollPhysics(),
            shrinkWrap: true,
            slivers: [
              SliverToBoxAdapter(
                child: Visibility(
                  visible: isEmptyList,
                  child: _buildEmptySpuListWidget(),
                ),
              ),
              SliverToBoxAdapter(
                child: Visibility(
                  visible: !isEmptyList,
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const ClampingScrollPhysics(),
                    itemCount: spuVoList?.length ?? 0,
                    itemBuilder: (context, index) {
                      GoodsSpuVo spuVo = spuVoList[index];
                      return GoodsSelectGoodsListItem(
                        spuVo: spuVo,
                        onGoodsCheckChanged: _onGoodsCheckChanged,
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          refreshController: _refreshController,
          enablePullDown: false,
          onLoadMore: () => _onLoadMore(),
        );
      },
    );
  }

  /// 构建选品结果蒙层
  Widget _buildSelectResultMaskWidget() {
    return Selector<SinglePoiGoodsSelectPageVo, bool>(
      selector: (BuildContext context, SinglePoiGoodsSelectPageVo pageVo) {
        return pageVo.showMask;
      },
      builder: (context, showMask, _) {
        if (showMask != true) {
          return const SizedBox.shrink();
        }
        return Stack(
          children: [
            GestureDetector(
              onTap: _closeSelectResultMask,
              child: Container(
                color: const Color(0x99222222),
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: SlideTransition(
                position: _offsetAnimation,
                child: GoodsSelectResultWidget(
                  onTapClear: _onTapClear,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 构建内容区widget
  Widget _buildContentWidget() {
    return SafeArea(
      child: Container(
        margin: const EdgeInsets.only(bottom: 64),
        child: Column(
          children: [
            Expanded(
              child: Row(
                children: [
                  _buildGroupListWidget(),
                  Expanded(
                    child: _buildGoodsListWidget(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SinglePoiGoodsSelectPageVo>.value(
      value: pageVo,
      child: Stack(
        children: [
          Scaffold(
            appBar: _buildAppBarWidget(),
            backgroundColor: Colors.white,
            body: _buildContentWidget(),
          ),
          _buildSelectResultMaskWidget(),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: GoodsSelectBottomWidget(
              onTapGoodsInfo: _onTapGoodsInfo,
              onTapConfirm: _onTapConfirm,
            ),
          ),
        ],
      ),
    );
  }
}
