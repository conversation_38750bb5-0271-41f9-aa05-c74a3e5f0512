import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/base_block.dart';

class StepAnimationWidget extends StatefulWidget {
  final TextStyle textStyle;
  final int dotCount;
  final Duration animationDuration;
  final List<StepBlock> steps;
  final bool completed; // 是否完成

  const StepAnimationWidget({
    Key key,
    this.textStyle = const TextStyle(
      fontSize: 15,
      color: Color(0xFF8145ED),
      fontWeight: FontWeight.w800,
    ),
    this.dotCount = 3,
    this.animationDuration = const Duration(milliseconds: 200),
    this.steps,
    this.completed = false,
  }) : super(key: key);

  @override
  _StepAnimationWidgetState createState() => _StepAnimationWidgetState();
}

class _StepAnimationWidgetState extends State<StepAnimationWidget>
    with TickerProviderStateMixin {
  int _currentDotCount = 0;
  Timer _timer;
  bool _completed = false; // 流是否结束，如果结束则显示「思考过程」，否则「思考中」
  bool _expend = true; // 是否展开和折叠，除了流式控制，还有手动控制，所以要跟_completed区分开

  @override
  void initState() {
    super.initState();
    _completed = widget.completed;
    _startThinking();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(StepAnimationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.completed != widget.completed) {
      setState(() {
        _completed = widget.completed;
        if (widget.completed) {
          _expend = false;
          _stopThinking();
        }
      });
    }
  }

  void _startThinking() {
    _timer?.cancel();
    _currentDotCount = 0;
    _startAnimation();
  }

  void _stopThinking() {
    _timer?.cancel();
    _currentDotCount = 0;
  }

  void _startAnimation() {
    _timer = Timer.periodic(widget.animationDuration, (timer) {
      setState(() {
        _currentDotCount = (_currentDotCount + 1) % (widget.dotCount + 1);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题区块
          Row(
            children: [
              Visibility(
                visible: !_completed,
                child: Row(children: [
                  SizedBox(
                    width: 18,
                    height: 18,
                    child: Image(
                      image: AdvancedNetworkImage(
                        'https://p1.meituan.net/waimaieassistant/40b2a27d7e1166aec5e5fe7eb52e77901908.png',
                        useDiskCache: true,
                      ),
                      fit: BoxFit.fill,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Text(
                    '正在思考中',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF222222),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  SizedBox(
                    width: 16, // 你可以根据实际需要调整宽度
                    child: Text(
                      '.' * _currentDotCount,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF222222),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
                ]),
              ),
              Visibility(
                visible: _completed,
                child: Row(children: [
                  SizedBox(
                    width: 18,
                    height: 18,
                    child: Image(
                      image: AdvancedNetworkImage(
                        'https://p1.meituan.net/waimaieassistant/40b2a27d7e1166aec5e5fe7eb52e77901908.png',
                        useDiskCache: true,
                      ),
                      fit: BoxFit.fill,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Text(
                    '思考过程',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF222222),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ]),
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _expend = !_expend;
                  });
                },
                child: Container(
                    width: 50, // 设置更大的热区宽度
                    height: 32, // 设置更大的热区高度
                    alignment: Alignment.centerLeft,
                    color: Colors.transparent,
                    child: Image(
                      image: AdvancedNetworkImage(
                        _expend
                            ? 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/7874d425f2c5485e/arrow_up.png'
                            : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/edeb84061e9ca377/arrow_down.png',
                        useDiskCache: true,
                      ),
                      width: 12,
                      height: 12,
                    )),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ClipRect(
            child: Align(
              heightFactor: _expend ? 1.0 : 0.0,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: _expend ? 1.0 : 0.0,
                child: Column(
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF9FAFC),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: _buildSteps(),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSteps() {
    if (widget.steps == null || widget.steps.isEmpty) {
      return const SizedBox.shrink();
    }
    const loadingGifUrl =
        'https://p0.meituan.net/waimaieassistant/c97ac2988c12dd1c702a578faca43f1d27107.gif';
    const finishImageUrl =
        'https://p0.meituan.net/waimaieassistant/72ab87dc4a1c64567200df3c57a1034e1186.png';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(widget.steps.length, (i) {
        final step = widget.steps[i];
        final isCurrent = step.status == '0';
        final isLast = i == widget.steps.length - 1;
        return Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 24,
              height: 30,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (!isLast)
                    Positioned(
                      top: 26,
                      bottom: 0,
                      left: 11,
                      child: Container(
                        width: 1,
                        color: const Color.fromARGB(255, 229, 228, 228),
                      ),
                    ),
                  // 步骤点
                  isCurrent
                      ? Image.network(
                          loadingGifUrl,
                          width: 16,
                          height: 16,
                          fit: BoxFit.contain,
                        )
                      : Image.network(
                          finishImageUrl,
                          width: 16,
                          height: 16,
                          fit: BoxFit.contain,
                        ),
                ],
              ),
            ),
            const SizedBox(width: 3),
            Expanded(
              child: Text(
                step.name ?? '' + (step.status == '0' ? '进行中' : '已完成'),
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF666666),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      }),
    );
  }
}
