import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:markdown/markdown.dart' as md;

/// 处理Markdown解析错误的包装组件
class SafeMarkdownBody extends StatelessWidget {
  final String data;
  final MarkdownStyleSheet styleSheet;
  final void Function(String text, String href, String title) onTapLink;
  final Map<String, MarkdownElementBuilder> builders;
  final List<md.BlockSyntax> blockSyntaxes;
  final md.ExtensionSet extensionSet;

  const SafeMarkdownBody({
    Key key,
    @required this.data,
    @required this.styleSheet,
    @required this.onTapLink,
    @required this.builders,
    @required this.blockSyntaxes,
    @required this.extensionSet,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    try {
      // 安全检查输入数据
      final String safeData = data?.isEmpty == true ? '' : (data ?? '');

      // 进一步清理数据，去除可能导致解析错误的格式
      String cleanedData = _cleanMarkdownContent(safeData);

      return MarkdownBody(
        data: cleanedData,
        selectable: false,
        styleSheet: styleSheet,
        onTapLink: onTapLink,
        builders: builders,
        blockSyntaxes: blockSyntaxes,
        extensionSet: extensionSet,
      );
    } catch (e) {
      debugPrint('渲染Markdown时出错: $e');

      // 尝试使用纯文本显示
      try {
        final strippedContent = _stripMarkdownSyntax(data ?? '');
        return Text(
          strippedContent.isNotEmpty ? strippedContent + '纯文本显示' : '无内容',
          style: const TextStyle(color: Colors.black87),
        );
      } catch (e2) {
        return Text(
          '我遇到无法理解的事情啦😭',
          style: TextStyle(color: Colors.red[700]),
        );
      }
    }
  }

  // 尝试修复自定义块格式
  String _attemptToFixWarningBlocks(String content) {
    final lines = content.split('\n');
    final List<String> newLines = [];
    bool insideWarningBlock = false;

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      // 检测自定义块开始
      if (RegExp(r'^:::\w+').hasMatch(line)) {
        if (insideWarningBlock) {
          // 如果已经在自定义块内，先关闭前一个
          newLines.add(':::');
        }

        insideWarningBlock = true;
        newLines.add(line); // 保留开始标记
      }
      // 检测自定义块结束
      else if (line == ':::') {
        insideWarningBlock = false;
        newLines.add(line); // 保留结束标记
      } else {
        newLines.add(lines[i]); // 保持原始格式
      }
    }

    // 确保所有自定义块都已关闭
    if (insideWarningBlock) {
      newLines.add(':::');
    }

    return newLines.join('\n');
  }

  // 清理Markdown内容，移除可能引起解析错误的格式
  String _cleanMarkdownContent(String content) {
    if (content == null || content.isEmpty) return '';

    try {
      // 处理代码块
      content = _fixCodeBlocks(content);

      // 处理自定义自定义块
      content = _fixWarningBlocks(content);

      // 把全部的英文的~，替换为中文的～
      content = content.replaceAll('~', '～');

      return content;
    } catch (e) {
      debugPrint('清理Markdown内容时出错: $e');
      return content; // 返回原始内容
    }
  }

  // 修复代码块格式
  String _fixCodeBlocks(String content) {
    // 确保代码块前后有空行
    content = content.replaceAll(RegExp(r'([^\n])```'), r'$1\n\n```');
    content = content.replaceAll(RegExp(r'```([^\n])'), r'```\n\n$1');

    // 确保代码块结束
    final codeBlockStarts = RegExp(r'```\w*').allMatches(content).length;
    final codeBlockEnds =
        RegExp(r'```\s*$', multiLine: true).allMatches(content).length;

    if (codeBlockStarts > codeBlockEnds) {
      content = content + '\n```';
    }

    return content;
  }

  // 修复或移除自定义块
  String _fixWarningBlocks(String content) {
    // 尝试修复自定义块，如果无法修复则移除
    try {
      final result = _attemptToFixWarningBlocks(content);
      if (result != null) return result;
    } catch (e) {
      debugPrint('修复自定义块时出错: $e');
    }

    // 如果无法修复，则移除所有自定义块标签
    return content
        .replaceAll(RegExp(r':::\w+\s*'), '')
        .replaceAll(RegExp(r':::'), '');
  }

  // 去除Markdown语法，显示纯文本
  String _stripMarkdownSyntax(String markdown) {
    if (markdown == null || markdown.isEmpty) return '';

    String result = markdown;

    // 移除代码块
    result =
        result.replaceAll(RegExp(r'```[\s\S]*?```', multiLine: true), '[代码块]');

    // 移除标题
    result = result.replaceAll(RegExp(r'#{1,6}\s+(.+)'), r'$1');

    // 移除粗体和斜体
    result = result.replaceAll(RegExp(r'\*\*(.+?)\*\*'), r'$1');
    result = result.replaceAll(RegExp(r'\*(.+?)\*'), r'$1');
    result = result.replaceAll(RegExp(r'__(.+?)__'), r'$1');
    result = result.replaceAll(RegExp(r'_(.+?)_'), r'$1');

    // 移除链接，保留文本
    result = result.replaceAll(RegExp(r'\[(.+?)\]\(.+?\)'), r'$1');

    // 移除自定义块标记
    result = result.replaceAll(RegExp(r':::\w+'), '');
    result = result.replaceAll(':::', '');

    // 移除列表标记
    result = result.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '');
    result = result.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    return result.trim();
  }
}
