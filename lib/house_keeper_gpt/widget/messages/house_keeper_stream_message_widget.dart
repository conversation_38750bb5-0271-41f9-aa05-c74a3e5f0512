import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/common/log_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/messages/house_keeper_message_container.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/thinking_animation_widget.dart';

class HouseKeeperStreamMessageWidget extends StatefulWidget {
  final HouseKeeperStreamMessageVo message;
  final HouseKeeperMessagePageModel model;

  const HouseKeeperStreamMessageWidget({
    Key key,
    @required this.message,
    @required this.model,
  }) : super(key: key);

  @override
  State<HouseKeeperStreamMessageWidget> createState() =>
      _HouseKeeperStreamMessageWidgetState();
}

class _HouseKeeperStreamMessageWidgetState
    extends State<HouseKeeperStreamMessageWidget> {
  bool _isPlaying = false;
  int _clickCount = 0;
  DateTime _lastClickTime;
  bool _goodClick = false;
  bool _badClick = false;

  @override
  void initState() {
    super.initState();
    widget.message.addListener(_onMessageChanged);
  }

  @override
  void dispose() {
    widget.message.removeListener(_onMessageChanged);
    if (_isPlaying) {
      widget.model.stopTTS();
    }
    super.dispose();
  }

  void _onMessageChanged() {
    if (mounted) {
      LogUtils.logWithTimestamp(
          'Message changed: Content: ${widget.message.content}');
      // 「IMPORTANT」此处才是页面刷新的根本触发
      setState(() {});
    }
  }

  void _handleClick() {
    final now = DateTime.now();
    // 如果距离上次点击超过2秒，重置计数器
    if (_lastClickTime != null &&
        now.difference(_lastClickTime).inSeconds > 2) {
      _clickCount = 0;
    }

    _clickCount++;
    _lastClickTime = now;

    if (_clickCount >= 5) {
      if (widget.message.errorFullMessage != null &&
          widget.message.errorFullMessage.isNotEmpty) {
        showDialog(
          context: context,
          barrierDismissible: true,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('错误详情'),
              content: SingleChildScrollView(
                child: Text(widget.message.errorFullMessage),
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text('关闭'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      }
      _clickCount = 0; // 重置计数器
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.message == null) {
      return const SizedBox.shrink();
    }

    switch (widget.message.state) {
      case StreamMessageState.initial:
        return _buildLoadingMessage();
      case StreamMessageState.streaming:
      case StreamMessageState.completed:
        return _buildMessageContent();
      case StreamMessageState.error:
        {
          FlutterLx.moudleView(
              '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_agvwm7cj_mv',
              val: {
                'usertext': '',
              });
          // 避免出现空消息下面有一个错误提示，如果有内容，则在下面增加几个错误消息
          if (widget.message.content != null &&
              widget.message.content.isNotEmpty) {
            return _buildMessageContent(errorWidget: _buildErrorMessage());
          }
          return _buildErrorMessage();
        }
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildLoadingMessage() {
    return const ThinkingAnimationWidget();
  }

  Widget _buildErrorMessage() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: GestureDetector(
                onTap: _handleClick,
                child: Text(
                  (widget.message.errorMessage != null &&
                          widget.message.errorMessage.isNotEmpty)
                      ? '系统 "溜号" 啦，稍后重试～(${widget.message.errorMessage})'
                      : '系统 "溜号" 啦，稍后重试～',
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 15,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageContent({Widget errorWidget}) {
    final isCompleted = widget.message.state == StreamMessageState.completed;

    if (isCompleted && !widget.message.content.contains("Guess")) {
      // 延迟300ms，确保BottomWidget内容完全加载后再滚动到底部
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          widget.model.scrollToBottom();
        }
      });
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      width: double.infinity,
      child: HouseKeeperMessageContainer(
        data: widget.message.content ?? '',
        vo: widget.message,
        model: widget.model,
        isCompleted: isCompleted,
        bottomWidget: isCompleted ? _buildBottomWidget() : null,
        errorWidget: errorWidget,
      ),
    );
  }

  Widget _buildBottomWidget() {
    return Container(
      padding: const EdgeInsets.only(left: 12, right: 12, bottom: 12),
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // ai生成提示
          Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(left: 2, bottom: 4),
            child: const Text(
              '以上内容由AI生成',
              style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF999999)),
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 0, bottom: 9),
            child: const Divider(
              height: 1,
              color: Color(0xFFEEEEEE),
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Visibility(
                visible: widget.message.isMessageReadable(),
                child: _buildPlayButton(),
              ),
              const SizedBox(width: 8),
              _buildActionButton(
                _goodClick
                    ? 'https://p0.meituan.net/waimaieassistant/34a0c692a2e337e00fa2adf54f87917d1861.png'
                    : 'https://p0.meituan.net/ingee/4fe073c998e0264089e28355b0ce771d725.png',
                () {
                  _goodClick = !_goodClick;
                  if (_goodClick) {
                    _badClick = false;
                  }
                  setState(() {});
                  if (widget.message.content != null && _goodClick) {
                    widget.model.handleClickGoods(
                        widget?.message?.getReadableContent() ?? '');
                  }
                },
              ),
              const SizedBox(width: 8),
              _buildActionButton(
                _badClick
                    ? 'https://p0.meituan.net/waimaieassistant/ff1f6b0be9b877e21bcadd4d661c0cf41883.png'
                    : 'https://p0.meituan.net/ingee/504c99f7be880f8934dc22cd46e59bff756.png',
                () {
                  _badClick = !_badClick;
                  if (_badClick) {
                    _goodClick = false;
                  }
                  setState(() {});
                  if (widget.message.content != null && _badClick) {
                    widget.model.handleClickBad(
                        widget?.message?.getReadableContent() ?? '');
                  }
                },
              ),
              const Spacer(),
              // Visibility(
              //   visible: widget.message.isMessageResend(),
              //   child: GestureDetector(
              //     onTap: () {
              //       FlutterLx.moudleClick('43392360', 'c_waimai_e_jxnzlx1r',
              //           'b_waimai_e_1td7z6yu_mc',
              //           val: {
              //             "userText": widget.message.userMessage,
              //             "agentText": widget.message.contextMessage
              //           });
              //       widget.model.handleReSend(widget.message);
              //     },
              //     child: Row(
              //       mainAxisSize: MainAxisSize.min,
              //       children: <Widget>[
              //         Image(
              //           width: 18,
              //           height: 18,
              //           fit: BoxFit.contain,
              //           image: AdvancedNetworkImage(
              //             'https://p0.meituan.net/ingee/595cf72d8bfbdaddd2a6b1ee96dbcc33858.png',
              //             useDiskCache: true,
              //           ),
              //         ),
              //         const SizedBox(width: 3),
              //         const Text(
              //           '重新生成',
              //           style:
              //               TextStyle(fontSize: 12, color: Color(0xFF222222)),
              //         ),
              //       ],
              //     ),
              //   ),
              // )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String imageUrl, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 20,
        height: 20,
        margin: const EdgeInsets.only(left: 8),
        child: Image.network(
          imageUrl,
          fit: BoxFit.fill,
          errorBuilder: (context, error, stackTrace) {
            return const SizedBox(width: 20, height: 20);
          },
        ),
      ),
    );
  }

  Widget _buildPlayButton() {
    final String imageUrl = _isPlaying
        ? 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/3bc6ebb66e8dd09f/voice_assistant_icon.gif' // 播放中的喇叭图标
        : 'https://p0.meituan.net/ingee/70ea2cba62f75317e91c829bb50fd797753.png'; // 原始图标

    return GestureDetector(
      onTap: () {
        final content = widget.message.getReadableContent();
        if (content.isEmpty) {
          return;
        }

        if (_isPlaying) {
          widget.model.stopTTS();
          setState(() {
            _isPlaying = false;
          });
          return;
        }

        setState(() {
          _isPlaying = true;
        });
        widget.model.playTTS(
          content,
          onComplete: () {
            if (mounted) {
              setState(() {
                _isPlaying = false;
              });
            }
          },
        );
      },
      child: Container(
        width: 20,
        height: 20,
        margin: const EdgeInsets.only(left: 8),
        child: Image.network(
          imageUrl,
          width: 20,
          height: 20,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return const SizedBox(width: 20, height: 20);
          },
        ),
      ),
    );
  }
}
