import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/utils/markdown_parser.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/messages/house_keeper_markdown_widget.dart';
import 'package:flutter_lx/flutter_lx.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/base_block.dart';

/// 消息容器组件，负责整体布局
class HouseKeeperMessageContainer extends StatefulWidget {
  /// Markdown 内容
  final String data;

  /// 消息数据
  final HouseKeeperStreamMessageVo vo;

  /// 点击链接的回调
  // final void Function(String text, String href, String title) onTapLink;

  /// 是否是流式消息, 历史消息为 false, 流式消息为 true
  final bool isStreaming;

  /// 页面模型
  final HouseKeeperMessagePageModel model;

  /// 底部工具栏
  final Widget bottomWidget;

  /// 底部工具栏
  final Widget errorWidget;

  /// 是否是完成消息
  final bool isCompleted;

  HouseKeeperMessageContainer({
    Key key,
    @required this.data,
    @required this.vo,
    this.isStreaming = true,
    this.model,
    this.bottomWidget,
    this.errorWidget,
    this.isCompleted = false,
  }) : super(key: key);

  @override
  _HouseKeeperMessageContainerState createState() =>
      _HouseKeeperMessageContainerState();
}

class _HouseKeeperMessageContainerState
    extends State<HouseKeeperMessageContainer>
    with AutomaticKeepAliveClientMixin {
  List<ContentBlock> _contentBlocks;
  bool _showThinking = false;
  bool _hasGuessContent = false;
  GuessBlock _guessBlock;

  @override
  bool get wantKeepAlive => true;

  void _parseContentBlocks() {
    _contentBlocks = MarkdownParser.preprocessData(widget.data ?? '');
    _guessBlock = _contentBlocks.firstWhere(
      (block) => block is GuessBlock,
      orElse: () => null,
    ) as GuessBlock;
    _hasGuessContent = _guessBlock != null;
    _showThinking = widget.vo?.state == StreamMessageState.streaming;
  }

  @override
  void didUpdateWidget(HouseKeeperMessageContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.data != oldWidget.data ||
        widget.vo?.state != oldWidget.vo?.state) {
      _parseContentBlocks();
    }
  }

  @override
  void initState() {
    super.initState();
    widget.vo?.addListener(_handleVoStateChanged);
    _parseContentBlocks();
  }

  @override
  void dispose() {
    widget.vo?.removeListener(_handleVoStateChanged);
    super.dispose();
  }

  void _handleVoStateChanged() {
    if (widget.vo?.state == StreamMessageState.completed) {
      setState(() {
        int lastStepIndex = -1;
        for (int i = _contentBlocks.length - 1; i >= 0; i--) {
          if (_contentBlocks[i].type == ContentBlockType.steps) {
            lastStepIndex = i;
            break;
          }
        }
        debugPrint('lastStepIndex: $lastStepIndex');
        if (lastStepIndex != -1) {
          final old = _contentBlocks[lastStepIndex] as StepBlock;
          _contentBlocks[lastStepIndex] = StepBlock(
            key: old.key,
            name: old.name,
            status: '1',
            content: old.content,
            isComplete: old.isComplete,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须加这
    // 只要 _contentBlocks 发生变化，子组件就会拿到最新的数据
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 主要内容
        SizedBox(
          width: double.infinity,
          child: HouseKeeperMessageBlockWidget(
            vo: widget.vo,
            contentBlocks: _contentBlocks
                .where((block) => block.type != ContentBlockType.guess)
                .toList(),
            isStreaming: widget.isStreaming,
            model: widget.model,
          ),
        ),
        // 底部工具栏
        Visibility(
          visible: widget.bottomWidget != null &&
              widget.vo?.state == StreamMessageState.completed,
          child: widget.bottomWidget ?? const SizedBox.shrink(),
        ),
        // 猜你想问模块
        Visibility(
          visible: _hasGuessContent && widget.isCompleted,
          child: _buildGuessContent(widget.data),
        ),
        // 错误提示
        Visibility(
          visible: widget.errorWidget != null,
          child: widget.errorWidget ?? const SizedBox.shrink(),
        ),
      ],
    );
  }

  /// 构建猜你想问模块
  Widget _buildGuessContent(String content) {
    if (_guessBlock == null || _guessBlock.questions.isEmpty) {
      return const SizedBox.shrink();
    }
    final questions = _guessBlock.questions;
    final List<Widget> questionWidgets = <Widget>[];
    for (final questionData in questions) {
      final text = questionData.text;
      final link = questionData.link;
      if (_hasGuessContent) {
        FlutterLx.moudleView(
            '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_sx79hk3h_mv',
            val: {
              'usertext': '',
              'questionname': content ?? '',
            });
      }
      questionWidgets.add(
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: InkWell(
            onTap: () {
              FlutterLx.moudleClick(
                  '43392360', 'c_waimai_e_jxnzlx1r', 'b_waimai_e_sx79hk3h_mc',
                  val: {
                    'usertext': '',
                    'questionname': text ?? '',
                  });
              if (link.startsWith('waimaieapi.jump')) {
                widget.model?.sendPlainMessage(text);
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFDEF0FF),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    text,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF333333),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }
    return Container(
      margin: const EdgeInsets.only(top: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: questionWidgets,
      ),
    );
  }
}
