import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_vo.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/utils/url_utils.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/step_animation_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/base_block.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/custom_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/markdown_block_widget.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/widget/system_block/think_block_widget.dart';

/// 通用的 Markdown 显示组件
///
/// 用于在应用的各个部分显示 Markdown 格式的内容，
/// 提供统一的样式和行为。
class HouseKeeperMessageBlockWidget extends StatefulWidget {
  final HouseKeeperStreamMessageVo vo;

  /// 是否是流式消息
  final bool isStreaming;

  final HouseKeeperMessagePageModel model;

  final List<ContentBlock> contentBlocks;

  const HouseKeeperMessageBlockWidget(
      {Key key,
      @required this.vo,
      @required this.contentBlocks,
      this.isStreaming = true,
      this.model})
      : super(key: key);

  @override
  _HouseKeeperMessageBlockWidgetState createState() =>
      _HouseKeeperMessageBlockWidgetState();
}

// 思考模式
enum ThinkingMode { none, thinking, step }

class _HouseKeeperMessageBlockWidgetState
    extends State<HouseKeeperMessageBlockWidget>
    with SingleTickerProviderStateMixin {
  /// 内容区块列表
  bool _streamCompleted = false;
  Map<String, bool> _blockLoadingStates = {};

  ThinkingMode _thinkingMode;

  void _updateThinkingMode() {
    // Step 在前，如果有 Step，则不显示 thinking
    if (widget.contentBlocks
        .any((block) => block.type == ContentBlockType.steps)) {
      _thinkingMode = ThinkingMode.step;
    } else if (widget.contentBlocks
        .any((block) => block.type == ContentBlockType.think)) {
      _thinkingMode = ThinkingMode.thinking;
    } else {
      _thinkingMode = ThinkingMode.none;
    }
  }

  @override
  Widget build(BuildContext context) {
    try {
      final List<Widget> children = [];
      final List<StepBlock> steps = [];
      int stepIndex = -1;

      // 创建一个闭包来捕获 context
      void handleTapLink(String text, String href, String title) {
        try {
          if (href != null && href.isNotEmpty) {
            if (href.startsWith('waimaieapi.jump')) {
              widget.model?.sendPlainMessage(text);
            } else if (isUrlCanOpen(href)) {
              RouteUtils.open(href);
            }
          }
        } catch (e) {
          debugPrint('处理链接点击时出错: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('无法打开链接: $href'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }

      if (widget.contentBlocks.isEmpty) {
        // 如果内容为空，则显示正在思考中
        children.add(
            Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
            children: [
              Row(children: [
                SizedBox(
                  width: 18,
                  height: 18,
                  child: Image(
                    image: AdvancedNetworkImage(
                      'https://p1.meituan.net/waimaieassistant/40b2a27d7e1166aec5e5fe7eb52e77901908.png',
                      useDiskCache: true,
                    ),
                    fit: BoxFit.fill,
                  ),
                ),
                const SizedBox(width: 4),
                const Text(
                  '正在思考中',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF222222),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ])
            ],
          ),
        ]));
      }

      for (final block in widget.contentBlocks) {
        switch (block.type) {
          case ContentBlockType.think:
            // 只有思考模式下，才显示思考块
            if (_thinkingMode != ThinkingMode.thinking) {
              continue;
            }
            if (block is ThinkBlock) {
              children.add(ThinkBlockWidget(
                block: block,
                showThinking: !_streamCompleted,
                onToggleThinking: _toggleThinking,
                model: widget.model,
              ));
            }
            break;
          case ContentBlockType.markdown:
            if (block is MarkdownBlock) {
              // 不全的标签会显示在正文，先作尾部排除吧
              if (block.content.endsWith('<') ||
                  block.content.endsWith('<s') ||
                  block.content.endsWith('<st') ||
                  block.content.endsWith('<ste') ||
                  block.content.endsWith('<step') ||
                  block.content.endsWith('<t') ||
                  block.content.endsWith('<th') ||
                  block.content.endsWith('<thi') ||
                  block.content.endsWith('<thin') ||
                  block.content.endsWith('<think')) {
                continue;
              }
              children.add(MarkdownBlockWidget(
                block: block,
                model: widget.model,
              ));
            }
            break;
          case ContentBlockType.custom:
            if (block is CustomBlock) {
              children.add(CustomBlockWidget(
                block: block,
                isLoading: _blockLoadingStates[block.blockType] ?? true,
                blockLoadingStates: _blockLoadingStates,
                model: widget.model,
              ));
            }
            break;
          case ContentBlockType.guess:
            // 猜你想问块，不显示在正文
            continue;
          case ContentBlockType.steps:
            // 只有思考模式下，才显示思考块
            if (_thinkingMode != ThinkingMode.step) {
              continue;
            }
            if (block is StepBlock) {
              // 如果 存在 stepIndex，则插入到 stepIndex 的位置
              if (stepIndex == -1) {
                stepIndex = children.length;
              }
              // 根据 key 做状态替换
              if (block.key != null && block.key.isNotEmpty) {
                final idx = steps.indexWhere((s) => s.key == block.key);
                if (idx >= 0) {
                  final old = steps[idx];
                  steps[idx] = StepBlock(
                    key: block.key,
                    name: block.name,
                    status: block.status,
                    content: old.content,
                    isComplete: block.isComplete,
                  );
                } else {
                  steps.add(block);
                }
              }
            }
            break;
        }
      }

      if (steps.isNotEmpty) {
        // 包含思考步骤，显示折叠的步骤卡片
        if (_streamCompleted) {
          children.insert(
            stepIndex,
            StepAnimationWidget(
              steps: steps,
              completed: _streamCompleted,
            ),
          );
        } else {
          children.insert(
            stepIndex,
            StepAnimationWidget(
              steps: steps,
              completed: _streamCompleted,
            ),
          );
        }
      }

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(12),
            topRight: const Radius.circular(12),
            bottomLeft: !_streamCompleted
                ? const Radius.circular(12)
                : const Radius.circular(0),
            bottomRight: !_streamCompleted
                ? const Radius.circular(12)
                : const Radius.circular(0),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            )
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: children,
        ),
      );
    } catch (e) {
      debugPrint('构建Markdown小部件时出错: $e');
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red),
        ),
        child: const Text(
          '我遇到无法理解的事情啦😭',
          style: TextStyle(color: Colors.red),
        ),
      );
    }
  }

  @override
  void didUpdateWidget(HouseKeeperMessageBlockWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.vo != oldWidget.vo) {
      _setupMessageListener();
    }
    // contentBlocks 变化时也要更新思考模式
    if (widget.contentBlocks != oldWidget.contentBlocks) {
      _updateThinkingMode();
    }
  }

  @override
  void dispose() {
    if (widget.vo != null) {
      widget.vo.removeListener(_handleMessageStateChanged);
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _thinkingMode = ThinkingMode.none;
    _setupMessageListener();
    _updateThinkingMode();
  }

  void _handleMessageStateChanged() {
    if (!mounted) return;

    final state = widget.vo?.state;
    if (state == StreamMessageState.streaming) {
      if (_streamCompleted) {
        setState(() => _streamCompleted = false);
      }
    } else if (state == StreamMessageState.completed) {
      if (!_streamCompleted) {
        // 延迟 500ms 后设置为完成状态，避免先滚动到底部，然后在折叠，在滚动到顶部
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            setState(() => _streamCompleted = true);
          }
        });
      }
    }
  }

  void _setupMessageListener() {
    if (widget.vo != null) {
      widget.vo.addListener(_handleMessageStateChanged);
    }
  }

  /// 切换思考内容的显示状态
  void _toggleThinking() {
    setState(() => _streamCompleted = !_streamCompleted);
  }
}
