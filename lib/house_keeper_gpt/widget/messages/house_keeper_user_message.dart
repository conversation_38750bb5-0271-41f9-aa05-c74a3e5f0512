import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/house_keeper_gpt/model/house_keeper_message_vo.dart';

class UserMessageWidget extends StatelessWidget {
  const UserMessageWidget({Key key, @required this.message}) : super(key: key);
  final HouseKeeperMessage message;

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.65,
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: const BoxDecoration(
        color: Color(0xFF050505),
        borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      child: Text(
        message?.content ?? '',
        style: const TextStyle(
          fontSize: 14.0,
          color: Colors.white,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
