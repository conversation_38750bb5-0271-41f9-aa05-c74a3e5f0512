import 'package:flutter/material.dart';

/// 基础输入信息组件，可显示errorTip
class HouseKeeperBaseInputWidget extends StatelessWidget {
  const HouseKeeperBaseInputWidget({
    Key key,
    this.title,
    this.titleLink,
    this.left,
    this.content,
    this.right,
    this.rightType = BaseInputRightIconType.custom,
    this.rightTap,
    this.bottom,
    this.isRequired = false,
    this.showInputErrorTip = false,
    this.inputErrorTipNotifier,
    this.inputErrorTip = 'invalid input',
    this.onInputErrorTipTap,
    this.horizontalSymmetric = 0,
    this.verticalSymmetric = 0,
  }) : super(key: key);

  final String title;
  final LayerLink titleLink;
  final Widget left;
  final Widget content;
  final Widget right;
  final Widget bottom;
  final BaseInputRightIconType rightType;
  final Function rightTap;
  final bool isRequired;
  final bool showInputErrorTip;
  final ValueNotifier<bool> inputErrorTipNotifier;
  final String inputErrorTip;
  final Function onInputErrorTipTap;
  final double horizontalSymmetric;
  final double verticalSymmetric;

  /// 构建必填widget
  Widget _buildRequiredWidget() {
    return isRequired
        ? const Image(
            width: 7,
            height: 7,
            image: AssetImage('images/goods_manage/icon_text_star.png'),
          )
        : Container(
            width: 7,
          );
  }

  /// 构建左侧widget
  Widget _buildLeftWidget() {
    Widget titleWidget = Container(
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          _buildRequiredWidget(),
          left ?? Container(),
        ],
      ),
    );
    if (titleLink != null) {
      return CompositedTransformTarget(
        link: titleLink,
        child: titleWidget,
      );
    }
    return titleWidget;
  }

  /// 构建中间widget
  Widget _buildCenterWidget() {
    Widget centerWidget = Container();
    if (content == null) {
      return centerWidget;
    }
    centerWidget = content;
    if (rightTap != null &&
        (rightType == BaseInputRightIconType.arrow ||
            rightType == BaseInputRightIconType.add)) {
      // 如果右边是箭头，那么整个item都需要点击
      centerWidget = GestureDetector(
        onTap: rightTap,
        behavior: HitTestBehavior.opaque,
        child: SizedBox(
          width: double.infinity,
          child: centerWidget,
        ),
      );
    }
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          centerWidget,
          _buildErrorTipWidget(),
        ],
      ),
    );
  }

  /// 构建右边widget
  Widget _buildRightWidget() {
    Widget rightWidget = Container();
    // 右边icon
    switch (rightType) {
      case BaseInputRightIconType.arrow:
        rightWidget = _buildArrowWidget();
        break;
      case BaseInputRightIconType.question:
        rightWidget = _buildQuestionWidget();
        break;
      case BaseInputRightIconType.add:
        rightWidget = _buildAddWidget();
        break;
      default:
        if (right != null) {
          rightWidget = right;
        }
        break;
    }
    if (rightTap != null) {
      rightWidget = GestureDetector(onTap: rightTap, child: rightWidget);
    }
    return rightWidget;
  }

  /// 构建箭头widget
  Widget _buildArrowWidget() {
    return Container(
      child: const Image(
        width: 16,
        height: 16,
        image: AssetImage('images/edit_goods/icon_arrow_right.png'),
      ),
    );
  }

  /// 构建加号widget
  Widget _buildAddWidget() {
    return Stack(
      alignment: AlignmentDirectional.center,
      children: <Widget>[
        Container(
          width: 20,
          height: 20,
          decoration: const BoxDecoration(
            color: Color(0xFFFFCC33),
            borderRadius: BorderRadius.all(
              Radius.circular(5),
            ),
          ),
        ),
        Container(
          width: 9,
          height: 2,
          color: const Color(0xFF222222),
        ),
        Container(
          width: 2,
          height: 9,
          color: const Color(0xFF222222),
        )
      ],
    );
  }

  /// 构建问题widget
  Widget _buildQuestionWidget() {
    return const Image(
      width: 14,
      height: 14,
      image: AssetImage('images/user_comment/question_mark_gray.png'),
      fit: BoxFit.fill,
    );
  }

  /// 构建错误tips
  Widget _buildErrorTipWidget() {
    Widget errorTipWidget = GestureDetector(
      onTap: () {
        if (onInputErrorTipTap != null) {
          onInputErrorTipTap();
        }
      },
      child: Container(
        padding: const EdgeInsets.only(top: 2),
        child: Text(
          '$inputErrorTip                                                          ',
          style: const TextStyle(
            color: Color(0xFFFF1A2D),
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
          maxLines: 1,
          overflow: TextOverflow.clip,
        ),
      ),
    );
    if (inputErrorTipNotifier != null) {
      return ValueListenableBuilder<bool>(
        valueListenable: inputErrorTipNotifier,
        builder: (BuildContext context, bool isShow, Widget child) {
          return Visibility(
            visible: isShow,
            child: errorTipWidget,
          );
        },
      );
    } else {
      return Visibility(
        visible: showInputErrorTip,
        child: errorTipWidget,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalSymmetric,
        vertical: verticalSymmetric,
      ),
      child: Column(
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              _buildLeftWidget(),
              _buildCenterWidget(),
              _buildRightWidget(),
            ],
          ),
          bottom ?? Container(),
        ],
      ),
    );
  }
}

enum BaseInputRightIconType {
  custom,
  add,
  question,
  arrow,
}
