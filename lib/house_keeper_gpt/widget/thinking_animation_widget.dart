import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';

/// 显示"正在思考中..."的动画组件，省略号会动态变化
class ThinkingAnimationWidget extends StatefulWidget {
  final TextStyle textStyle;
  final int dotCount;
  final Duration animationDuration;

  const ThinkingAnimationWidget({
    Key key,
    this.textStyle = const TextStyle(
      fontSize: 15,
      color: Color(0xFF8145ED),
      fontWeight: FontWeight.w800,
    ),
    this.dotCount = 3,
    this.animationDuration = const Duration(milliseconds: 200),
  }) : super(key: key);

  @override
  _ThinkingAnimationWidgetState createState() =>
      _ThinkingAnimationWidgetState();
}

class _ThinkingAnimationWidgetState extends State<ThinkingAnimationWidget> {
  int _currentDotCount = 0;
  Timer _timer;

  @override
  void initState() {
    super.initState();
    _startAnimation();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startAnimation() {
    _timer = Timer.periodic(widget.animationDuration, (timer) {
      setState(() {
        _currentDotCount = (_currentDotCount + 1) % (widget.dotCount + 1);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      constraints: const BoxConstraints(
        minWidth: 100,
        maxWidth: 200,
        minHeight: 40,
      ),
      child: Row(children: [
        SizedBox(
          width: 18,
          height: 18,
          child: Image(
            image: AdvancedNetworkImage(
              'https://p1.meituan.net/waimaieassistant/40b2a27d7e1166aec5e5fe7eb52e77901908.png',
              useDiskCache: true,
            ),
            fit: BoxFit.fill,
          ),
        ),
        const SizedBox(width: 4),
        const Text(
          '正在思考中',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF222222),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '.' * _currentDotCount,
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF222222),
            fontWeight: FontWeight.w500,
          ),
        ),
      ]),
    );
  }
}
