import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/store/store_tools.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

class ShopAgentExecutor with AgentExecutor {
  @override
  String toolName = 'shop';

  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String funtion = llmContext.functionName;
    if (funtion == ShopAgentExecutorMethod.poiID.value) {
      PoiInfo poiInfo = PoiMananger.getInstance().poiInfo;
      String poiId = poiInfo?.poiId;
      if (StringUtil.isNotEmpty(poiId)) {
        response.ttsString = "您的门店ID是$poiId";
        response.needClose = true;
      }
      return Future.value(response);
    }
    if (llmContext.conversionState == ConversationState.initial) {
      if (funtion == ShopAgentExecutorMethod.closeDoor.value &&
          !StoreTools.isPoiOpen()) {
        response.ttsString = "门店已经停业了，不需要重复操作";
        response.needClose = true;
      } else if (funtion == ShopAgentExecutorMethod.openDoor.value &&
          StoreTools.isPoiOpen()) {
        response.ttsString = "门店已经在营业中了，不需要重复操作";
        response.needClose = true;
      } else {
        response.ttsString = "请问你确认要$funtion吗？";
        response.needConfirm = true;
        response.needClose = false;
      }
    } else {
      String confirm = llmContext.actionList.last;
      response.needClose = true;
      if (confirm == ConversationConfirmState.confirm.value) {
        return executeAction(llmContext, param);
      } else {
        response.ttsString = "已取消本次操作";
        response.needClose = true;
      }
    }
    return Future.value(response);
  }

  Future<AgentExecutorResponse> executeAction(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String funtion = llmContext.functionName;
    String status =
        (funtion == ShopAgentExecutorMethod.closeDoor.value) ? '3' : '1';
    bool value = await StoreTools.setShopOperation(status);
    if (value) {
      response.ttsString = '已为你执行$funtion';
    } else {
      response.ttsString = '不好意思，$funtion失败了，你可以手动操作哦';
    }
    VoiceAssistantReporter.reportOpenDoor(
        val: {"status": funtion, "success": value});
    response.operationDesc = response.ttsString;
    response.needClose = true;
    return response;
  }

  ShopAgentExecutor() {
    actionList = [
      ShopAgentExecutorMethod.closeDoor.value,
      ShopAgentExecutorMethod.openDoor.value,
      ShopAgentExecutorMethod.poiID.value
    ];
  }
}

class ShopAgentExecutorMethod {
  /// 返回的状态
  static const ShopAgentExecutorMethod closeDoor =
      ShopAgentExecutorMethod._('停止营业', "歇业，停业，把店关闭");
  static const ShopAgentExecutorMethod openDoor =
      ShopAgentExecutorMethod._('恢复营业', "开门，开业，开工，把店打开");
  static const ShopAgentExecutorMethod poiID =
      ShopAgentExecutorMethod._('查看门店ID', "门店账号，门店ID，查看门店信息");
  final String value;
  final String description;
  const ShopAgentExecutorMethod._(this.value, this.description);
}
