import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';

class WMLLMContext {
  BuildContext currentBuildContext;
  List<AgentExecutorResponse> executeResults = [];
  List<ConversionContext> _conversionContext = [];
  List<String> _gptResponse = [];
  List<String> _btnClick = [];

  /// 所有理解后的内容。包含GPT回调以及需要进行下一步操作的按钮点击
  List<String> actionList = [];
  ConversationState conversionState;
  int currentStep = 0;

  void resetContext() {
    executeResults = [];
    _conversionContext = [];
    _gptResponse = [];
    currentStep = 0;
    actionList = [];
    _btnClick = [];
    conversionState = ConversationState.initial;
  }

  void addASRText(String text) {
    currentStep++;
    _conversionContext.add(ConversionContext(text));
  }

  String get asrResult => _conversionContext.last.asrText;

  String get functionName => actionList.first;

  void addGPTResponse(String text) {
    _gptResponse.add(text);
    actionList.add(text);
    _updateState();
  }

  void addBtnClick(String text) {
    _btnClick.add(text);
    actionList.add(text);
    _updateState();
  }

  void _updateState() {
    switch (conversionState) {
      case ConversationState.needsConfirmation:
        conversionState = ConversationState.confirmationFinish;
        break;
      case ConversationState.needsMoreData:
        conversionState = ConversationState.hasMoreData;
        break;
      default:
        break;
    }
  }

  /// 修改状态
  void addAgentExecute(AgentExecutorResponse response) {
    executeResults.add(response);
    if (response.needConfirm) {
      transStateTo(ConversationState.needsConfirmation);
    }
    if (response.needMoreInfo) {
      transStateTo(ConversationState.needsMoreData);
    }
    if (response.needClose) {
      transStateTo(ConversationState.completed);
    }
    if (response.needInit) {
      transStateTo(ConversationState.initial);
    }
  }

  void transStateTo(ConversationState state) {
    conversionState = state;
  }

  WMLLMContext({
    this.conversionState = ConversationState.initial,
  });

  String get promptCode => executeResults.last.promptCode;
  String get promptParameter => executeResults.last.promptParameter;
  String get prompt => executeResults.last.prompt;
}

class ConversionContext {
  String asrText;
  ConversionContext(this.asrText);
}

class ConversationState {
  static const ConversationState initial = ConversationState._('对话开始');

  /// 返回的状态
  static const ConversationState needsMoreData = ConversationState._('需要更多的数据');
  static const ConversationState needsConfirmation =
      ConversationState._('需要用户确认');
  static const ConversationState completed = ConversationState._('对话完成');

  /// 用户操作后的状态
  static const ConversationState hasMoreData =
      ConversationState._('用户提供了更多的数据');
  static const ConversationState confirmationFinish =
      ConversationState._('用户确认完成');
  final String value;
  const ConversationState._(this.value);
}

class ConversationConfirmState {
  static const ConversationConfirmState confirm =
      ConversationConfirmState._('true');
  static const ConversationConfirmState cancle =
      ConversationConfirmState._('false');
  final String value;
  const ConversationConfirmState._(this.value);
}
