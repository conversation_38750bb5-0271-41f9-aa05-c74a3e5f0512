import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_service.dart';

class CustomerAgentExecutor with AgentExecutor {
  CustomerAgentExecutor() {
    actionList = [];
  }

  @override
  String get toolName => 'Customer';

  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String prompt = '商家问"${llmContext.asrResult}"，请给出人性化的回答';
    String llmResponse = await LLMService.customerService(prompt);
    response.ttsString = llmResponse;
    response.needInit = true;
    return response;
  }
}
