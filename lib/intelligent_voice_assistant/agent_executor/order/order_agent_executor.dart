import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/chain.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/order/agentMethod/food_done.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/order/agentMethod/order_action.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/order/agentMethod/order_call_user.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/order/agentMethod/order_detail.dart';

class OrderAgentExecutor with AgentExecutor {
  @override
  String get toolName => 'Order';
  List<OrderAgentExecutorMethod> actions;

  OrderAgentExecutor() {
    actions = [
      OrderAgentExecutorMethod.foodDone(),
      OrderAgentExecutorMethod.orderDetail(),
      OrderAgentExecutorMethod.accordCallUser(),
      OrderAgentExecutorMethod.selfBtn(),
      OrderAgentExecutorMethod.addFree(),
      OrderAgentExecutorMethod.connectRider(),
      OrderAgentExecutorMethod.partRefund(),
    ];
    actionList = actions.map((action) => action.value).toList();
  }

  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    OrderAgentExecutorMethod method;
    String functionName = llmContext.functionName;
    actions.forEach((element) {
      if (element.value == functionName) {
        method = element;
      }
    });
    if (method != null) {
      return method.executor.execute(llmContext, param);
    } else {
      return Future.value(null);
    }
  }
}

class OrderAgentResponseData {
  int orderSeq;
  String orderId;
  OrderAgentResponseData(this.orderSeq, this.orderId);
}

class OrderAgentExecutorMethod {
  final String value;
  final String description;
  final ChainExecutor executor;

  OrderAgentExecutorMethod._(this.value, this.description, {this.executor});
  static OrderAgentExecutorMethod foodDone() =>
      OrderAgentExecutorMethod._('出餐完成', "出餐，做好了，送餐",
          executor: FoodDoneChain());
  static OrderAgentExecutorMethod orderDetail() =>
      OrderAgentExecutorMethod._('查看订单', "查看订单详情，查看订单，查看订单状态",
          executor: OrderDetailChain());
  static OrderAgentExecutorMethod accordCallUser() =>
      OrderAgentExecutorMethod._('电话联系顾客', "", executor: OrderCallUserChain());
  static OrderAgentExecutorMethod selfBtn() =>
      OrderAgentExecutorMethod._('自行配送', "", executor: OrderActionChain());
  static OrderAgentExecutorMethod addFree() =>
      OrderAgentExecutorMethod._('加小费', "", executor: OrderActionChain());
  static OrderAgentExecutorMethod connectRider() =>
      OrderAgentExecutorMethod._('联系骑手', "", executor: OrderActionChain());
  static OrderAgentExecutorMethod partRefund() =>
      OrderAgentExecutorMethod._('部分退款', "", executor: OrderActionChain());
}
