import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/chain.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/order/order_agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_service.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/prompt.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/order/order_tools.dart';

class OrderDetail<PERSON>hain extends ChainExecutor {
  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    String wmOrderId = param['wmOrderId']?.toString();
    if (StringUtil.isNotEmpty(wmOrderId)) {
      return executeDetail(llmContext, wmOrderId);
    }
    if (llmContext.conversionState == ConversationState.initial) {
      return executeInit(llmContext, param);
    } else if (llmContext.conversionState == ConversationState.hasMoreData) {
      return executeHasMoreData(llmContext, param);
    }
  }

  Future<AgentExecutorResponse> executeDetail(
      WMLLMContext llmContext, String orderId) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    response.promptCode = ChatGPTPrompt.orderPrompt;
    OrderTools.gotoOrderDetailPage(orderId);
    response.needInit = true;
    return response;
  }

  Future<AgentExecutorResponse> executeInit(
      WMLLMContext llmContext, Map param) async {
    String orderSeqStr = await LLMService.queryService(
      ChatGPTPrompt.fuzzyOrderDetailPrompt(),
      llmContext.asrResult,
      'orderParameter',
    );
    return executeOrderDetailAction(llmContext, param, orderSeqStr);
  }

  Future<AgentExecutorResponse> executeHasMoreData(
      WMLLMContext llmContext, Map param) async {
    String orderSeqStr = llmContext.actionList.last;
    return executeOrderDetailAction(llmContext, param, orderSeqStr);
  }

  Future<AgentExecutorResponse> executeOrderDetailAction(
      WMLLMContext llmContext, Map param, String orderSeqStr) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    response.prompt = ChatGPTPrompt.fuzzyOrderDetailPrompt();
    response.promptParameter = "orderParameter";
    if (StringUtil.isEmpty(orderSeqStr)) {
      if (llmContext.currentStep >= 2) {
        response.ttsString = "不好意思，没有识别出订单，你可以重新说出你的诉求";
        response.needInit = true;
        return response;
      } else {
        response.ttsString = "不好意思，没有识别出订单，你可以说出订单号或者手机尾号或者顾客地址或者订单菜品；";
        response.needMoreInfo = true;
        return response;
      }
    }
    String orderId = await OrderTools.getOrderId(orderSeqStr);
    if (StringUtil.isNotEmpty(orderId)) {
      response.ttsString = "已为你找到订单详情页面，请查看";
      response.needClose = true;
      OrderTools.gotoOrderDetailPage(orderId);
      VoiceAssistantReporter.reportOrderDetail();
    } else {
      response.ttsString = "未找到该订单";
      response.needClose = true;
      MTFToast.showToast(msg: '未找到该订单');
    }
    return Future.value(response);
  }
}
