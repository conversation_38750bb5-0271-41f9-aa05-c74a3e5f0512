import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/chain.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/order/order_agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/order/order_tools.dart';

class OrderActionChain extends ChainExecutor {
  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    if (llmContext.functionName ==
        OrderAgentExecutorMethod.partRefund().value) {
      return partRefund(llmContext, param);
    }
    if (llmContext.functionName == OrderAgentExecutorMethod.selfBtn().value) {
      return executeSelfBtn(llmContext, param);
    }
    if (llmContext.functionName == OrderAgentExecutorMethod.addFree().value) {
      return executeAddFree(llmContext, param);
    }
    if (llmContext.functionName ==
        OrderAgentExecutorMethod.connectRider().value) {
      return executeConnectRider(llmContext, param);
    }
    return null;
  }

  Future<AgentExecutorResponse> executeSelfBtn(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String orderSeq = param['orderSeq'].toString();
    await OrderTools.selfLogisticsBtn(llmContext.currentBuildContext, orderSeq);
    response.needInit = true;
    return response;
  }

  Future<AgentExecutorResponse> partRefund(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String orderSeq = param['orderSeq'].toString();
    await OrderTools.partRefund(orderSeq);
    response.needInit = true;
    return response;
  }

  Future<AgentExecutorResponse> executeAddFree(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String orderSeq = param['orderSeq'].toString();
    await OrderTools.addFee(orderSeq);
    response.needInit = true;
    return response;
  }

  Future<AgentExecutorResponse> executeConnectRider(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String orderSeq = param['orderSeq'].toString();
    await OrderTools.createPhone(llmContext.currentBuildContext, orderSeq);
    response.needInit = true;
    return response;
  }
}
