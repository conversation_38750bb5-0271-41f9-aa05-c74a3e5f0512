import 'package:flutter/material.dart';
import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/chain.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/order/order_agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_service.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/prompt.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/order/order_tools.dart';

class FoodDoneChain extends ChainExecutor {
  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    if (llmContext.conversionState == ConversationState.initial) {
      return executeInit(llmContext, param);
    } else if (llmContext.conversionState == ConversationState.hasMoreData) {
      return executeHasMoreData(llmContext, param);
    } else if (llmContext.conversionState ==
        ConversationState.confirmationFinish) {
      return executeFinish(llmContext, param);
    }
    return null;
  }

  Future<AgentExecutorResponse> executeInit(
      WMLLMContext llmContext, Map param) async {
    String orderSeqStr = await LLMService.queryService(
      ChatGPTPrompt.fuzzyFoodDonePrompt(),
      llmContext.asrResult,
      'orderParameter',
    );
    debugPrint("executeInit");
    return executeFoodDoneAction(llmContext, param, orderSeqStr);
  }

  Future<AgentExecutorResponse> executeHasMoreData(
      WMLLMContext llmContext, Map param) async {
    String orderSeqStr = llmContext.actionList.last;
    return executeFoodDoneAction(llmContext, param, orderSeqStr);
  }

  Future<AgentExecutorResponse> executeFinish(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    AgentExecutorResponse lastResponse = llmContext.executeResults.last;
    OrderAgentResponseData data = lastResponse.data as OrderAgentResponseData;
    String orderId = data.orderId;
    int orderSeq = data.orderSeq;
    String functionConfirm = llmContext.actionList.last;
    if (functionConfirm == ConversationConfirmState.confirm.value) {
      if (StringUtil.isNotEmpty(orderId)) {
        return getFoodDoneResult(orderSeq, orderId, response);
      } else {
        response.ttsString = "没有查询到该订单";
        response.needClose = true;
        return response;
      }
    } else {
      response.ttsString = "已取消本次操作";
      response.needClose = true;
      return response;
    }
  }

  Future<AgentExecutorResponse> getFoodDoneResult(
      int orderSeq, String orderId, AgentExecutorResponse response) async {
    int result = await OrderTools.setFoodDone(orderSeq, orderId);
    if (result == -1) {
      response.ttsString = "不好意思，出餐失败了";
      response.needClose = true;
    } else if (result == 1) {
      response.ttsString = "$orderSeq号订单已经出过餐了";
      response.needClose = true;
    } else {
      response.ttsString = '已为你执行$orderSeq号订单出餐';
      response.operationDesc = response.ttsString;
      response.needClose = true;
    }
    VoiceAssistantReporter.reportFoodDone(val: {"success": result});
    return response;
  }

  Future<AgentExecutorResponse> executeFoodDoneAction(
      WMLLMContext llmContext, Map param, String orderSeqStr) async {
    debugPrint("executeFoodDoneAction");
    AgentExecutorResponse response = AgentExecutorResponse();
    response.prompt = ChatGPTPrompt.fuzzyFoodDonePrompt();
    response.promptParameter = "orderParameter";
    if (StringUtil.isEmpty(orderSeqStr)) {
      if (llmContext.currentStep >= 2) {
        response.ttsString = "不好意思，没有识别出订单，你可以重新说出你的诉求";
        response.needInit = true;
        return response;
      } else {
        response.ttsString = "不好意思，没有识别出订单，你可以说出订单号或者手机尾号或者顾客地址或者订单菜品；";
        response.needMoreInfo = true;
        return response;
      }
    }
    int orderSeq = await OrderTools.getDayseq(orderSeqStr);
    String orderId = await OrderTools.getOrderId(orderSeq.toString());
    if (StringUtil.isEmpty(orderId)) {
      response.ttsString = "没有查询到该订单";
      response.needClose = true;
      MTFToast.showToast(msg: '没有查询到该订单');
      return response;
    }
    //出餐考核需求后，出餐完成按钮展示有两套逻辑
    List list = await Future.wait([
      OrderTools.getOldOrderIsShow(orderSeq.toString()),
      OrderTools.getNewOrderIsShow(orderSeq.toString()),
      OrderTools.getOrderExamine(orderSeq.toString()),
      OrderTools.getOrderCanClickLimitTime(orderSeq.toString()),
      OrderTools.getIsPhfOrder(orderSeq.toString())
    ]);
    bool isOldShow = list[0];
    bool isNewShow = list[1];
    bool isFoodExamine = list[2];
    int canClickLimitTime = list[3];
    bool isPhfOrder = list[4];
    if (isPhfOrder) {
      response.ttsString = "暂不支持拼好饭订单出餐";
      response.needClose = true;
      MTFToast.showToast(msg: '暂不支持拼好饭订单出餐');
      response.needClose = true;
      return response;
    }
    if (isFoodExamine ?? false) {
      if (isNewShow != true) {
        return notSatisfyCondition(response);
      }
    } else {
      if ((isOldShow != true) ||
          canClickLimitTime > DateTime.now().millisecondsSinceEpoch ~/ 1000) {
        return notSatisfyCondition(response);
      }
    }
    response.ttsString = "请问你确认$orderSeq号订单出餐完成吗？";
    response.needConfirm = true;
    response.data = OrderAgentResponseData(orderSeq, orderId);
    return response;
  }

  AgentExecutorResponse notSatisfyCondition(AgentExecutorResponse response) {
    MTFToast.showToast(
      msg: '暂不满足出餐条件',
    );
    response.ttsString = "暂不满足出餐条件";
    response.needClose = true;

    return response;
  }
}
