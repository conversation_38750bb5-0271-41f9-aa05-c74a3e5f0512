import 'package:mtf_toast/mtf_toast.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/chain.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_service.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/prompt.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/order/order_tools.dart';

class OrderCallUserChain extends ChainExecutor {
  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    String wmOrderId = param['wmOrderId']?.toString();
    if (StringUtil.isNotEmpty(wmOrderId)) {
      return executeCallUser(llmContext, wmOrderId);
    }
    if (llmContext.conversionState == ConversationState.initial) {
      return executeInit(llmContext, param);
    } else if (llmContext.conversionState == ConversationState.hasMoreData) {
      return executeHasMoreData(llmContext, param);
    }
  }

  Future<AgentExecutorResponse> executeCallUser(
      WMLLMContext llmContext, String orderId) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    response.promptCode = ChatGPTPrompt.orderPrompt;
    OrderTools.callUser(llmContext.currentBuildContext, orderId);
    response.needInit = true;
    return response;
  }

  Future<AgentExecutorResponse> executeInit(
      WMLLMContext llmContext, Map param) async {
    String orderSeqStr = await LLMService.queryService(
      ChatGPTPrompt.fuzzyCallUserPrompt(),
      llmContext.asrResult,
      'orderParameter',
    );
    return executeCallUserAction(llmContext, param, orderSeqStr);
  }

  Future<AgentExecutorResponse> executeHasMoreData(
      WMLLMContext llmContext, Map param) async {
    String orderSeqStr = llmContext.actionList.last;
    return executeCallUserAction(llmContext, param, orderSeqStr);
  }

  Future<AgentExecutorResponse> executeCallUserAction(
      WMLLMContext llmContext, Map param, String orderSeqStr) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    response.prompt = ChatGPTPrompt.fuzzyCallUserPrompt();
    response.promptParameter = "orderParameter";
    if (StringUtil.isEmpty(orderSeqStr)) {
      if (llmContext.currentStep >= 2) {
        response.ttsString = "不好意思，没有识别出订单，你可以重新说出你的诉求";
        response.needInit = true;
        return response;
      } else {
        response.ttsString = "不好意思，没有识别出订单，你可以说出订单号或者手机尾号或者顾客地址或者订单菜品；";
        response.needMoreInfo = true;
        return response;
      }
    }
    String orderId = await OrderTools.getOrderId(orderSeqStr);
    if (StringUtil.isNotEmpty(orderId)) {
      return callUser(llmContext, orderId, response);
    } else {
      response.ttsString = "未找到该订单";
      response.needClose = true;
      MTFToast.showToast(msg: '未找到该订单');
    }
    return Future.value(response);
  }

  Future<AgentExecutorResponse> callUser(WMLLMContext llmContext,
      String orderId, AgentExecutorResponse response) async {
    bool isShowCallUser = await OrderTools.isShowCallUser(orderId);
    if (isShowCallUser ?? false) {
      OrderTools.callUser(llmContext.currentBuildContext, orderId);
      response.needInit = true;
    } else {
      response.ttsString = "暂不支持联系顾客";
      response.needClose = true;
      MTFToast.showToast(msg: '暂不支持联系顾客');
    }
    VoiceAssistantReporter.reportCallUser();
    return response;
  }
}
