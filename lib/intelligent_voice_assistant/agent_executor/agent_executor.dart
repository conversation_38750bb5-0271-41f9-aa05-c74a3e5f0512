import 'package:flutter/widgets.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/app/function_agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/customer/customer_agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/goods/goods_agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/im/im_agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/shop/shop_agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'order/order_agent_executor.dart';

class AgentExecutorResponse {
  /// 状态流转 每次需填写一个为true
  bool needConfirm = false;
  bool needClose = false;
  bool needMoreInfo = false;
  bool needInit = false;

  /// 数据展示 需用户确认 操作区上部分
  bool showChose = false;
  String replyContent;

  ///  语音播报
  String ttsString;

  /// 操作区字符串
  String operationDesc;

  /// 返回数据
  dynamic data;
  String promptCode;
  String prompt;
  String promptParameter;

  bool selfCheck() {
    if (needConfirm == false &&
        needClose == false &&
        needMoreInfo == false &&
        needInit == false) {
      return false;
    } else {
      return true;
    }
  }

  String debugStateString() {
    String str = "";
    if (needConfirm == true) {
      str = str + "需要用户确认，";
    }
    if (needMoreInfo == true) {
      str = str + "需要更多的数据，";
    }
    if (needInit == true) {
      str = str + "内容重置。";
    }
    if (needClose == true) {
      str = str + "即将关闭。";
    }
    return str;
  }

  void debugOutput() {
    debugPrint('AgentExecutorResponse Debug Output:');
    debugPrint('-------------------------------------');
    debugPrint('State:${debugStateString()}');
    debugPrint('Show Chose: $showChose');
    debugPrint('Reply Content: $replyContent');
    debugPrint('TTS String: $ttsString');
    debugPrint('Operation Description: $operationDesc');
    debugPrint('Data: ${data.toString()}');
    debugPrint('Prompt Code: $promptCode');
    debugPrint('Prompt: $prompt');
    debugPrint('Prompt Parameter: $promptParameter');
    debugPrint('-------------------------------------');
  }
}

abstract class AgentExecutor {
  Future<AgentExecutorResponse> execute(WMLLMContext llmContext, Map param);
  String toolName;
  List<String> actionList = [];
}

class WMAgentExecutor {
  List<AgentExecutor> _agents = [];
  static final WMAgentExecutor _instance =
      WMAgentExecutor._privateConstructor();

  // 私有构造函数
  WMAgentExecutor._privateConstructor() {
    _agents = [
      orderAgentExector,
      goodsAgentExecutor,
      shopAgentExecutor,
      functionAgentExecutor,
      imAgentExecutor,
      customerAgentExecutor
    ];
  }

  // 注册 Executor
  void registerExecutor(AgentExecutor executor) {
    _agents.add(executor);
  }

  // 提供一个工厂方法来获取单例
  factory WMAgentExecutor() {
    return _instance;
  }
// 内置 agent
  OrderAgentExecutor orderAgentExector = OrderAgentExecutor();
  GoodsAgentExecutor goodsAgentExecutor = GoodsAgentExecutor();
  ShopAgentExecutor shopAgentExecutor = ShopAgentExecutor();
  FunctionAgentExecutor functionAgentExecutor = FunctionAgentExecutor();
  IMAgentExecutor imAgentExecutor = IMAgentExecutor();
  CustomerAgentExecutor customerAgentExecutor = CustomerAgentExecutor();
  // 执行方法
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    String function = llmContext.functionName;
    AgentExecutor agentExecutor = _getExecutor(function);
    AgentExecutorResponse response =
        await agentExecutor.execute(llmContext, param);
    assert(response.selfCheck() == true,
        "请至少设置 response 中的一个步骤为true，需要进行下一步的状态流转");
    response.debugOutput();
    return response;
  }

  // 获取对应的 Executor
  AgentExecutor _getExecutor(String functionName) {
    return _agents.firstWhere(
      (executor) => executor.actionList.contains(functionName),
      orElse: () => customerAgentExecutor, // 默认 Executor
    );
  }
}
