import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_response.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_service.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/im/im_tools.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

class IMAgentExecutor with AgentExecutor {
  @override
  String get toolName => 'IM';

  IMAgentExecutor() {
    actionList = [IMAgentExecutorMethod.autpReply.value];
  }

  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    if (llmContext.functionName == IMAgentExecutorMethod.autpReply.value) {
      return executeAutoReply(llmContext, param);
    }
    return null;
  }

  Future<AgentExecutorResponse> executeAutoReply(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    if (llmContext.conversionState == ConversationState.initial) {
      return executeInit(llmContext, param);
    } else if (llmContext.conversionState ==
        ConversationState.confirmationFinish) {
      String functionConfirm = llmContext.actionList.last;
      if (functionConfirm == ConversationConfirmState.confirm.value) {
        AgentExecutorResponse lastResponse = llmContext.executeResults.last;
        IMAgentExecutorResponse data =
            lastResponse.data as IMAgentExecutorResponse;
        IMTools.sendIMMessage(data.reply, data.pushExtra);
        response.needClose = true;
        response.ttsString = "已经回复";
      } else {
        response.ttsString = "已取消本次操作";
        response.needClose = true;
      }
    }
    return Future.value(response);
  }

  Future<AgentExecutorResponse> executeInit(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String content = param['userImContent'] ?? '';
    String pushExtra = param['pushExtra'] ?? '';
    int orderId = param['orderSeq'] ?? 0;
    String prompt = imReplyPrompt(
      content,
      orderId,
    );
    String value = await LLMService.createServiceChat(prompt);
    List<Message> chats = [
      LLMService.createMessage(prompt),
      LLMService.createMessage(value)
    ];
    if (!value.contains('系统溜号了')) {
      IMAgentExecutorResponse data =
          IMAgentExecutorResponse(value, pushExtra, content);
      response.data = data;
      response.needConfirm = true;
      response.showChose = true;
      response.ttsString = "已为你自动生成一条回复消息: $value 是否帮您自动回复";
      response.replyContent = value;
      VoiceAssistantReporter.reportGenImMessage(val: {
        'reply': value ?? "",
        'user': content ?? '',
      });
      return response;
    } else {
      response.ttsString = "系统问题，请稍后重试";
      response.needClose = true;
    }
    return response;
  }

  String imReplyPrompt(
    String content,
    int orderId,
  ) {
    PoiInfo poiInfo = PoiMananger.getInstance().poiInfo;
    String poiId = poiInfo?.poiId ?? 0;

    int promptLenght = orderId % 2 == 0 ? 20 : 30;

    int poiFlag = int.tryParse(poiId) % 2;
    String prompt;
    if (poiFlag == 0) {
      prompt = '你是一个外卖商家，你收到了一条用户发来的消息，消息内容是"$content"，'
          '不要提及配送,不要说已送达,不要提及退款，退钱，补送'
          '请帮我生成一条回复消息，意思是已经收到了用户的消息，回复给用户，不要超过$promptLenght个字，可以口语化一点。'
          '请严格直接返回「回复的内容」，不要有多余的文案解释';
    } else {
      prompt = '你是一个外卖商家，你收到了一条用户发来的消息，消息内容是"$content"，'
          '不要提及配送,不要说已送达,不要提及退款，退钱，补送。'
          '请帮我生成一条回复消息安抚顾客，开头表示已收到信息，中间对信息进行回复，最终感谢，不要超过$promptLenght个字，口语化一点。'
          '如果用户是要催单，要有给用户加急处理的意思，如果已等待超过30分钟，要表现出高度关注。'
          '请严格直接返回「回复的内容」，不要有多余的文案解释。';
    }

    return prompt;
  }
}

class IMAgentExecutorResponse {
  String content;
  String reply;
  String pushExtra;
  IMAgentExecutorResponse(this.reply, this.pushExtra, this.content);
}

class IMAgentExecutorMethod {
  /// 返回的状态
  static const IMAgentExecutorMethod autpReply =
      IMAgentExecutorMethod._('安抚顾客', "帮助用户自动回复IM");
  final String value;
  final String description;
  const IMAgentExecutorMethod._(this.value, this.description);
}
