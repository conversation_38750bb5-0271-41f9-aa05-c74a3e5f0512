import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';

class FunctionAgentExecutor with AgentExecutor {
  List<FunctionAgentExecutorMethod> actions;
  FunctionAgentExecutor() {
    actions = [
      FunctionAgentExecutorMethod.manageAnalysis,
      FunctionAgentExecutorMethod.customerReview,
      FunctionAgentExecutorMethod.activityCenter,
      FunctionAgentExecutorMethod.viewProduct,
      FunctionAgentExecutorMethod.deliveryService,
      FunctionAgentExecutorMethod.serviceMarket,
      FunctionAgentExecutorMethod.ruleCenter,
      FunctionAgentExecutorMethod.storeDecoration,
      FunctionAgentExecutorMethod.meituanMember,
      FunctionAgentExecutorMethod.activityRegistration,
      FunctionAgentExecutorMethod.productAssistant,
      FunctionAgentExecutorMethod.merchantCommunity,
      FunctionAgentExecutorMethod.takeoutClassroom,
      FunctionAgentExecutorMethod.storePickup,
      FunctionAgentExecutorMethod.wechatPromotionCode,
      FunctionAgentExecutorMethod.orderInsurance,
      FunctionAgentExecutorMethod.rapidRefund,
      FunctionAgentExecutorMethod.takeoutPeaceCard,
      FunctionAgentExecutorMethod.changeBusinessStatus,
      FunctionAgentExecutorMethod.setStoreInfo,
      FunctionAgentExecutorMethod.setBusinessHours,
      FunctionAgentExecutorMethod.breakProtection,
      FunctionAgentExecutorMethod.setOrders,
      FunctionAgentExecutorMethod.setNotificationsAndRingtones,
      FunctionAgentExecutorMethod.setPrinting,
      FunctionAgentExecutorMethod.feedback,
      FunctionAgentExecutorMethod.riderNotAcceptingOrders,
      FunctionAgentExecutorMethod.applyForMealDamageCompensation,
      FunctionAgentExecutorMethod.handleRefund
    ];
    actionList = actions.map((action) => action.value).toList();
  }

  @override
  String get toolName => 'Function';

  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String functionName = llmContext.functionName;
    String url;
    var element = actions.firstWhere((element) => element.value == functionName,
        orElse: () => null);
    if (element != null) {
      url = element.url;
    }
    if (StringUtil.isNotEmpty(url)) {
      RouteUtils.open(url);
      response.ttsString = "已为你找到合适的页面说明，请查看";
      response.needClose = true;
    } else {
      response.ttsString = "暂未找到合适页面";
      response.needInit = true;
    }
    return Future.value(response);
  }
}

class FunctionAgentExecutorMethod {
  /// 返回的状态
  static const FunctionAgentExecutorMethod manageAnalysis =
      FunctionAgentExecutorMethod._('经营分析', '查看经营数据，经营分析，数据',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=flap&flap_id=b_fe_bizdata&flap_entry=IndexPage&moduleName=waimai_e_fe_flutter_bizdata&init_route=Home');
  static const FunctionAgentExecutorMethod customerReview =
      FunctionAgentExecutorMethod._('顾客评价', '顾客评价，评价，评分，有没有差评',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=user_comment/home');
  static const FunctionAgentExecutorMethod activityCenter =
      FunctionAgentExecutorMethod._(
          '活动中心',
          '活动中心，营销活动，活动，营销，满减，折扣菜，配送费设置，代金券，推广',
          'https://waimaieapp.meituan.com/igate/wmact/h5/index.html?el_biz=waimaibiz&el_page=waimai_mfe_business_marketing_b.h5/index');

  static const FunctionAgentExecutorMethod viewProduct =
      FunctionAgentExecutorMethod._('查看商品', '商品、商品管理、新建商品、编辑商品、菜品，商品操作',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=food/management');
  static const FunctionAgentExecutorMethod deliveryService =
      FunctionAgentExecutorMethod._('配送服务', '配送，配送服务，配送范围',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=dynamic_delivery');
  static const FunctionAgentExecutorMethod serviceMarket =
      FunctionAgentExecutorMethod._('服务市场', '服务市场，代运营，包材，餐盒，餐具',
          'itakeawaybiz://waimaieapi.meituan.com/mrn?mrn_biz=waimaibiz&mrn_entry=wmb-mrn-plus&mrn_component=service_market&showBack=1');
  static const FunctionAgentExecutorMethod ruleCenter =
      FunctionAgentExecutorMethod._('规则中心', '规则，违规',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=rule/main_page');
  static const FunctionAgentExecutorMethod storeDecoration =
      FunctionAgentExecutorMethod._(
          '店铺装修',
          '门店装修，店铺装修，装修，门店海报，海报，店铺招牌，招牌，老板推荐，门店展示，主题装修',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=shop/decoration/main');
  static const FunctionAgentExecutorMethod meituanMember =
      FunctionAgentExecutorMethod._('美团会员', '会员，美团会员',
          'https://waimaieapp.meituan.com/v1/mss_4067b040e3364072a20737a5254654ea/business-vip-f43d85e5/production/pages/index.html?el_page=business_vip.index&el_biz=waimaibiz');
  static const FunctionAgentExecutorMethod activityRegistration =
      FunctionAgentExecutorMethod._('活动报名', '活动报名，报名活动',
          'https://waimaieapp.meituan.com/igate/wmactapp/enrollment/index');
  static const FunctionAgentExecutorMethod productAssistant =
      FunctionAgentExecutorMethod._('商品助手', '商品助手',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=flap&flap_id=shop_assistant&flap_entry=ShopAssistantPage&moduleName=waimai_e_flutter_goods');
  static const FunctionAgentExecutorMethod merchantCommunity =
      FunctionAgentExecutorMethod._('商家社区', '社区',
          'https://waimaieapp.meituan.com/community/page/index?hideNativeNavBar=1');
  static const FunctionAgentExecutorMethod takeoutClassroom =
      FunctionAgentExecutorMethod._('外卖课堂', '课堂，外卖学院，文章',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=college/home');
  static const FunctionAgentExecutorMethod storePickup =
      FunctionAgentExecutorMethod._('到店自取', '到店自取，自取',
          'https://waimaieapi.meituan.com/i?router-view=selfpickup');
  static const FunctionAgentExecutorMethod wechatPromotionCode =
      FunctionAgentExecutorMethod._('微信推广码', '二维码，推广码，门店二维码',
          'https://proxy.waimai.st.sankuai.com/igate/wmactapp/poiLink');
  static const FunctionAgentExecutorMethod orderInsurance =
      FunctionAgentExecutorMethod._('订单保险', '订单保险，保险',
          'https://insurancex.meituan.com/operation/view/insurance/wmbusiness/index.html?insurance_source=waimaie&entrance_source=waimai_Home_core');
  static const FunctionAgentExecutorMethod rapidRefund =
      FunctionAgentExecutorMethod._('极速退款', '极速退款',
          'https://waimaieapi.meituan.com/i?router-view=refund');
  static const FunctionAgentExecutorMethod takeoutPeaceCard =
      FunctionAgentExecutorMethod._('外卖安心卡', '安心卡，外卖安心卡',
          'https://waimaieapp.meituan.com/growth/index#/securityCard');
  static const FunctionAgentExecutorMethod changeBusinessStatus =
      FunctionAgentExecutorMethod._('修改营业状态', '营业状态，停止营业，开始营业，营业',
          'itakeawaybiz://waimaieapi.meituan.com/updateShopStatus');
  static const FunctionAgentExecutorMethod setStoreInfo =
      FunctionAgentExecutorMethod._(
          '设置门店信息',
          '设置门店，门店设置，门店名称，门店品类，头像，logo，设置门店信息',
          'itakeawaybiz://waimaieapi.meituan.com/mrn?mrn_biz=waimaibiz&mrn_entry=poiSettings&mrn_component=poiSettings');
  static const FunctionAgentExecutorMethod setBusinessHours =
      FunctionAgentExecutorMethod._('设置营业时间', '营业时间',
          'itakeawaybiz://waimaieapi.meituan.com/updateShippingTime');
  static const FunctionAgentExecutorMethod breakProtection =
      FunctionAgentExecutorMethod._('歇业保护', '歇业保护',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=flap&flap_id=close_business_protect&flap_entry=ClosingProtectPage');
  static const FunctionAgentExecutorMethod setOrders =
      FunctionAgentExecutorMethod._('设置订单', '订单设置，接单设置',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=order/setting/new');
  static const FunctionAgentExecutorMethod setNotificationsAndRingtones =
      FunctionAgentExecutorMethod._('设置消息与铃声', '铃声，消息设置，接单铃声，来单不响，声音',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=message_and_sound_setting&noDisturbGray=true');
  static const FunctionAgentExecutorMethod setPrinting =
      FunctionAgentExecutorMethod._('设置打印', '打印机，打印设置',
          'itakeawaybiz://waimaieapi.meituan.com/printer/setting');
  static const FunctionAgentExecutorMethod feedback = FunctionAgentExecutorMethod._(
      '意见反馈',
      '反馈，意见，投诉',
      'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=flap&flap_id=capture_suggest&flap_entry=CaptureSuggestPage&moduleName=waimai_e_flutter_operation&type=101');
  static const FunctionAgentExecutorMethod riderNotAcceptingOrders =
      FunctionAgentExecutorMethod._('骑手不接单', '无骑手接单，不接单',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=flap&flap_id=fe_college&flap_entry=CollegeDetailPage&moduleName=waimai_e_fe_flutter_college&id=1681&contentType=2');
  static const FunctionAgentExecutorMethod applyForMealDamageCompensation =
      FunctionAgentExecutorMethod._('申请餐损赔付', '餐损，赔付',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=flap&flap_id=fe_college&flap_entry=CollegeDetailPage&moduleName=waimai_e_fe_flutter_college&id=1711&contentType=2');
  static const FunctionAgentExecutorMethod handleRefund =
      FunctionAgentExecutorMethod._('处理退款', '退款，处理退款',
          'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=flap&flap_id=fe_college&flap_entry=CollegeDetailPage&moduleName=waimai_e_fe_flutter_college&id=1703&contentType=2');

  final String value;
  final String description;
  final String url;
  const FunctionAgentExecutorMethod._(this.value, this.description, this.url);
}
