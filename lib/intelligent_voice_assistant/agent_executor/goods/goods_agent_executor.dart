import 'package:flutter/material.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_service.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/prompt.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/goods/goods_api.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/goods/goods_tools.dart';

class GoodsAgentExecutor with AgentExecutor {
  GoodsAgentExecutor() {
    actionList = [
      GoodsAgentExecutorMethod.listProduct.value,
      GoodsAgentExecutorMethod.removeProduct.value
    ];
  }

  @override
  String get toolName => 'Goods';

  @override
  Future<AgentExecutorResponse> execute(
      WMLLMContext llmContext, Map param) async {
    /// 进行一次 spuName 提取 根据第一次的语音，这里没想好这个逻辑应该放在那个地方
    if (llmContext.conversionState == ConversationState.initial) {
      return executeInit(llmContext, param);
    } else if (llmContext.conversionState == ConversationState.hasMoreData) {
      return executeHasMoreData(llmContext, param);
    } else if (llmContext.conversionState ==
        ConversationState.confirmationFinish) {
      return executeFinish(llmContext, param);
    }
    return null;
  }

  Future<AgentExecutorResponse> executeHasMoreData(
      WMLLMContext llmContext, Map param) {
    String spuName = llmContext.actionList.last;
    return executeGoodsAction(llmContext, param, spuName);
  }

  Future<AgentExecutorResponse> executeGoodsAction(
      WMLLMContext llmContext, Map param, String spuName) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    String action = llmContext.functionName;
    response.promptCode = ChatGPTPrompt.productPrompt;
    response.promptParameter = "spuName";
    if (StringUtil.isEmpty(spuName)) {
      if (llmContext.currentStep > 2) {
        response.needClose = true;
        response.ttsString = "不好意思，没有识别出商品名";
      } else {
        response.ttsString = "不好意思，没有识别出商品名，请再说一次";
        response.needMoreInfo = true;
      }
      return Future.value(response);
    }
    WmProductSpuVo spuVo = await GoodsApi.getSpuVO(spuName);
    int spuId = spuVo?.id ?? -1;
    if (spuId == -1) {
      response.ttsString = "不好意思，没有找到该商品，请检查下商品哦";
      response.needClose = true;
    } else {
      if (spuVo?.sellStatus == FoodSellStatus.notInSell &&
          GoodsAgentExecutorMethod.removeProduct.value == action) {
        response.ttsString = "${spuVo?.name}已经是下架状态，无需重复操作'";
        response.needClose = true;
      } else if (spuVo?.sellStatus == FoodSellStatus.selling &&
          GoodsAgentExecutorMethod.listProduct.value == action) {
        response.ttsString = "${spuVo?.name}已经是售卖中状态，无需重复操作";
        response.needClose = true;
      } else {
        String funcstr = GoodsAgentExecutorMethod.removeProduct.value == action
            ? '下架'
            : '上架';
        // 商品号有效，再次和商家确认
        String spuStr = '请问你确认要把${spuVo?.name}$funcstr吗？';
        response.ttsString = spuStr;
        response.needConfirm = true;
        response.data = GoodsAgentResponseData(spuName, spuId);
      }
      return response;
    }
    return response;
  }

  Future<AgentExecutorResponse> executeFinish(
      WMLLMContext llmContext, Map param) async {
    debugPrint("_executeFinish_");
    String confirm = llmContext.actionList.last;
    AgentExecutorResponse response = AgentExecutorResponse();
    response.needClose = true;
    if (confirm == ConversationConfirmState.cancle.value) {
      response.ttsString = "已取消本次操作";
      response.needClose = true;
      return response;
    }
    AgentExecutorResponse lastResponse = llmContext.executeResults.last;
    if (lastResponse.data == null) {
      response.ttsString = "系统状态异常";
      response.needClose = true;
      return response;
    }
    if (confirm == ConversationConfirmState.confirm.value) {
      int sellStatus = llmContext.functionName ==
              GoodsAgentExecutorMethod.removeProduct.value
          ? 1
          : 0;
      GoodsAgentResponseData data = lastResponse.data as GoodsAgentResponseData;
      int spuId = data.spuId;
      return executeAction(llmContext, sellStatus, spuId);
    }
    return response;
  }

  Future<AgentExecutorResponse> executeAction(
    WMLLMContext llmContext,
    int sellStatus,
    int spuId,
  ) async {
    AgentExecutorResponse response = AgentExecutorResponse();
    bool value = await GoodsTools.setFoodStatus(sellStatus, spuId);
    if (value == true) {
      response.ttsString = '已为你执行${llmContext.functionName}';
      response.operationDesc = response.ttsString;
      response.needClose = true;
      VoiceAssistantReporter.reportGoodToList(
          val: {"sellStatus": sellStatus, "success": true});
    } else {
      response.ttsString = '不好意思，${llmContext.functionName}失败了';
      response.operationDesc = response.ttsString;
      response.needClose = true;
      VoiceAssistantReporter.reportGoodToList(
          val: {"sellStatus": sellStatus, "success": false});
    }
    return response;
  }

  Future<AgentExecutorResponse> executeInit(
      WMLLMContext llmContext, Map param) async {
    String spuName = await LLMService.queryService(
        '', llmContext.asrResult, 'spuName',
        promptCode: ChatGPTPrompt.productPrompt);
    return executeGoodsAction(llmContext, param, spuName);
  }
}

class GoodsAgentResponseData {
  String spuName;
  int spuId;
  GoodsAgentResponseData(this.spuName, this.spuId);
}

class GoodsAgentExecutorMethod {
  /// 返回的状态
  static const GoodsAgentExecutorMethod removeProduct =
      GoodsAgentExecutorMethod._('商品下架', "下架商品，卖完，无库存，撤了");
  static const GoodsAgentExecutorMethod listProduct =
      GoodsAgentExecutorMethod._('商品上架', "商品上架，上架，有库存，有货");
  final String value;
  final String description;
  const GoodsAgentExecutorMethod._(this.value, this.description);
}
