import 'dart:async';

import 'package:flap/flap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lx/flutter_lx.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:provider/provider.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/widget/voice_assistant_associated_widget.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/widget/voice_assistant_content_widget.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/widget/voice_assistant_wakeup_setting_widget.dart';

@Flap('voice_assistant_gpt')
@MTFRoute('voice_assistant_gpt')
class VoiceAssistantGPTPage extends StatefulWidget {
  const VoiceAssistantGPTPage({Key key, this.pageName, this.params})
      : super(key: key);

  final Map<dynamic, dynamic> params;

  final String pageName;

  @override
  State<VoiceAssistantGPTPage> createState() => _VoiceAssistantPageState();
}

class _VoiceAssistantPageState extends State<VoiceAssistantGPTPage>
    with SingleTickerProviderStateMixin, RouteLifecycleStateMixin {
  VoiceAssistantPageModel pageModel = VoiceAssistantPageModel();
  VoiceAssistantPageVo pageVo = VoiceAssistantPageVo();
  AnimationController _animationController;

  Animation _scaleAnimation;
  StreamSubscription closeSubscription;

  String cid = 'c_waimai_e_7zqo13nu';
  String pageInfoKey = '42041416';

  @override
  void initState() {
    super.initState();
    pageModel.initData(pageVo, widget.params, () {});
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 在这里使用context
      pageModel.context = context;
    });
    _animationController = AnimationController(
        duration: const Duration(milliseconds: 250), vsync: this);
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.forward) {
        debugPrint("status is forward");
        //执行 controller.forward() 会回调此状态
      } else if (status == AnimationStatus.completed) {
        //动画从 controller.forward() 正向执行 结束时会回调此方法
        debugPrint("status is completed");
      } else if (status == AnimationStatus.reverse) {
        //执行 controller.reverse() 会回调此状态
        debugPrint("status is reverse");
      } else if (status == AnimationStatus.dismissed) {
        //动画从 controller.reverse() 反向执行 结束时会回调此方法
        debugPrint("status is dismissed");
        RouteUtils.close(context, animated: false);
      }
    });

    _scaleAnimation = Tween(begin: 0.0, end: 1.0).animate(CurvedAnimation(
        parent: _animationController,
        curve: const Cubic(0.33, 0.00, 0.35, 1.0)));

    Future.delayed(Duration.zero, () {
      if (!mounted) {
        return;
      }
      _animationController?.forward();
    });

    closeSubscription =
        RouteUtils.subscribe('voice_assistant_page_close_notification', (data) {
      if (!mounted) {
        return;
      }
      _animationController?.reverse();
    });
  }

  @override
  void didAppear() {
    FlutterLx.pageView(pageInfoKey, cid);
  }

  @override
  void didDisappear() {
    FlutterLx.pageDidDisappear(pageInfoKey, cid);
  }

  @override
  void dispose() {
    VoiceAssistantReporter.reportClose(
        pageVo.startTime, pageVo?.mainEntranceType);
    _animationController.dispose();
    pageModel.dispose();
    closeSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
          child: ChangeNotifierProvider<VoiceAssistantPageVo>.value(
        value: pageVo,
        child: Stack(
          children: <Widget>[
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                _animationController.reverse();
              },
              child: Container(),
            ),
            Positioned(
              height: MediaQuery.of(context).size.height * 0.8,
              bottom: 45,
              left: 12,
              right: 12,
              child: ScaleTransition(
                alignment: Alignment.bottomRight,
                scale: _scaleAnimation,
                child: Selector<VoiceAssistantPageVo, VoiceAssistantPageType>(
                    selector: (_, pageVo) {
                  return pageVo.pageType;
                }, builder: (context, pageType, _) {
                  if (pageType == VoiceAssistantPageType.setting) {
                    return VoiceAssistantWakeupSettingWidget(
                      pageModel: pageModel,
                    );
                  }
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      VoiceAssistantAssociatedWidget(
                        pageModel: pageModel,
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      VoiceAssistantContentWidget(
                        pageModel: pageModel,
                      )
                    ],
                  );
                }),
              ),
            )
          ],
        ),
      )),
    );
  }
}
