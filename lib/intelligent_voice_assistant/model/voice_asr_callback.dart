import 'package:waima<PERSON>_e_flutter_house_keeper/intelligent_voice_assistant/model/recog_result.dart';

mixin VoiceASRCallback on Object {
  /// 语音录制达到最大的录制时间自动结束
  void onOvertimeClose();

  /// 识别的中间结果
  ///
  /// @param audioId 语音ID，可以通过这个id从服务端获取相关信心
  /// @param result 识别结果
  void onASRTempResult(String audioId, String result);

  /// 识别成功
  ///
  /// @param audioId 语音ID，可以通过这个id从服务端获取相关信心
  /// @param result 识别结果
  void onASRSuccess(String audioId, String result);

  /// 识别失败
  ///
  /// @param audioId 语音ID
  /// @param code 错误码
  /// @param message 错误信息
  void onASRFailed();

  /// 此接口获取声音的分贝值
  void onVoiceDBSize(double voiceDB);
}
