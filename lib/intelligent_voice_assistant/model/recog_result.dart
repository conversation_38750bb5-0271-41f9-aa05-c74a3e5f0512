class ASRError {
  ASRError({this.audioId, this.code, this.message});

  ASRError.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    audioId = json['audioId'] ?? '';
    code = json['code'] ?? 0;
    message = json['message'] ?? '';
  }

  String audioId;
  int code;
  String message;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['audioId'] = this.audioId;
    data['code'] = this.code;
    data['message'] = this.message;
    return data;
  }
}
