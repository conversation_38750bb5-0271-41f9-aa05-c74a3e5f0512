///外部传过来的数据
class SceneData {
  SceneData();
  SceneData.fromJson(Map<String, dynamic> json) {
    operationDesc = json['operationDesc'];
    sceneTagCode = json['sceneTagCode'];
    if (json['imBtnInfos'] != null) {
      imBtnInfos = <IMBtnInfo>[];
      json['imBtnInfos'].forEach((v) {
        imBtnInfos.add(IMBtnInfo.fromJson(v));
      });
    }
    pushExtra = json['pushExtra'];
    userImContent = json['userImContent'];
    wmOrderId = json['wmOrderId'];
    orderSeq = json['orderSeq'];
    entranceType = json['entranceType'];
    comfortText = json['comfortText'];
    suggestText = json['suggestText'];
    isSuggestWordGray = json['isSuggestWordGray'];
  }
  String operationDesc; //按钮左侧文案
  String sceneTagCode; //场景tag
  List<IMBtnInfo> imBtnInfos; //点击事件数据
  String pushExtra; //push的内容
  String userImContent; //用户发来的消息
  String wmOrderId; //订单号
  int orderSeq; //订单序号
  String entranceType; //入口来源：点击，唤醒，push；来源不同，打开弹窗后的tts播放不同
  String comfortText; //安抚话术
  String suggestText; //建议话术
  bool isSuggestWordGray; //生成话术的灰度

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['operationDesc'] = operationDesc;
    data['sceneTagCode'] = sceneTagCode;
    if (imBtnInfos != null) {
      data['imBtnInfos'] = imBtnInfos.map((v) => v.toJson()).toList();
    }
    data['pushExtra'] = pushExtra;
    data['userImContent'] = userImContent;
    data['wmOrderId'] = wmOrderId;
    data['orderSeq'] = orderSeq;
    data['entranceType'] = entranceType;
    data['comfortText'] = comfortText;
    data['suggestText'] = suggestText;
    data['isSuggestWordGray'] = isSuggestWordGray;
    return data;
  }
}

class IMBtnInfo {
  IMBtnInfo(this.name, this.des, this.id);
  IMBtnInfo.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    des = json['description'];
    id = json['id'];
    if (json['parameters'] != null) {
      parameters = <ParametersInfo>[];
      json['parameters'].forEach((v) {
        parameters.add(ParametersInfo.fromJson(v));
      });
    }
  }
  String name; //参数名
  String des; //描述
  int id;
  List<ParametersInfo> parameters;

  @override
  String toString() {
    return '{name: $name, des: $des, id: $id, }';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['description'] = des;
    data['id'] = id;
    if (parameters != null) {
      data['parameters'] = parameters.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ParametersInfo {
  ParametersInfo.fromJson(Map<String, dynamic> json) {
    index = json['index'];
    type = json['type'];
    des = json['des'];
  }
  int index;
  String type;
  String des;
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['index'] = index;
    data['type'] = type;
    data['des'] = des;
    return data;
  }
}

class GPTResult {
  int id;
}
