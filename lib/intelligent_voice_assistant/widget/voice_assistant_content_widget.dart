import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/constant.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/widget/voice_assistant_ball_widget.dart';

class VoiceAssistantContentWidget extends StatelessWidget {
  const VoiceAssistantContentWidget({Key key, this.pageModel})
      : super(key: key);

  final VoiceAssistantPageModel pageModel;

  Widget _buildNormalContent(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: const [
          Text(
            '您可以说',
            style: TextStyle(color: Colors.white, fontSize: 12),
          ),
          TextCarouselWidget(),
        ],
      ),
    );
  }

  Widget _buildTTSContent(
      BuildContext context, VoiceAssistantState state, String tts) {
    String title = StringUtil.isNotEmpty(tts) ? '“$tts”' : '';
    return Container(
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(color: Colors.white, fontSize: 16.5),
            // maxLines: 1,
            // overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildListeningContent(
      BuildContext context, VoiceAssistantState state, String asrResult) {
    String title = StringUtil.isNotEmpty(asrResult) ? '“$asrResult”' : '';
    return Container(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 200),
        child: SingleChildScrollView(
          child: AutoSizeText(text: title),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Selector<VoiceAssistantPageVo, VoiceAssistantState>(
                  selector: (_, pageVo) {
                return pageVo.state;
              }, builder: (context, data, _) {
                VoiceAssistantState state = data;
                String url =
                    'http://p0.meituan.net/tuling/ac15c48711ff5feb5499bd4ed37f325a879.gif';
                if (state == VoiceAssistantState.listening ||
                    state == VoiceAssistantState.analysing) {
                  url =
                      'http://p0.meituan.net/tuling/3bc6ebb66e8dd09f2f8c5f28411bf3a411864.gif';
                }
                return Visibility(
                    visible: state != VoiceAssistantState.normal,
                    child: Image.network(
                      url,
                      width: 12.25,
                      height: 12.25,
                      fit: BoxFit.cover,
                    ));
              }),
              const SizedBox(width: 4),
              Selector<VoiceAssistantPageVo, VoiceAssistantState>(
                  selector: (_, pageVo) {
                return pageVo.state;
              }, builder: (context, state, _) {
                return Text(
                  state.value ?? '',
                  style: const TextStyle(color: Colors.white, fontSize: 13.5),
                );
              })
            ],
          ),
          Row(
            children: [
              Selector<VoiceAssistantPageVo, VoiceAssistantState>(
                  selector: (_, pageVo) {
                return pageVo.state;
              }, builder: (context, data, _) {
                VoiceAssistantState state = data;
                return GestureDetector(
                  onTap: () {
                    if (state == VoiceAssistantState.analysing) {
                      return;
                    }
                    pageModel.changePageType(VoiceAssistantPageType.setting);
                    VoiceAssistantReporter.reportSetting();
                  },
                  child: Text(
                    '使用说明',
                    style: state == VoiceAssistantState.analysing
                        ? const TextStyle(
                            color: Color(0xFFA5A5A5), fontSize: 13.5)
                        : const TextStyle(color: Colors.white, fontSize: 13.5),
                  ),
                );
              }),
              const SizedBox(width: 18),
              GestureDetector(
                onTap: () {
                  RouteUtils.close(context);
                },
                child: Padding(
                  padding: const EdgeInsets.only(left: 9),
                  child: Image.asset('images/voice/voice_close.png',
                      fit: BoxFit.cover, width: 11, height: 11),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
            image: DecorationImage(
              image: AdvancedNetworkImage(
                  'http://p0.meituan.net/tuling/99592b7ffad49b4891b3d01d397d7a571468959.gif'),
              fit: BoxFit.cover,
            ),
            borderRadius: const BorderRadius.all(Radius.circular(12))),
        width: SystemUtil.getScreenWidth(context) - 24,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildAppBar(context),
            Selector<VoiceAssistantPageVo,
                    Tuple3<VoiceAssistantState, String, String>>(
                selector: (_, pageVo) {
              return Tuple3(pageVo.state, pageVo.asrResult, pageVo.ttsContent);
            }, builder: (context, data, _) {
              VoiceAssistantState state = data.item1;
              debugPrint('状态:${state.value}');
              Widget container = Container();
              switch (state) {
                case VoiceAssistantState.normal:
                  container = _buildNormalContent(context);
                  break;
                case VoiceAssistantState.listening:
                  String asrResult = data.item2;
                  container = _buildListeningContent(context, state, asrResult);
                  break;
                case VoiceAssistantState.analysing:
                  String asrResult = data.item2;
                  container = _buildListeningContent(context, state, asrResult);
                  break;
                case VoiceAssistantState.tts:
                  container = _buildTTSContent(context, state, data.item3);
                  break;
                default:
                  container = _buildNormalContent(context);
              }
              return Container(
                  margin: const EdgeInsets.only(left: 15, right: 15),
                  constraints: const BoxConstraints(minHeight: 63),
                  alignment: Alignment.center,
                  child: container);
            }),
            Center(
                child: VoiceAssistantBallWidget(
              pageModel: pageModel,
            ))
          ],
        ));
  }
}

class TextCarouselWidget extends StatefulWidget {
  const TextCarouselWidget({Key key}) : super(key: key);

  @override
  _TextCarouselState createState() => _TextCarouselState();
}

class _TextCarouselState extends State<TextCarouselWidget>
    with AutomaticKeepAliveClientMixin {
  PageController _controller;
  Timer _timer;
  final List<String> _texts = VoiceAssistantConstant.guideTips;

  @override
  void initState() {
    super.initState();
    //生成一个在页面列表长度范围内的随机数
    int initialPage = Random().nextInt(_texts.length);
    _controller = PageController(initialPage: initialPage);

    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_controller.page >= _texts.length - 1) {
        _controller.jumpToPage(0);
      } else {
        _controller.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.ease,
        );
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return SizedBox(
      height: 30,
      child: PageView.builder(
        controller: _controller,
        scrollDirection: Axis.vertical,
        itemCount: _texts.length,
        itemBuilder: (context, index) {
          return Center(
            child: Text(
              _texts[index],
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          );
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class AutoSizeText extends StatelessWidget {
  final String text;

  const AutoSizeText({this.text});

  double _getFontSize(String text, double maxWidth) {
    List<double> fontSizes = [16.5, 13.5, 12];
    List<bool> sizeLine = [];
    for (int i = 0; i < fontSizes.length; i++) {
      final textPainter = TextPainter(
        text: TextSpan(text: text, style: TextStyle(fontSize: fontSizes[i])),
        maxLines: i + 1,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout(maxWidth: maxWidth);
      if (textPainter.didExceedMaxLines == true) {
        sizeLine.add(false);
      } else {
        sizeLine.add(true);
      }
    }
    int sizeResult = 2;
    for (int i = sizeLine.length - 1; i >= 0; i--) {
      bool result = sizeLine[i];
      if (result == true) {
        sizeResult = i;
      }
    }
    return fontSizes[sizeResult];
  }

  @override
  Widget build(BuildContext context) {
    double maxWidth = MediaQuery.of(context).size.width - 30; // 假设容器的最大宽度是300
    double fontSize = _getFontSize(text, maxWidth);
    return Text(
      text,
      style: TextStyle(fontSize: fontSize, color: Colors.white),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }
}
