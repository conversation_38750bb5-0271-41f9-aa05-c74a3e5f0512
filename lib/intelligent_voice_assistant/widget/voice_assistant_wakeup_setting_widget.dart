import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:provider/provider.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_vo.dart';

class VoiceAssistantWakeupSettingWidget extends StatelessWidget {
  const VoiceAssistantWakeupSettingWidget({Key key, this.pageModel})
      : super(key: key);
  final VoiceAssistantPageModel pageModel;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          decoration: BoxDecoration(
              image: DecorationImage(
                image: AdvancedNetworkImage(
                    'http://p0.meituan.net/tuling/99592b7ffad49b4891b3d01d397d7a571468959.gif'),
                fit: BoxFit.cover,
              ),
              borderRadius: const BorderRadius.all(Radius.circular(12))),
          height: 323,
          width: SystemUtil.getScreenWidth(context) - 24,
          padding: const EdgeInsets.fromLTRB(15, 10, 15, 20),
          child: Column(children: [
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              GestureDetector(
                onTap: () {
                  pageModel.changePageType(VoiceAssistantPageType.home);
                },
                child: Container(
                  padding: const EdgeInsets.only(right: 10, top: 5, bottom: 5),
                  alignment: Alignment.centerLeft,
                  child: Image.asset('images/voice/voice_back.png',
                      fit: BoxFit.cover, width: 8, height: 13.5),
                ),
              ),
              const Text(
                '商家助手使用说明',
                style: TextStyle(
                    color: Color(0xFFFFFFFF),
                    fontSize: 18,
                    fontWeight: FontWeight.w500),
              ),
              GestureDetector(
                onTap: () {
                  RouteUtils.close(context);
                },
                child: Image.asset('images/voice/voice_close.png',
                    fit: BoxFit.cover, width: 12.25, height: 12.25),
              ),
            ]),
            const SizedBox(
              height: 20,
            ),
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.only(right: 5),
                child: SingleChildScrollView(
                  child: Selector<VoiceAssistantPageVo, bool>(
                    selector: (_, pageVo) {
                      return pageVo.isOpenWakeUp;
                    },
                    builder: (_, isOpenWakeup, child) {
                      return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Row(
                            //     mainAxisAlignment:
                            //         MainAxisAlignment.spaceBetween,
                            //     children: [
                            //       const Text(
                            //         '说“美团美团”呼叫我',
                            //         style: TextStyle(
                            //             color: Color(0xFFFFFFFF),
                            //             fontSize: 12,
                            //             fontWeight: FontWeight.w500),
                            //       ),
                            //       GestureDetector(
                            //         onTap: () {
                            //           pageModel.openWakeup(!isOpenWakeup);
                            //
                            //           FlutterLx.moudleClick(
                            //               '42041416',
                            //               'c_waimai_e_faoamap0',
                            //               'b_waimai_e_ukq18rti_mc',
                            //               val: {
                            //                 'speech_message_switch_status':
                            //                     !isOpenWakeup
                            //               });
                            //         },
                            //         child: Container(
                            //           decoration: BoxDecoration(
                            //               border: Border.all(
                            //                   color: const Color(0xFFFFFFFF)),
                            //               borderRadius:
                            //                   BorderRadius.circular(17)),
                            //           padding: const EdgeInsets.only(
                            //               left: 12, right: 11),
                            //           child: Text(
                            //             isOpenWakeup ? '关闭语音唤醒' : "开启语音唤醒",
                            //             style: const TextStyle(
                            //                 color: Color(0xFFFFFFFF),
                            //                 fontSize: 12,
                            //                 fontWeight: FontWeight.w500),
                            //           ),
                            //         ),
                            //       ),
                            //     ]),
                            // const SizedBox(
                            //   height: 15,
                            // ),
                            // const Text(
                            //   '语音助手指导',
                            //   style: TextStyle(
                            //       color: Color(0xFFFFFFFF),
                            //       fontSize: 18,
                            //       fontWeight: FontWeight.w500),
                            // ),
                            // const SizedBox(
                            //   height: 10,
                            // ),
                            // const Divider(
                            //   color: Color(0xFF979797),
                            //   height: 0.5,
                            // ),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: const [
                                  Text(
                                    '快速出餐',
                                    style: TextStyle(
                                        color: Color(0xFFFFFFFF),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    '“3号订单出餐完成 / 3号订单出餐啦”',
                                    style: TextStyle(
                                        color: Color(0xccFFFFFF),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ]),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: const [
                                  Text(
                                    '查看订单',
                                    style: TextStyle(
                                        color: Color(0xFFFFFFFF),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    '“看下3号订单 / 3号订单详情”',
                                    style: TextStyle(
                                        color: Color(0xccFFFFFF),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ]),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: const [
                                  Text(
                                    '调整营业状态',
                                    style: TextStyle(
                                        color: Color(0xFFFFFFFF),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    '“停止营业 / 关门啦 / 恢复营业”',
                                    style: TextStyle(
                                        color: Color(0xccFFFFFF),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ]),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: const [
                                  Text(
                                    '商品上下架',
                                    style: TextStyle(
                                        color: Color(0xFFFFFFFF),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    '“小鸡炖蘑菇下架 / 商品下架”',
                                    style: TextStyle(
                                        color: Color(0xccFFFFFF),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ]),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: const [
                                  Text(
                                    '查看经营情况',
                                    style: TextStyle(
                                        color: Color(0xFFFFFFFF),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    '“看下经营数据 / 看下经营情况”',
                                    style: TextStyle(
                                        color: Color(0xccFFFFFF),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ]),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: const [
                                  Text(
                                    '查看顾客评价',
                                    style: TextStyle(
                                        color: Color(0xFFFFFFFF),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    '“看下顾客评价 / 查看评价”',
                                    style: TextStyle(
                                        color: Color(0xccFFFFFF),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ]),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: const [
                                  Text(
                                    '商品管理',
                                    style: TextStyle(
                                        color: Color(0xFFFFFFFF),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    '“看下商品管理 / 查看商品”',
                                    style: TextStyle(
                                        color: Color(0xccFFFFFF),
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ]),
                          ]);
                    },
                  ),
                ),
              ),
            )
          ]),
        ),
      ],
    );
  }
}
