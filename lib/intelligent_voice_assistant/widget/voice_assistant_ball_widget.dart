import 'package:flutter/widgets.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_vo.dart';

List<String> paths = List.generate(45, (index) => index)
    .map((index) =>
        'images/boll_animation/icon_boll_${index.toString().padLeft(5, '0')}.png')
    .toList();

class VoiceAssistantBallWidget extends StatelessWidget {
  const VoiceAssistantBallWidget({Key key, this.pageModel}) : super(key: key);

  final VoiceAssistantPageModel pageModel;

  Widget _buildNormal(BuildContext context) {
    return Container(
      height: 32,
      decoration: BoxDecoration(
          gradient: const LinearGradient(
              colors: [Color(0xffffe14d), Color(0xffffc34d)]),
          borderRadius: BorderRadius.circular(25)),
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
      alignment: Alignment.center,
      child: const Text('按住说话',
          style: TextStyle(
            fontSize: 12.0,
            color: Color(0xFF222222),
            fontWeight: FontWeight.w500,
            decoration: TextDecoration.none,
          )),
    );
  }

  Widget _buildAnalysing(BuildContext context) {
    return Container(
      height: 32,
      decoration: BoxDecoration(
          color: Color(0xffeeeeee), borderRadius: BorderRadius.circular(25)),
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
      alignment: Alignment.center,
      child: const Text('说完了',
          style: TextStyle(
            fontSize: 14.0,
            color: Color(0xFFCCCCCC),
            fontWeight: FontWeight.w500,
            decoration: TextDecoration.none,
          )),
    );
  }

  Widget _buildListening(BuildContext context, bool isMax) {
    String url = isMax
        ? 'http://p0.meituan.net/tuling/b8532fea1b0d5d9bcf3974a95654d28b43310.gif'
        : 'http://p0.meituan.net/tuling/4fe72cb1fc9210ff01bdbea52380798821621.gif';
    return Container(
      height: 32,
      decoration: BoxDecoration(
          color: const Color(0x66FFFFFF),
          borderRadius: BorderRadius.circular(25)),
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
      alignment: Alignment.center,
      child: Image(
        image: AdvancedNetworkImage(url),
        width: 227.5,
        height: 32,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Selector<VoiceAssistantPageVo, Tuple2<VoiceAssistantState, bool>>(
        selector: (_, pageVo) {
      return Tuple2(pageVo.state, pageVo.dbSize > 40);
    }, builder: (context, data, _) {
      bool isListening = data.item1 == VoiceAssistantState.listening;
      bool dbMax = data.item2;
      return Padding(
        padding: const EdgeInsets.fromLTRB(10, 0, 10, 10),
        child: Column(
          children: [
            Text(isListening ? '松手发送' : '',
                style: const TextStyle(color: Color(0xFFFFFFFF), fontSize: 10)),
            const SizedBox(height: 6),
            GestureDetector(
                onPanStart: (details) {
                  pageModel.startListening();
                  pageModel.stopTTS();
                },
                onPanEnd: (details) {
                  pageModel.stopListening();
                },
                onPanCancel: () {
                  pageModel.stopListening();
                  // controller.repeat();
                },
                onPanDown: (details) {},
                onPanUpdate: (details) {
                  // // 获取全局坐标
                  // final globalPosition = details.globalPosition;
                  // RenderBox renderBox = context.findRenderObject() as RenderBox;
                  // final localPosition = renderBox.globalToLocal(globalPosition);
                  // final size = renderBox.size;
                  // if (localPosition.dy < 0 || localPosition.dy > size.height) {
                  //   pageModel.cancel();
                  // }
                },
                child: buildChild(data.item1, context, data.item2))
          ],
        ),
      );
    });
  }

  Widget buildChild(
      VoiceAssistantState state, BuildContext context, bool dbMax) {
    if (state == VoiceAssistantState.listening) {
      return _buildListening(context, dbMax);
    } else if (state == VoiceAssistantState.analysing) {
      return _buildAnalysing(context);
    } else {
      return _buildNormal(context);
    }
  }
}
