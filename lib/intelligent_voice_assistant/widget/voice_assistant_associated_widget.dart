import 'package:flutter/material.dart';
import 'package:flutter_advanced_networkimage/provider.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:mtf_statistics_route/mtf_statistics_exposure_container.dart';
import 'package:provider/provider.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_push_data.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_model.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_vo.dart';

class VoiceAssistantAssociatedWidget extends StatelessWidget {
  const VoiceAssistantAssociatedWidget({Key key, this.pageModel})
      : super(key: key);
  final VoiceAssistantPageModel pageModel;
  Widget _buildActionButton(
      BuildContext context, String title, Function callback) {
    return Container(
      child: GestureDetector(
        onTap: callback,
        child: Container(
          constraints: const BoxConstraints(minWidth: 50, maxWidth: 80),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                  colors: [Color(0xffffe14d), Color(0xffffc34d)]),
              borderRadius: BorderRadius.circular(16)),
          padding: const EdgeInsets.symmetric(vertical: 3.5),
          margin: const EdgeInsets.only(left: 4),
          alignment: Alignment.center,
          child: Text(title,
              style: const TextStyle(
                fontSize: 11.0,
                color: Color(0xFF222222),
                fontWeight: FontWeight.w500,
                decoration: TextDecoration.none,
              )),
        ),
      ),
    );
  }

  List<Widget> _buildConfirmList(
      BuildContext context, String user, String reply) {
    List<Widget> btnList = [
      _buildActionButton(context, '确认', () {
        VoiceAssistantReporter.reportIMConfirm(user: user, reply: reply);
        pageModel.stopTTS();
        pageModel.btnClick('确认');
      }),
      _buildActionButton(context, '取消', () {
        VoiceAssistantReporter.reportIMCancel(user: user, reply: reply);
        pageModel.stopTTS();
        pageModel.btnClick('取消');
      }),
    ];
    return btnList;
  }

  bool needShowNext(List btnList, double textWidth) {
    return true;
    // btnList.length * 100 + getTextWdith > SystemUtil.getScreenWidth(context);
  }

  @override
  Widget build(BuildContext context) {
    return Selector<VoiceAssistantPageVo, Tuple3<SceneData, String, bool>>(
        selector: (_, pageVo) {
      return Tuple3(
          pageVo.sceneData, pageVo.replyContent, pageVo.isReplyConfirmation);
    }, builder: (context, data, _) {
      SceneData sceneData = data.item1;
      String reply = data.item2;
      bool isConfirm = data.item3;
      String content = '';
      if (!isConfirm) {
        content = sceneData?.operationDesc ?? '';
      } else {
        content = "IM回复顾客:$reply";
      }
      Map<String, dynamic> params = sceneData?.toJson();
      var btnList = _buildBtnList(context, sceneData?.imBtnInfos ?? [],
          isConfirm, sceneData?.userImContent, reply);
      double getTextWdith = _getTextWidth(
          content, TextStyle(fontSize: 11, color: Colors.white), context);
      bool needNext = needShowNext(btnList, getTextWdith);
      if (needNext) {
        btnList.insert(0, Spacer());
      }
      return Visibility(
        visible: sceneData != null,
        child: MTFStatisticsExposureContainer(
          pageInfoKey: '42041416',
          cid: 'c_waimai_e_7zqo13nu',
          bid: 'b_waimai_e_z7ki4r62_mv',
          val: params,
          child: Container(
              decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AdvancedNetworkImage(
                        'http://p0.meituan.net/tuling/99592b7ffad49b4891b3d01d397d7a571468959.gif'),
                    fit: BoxFit.cover,
                  ),
                  borderRadius: const BorderRadius.all(Radius.circular(12))),
              width: SystemUtil.getScreenWidth(context) - 24,
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              child: needNext
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                margin: EdgeInsets.only(right: 2, top: 3),
                                child: Image.network(
                                    'http://p0.meituan.net/tuling/ac15c48711ff5feb5499bd4ed37f325a879.gif',
                                    fit: BoxFit.cover,
                                    width: 12.25,
                                    height: 12.25),
                              ),
                              Flexible(
                                child: Text(content,
                                    style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 12,
                                        color: Colors.white)),
                              )
                            ],
                          ),
                          Container(
                            margin: EdgeInsets.only(top: 2),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: btnList,
                            ),
                          )
                        ])
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              margin: EdgeInsets.only(right: 2, top: 3),
                              child: Image.network(
                                  'http://p0.meituan.net/tuling/ac15c48711ff5feb5499bd4ed37f325a879.gif',
                                  fit: BoxFit.cover,
                                  width: 12.25,
                                  height: 12.25),
                            ),
                            Container(
                              child: Text(content,
                                  style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontSize: 12,
                                      color: Colors.white)),
                            ),
                          ],
                        ),
                        Row(
                          children: btnList,
                        )
                      ],
                    )),
        ),
      );
    });
  }

  double _getTextWidth(String text, TextStyle style, BuildContext context) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    )..layout(minWidth: 0, maxWidth: double.infinity);
    return textPainter.width;
  }

  List<Widget> _buildBtnList(BuildContext context, List<IMBtnInfo> btnList,
      bool isConfirm, String user, String reply) {
    List<Widget> widgetList = [];
    if (!isConfirm) {
      List<Widget> widgetList1 = btnList.map((btnInfo) {
        Map<String, dynamic> params = btnInfo.toJson();
        return Container(
          child: MTFStatisticsExposureContainer(
            pageInfoKey: '42041416',
            cid: 'c_waimai_e_7zqo13nu',
            bid: 'b_waimai_e_b7bmw3e7_mv',
            val: params,
            child: _buildActionButton(context, btnInfo?.name ?? '', () {
              pageModel.messageAction(btnInfo?.id ?? 0);
              VoiceAssistantReporter.reportIMBtnMC(
                  val: btnInfo?.toJson() ?? {});
            }),
          ),
        );
      }).toList();
      widgetList.addAll(widgetList1);
    }
    if (isConfirm) {
      List<Widget> widgetList2 = _buildConfirmList(context, user, reply);
      widgetList.addAll(widgetList2);
    }
    return widgetList;
  }
}
