import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_push_data.dart';

class VoiceAssistantState {
  static const VoiceAssistantState normal = VoiceAssistantState._('');
  static const VoiceAssistantState listening = VoiceAssistantState._('我正在听');
  static const VoiceAssistantState analysing = VoiceAssistantState._('语音分析中');
  static const VoiceAssistantState tts = VoiceAssistantState._('播放中'); // 播放声音

  final String value;

  const VoiceAssistantState._(this.value);
}

enum VoiceAssistantPageType {
  home,
  setting,
}

class VoiceAssistantPageVo extends ChangeNotifier {
  /// 设置页
  VoiceAssistantPageType pageType;

  VoiceAssistantState state = VoiceAssistantState.normal;

  DateTime startTime = DateTime.now();

  /// 语音识别结果
  String asrResult;

  String ttsContent;
  String mainEntranceType; //入口来源：点击，唤醒，push；来源不同，打开弹窗后的tts播放不同
  // 页面是否已销毁
  bool disposed = false;

  //IM发过来的数据
  SceneData sceneData;

  bool _isReplyConfirmation = false;
  // 是否开启语音唤醒
  bool isOpenWakeUp = true;

  String _replyContent = '';

  double dbSize = 0.0;

  bool get isReplyConfirmation => _isReplyConfirmation;
  set isReplyConfirmation(bool value) {
    _isReplyConfirmation = value;
    safeNotifyListeners();
  }

  String get replyContent => _replyContent;

  set replyContent(String value) {
    _replyContent = value;
    safeNotifyListeners();
  }

  void setAsrResult(String asrResult) {
    this.asrResult = asrResult;
    safeNotifyListeners();
  }

  void setOperationDesc(String operationDesc) {
    sceneData ??= SceneData();
    sceneData.operationDesc = operationDesc;
    safeNotifyListeners();
  }

  @override
  void dispose() {
    disposed = true;
    super.dispose();
  }

  /// 通知页面刷新
  void safeNotifyListeners() {
    if (disposed) {
      return;
    }
    notifyListeners();
  }
}
