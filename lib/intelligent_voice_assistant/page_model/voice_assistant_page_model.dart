import 'dart:async';
import 'dart:convert';
import 'dart:core';

import 'package:flutter/cupertino.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/aot/im_scene/voice_assistant_im_msg_queue.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/agent_executor.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/agent_executor/llm_context.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/constant.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_push_data.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_scene_constant.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/voice_asr_callback.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/page_model/voice_assistant_page_vo.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/plugin/voice_assistant_plugin.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_api.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_response.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_service.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/prompt.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/im/im_tools.dart';
import 'package:waimai_e_sound/waimai_e_sound.dart';

enum ActionId { cancel, notClear, action, other }

class VoiceAssistantPageModel with VoiceASRCallback {
  VoiceAssistantPageModel();

  VoiceAssistantPageVo pageVo;
  BuildContext _context;

  VoiceAssistantASRPlugin asrPlugin = VoiceAssistantASRPlugin();

  WMAgentExecutor agentExecutor = WMAgentExecutor();

  List<Message> reportMessages = []; //一次操作动作下的聊天记录，用于埋点上报

  WMLLMContext llMContext = WMLLMContext(); // 当前问答上下文

  Function completeCallback;

  Timer _recordTimer;
  WaimaiESoundPlugin mWaimaiESoundPlugin = WaimaiESoundPlugin();

  bool isAutoClose = true;

  StreamSubscription imMegSubscription;

  set context(BuildContext value) {
    _context = value;
    llMContext.currentBuildContext = value;
  }

  void initData(VoiceAssistantPageVo pageVo, Map params, Function callback) {
    imMegSubscription ??=
        RouteUtils.subscribe(kImMessageSendCallback, _handleIMMessageCallback);
    asrPlugin.asrCallback = this;
    VoiceAssistantIMMsgManager.setVoiceAssistantProcessing(true);
    this.pageVo = pageVo;
    if (params['mainEntranceType'] != null) {
      pageVo.mainEntranceType = params['mainEntranceType'];
      if (pageVo.mainEntranceType == entranceFromPush) {
        handleSceneData();
      }
    } else {
      pageVo.mainEntranceType = entranceFromClick;
    }

    initialEntryTTSText();
    completeCallback = callback;
    pageVo.safeNotifyListeners();
  }

  void _handleIMMessageCallback(dynamic result) {
    Map data = jsonDecode(result ?? '');
    String sendResult = data['sendResult'];
    if (sendResult == 'true') {
      playTTS('已为您回复顾客');
    }
  }

  void getWakeupState() {
    SharedPreferences.getInstance().then((sp) {
      pageVo.isOpenWakeUp = sp.getBool(kShouldWakeupVoiceAssistantKey) ?? true;
      pageVo.safeNotifyListeners();
      debugPrint(
          'getsp ====> autoWakeupKey:${sp.getBool(kShouldWakeupVoiceAssistantKey)}');
    }).catchError((error) {
      KNB.sendLog(text: 'auto wakeup key:$error');
    });
  }

  /// 开始处理消息
  bool handleSceneData() {
    var map = VoiceAssistantIMMsgManager.dequeue();
    if (map == null) {
      return false;
    }
    SceneData sceneData = SceneData.fromJson(map);

    /// 当前没有待处理消息
    if (sceneData == null) {
      return false;
    }
    pageVo.mainEntranceType = sceneData?.entranceType;
    pageVo.sceneData = sceneData;
    pageVo.safeNotifyListeners();
    return true;
  }

  void addTTSListener() {
    mWaimaiESoundPlugin.setCompleteHandlerListener(
        SoundName.voiceAssistantTTSSoundWithCallback, () {
      debugPrint(
          '开始倒计时：voiceAssistantTTSSoundWithCallback $isAutoClose +++${pageVo.mainEntranceType}');
      if (isAutoClose && pageVo.mainEntranceType == entranceFromPush) {
        startTimer();
      }
    });
  }

  void removeTTSListener() {
    mWaimaiESoundPlugin
        .removeHandlerListener(SoundName.voiceAssistantTTSSoundWithCallback);
    mWaimaiESoundPlugin.removeHandlerListener(SoundName.voiceAssistantTTSSound);
  }

  ///进入页面后播放语音引导文案
  void initialEntryTTSText() {
    String entranceType = pageVo?.mainEntranceType;
    switch (entranceType) {
      case entranceFromClick:
        String guide = VoiceAssistantConstant.getRandomGuideTip();
        //点击
        playTTS("欢迎使用商家助手，您可以说$guide", changeTTS: false);
        break;
      case entranceFromWake:
        //唤醒
        playTTS("在呢，您有什么问题呢？", changeTTS: false);
        break;
      case entranceFromPush:
        //push
        String tagCode = pageVo?.sceneData?.sceneTagCode;
        String sceneStr = tagCode == sceneOrderRemind
            ? '向你催单'
            : (tagCode == sceneFoodLess
                ? '反馈餐品少送'
                : '反馈添加备注，内容是：${pageVo?.sceneData?.userImContent}');
        String action =
            pageVo?.sceneData?.imBtnInfos?.map((i) => i.name)?.join('，或者');
        playTTS("你的${pageVo?.sceneData?.orderSeq}号订单顾客$sceneStr，"
            "你可以选择$action，可以说出你的选择哦");

        break;
      default:
        break;
    }
  }

  void dispose() {
    releaseTimer();
    VoiceAssistantIMMsgManager.setVoiceAssistantProcessing(false);
    VoiceAssistantIMMsgManager.clear();
    imMegSubscription?.cancel;
    removeTTSListener();
    WaimaiESoundPlugin.stopSoundPlay(
        soundName: SoundName.voiceAssistantTTSSoundWithCallback);
    asrPlugin.asrCallback = null;
    asrPlugin.stopListening();
  }

  void changePageType(VoiceAssistantPageType pageType) {
    stopTTS();
    isAutoClose = false;
    pageVo.pageType = pageType;
    pageVo.safeNotifyListeners();
  }

  /// 开始收音
  void startListening() {
    if (pageVo.state == VoiceAssistantState.listening) {
      /// 当前正在分析中
      return;
    }
    pageVo.asrResult = '';
    asrPlugin.startListening();
    stopTTS();
    pageVo.state = VoiceAssistantState.listening;
    pageVo.safeNotifyListeners();
    VoiceAssistantReporter.reportClickVoice(
        val: {"from": pageVo?.mainEntranceType});
  }

  /// 停止收音
  void stopListening() {
    asrPlugin.stopListening();
    pageVo.state = VoiceAssistantState.analysing;
    pageVo.safeNotifyListeners();
  }

  /// 修改语音唤醒开启状态
  void openWakeup(bool isOpen) async {
    if (isOpen) {
      VoiceAssistantASRPlugin.startWakeUpListening();
    } else {
      VoiceAssistantASRPlugin.stopWakeUpListening();
    }

    SharedPreferences sp = await SharedPreferences.getInstance();
    sp.setBool(kShouldWakeupVoiceAssistantKey, isOpen);
    pageVo.isOpenWakeUp = isOpen;
    pageVo.safeNotifyListeners();
  }

  /// 开启定时器
  void startTimer() {
    _recordTimer = Timer(const Duration(seconds: 10), () {
      debugPrint('倒计时结束:$isAutoClose');
      if (isAutoClose) {
        closePage(isFromOverTime: true);
      }
    });
  }

  /// 释放定时器
  void releaseTimer() {
    if (_recordTimer != null) {
      _recordTimer.cancel();
      _recordTimer = null;
    }
  }

  void responseExecutor(AgentExecutorResponse response) {
    if (response != null) {
      if (response.ttsString != null) {
        playTTS(response.ttsString,
            soundName: SoundName.voiceAssistantTTSSound);
      }
      if (response?.needClose == true) {
        pageStateNormalAndClose();
      }
      if (response?.needInit == true) {
        llMContext.resetContext();
        pageVo.state = VoiceAssistantState.normal;
        pageVo.safeNotifyListeners();
        resetTimer();
      }
      if (response?.showChose == true) {
        pageVo.isReplyConfirmation = true;
        resetTimer();
      }
      if (!StringUtil.isEmpty(response.operationDesc)) {
        pageVo.setOperationDesc(response.operationDesc);
      }
      if (!StringUtil.isEmpty(response.replyContent)) {
        pageVo.replyContent = response.replyContent;
      }
      return;
    }
  }

  @override
  void onASRFailed() {
    pageVo.state = VoiceAssistantState.normal;
    pageVo.safeNotifyListeners();
  }

  @override
  void onASRSuccess(String audioId, String result) {
    debugPrint('asrCallback---asr数据回调成功：$result');
    if (pageVo.disposed == true) {
      return;
    }
    if (StringUtil.isEmpty(result)) {
      return;
    }
    pageVo.state = VoiceAssistantState.analysing;
    pageVo.asrResult = result;
    isAutoClose = false;
    pageVo.safeNotifyListeners();
    llMContext.addASRText(result);
    getStepChatGPT(result);
    VoiceAssistantReporter.reportASRResult(asr: result);
  }

  void sendIMMessage(String text) {
    IMTools.sendIMMessage(text, pageVo?.sceneData?.pushExtra);
    VoiceAssistantReporter.reportReplyUserContent(val: {"content": text});
    closePageDelay();
    return;
  }

  bool replyCustomerBtnIDShow() {
    bool hasImBtn = false;
    if (pageVo.sceneData != null && pageVo.sceneData.imBtnInfos != null) {
      for (var element in pageVo?.sceneData?.imBtnInfos) {
        if (element.id == replyCustomerBtnID) {
          hasImBtn = true;
        }
      }
    }
    return hasImBtn;
  }

  @override
  void onASRTempResult(String audioId, String result) {
    debugPrint('asrCallback-model页面接收--${pageVo.asrResult}--$result');
    if (pageVo.asrResult == result) {
      return;
    }
    pageVo.asrResult = result;
    if (StringUtil.isNotEmpty(result)) {
      isAutoClose = false;
    }
    pageVo.safeNotifyListeners();
  }

  @override
  void onOvertimeClose() {}

  @override
  void onVoiceDBSize(double voiceDB) {
    VoiceAssistantReporter.reportDecibel(voiceDB);
    pageVo.dbSize = voiceDB;
    pageVo.safeNotifyListeners();
  }

  void cancel() {
    playTTS('好的，我退下了');
    closePageDelay();
  }

  ActionId getActionId(int id) {
    if (id == 10000) {
      return ActionId.cancel;
    }
    if (id == -1) {
      return ActionId.notClear;
    }
    if (id > 0 && id < 10) {
      return ActionId.action;
    }
    return ActionId.other;
  }

  Future<String> getGPTRespose(WMLLMContext context) async {
    if (context.conversionState == ConversationState.initial &&
        ArrayUtil.isNotEmpty(pageVo?.sceneData?.imBtnInfos)) {
      var pushResult = await getPushGPTResponse(context);
      return pushResult.item2;
    }
    String promptCode = "";
    String prompt = '';
    String parameter = "";
    if (context.conversionState == ConversationState.initial) {
      promptCode = ChatGPTPrompt.funcListPrompt;
      parameter = 'name';
    } else if (context.conversionState == ConversationState.needsConfirmation) {
      promptCode = ChatGPTPrompt.confirmPrompt;
      parameter = 'isContinue';
    } else if (context.conversionState == ConversationState.needsMoreData) {
      prompt = context.prompt;
      promptCode = context.promptCode;
      parameter = context.promptParameter;
    }
    return LLMService.queryService(prompt, context.asrResult, parameter,
        promptCode: promptCode);
  }

  Future<Tuple2<int, String>> getPushGPTResponse(WMLLMContext context) async {
    String prompt = ChatGPTPrompt.imFuncPrompt(pageVo?.sceneData?.imBtnInfos);
    String idStr =
        await LLMService.queryService(prompt, context.asrResult, 'id');
    ActionId actionId = getActionId(int.parse(idStr));
    switch (actionId) {
      case ActionId.cancel:
        cancel();
        break;
      case ActionId.notClear:
        playTTS("不好意思，我没有听清，请再说一遍");
        break;
      case ActionId.action:
        functionNameBy(int.parse(idStr));
        break;
      case ActionId.other:
        break;
    }

    return Tuple2(int.parse(idStr), functionNameBy(int.parse(idStr)));
  }

  //分步骤询问
  Future<void> getStepChatGPT(String result) async {
    String gptResult = await getGPTRespose(llMContext);
    if (gptResult == LLMApi.chatGPTError) {
      playTTS(gptResult);
      closePage();
      return;
    }
    llMContext.addGPTResponse(gptResult);
    if (llMContext.conversionState == ConversationState.initial) {
      if (StringUtil.isNotEmpty(gptResult)) {
        VoiceAssistantReporter.reportLastOp();
      }
    }
    agentExecutorExecutor();
  }

  /// 按钮点击 确认取消
  void btnClick(String name) {
    if (name == '确认') {
      llMContext.addBtnClick(ConversationConfirmState.confirm.value);
    } else {
      llMContext.addBtnClick(ConversationConfirmState.cancle.value);
    }
    agentExecutorExecutor();
  }

  /// 消息转发 操作按钮
  void messageAction(int index) async {
    String result;
    ActionId actionId = getActionId(index);
    switch (actionId) {
      case ActionId.cancel:
        cancel();
        break;
      case ActionId.notClear:
        playTTS("不好意思，我没有听清，请再说一遍");
        break;
      case ActionId.action:
        result = functionNameBy(index);
        break;
      case ActionId.other:
        break;
    }
    if (result == null) {
      return;
    }
    llMContext.addBtnClick(result);
    agentExecutorExecutor();
  }

  Future<void> agentExecutorExecutor() async {
    Map params = currentParams();
    AgentExecutorResponse response =
        await agentExecutor.execute(llMContext, params);
    llMContext.addAgentExecute(response);
    responseExecutor(response);
  }

  Map currentParams() {
    var map = {};
    if (pageVo?.sceneData?.userImContent != null) {
      map['userImContent'] = pageVo?.sceneData?.userImContent;
    }
    if (pageVo?.sceneData?.pushExtra != null) {
      map['pushExtra'] = pageVo?.sceneData?.pushExtra;
    }
    if (pageVo?.sceneData?.orderSeq != null) {
      map['orderSeq'] = pageVo?.sceneData?.orderSeq;
    }
    if (StringUtil.isNotEmpty(pageVo?.sceneData?.wmOrderId)) {
      map['wmOrderId'] = pageVo?.sceneData?.wmOrderId;
    }
    return map;
  }

  void resetTimer() {
    isAutoClose = true;
    releaseTimer();
    startTimer();
  }

  //页面回到normal且关闭状态
  void pageStateNormalAndClose() {
    pageVo.asrResult = '';
    pageVo.safeNotifyListeners();
    asrPlugin.stopListening();
    SystemUtil.postDelayFunction(() {
      closePage();
    }, timeout: const Duration(seconds: 4));
  }

  void playTTS(String contentMeg, {String soundName, bool changeTTS = true}) {
    reportMessages.add(Message(role: 'assistant', content: contentMeg));
    if (changeTTS) {
      pageVo.state = VoiceAssistantState.tts;
    }
    pageVo.ttsContent = contentMeg;
    pageVo.safeNotifyListeners();
    debugPrint('倒计时：播放铃声  $contentMeg');
    addTTSListener();
    WaimaiESoundPlugin.startPlayTtsSound(
        soundName ?? SoundName.voiceAssistantTTSSoundWithCallback, contentMeg,
        playCount: 1, useEmbed: true, flag: true);
  }

  String functionNameBy(int index) {
    var map = {
      1: "自行配送",
      2: "加小费",
      3: "联系骑手",
      4: "换骑手",
      5: "催取餐",
      6: "电话联系顾客",
      7: "部分退款",
      8: "安抚顾客",
      9: "查看订单"
    };
    return map[index];
  }

  void stopTTS() {
    WaimaiESoundPlugin.stopSoundPlay(
        soundName: SoundName.voiceAssistantTTSSoundWithCallback);
    WaimaiESoundPlugin.stopSoundPlay(
        soundName: SoundName.voiceAssistantTTSSound);
  }

  void closePage({bool isFromOverTime}) {
    bool hasData = handleSceneData();
    if (!hasData) {
      if (isFromOverTime ?? false) {
        VoiceAssistantReporter.reportOvertimeClose();
      }
      RouteUtils.publish('voice_assistant_page_close_notification');
    }
  }

  void closePageDelay() {
    SystemUtil.postDelayFunction(() {
      closePage();
    }, timeout: const Duration(seconds: 2));
  }
}
