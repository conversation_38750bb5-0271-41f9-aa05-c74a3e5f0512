import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:wef_network/wef_request.dart';

class OrderApi {
  static Future<List> getOrder(String searchKey) async {
    Map params = {
      'searchItem': searchKey?.toString(),
      'pageSize': 10,
      'pageNum': 1,
      'code': -1,
      'sortType': 0,
    };
    return postEApi(
            path: 'gw/api/order/mix/search/common',
            params: params,
            isControlShowToast: true)
        .then((response) {
      List orders = response.data['wmOrderList'];
      return orders;
    });
  }

  //暂时未用到，加小费接口
  static Future<String> addFee(String orderViewId, String tipFee) async {
    Map<String, dynamic> params = {
      'orderViewId': orderViewId,
      'tipFee': tipFee
    };
    return postEApi(
            path: 'api/logistics/update/tipfee',
            params: params,
            isControlShowToast: true)
        .then((response) {
          
      return response?.msg;
    }).catchError((onError) {
      return null;
    });
  }

  //转自配
  static Future<bool> selfLogistics(
    String reminderOrderd,
    String reminderOrderSeq,
  ) {
    Map param = {
      'orderViewId': reminderOrderd,
    };
    return postEApi(path: 'api/logistics/orderchange/self', params: param)
        .then((response) {
      return response.code == 0;
    });
  }

  //出餐完成
  static Future<int> setFoodDone(int orderSeq, String orderId) async {
    if (StringUtil.isEmpty(orderId)) {
      return -1;
    }
    Map param = {'orderViewId': orderId};
    return postEApi(
            path: '/api/order/v5/fooddone',
            params: param,
            isControlShowToast: true)
        .then((response) {
      if (response?.data != null && response.code != null) {
        int status = response.data['status'];
        return status;
      }
    }).catchError((e) {
      return -1;
    });
  }
}
