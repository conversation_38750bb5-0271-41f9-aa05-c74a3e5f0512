import 'package:flutter/cupertino.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:tuple/tuple.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/tools/order/order_api.dart';
import 'package:waimai_e_flutter_order/order/common/utils/order_utils.dart';
import 'package:waimai_e_flutter_order/order_2.0/model/wm_order.dart';
import 'package:waimai_e_flutter_order/order_2.0/service/order_action_manager.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import 'package:wef_network/wef_request.dart';

/// 催单场景
class OrderTools {
  ///转自配功能
  static Future<Tuple2<bool, String>> selfLogisticsBtn(
      BuildContext buildContext, String orderId) async {
    WMOrder order = await getOrder(orderId);
    PoiInfo poiInfo = PoiMananger.getInstance().poiInfo;
    if (order == null || poiInfo == null) {
      return Future.value(const Tuple2(false, "没有对应的订单"));
    }
    OrderActionManager.selfLogisticsBtn(order, buildContext, poiInfo);
    return Future.value(const Tuple2(true, ""));
  }

  ///加小费功能
  static Future<Tuple2<bool, String>> addFee(String orderId) async {
    WMOrder order = await getOrder(orderId);
    PoiInfo poiInfo = PoiMananger.getInstance().poiInfo;
    if (order == null || poiInfo == null) {
      return Future.value(const Tuple2(false, "没有对应的订单"));
    }
    OrderActionManager.addFee(order, poiInfo);
    return Future.value(const Tuple2(true, ""));
  }

  ///联系骑手功能
  static Future<Tuple2<bool, String>> createPhone(
    BuildContext context,
    String orderId,
  ) async {
    WMOrder order = await getOrder(orderId);
    if (order == null) {
      return Future.value(const Tuple2(false, "没有对应的订单"));
    }
    OrderActionManager.createPhoneClickEvent(context, order, () {
      bool canPop = Navigator.of(context).canPop();
      return canPop;
    });
    return Future.value(const Tuple2(false, ""));
  }

  ///换骑手功能
  ///todo： qiuju
  static Future<Tuple2<bool, String>> changeRiderButton(
      BuildContext context, String orderId) async {
    WMOrder order = await getOrder(orderId);
    if (order == null) {
      return Future.value(const Tuple2(false, "没有对应的订单"));
    }
    OrderActionManager.changeRiderButton(order, context, () {
      bool canPop = Navigator.of(context).canPop();
      return canPop;
    });
    return Future.value(const Tuple2(false, ""));
  }

  ///催取餐功能
  static Future<Tuple2<bool, String>> urgeBtn(
      BuildContext context, String orderId) async {
    WMOrder order = await getOrder(orderId);
    if (order == null) {
      return Future.value(const Tuple2(false, "没有对应的订单"));
    }
    OrderActionManager.urgeBtn(order, context);
    return Future.value(const Tuple2(false, ""));
  }

  ///电话联系顾客功能
  static Future<Tuple2<bool, String>> callUser(
      BuildContext context, String orderId) async {
    WMOrder order = await getOrder(orderId);
    PoiInfo poiInfo = PoiMananger.getInstance().poiInfo;
    if (order == null || poiInfo == null) {
      return Future.value(const Tuple2(false, "没有对应的订单"));
    }
    OrderActionManager.callUser(order, poiInfo, context, () {
      bool canPop = Navigator.of(context).canPop();
      return canPop;
    }, didClickCallback: () {
      if (Navigator.canPop(context)) {
        RouteUtils.close(context);
      }
    });
    return Future.value(const Tuple2(false, ""));
  }

  ///部分退款功能
  static Future<Tuple2<bool, String>> partRefund(String orderId) async {
    WMOrder order = await getOrder(orderId);
    if (order == null) {
      return Future.value(const Tuple2(false, "没有对应的订单"));
    }
    OrderActionManager.partRefund(order);
    return Future.value(const Tuple2(true, ""));
  }

  static Future<String> getOrderId(String serchKey) async {
    WMOrder order = await getOrder(serchKey);
    return order?.basicInfo?.orderViewId?.toString();
  }

  static Future<bool> getOldOrderIsShow(String serchKey) async {
    WMOrder order = await getOrder(serchKey);
    return (order?.mealInfo?.isShow ?? false) &&
        (order?.mealInfo?.foodDoneButtonVo?.isShow ?? false);
  }

  static Future<bool> getNewOrderIsShow(String serchKey) async {
    WMOrder order = await getOrder(serchKey);
    return (order?.mealInfo?.isShow ?? false) &&
        (order?.mealInfo?.foodDoneExamineVo?.foodDoneButtonVo?.isShow ?? false);
  }

  static Future<bool> getOrderExamine(String serchKey) async {
    WMOrder order = await getOrder(serchKey);
    return order?.mealInfo?.isFoodExamine();
  }

  static Future<int> getOrderCanClickLimitTime(String serchKey) async {
    WMOrder order = await getOrder(serchKey);
    return order?.mealInfo?.foodDoneButtonVo?.canClickLimitTime ?? 0;
  }

  static Future<bool> getIsPhfOrder(String serchKey) async {
    WMOrder order = await getOrder(serchKey);
    return order?.businessType == 2;
  }

  //根据订单序号获取订单信息
  static Future<WMOrder> getOrder(String searchKey) async {
    if (searchKey == "-1") {
      return Future(() => null);
    }
    if (_cache[searchKey] != null) {
      return _cache[searchKey];
    }

    Map params = {
      'searchItem': searchKey?.toString(),
      'pageSize': 10,
      'pageNum': 1,
      'code': -1,
      'sortType': 0,
    };
    return postEApi(
            path: 'gw/api/order/mix/search/common',
            params: params,
            isControlShowToast: true)
        .then((response) {
      List orders = response.data['wmOrderList'];
      if (ArrayUtil.isNotEmpty(orders)) {
        // var order = orders[0];
        var orderBase = OrderFactory.createOrderBaseFromJson(orders[0]);
        if (orderBase is WMOrder) {
          WMOrder order = orderBase;
          _cache[searchKey] = order;
          Future.delayed(const Duration(seconds: 1), () {
            _cache.remove(searchKey);
          });
          return order;
        }
        return null;
      } else {
        return null;
      }
    }).catchError((e) {
      return null;
    });
  }

  static Future<bool> isShowSelfLogisticsButtonVo(String searchKey) async {
    WMOrder order = await getOrder(searchKey);
    return OrderActionManager.isShowSelfLogisticsButtonVo(order);
  }

  static Future<bool> isShowCallUser(String searchKey) async {
    WMOrder order = await getOrder(searchKey);
    return OrderActionManager.isShowCallUser(order);
  }

  static Future<bool> isShowAddFeeButtonVo(String searchKey) async {
    WMOrder order = await getOrder(searchKey);
    return OrderActionManager.isShowAddFeeButtonVo(order);
  }

  static Future<bool> isShowPhoneBtn(String searchKey) async {
    WMOrder order = await getOrder(searchKey);
    return OrderActionManager.isShowPhoneBtn(order);
  }

  static Future<bool> isShowPartRefund(String searchKey) async {
    WMOrder order = await getOrder(searchKey);
    return OrderActionManager.isShowPartRefund(order);
  }

  static Future<int> getDayseq(String searchKey) async {
    WMOrder order = await getOrder(searchKey);
    return Future.value(order?.basicInfo?.dayseq);
  }

  static final Map<String, dynamic> _cache = {};
  //查看订单详情
  static Future<bool> gotoOrderDetailPage(String orderId) async {
    if (StringUtil.isEmpty(orderId)) {
      return false;
    }
    RouteUtils.open(
        'itakeawaybiz://waimaieapi.meituan.com/mtf?mtf_page=order/detail&moduleName=waimai_e_flutter_order&orderViewId=$orderId');
    return true;
  }

  static Future<int> setFoodDone(int orderSeq, String orderId) async {
    return OrderApi.setFoodDone(orderSeq, orderId);
  }
}
