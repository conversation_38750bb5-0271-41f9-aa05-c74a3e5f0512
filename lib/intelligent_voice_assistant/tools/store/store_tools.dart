import 'package:waimai_e_native_business/waimai_e_native_business.dart';
import 'package:wef_network/wef_request.dart';

class StoreTools {
  //执行门店停止和恢复操作
  static Future<bool> setShopOperation(String status) {
    Map requestData = {'status': status};
    return postApi(
      baseUrl: EApiBaseUrl.eapi,
      path: '/api/poi/change_status',
      params: requestData,
      isControlShowToast: true,
    ).then((response) async {
      if (response != null) {
        return response.code == 0;
      }
      return false;
    }).catchError((onError) {
      return false;
    });
  }

  static bool isPoiOpen() {
    PoiInfo poiInfo = PoiMananger.getInstance().poiInfo;
    return poiInfo != null && poiInfo?.status == '1' && poiInfo?.subStatus == 0;
  }
}
