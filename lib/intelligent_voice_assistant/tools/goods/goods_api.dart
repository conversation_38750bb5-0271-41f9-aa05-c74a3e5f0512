import 'dart:convert';

import 'package:mtf_toast/mtf_toast.dart';
import 'package:wef_network/wef_request.dart';

class FoodSellStatus {
  //null 为首次进入尚未初始化的情况。
  static bool isAllOrDefault(int sellStatus) {
    return sellStatus == null || sellStatus == all;
  }

  // 是否为不可售或折扣Tab
  static bool isOffSellOrDiscountTab(int sellStatus) {
    return sellStatus == notInSell ||
        sellStatus == soldOut ||
        sellStatus == discount ||
        sellStatus == buyGift;
  }

  // -1 全部; 0 售卖中; 1 已下架;2 信息不全; 3 售罄; 4 无图; 5 待添加标签; 6 问题图片; 7 折扣 8 买赠 9单点不送
  // 保证数据一致性，将 WMBFoodSellStatus 映射为 WMBSelectButtonDataItemType
  static const int all = -1;
  static const int selling = 0;
  static const int notInSell = 1;
  static const int infoIncomplete = 2;
  static const int soldOut = 3;
  static const int noPicture = 4;
  static const int needAddTag = 5;
  static const int errorImage = 6;
  static const int discount = 7;
  static const int buyGift = 8;
  static const int noSingleDelivery = 9;
}

class WmProductSpuVo {
  WmProductSpuVo({
    this.id,
    this.name,
    this.tagId,
    this.tagName,
    this.price,
    this.unit,
    this.minOrderCount,
    this.sellStatus,
    this.description,
    this.monthSale,
    this.stock,
    this.shippingTimeX,
    this.discountPrice,
    this.secondTagId,
    this.secondTagName,
    this.level,
    this.offSellType,
    this.spTagId,
    this.categoryName,
    this.skuMaxPrice,
    this.searchTerms,
    this.example,
    this.allSoldOut,
    this.someSoldOut,
    this.needBindTag,
    this.wmProductVideoId,
    this.isChecked = false,
    this.recommendName,
    this.overrangeOperationStatus,
    this.onlySellInCombo,
    this.comboPriceIllegal,
    this.hasBindCoupon,
    this.cannotRecover,
    this.isNewCombo,
    this.showViolationPic,
  });

  WmProductSpuVo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    tagId = json['tagId'];
    tagName = json['tagName'];
    price = json['price']?.toDouble() ?? 0.0;
    unit = json['unit'];
    minOrderCount = json['minOrderCount'];
    sellStatus = json['sellStatus'];
    description = json['description'];
    monthSale = json['monthSale'];
    stock = json['stock'];

    shippingTimeX = json['shippingTimeX'];
    discountPrice = json['discountPrice']?.toDouble() ?? 0.0;
    secondTagId = json['secondTagId'];
    secondTagName = json['secondTagName'];
    level = json['level'];
    offSellType = json['offSellType'];

    spTagId = json['spTagId'];
    categoryName = json['categoryName'];
    skuMaxPrice = json['skuMaxPrice']?.toDouble() ?? 0.0;
    searchTerms = json['searchTerms'];
    example = json['example'];

    allSoldOut = json['allSoldOut'];
    someSoldOut = json['someSoldOut'];
    needBindTag = json['needBindTag'];
    recommendName = json['recommendNameByIntelligent'] ?? '';
    overrangeOperationStatus = json['overrangeOperationStatus'] ?? 0;
    canBeSelected = json['canBeSelected'] == 0;
    cannotSelectedReason = json['cannotSelectedReason'] ?? '';
    onlySellInCombo = json['onlySellInCombo'] ?? false;
    comboPriceIllegal = json['comboPriceIllegal'] ?? false;
    hasBindCoupon = json['hasBindCoupon'] ?? 0;
    relateComboList = json['relateComboList'] ?? [];
    isNewCombo = json['isNewCombo'] ?? false;

    cannotRecover = json['cannotRecover'] ?? false;
    appFoodCode = json['app_food_code'] ?? '';

    showViolationPic = json['showDefaultPic'] ?? false;
  }

  int id;
  String name; // 商品标题
  int tagId; // 商品分类
  String tagName; //商品分类
  double price; //价格

  String unit; //商品单位
  int minOrderCount; //最小购买数量
  int sellStatus; //上下架状态 0上架 1下架
  String description; //商品描述
  int monthSale; //月售
  int stock;
  bool showViolationPic; // 是否展示违规图(当因图片违规被删除，从而导致商品无图时，要展示违规提示图)
  String shippingTimeX;
  double discountPrice;
  int secondTagId;
  String secondTagName;
  int level;
  int offSellType; //下架类型，0－正常下架  1－风控下架
  int spTagId; //后台分类Id，标准分类ID
  String categoryName;
  double skuMaxPrice;
  Null searchTerms;
  int example;
  bool allSoldOut;
  bool someSoldOut;
  bool needBindTag;
  int wmProductVideoId;
  int overrangeOperationStatus; // 是否超范围经营。1是，0否。

  // 活动tag信息
  String discountTips;

  // 活动

  // 商品标签信息，数据来自 /reuse2/product/r/template 非 getSpu
  bool isChecked;

  //v6.13 类目升级，商品类目状态为2时表示旧类目状态。
  bool isOutdatedCategory;

  String recommendName; // 商品信息优化推荐名称
  // 为主图推荐的优化图
  List<String> recommendedPics;

  // 套餐商品，是否能够被选择，0-可以选择  1-不可以选择
  bool canBeSelected;

  // 套餐商品，展示不能被选择的原因
  String cannotSelectedReason = '';

  // 是否仅在套餐内可售
  bool onlySellInCombo;

  // 套餐价格异常
  bool comboPriceIllegal;

  // 是否有绑定的商品券（0:未绑定 1:绑定了商品券）
  int hasBindCoupon;

  // 商品单品关联的套餐商品名列表
  List<String> relateComboList;

  // 是否新套餐
  bool isNewCombo;

  String appFoodCode;

  bool cannotRecover; // 回收站商品是否可恢复

  // 商品信息版本，用于单商品刷新
  int version;

  /// 更新数据版本号
  void updateVersion() {
    version = (version ?? 0) + 1;
  }

  /// 是否绑定了商品券
  bool get isBindCoupon => hasBindCoupon == 1;
}

//门店审核状态，默认状态-1
class ProductState {
  static const int def = -1;
  static const int invalid = 0;
  static const int waitReviewing = 1;
  static const int reviewing = 2;
  static const int reviewReject = 3;
  static const int reviewPassed = 4;
}

class ProductStatusInfo extends Object {
  const ProductStatusInfo(this.status, this.describe, this.memo);

  static ProductStatusInfo fromJson(Map<String, dynamic> parsedJson) {
    return ProductStatusInfo(
        parsedJson['status'], parsedJson['describe'], parsedJson['memo']);
  }

  final String describe;
  final String memo;
  final int status;
}

class GoodsApi {
  /// 单品/套餐上下架
  static Future<ResponseData> updateSellingStatus(int status, int spuId) {
    Map params = {
      'sellStatus': '$status',
      'spuIds': jsonEncode([spuId]),
    };
    return postEApi(path: '/api/product/setSpuSell', params: params);
  }

  //模糊查询获取第一个商品
  static Future<WmProductSpuVo> getSpuVO(String keyWord) async {
    return fetchGoodsListBySearch(keyWord).then((value) {
      if (value?.isNotEmpty ?? false) {
        return value[0];
      }
    }).catchError((e) {
      return null;
    });
  }

  static Future<List<WmProductSpuVo>> fetchGoodsListBySearch(String spuPrefix,
      {int scenario = 1}) {
    Map<String, dynamic> params = {
      'spuPrefix': spuPrefix,
      'scenario': scenario,
    };
    return getEApi(
      params: params,
      path: '/api/product/searchSpu',
    ).then((response) {
      List<WmProductSpuVo> searchSpuList = <WmProductSpuVo>[];
      if (response?.data == null ||
          response?.data == '' ||
          (response?.data as List).isEmpty) {
        return searchSpuList;
      }

      response?.data?.forEach((searchVo) {
        if (searchVo != null && searchVo['spuVos'] != null) {
          searchVo['spuVos'].forEach((spuVo) {
            WmProductSpuVo productSpuVo = WmProductSpuVo.fromJson(spuVo);
            searchSpuList.add(productSpuVo);
          });
        }
      });
      return searchSpuList;
    }).catchError((error) {
      String errString = "网络请求异常，请稍后重试";
      if (error is NetWorkError && error.message != null) {
        errString = error.message;
      }
      MTFToast.showToast(msg: errString);
    });
  }

  /// 商品状态，接口文档：https://km.sankuai.com/collabpage/1827509374
  static Future<ProductStatusInfo> fetchProductState() {
    return getEApi(path: '/api/product/online/status').then((response) {
      return ProductStatusInfo.fromJson(response.data);
    }).catchError((error) {});
  }
}
