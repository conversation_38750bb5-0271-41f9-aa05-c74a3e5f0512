class ChatGPTResponse {
  ChatGPTResponse(
      {this.id, this.created, this.model, this.object, this.choices});

  factory ChatGPTResponse.fromJson(Map<String, dynamic> json) {
    List list = json['choices'];

    List<Choice> newList =
        // ignore: unnecessary_null_comparison
        list != null ? list.map((m) => Choice.fromJson(m)).toList() : [];

    return ChatGPTResponse(
      id: json['id'],
      created: json['created'],
      model: json['model'],
      object: json['object'],
      choices: newList,
    );
  }

  String id;
  num created;
  String model;
  String object;
  List<Choice> choices;
}

class Choice {
  Choice({this.message, this.index});

  factory Choice.fromJson(Map<String, dynamic> json) {
    return Choice(
      index: json['index'],
      message: Message.fromJson(json['message']),
    );
  }
  final Message message;
  final int index;
}

class Message {
  const Message({
    this.role,
    this.content,
    this.contentByPrompt,
    this.promptCode,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      role: json['role'],
      content: json['content'],
      contentByPrompt: json['contentByPrompt'],
      promptCode: json['promptCode'],
    );
  }
  final String role;
  final String content;
  final int contentByPrompt; //content是否通过prompt获取 0-否 1-是
  final String promptCode; //对应的promptId

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['role'] = role;
    data['content'] = content;
    data['contentByPrompt'] = contentByPrompt;
    data['promptCode'] = promptCode;
    return data;
  }
}

class CustomFieldValue {
  const CustomFieldValue({
    this.name,
    this.value,
  });

  factory CustomFieldValue.fromJson(Map<String, dynamic> json) {
    return CustomFieldValue(
      name: json['name'],
      value: json['value'],
    );
  }
  final String name;
  final String value;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['value'] = value;
    return data;
  }
}

class ChatGPTPromptResponse {
  ChatGPTPromptResponse({this.status, this.message, this.data});

  factory ChatGPTPromptResponse.fromJson(Map<String, dynamic> json) {
    return ChatGPTPromptResponse(
      status: json['status'],
      message: json['message'],
      data: PromptApplyWebResponse.fromJson(json['data']),
    );
  }

  int status;
  String message;
  PromptApplyWebResponse data;
}

class PromptApplyWebResponse {
  const PromptApplyWebResponse({
    this.finalPrompt,
    this.result,
  });

  factory PromptApplyWebResponse.fromJson(Map<String, dynamic> json) {
    return PromptApplyWebResponse(
      finalPrompt: json['finalPrompt'],
      result: json['result'],
    );
  }
  final String finalPrompt;
  final String result;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['finalPrompt'] = finalPrompt;
    data['result'] = result;
    return data;
  }
}
