import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/im_push_data.dart';

class ChatGPTPrompt {
  static const String funcListPrompt = 'FUNC_LIST';
  static const String productPrompt = 'PRODUCT';
  static const String orderPrompt = 'ORDER';
  static const String servicePrompt = 'SERVICE';
  static const String confirmPrompt = 'CONFIRM';
  static const String firstPrompt = 'FIRST_CHECK';
  // static const String fuzzySearchOrderPrompt = 'FUZZY_SEARCH_ORDER';

  static String fuzzyFoodDonePrompt() {
    return _fuzzySearchPrompt('订单出餐', '出餐');
  }

  static String fuzzyOrderDetailPrompt() {
    return _fuzzySearchPrompt('查看订单', '看订单');
  }

  static String fuzzyCallUserPrompt() {
    return _fuzzySearchPrompt('联系订单顾客', '联系顾客');
  }

  static String _fuzzySearchPrompt(String action, String example) {
    return '我们现在需要进行$action操作，这个操作支持通过订单号，订单序号，商品名称，手机尾号，顾客地址来进行。请根据我说的内容，判断是否包含在这四个参数范围内，并返回相应的数据。例如，如果我说"10号订单"，你应该返回{"orderParameter":"10"}。如果我说"手机尾号1234$example"，你应该返回{"orderParameter":"1234"}。如果我说"顾客地址望京科技园"，你应该返回{"orderParameter":"望京科技园"}。如果我说的内容没有命中这四个参数的任何一个，或者没有提供任何有效的数据，你应该返回{"orderParameter":""}。请注意，返回的数据必须严格按照这个格式，不要添加任何多余的文案解释。只有当我说的内容确实匹配到订单号，订单序号，商品名称，手机尾号这四个参数中的任何一个，orderParameter才会有数据。特别注意，如果我说的是数字，比如"三七零二"，你应该将其识别为对应的阿拉伯数字，即"3702"，并返回{"orderParameter":"3702"}。请特别注意，如果我说的内容包含"幺"，你应识别为"1"，并保持其在数字中的顺序不变。比如我说"幺三幺八"，你应该返回{"orderParameter":"1318"},我说"二九幺六"，你应该返回{"orderParameter":"2916"}，我说"八九幺幺"，你应该返回{"orderParameter":"8911"},我说"二幺幺三"，你应该返回{"orderParameter":"2113"}。';
  }

  static String imFuncPrompt(List<IMBtnInfo> operations) {
    String action = operations.map((item) => item.toString()).join("还是");
    action = action +
        "还是{'name':'不需要'，'des':'不需要，不用了，算了，不，算了、不操作了、等一下、先不了、不了、缓缓吧、取消'，'id'=10000}";
    IMBtnInfo op = operations[0];

    return '请根据下面要求操作，并必须返回#json#格式，请严格按照示例返回，不要有多余的文案解释。'
        '判断与$action的哪项意图是匹配的。请返回他的id,例如我是的是"${op.name}",则返回{"id":${op.id}}'
        '如果都没命中，返回{"id":-1}'
        '请返回的内容是一个json对象，可以解析成功'
        '请严格按照示例返回，不要有多余的文案解释。';
  }
}
