import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/voice_assistant_reporter.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_general.dart';
import 'package:wef_network/wef_request.dart';

import 'llm_response.dart';

class LLMApi {
  static const String chatGPTError = '系统溜号了，稍后再试一下吧';

  //测试账号：1694532784649670726 正式：1696806567389331530
  //聊天调用
  static Future<Message> chat(List<Message> messages) async {
    List<Message> chatMessage = [];
    chatMessage.addAll(messages);
    debugPrint('ChatGPT接收的问题--message--${jsonEncode(chatMessage)}');
    Map body = {
      'appId': '1696806567389331530',
      'model': 'gpt-3.5-turbo-0613',
      'messages': jsonEncode(chatMessage),
      // 'stream': false,
      'temperature': 0.7
    };
    return postEApi(
            path: '/gw/api/friday/invioce/openai/chat',
            params: body,
            isControlShowToast: true)
        .then((response) {
      debugPrint('ChatGPT的回答--message--${response.data}}');
      if (response?.code == 0 && response?.data != null) {
        ChatGPTResponse gptResponse =
            ChatGPTResponse.fromJson(jsonDecode(response.data));
        Message message = gptResponse?.choices[0]?.message;
        VoiceAssistantReporter.reportOneQuery(chatMessage, message);
        return message;
      } else {
        VoiceAssistantReporter.reportOneQuery(chatMessage, null);
        return null;
      }
    }).catchError((e) {
      debugPrint('jiekou异常:$e');
      return null;
    });
  }

  //请求后端接口获得文案：场景1：bc联动生成的回复内容
  static Future<UnionAnswerData> getGptTextResult(
      Map<String, dynamic> params, String sceneType) async {
    Map body = {
      'context': jsonEncode(params),
      'sceneType': sceneType,
    };
    return postEApi(
            path: '/gw/api/friday/invioce/general/chat',
            params: body,
            isControlShowToast: true)
        .then((response) {
      if (response?.code == 0 && response?.data != null) {
        GeneralChatResponse generalChatResponse =
            GeneralChatResponse.fromJson(response.data);
        return generalChatResponse?.unionAnswerData;
      } else {
        return null;
      }
    }).catchError((e) {
      debugPrint('接口异常:$e');
      return null;
    });
  }

  static Future<bool> getGrayAb(String key) {
    return postEApi(
      path: '/gw/api/friday/invioce/common/gray',
      params: {'key': key},
    ).then((value) {
      if (value?.data == null) {
        return false;
      }
      bool isGray = value.data;
      return isGray ?? false;
    });
  }
}
