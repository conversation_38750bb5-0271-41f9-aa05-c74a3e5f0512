import 'dart:convert';

import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/common/utils.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/prompt.dart';

import 'llm_api.dart';
import 'llm_response.dart';

class LLMService {
  static Future<String> queryService(
      String prompt, String content, String parameter,
      {String promptCode}) async {
    List<Message> messages =
        await _prepareMessages(prompt, content, parameter, promptCode);
    Message replyMessage = await LLMApi.chat(messages);
    if (replyMessage == null || replyMessage?.content == LLMApi.chatGPTError) {
      return LLMApi.chatGPTError;
    }
    return _extractReplyResult(replyMessage.content, parameter);
  }

  static Future<List<Message>> _prepareMessages(String prompt, String content,
      String parameter, String promptCode) async {
    List<Message> messages = <Message>[];
    if (StringUtil.isEmpty(promptCode)) {
      messages.add(createMessage('', promptCode: ChatGPTPrompt.firstPrompt));
    }
    messages.add(createMessage(prompt, promptCode: promptCode));
    messages.add(createMessage('我说的是:$content,请回复'));
    return messages;
  }

  static String _extractReplyResult(String replyResult, String parameter) {
    Map<String, dynamic> funcMap =
        Utils.isJsonString(replyResult) ? jsonDecode(replyResult) : {};
    if (funcMap.containsKey(parameter)) {
      return funcMap[parameter].toString();
    } else {
      return LLMApi.chatGPTError;
    }
  }

  /// 客服身份
  static Future<String> customerService(String result) async {
    List<Message> serviceMessages = _createServiceMessages(result);
    Message serviceReplyMessage = await LLMApi.chat(serviceMessages);
    String serviceReplyContent = serviceReplyMessage?.content ?? '';
    return serviceReplyContent;
  }

  static Future<String> createServiceChat(String result) async {
    List<Message> serviceMessages = [];
    Message serviceMessage =
        createMessage('', promptCode: ChatGPTPrompt.servicePrompt);
    Message message = createMessage(result);
    serviceMessages.add(serviceMessage);
    serviceMessages.add(message);
    Message serviceReplyMessage = await LLMApi.chat(serviceMessages);
    if (serviceReplyMessage == null) {
      return LLMApi.chatGPTError;
    }
    String serviceReplyContent = serviceReplyMessage?.content;
    //人性化回答上报数据
    List<Message> reportMessages = [];
    reportMessages.add(createMessage(result));
    reportMessages.add(serviceReplyMessage);
    return serviceReplyContent;
  }

  static List<Message> _createServiceMessages(String result) {
    return [
      createMessage('', promptCode: ChatGPTPrompt.servicePrompt),
      createMessage(result),
    ];
  }

  //生成message
  static Message createMessage(String content,
      {String promptCode, String role}) {
    if (StringUtil.isNotEmpty(promptCode)) {
      return Message(
          role: role ?? 'system',
          content: content,
          contentByPrompt: 1,
          promptCode: promptCode);
    } else {
      return Message(
          role: role ?? 'user',
          content: content,
          contentByPrompt: 0,
          promptCode: '');
    }
  }
}
