class GeneralChatResponse {
  const GeneralChatResponse(
      {this.sceneType, this.answer, this.unionAnswerData});
  factory GeneralChatResponse.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return null;
    }
    return GeneralChatResponse(
      sceneType: json['sceneType'],
      answer: json['answer'],
      unionAnswerData: UnionAnswerData.fromJson(json['unionAnswer']),
    );
  }
  final String sceneType;
  final String answer;
  final UnionAnswerData unionAnswerData;
}

class UnionAnswerData {
  const UnionAnswerData({
    this.comfortText,
    this.suggestText,
  });
  factory UnionAnswerData.fromJson(Map<String, dynamic> json) {
    return UnionAnswerData(
      comfortText: json['comfortText'],
      suggestText: json['suggestText'],
    );
  }
  final String comfortText;
  final String suggestText;
}
