import 'dart:math';

const String kShouldWakeupVoiceAssistantKey =
    'com.meituan.waimai.e.shouldWakeupVoiceAssistant';
//IM发送消息成功或者失败的广播
const String kImMessageSendCallback =
    "waimai_e_flutter/send_im_message_callback";

class VoiceAssistantConstant {
  static const String bcImComfort = 'COMFORT'; //BC场景生成回复文案
  static const String bcImComfortSuggest = 'COMFORT_SUGGEST'; //BC场景生成回复和建议文案
  //控制话术接口是否在push处还是在点击安抚顾客处触发
  static const String bcImGenerateTextGrayKey =
      'voice_assistant_suggest_word_gray';

  static const List<String> guideTips = [
    "停止营业", //0
    "恢复营业", //1
    "商品下架", //2
    "商品上架", //3
    "3号订单出餐完成", //4
    '查看订单', //5
    '查看"经营分析"', //6
    '查看"顾客评价"', //7
    '查看"活动中心"', //8
    '查看商品', //9
    '查看"配送服务"', //10
    '查看"服务市场"', //11
    '查看"规则中心"', //12
    '查看"店铺装修"', //13
    '查看"美团会员"',
    '查看"活动报名"',
    '查看"商品助手"',
    '查看"商家社区"',
    '查看"外卖课堂"',
    '查看"到店自取"',
    '查看"微信推广码"',
    '查看"订单保险"',
    '查看"极速退款"',
    '查看"外卖安心卡"',
    '修改营业状态',
    '设置门店信息',
    '设置营业时间',
    '查看"歇业保护"',
    '设置订单',
    '设置消息与铃声',
    '设置打印',
    '查看"意见反馈"',
    '骑手不接单',
    '申请餐损赔付',
    '处理退款',
    // '完成功能',
    // '提升单量',
    // '提升排名',
    // '差评回复',
    '查看门店ID'
  ];

  static String getRandomGuideTip() {
    int index = Random().nextInt(guideTips.length);
    return guideTips[index];
  }
}
