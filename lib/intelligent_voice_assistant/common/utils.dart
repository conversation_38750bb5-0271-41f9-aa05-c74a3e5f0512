import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_sound/blacklist/waimai_e_sound_plugin.dart';
import 'package:waimai_e_sound/waimai_e_sound.dart';

void playTTS(String content, {String soundName = ''}) {
  WaimaiESoundPlugin.startPlayTtsSound(soundName, content, playCount: 1);
}

class Utils {
  //判断是否为json串
  static bool isJsonString(String str) {
    if (str == null || StringUtil.isEmpty(str)) {
      return false;
    }
    if (str.startsWith("{") && str.endsWith("}")) {
      try {
        return true;
      } catch (e) {
        return false;
      }
    }
    return false;
  }
}
