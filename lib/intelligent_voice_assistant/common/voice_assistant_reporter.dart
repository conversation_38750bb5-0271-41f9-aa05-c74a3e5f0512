import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/service/llm_response.dart';

class VoiceAssistantReporter {
  static const String pageInfoKey = "42041416";
  static const String cid = "c_waimai_e_7zqo13nu";

  static Map<String, dynamic> allMessage = {};

  static List<int> dbs = [];

  static void _reportMV(String bid, {Map<String, dynamic> val}) {
    FlutterLx.moudleView(pageInfoKey, cid, bid, val: val);
  }

  static void _reportMC(String bid, {Map<String, dynamic> val}) {
    FlutterLx.moudleClick(pageInfoKey, cid, bid, val: val);
  }

  static void _addAllMessageWithTime(Map<String, dynamic> param) {
    String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    Map<String, dynamic> temp =
        param.map((key, value) => MapEntry('${key}_$timestamp', value));
    allMessage.addAll(temp);
  }

  static void reportClose(DateTime startTime, String from) {
    Map<String, String> params = {
      'time': DateTime.now().difference(startTime).inSeconds?.toString(),
      'from': from
    };
    allMessage.addAll(params);
    _reportMC("b_waimai_e_hpxwph3f_mc", val: allMessage);
    allMessage.clear();
  }

  static void reportSetting() {
    _reportMC("b_waimai_e_169905cr_mc");
  }

  static void reportOneQuery(List<Message> message, Message gpt) {
    List<Message> list = message ?? [];
    list.add(gpt);
    Map<String, dynamic> json = list?.asMap()?.map((index, item) {
      return MapEntry('${item?.role}_$index', item?.toJson());
    });
    _addAllMessageWithTime(json);
    _reportMV("b_waimai_e_6iwpjgri_mv", val: json);
  }

  static void reportIMBtnMC({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_b7bmw3e7_mc', val: val);
  }

  static void reportReplyUserContent({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_ge5j9kmt_mc', val: val);
  }

  static void reportGenImMessage({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_72tzmoup_mc', val: val);
  }

  static void reportIMConfirm({String user, String reply}) {
    _reportMC('b_waimai_e_v913tpbc_mc', val: {"user": user, "replay": reply});
  }

  static void reportIMCancel({String user, String reply}) {
    _reportMC('b_waimai_e_niuiq5tf_mc', val: {"user": user, "replay": reply});
  }

  static void reportASRResult({String asr}) {
    _reportMC('b_waimai_e_43wc98d7_mc', val: {"asr": asr});
  }

  /// 超时关闭弹窗
  static void reportOvertimeClose() {
    _reportMC('b_waimai_e_5qzsh1cl_mc');
  }

  static void reportDecibel(double db) {
    if (dbs.length > 3) {
      return;
    }
    dbs.add(db?.toInt() ?? 0);
    Map<String, dynamic> val = {"db": db?.toInt()};
    _reportMC('b_waimai_e_h5m5r0tv_mc', val: val);
  }

  static void reportLastOp({String operation}) {
    _addAllMessageWithTime({'operation': operation});
    _reportMC('b_waimai_e_phv0598h_mc', val: allMessage);
  }

  static void reportPush({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_fuwuv3ut_mc', val: val);
  }

  /// 商品上下架
  static void reportGoodToList({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_8tril7ta_mc', val: val);
  }

  /// 出餐完成
  static void reportFoodDone({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_lz866o04_mc', val: val);
  }

  /// 查看订单
  static void reportOrderDetail({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_kg9tiuh7_mc', val: val);
  }

  /// 联系顾客
  static void reportCallUser({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_tyxvojed_mc', val: val);
  }

  /// 开门营业
  static void reportOpenDoor({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_nxogwn67_mc', val: val);
  }

  /// 点击按住说话按钮
  static void reportClickVoice({Map<String, dynamic> val}) {
    _reportMC('b_waimai_e_59nhn4lb_mc', val: val);
  }
}
