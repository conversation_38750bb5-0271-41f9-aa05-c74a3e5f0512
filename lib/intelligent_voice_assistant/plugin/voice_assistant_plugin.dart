import 'dart:core';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:waimai_e_flutter_house_keeper/intelligent_voice_assistant/model/voice_asr_callback.dart';

class VoiceAssistantASRPlugin {
  VoiceAssistantASRPlugin() {
    init();
  }

  static const _voicePlugin = MethodChannel('com.wme.voice/plugin');
  VoiceASRCallback _asrCallback;

  void init() {
    _voicePlugin.setMethodCallHandler(_methodHandler);
  }

  VoiceASRCallback get asrCallback => _asrCallback;

  set asrCallback(VoiceASRCallback value) {
    _asrCallback = value;
  }

  /// 开始收音
  void startListening() {
    invokeMethod('startASR');
  }

  /// 停止收音
  void stopListening() {
    invokeMethod('stopASR');
  }

  /// 语音唤醒状态是否开启
  Future<bool> isWakeupListening() async {
    bool isOpen = await invokeMethod('isWakeupListening') ?? false;
    return isOpen;
  }

  /// 开启语音唤醒
  static void startWakeUpListening() {
    invokeMethod('wakeupAndASR', {'bizID': 'dj-ad492866f1fc5a10'});
  }

  /// 关闭语音唤醒
  static void stopWakeUpListening() {
    invokeMethod('stopWakeUP');
  }

  Future<dynamic> _methodHandler(MethodCall call) async {
    // debugPrint('com.wme.voice/plugin11 1--${call.method}---${call.arguments}');
    switch (call.method) {
      case 'onOvertimeClose':
        _asrCallback?.onOvertimeClose();
        break;
      case 'tempResult':
        debugPrint(
            'asrCallback-回调tempResult--${_asrCallback == null}----${call.arguments}');
        _asrCallback?.onASRTempResult('', call.arguments);
        break;
      case 'success':
        debugPrint('asrCallback-回调success--${_asrCallback == null}');
        debugPrint(
            'com.wme.voice/plugin11 2--${call.method}---${call.arguments}');
        _asrCallback?.onASRSuccess('', call.arguments);
        break;
      case 'failed':
        _asrCallback?.onASRFailed();
        break;
      case 'onVoiceDBSize':
        _asrCallback?.onVoiceDBSize(call.arguments);
        break;
    }
  }

  static Future invokeMethod(String method, [dynamic params]) {
    return _voicePlugin.invokeMethod(method, params);
  }
}
