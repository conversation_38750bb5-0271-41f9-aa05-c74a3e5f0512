import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lx/flutter_lx.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:roo_flutter/basic_components/dialog/roo_dialog.dart';
import 'package:waimai_e_flutter_order/native_plugins/order_sound_plugin.dart';
import 'package:waimai_e_flutter_order_stone/component/base_page.dart';

@MTFRoute('voice/open/dialog')
class VoiceOpenDialog extends BasePage {
  VoiceOpenDialog({Key key, String pageName, this.params})
      : super(key: key, pageName: pageName, params: params);

  @override
  final Map<dynamic, dynamic> params;

  @override
  State<StatefulWidget> createState() {
    return VoiceOpenDialogState();
  }
}

class VoiceOpenDialogState extends State<VoiceOpenDialog> {
  final String _pageInfoKey = '42202009';
  final String _cid = 'c_waimai_e_9qy84fu2';

  @override
  void initState() {
    super.initState();
    FlutterLx.moudleView(_pageInfoKey, _cid, 'b_waimai_e_e3ggbel2_mv');
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      bottom: false,
      child: Container(
        color: const Color(0x99222222),
        child: RooDialog(
          context,
          titleText: "打开声音",
          subTitleText: "检测到您的订单响铃时手机声音过小，下次响铃时是否自动打开手机声音?",
          operationList: [
            OperationItem("打开", OperationType.confirm, callback: () {
              FlutterLx.moudleClick(
                  _pageInfoKey, _cid, 'b_waimai_e_vb68jii3_mc');
              OrderSoundPlugin.setAutoOpenVolume(VoiceControl.always);
            }),
            OperationItem("下次问我", OperationType.cancel, callback: () {
              FlutterLx.moudleClick(
                  _pageInfoKey, _cid, 'b_waimai_e_58tc7bi5_mc');
              OrderSoundPlugin.setAutoOpenVolume(VoiceControl.next);
            }),
            OperationItem("不打开", OperationType.cancel, callback: () {
              FlutterLx.moudleClick(
                  _pageInfoKey, _cid, 'b_waimai_e_zl9pes8f_mc');
              OrderSoundPlugin.setAutoOpenVolume(VoiceControl.never);
            }),
          ],
          contentTextAlign: TextAlign.center,
          confirmText: '我知道了',
        ),
      ),
    );
  }
}

class VoiceControl {
  static const int always = 0;
  static const int next = 1;
  static const int never = 2;
}
