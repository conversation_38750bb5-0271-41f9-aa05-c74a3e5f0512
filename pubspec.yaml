name: waimai_e_flutter_house_keeper
description: A new Flutter package project.
version: 7.14.0
homepage:

environment:
  sdk: '>=2.10.0 <3.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  flap: ^0.16.463
  flutter_advanced_networkimage: ^0.7.2-mt.24
  mt_flutter_route: ^4.6.30
  flutter_lx: ^2.8.0
  provider: ^5.0.0
  waimai_e_base_ui: ^1.0.26
  flutter_markdown: ^0.6.3
  mtf_toast: ^1.1.1
  keframe: ^2.0.2
  roo_flutter: ^1.5.94
  shared_preferences: ^0.5.12
  flutter_easyloading: ^2.2.2-mt.2
  pull_to_refresh: ^2.0.0
  waimai_e_horn: ^1.1.0
  waimai_e_perf_opt: ^0.0.15
  waimai_e_sound: ^0.0.35
  mtf_statistics_route: ^0.3.4
  waimai_e_native_order: ^0.0.2
  waimai_e_push: ^0.0.6
  waimai_e_flutter_order_stone: ^6.100.30

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^1.0.0
  
dependency_overrides:
  http_parser: 4.0.0
  plugin_platform_interface: 2.1.3
  waimai_e_native_business: 0.2.16
  video_player: 2.5.2-mt.1
  toast_widget_flutter: 0.2.2
  waimai_e_flutter_order:
    git:
      url: ssh://*******************/wm/waimai_e_flutter_order.git
      ref: 7.16.56

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages

flap:
  blacklist:
    - lib/aot/*.dart
    - lib/aot/im_scene/*.dart
