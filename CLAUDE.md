# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flutter package for <PERSON><PERSON><PERSON>'s food delivery service, providing AI-powered business assistant features including:
- House Keeper: An AI assistant for restaurant management tasks
- Intelligent Voice Assistant: Voice-controlled GPT assistant for business operations
- Voice Command Manager: Voice input processing for operations
- Work Voice: Voice-based workflow management

## Development Commands

### Setup
```bash
flutter pub get
```

### Code Analysis
```bash
flutter analyze
```
Note: This project currently has 693 lint issues - these are mostly style issues (prefer_const_constructors, unnecessary_this, etc.) and do not affect functionality.

### Testing
No test directory exists - tests are not currently configured for this project.

### Dependencies
Uses Flutter 2.10.106 with custom Meituan packages and overrides. Dependencies are managed through pubspec.yaml with specific version overrides for internal packages.

## Code Architecture

### Main Components

#### 1. House Keeper (`lib/house_keeper/`)
- **Purpose**: AI-powered business assistant for restaurant management
- **Entry Point**: `house_keeper_page.dart` - Main chat interface
- **Key Components**:
  - `service/house_keeper_llm_api.dart` - LLM API integration with streaming support
  - `agent/tools/` - Business domain tools (goods, finance, comments, merchant registration)
  - `widget/custom_block/` - Custom UI components for different business functions
  - `model/` - Data models for messages, pages, and business entities

#### 2. Intelligent Voice Assistant (`lib/intelligent_voice_assistant/`)
- **Purpose**: Voice-controlled GPT assistant for business operations
- **Entry Point**: `voice_assistant_page.dart` - Voice interface
- **Key Components**:
  - `agent_executor/` - Business domain executors (goods, orders, customers, IM)
  - `service/llm_service.dart` - LLM service integration
  - `tools/` - Voice-specific business tools

#### 3. Voice Assistant (`lib/voice_assistant/`)
- **Purpose**: Voice command processing and management
- **Key Components**:
  - `voice_command_manager.dart` - Central command processing
  - `model/voice_command.dart` - Voice command data structures

#### 4. Common Utilities (`lib/common/`)
- Base UI components and utilities shared across features
- Image handling and Flutter utilities

### Architecture Patterns

#### Agent-Based Design
The system uses an agent-executor pattern where:
- Each business domain (goods, orders, customers) has its own agent executor
- Agents handle specific business logic and API calls
- Tools provide reusable functionality across agents

#### Custom Block System
UI components are organized in a custom block system:
- `custom_block_factory.dart` - Factory for creating business-specific UI blocks
- Business blocks: finance, goods, comments, merchant registration
- Each block type handles specific business workflows

#### LLM Integration
- Streaming LLM responses via Server-Sent Events (SSE)
- Multiple request handlers (FormUrlEncoded, JSON)
- Configurable prompts via `config/prompt_config.dart`

### Key Design Principles

1. **Domain Separation**: Business logic is separated by domain (goods, orders, finance, etc.)
2. **Reusable Components**: Common UI patterns are abstracted into reusable widgets
3. **Streaming Communication**: Real-time LLM responses via SSE
4. **Voice Integration**: Multiple voice input methods (ASR, commands, wake-up)
5. **Configuration-Driven**: Prompts and behaviors are configurable via data models

### Navigation
- Uses `@MTFRoute` annotations for routing
- `@Flap` annotations for feature flags
- Main pages: `open_assistant` (House Keeper) and `voice_assistant_gpt` (Voice Assistant)

### Data Flow
1. User input (text/voice) → Page Model → Agent Executor → LLM API
2. LLM response → Custom Block Factory → UI Components → User interface
3. Business actions → Domain-specific tools → External APIs → Business logic

## File Organization

- `lib/house_keeper/` - Main AI assistant features
- `lib/intelligent_voice_assistant/` - Voice-controlled assistant
- `lib/voice_assistant/` - Voice command processing
- `lib/common/` - Shared utilities and components
- `lib/aot/` - Ahead-of-time compilation specific code (blacklisted in flap config)

## Important Notes

- This is a package project, not a standalone app
- Uses internal Meituan packages and APIs
- Voice functionality requires platform-specific permissions
- Custom UI blocks are dynamically created based on LLM responses
- All business logic is domain-specific and tied to restaurant management workflows